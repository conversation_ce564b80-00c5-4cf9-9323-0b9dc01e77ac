{"name": "@uiw/codemirror-extensions-hyper-link", "version": "4.21.24", "description": "Hyper link Extensions for CodeMirror6.", "homepage": "https://uiwjs.github.io/react-codemirror/#/extensions/hyper-link", "funding": "https://jaywcjlove.github.io/#/sponsor", "author": "kenny wong <<EMAIL>>", "license": "MIT", "main": "./cjs/index.js", "module": "./esm/index.js", "scripts": {"watch": "tsbb watch src/*.ts --use-babel", "build": "tsbb build src/*.ts --use-babel"}, "repository": {"type": "git", "url": "https://github.com/uiwjs/react-codemirror.git"}, "files": ["src", "esm", "cjs"], "peerDependencies": {"@codemirror/state": ">=6.0.0", "@codemirror/view": ">=6.0.0"}, "devDependencies": {"@codemirror/state": "^6.1.0", "@codemirror/view": "^6.0.0"}, "keywords": ["codemirror", "codemirror6", "link", "url", "hyper-link", "extensions", "ide", "code"], "jest": {"coverageReporters": ["lcov", "json-summary"]}}