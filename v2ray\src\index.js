/**
 * 代理节点转换工具主入口
 */

// 首先导入兼容性修复
import './utils/index.js';

import { parseProxyUrls as parseUrls, generateProxyUrls, parseBase64Subscription } from './parsers/index.js';
import { FormatConverter } from './converters/index.js';
import { deduplicateNodes as deduplicateNodesUtil, smartDeduplicate } from './utils/deduplication.js';
import { renameNodes as renameNodesUtil, detectRegion } from './utils/rename.js';
import { OutputFormats, ProxyTypes } from './types.js';

/**
 * 代理节点转换器主类
 */
export class ProxyConverter {
  constructor(options = {}) {
    this.options = {
      autoDetectFormat: true,
      enableDeduplication: true,
      enableRename: true,
      ...options
    };
  }

  /**
   * 解析代理URL或订阅内容
   * @param {string|string[]} input - 输入内容
   * @param {string} format - 输入格式（可选，自动检测）
   * @returns {Object[]} 解析后的节点数组
   */
  parse(input, format = null) {
    try {
      if (!input) {
        return [];
      }

      // 自动检测格式
      if (!format && this.options.autoDetectFormat) {
        format = FormatConverter.detectFormat(input);
      }

      // 根据格式解析
      if (format) {
        return FormatConverter.parse(input, format);
      }

      // 尝试直接解析为URL
      if (typeof input === 'string') {
        return parseUrls(input);
      }

      if (Array.isArray(input)) {
        return parseUrls(input);
      }

      return [];
    } catch (error) {
      console.error('解析失败:', error);
      return [];
    }
  }

  /**
   * 转换为指定格式
   * @param {Object[]} nodes - 节点数组
   * @param {string} format - 输出格式
   * @param {Object} options - 转换选项
   * @returns {string|Object} 转换后的内容
   */
  convert(nodes, format, options = {}) {
    try {
      if (!nodes || !Array.isArray(nodes) || nodes.length === 0) {
        return format === OutputFormats.JSON ? [] : '';
      }

      return FormatConverter.convert(nodes, format, options);
    } catch (error) {
      console.error('转换失败:', error);
      return format === OutputFormats.JSON ? [] : '';
    }
  }

  /**
   * 节点去重
   * @param {Object[]} nodes - 节点数组
   * @param {Object} options - 去重选项
   * @returns {Object[]} 去重后的节点数组
   */
  deduplicate(nodes, options = {}) {
    try {
      if (!this.options.enableDeduplication) {
        return nodes;
      }

      if (options.smart) {
        const result = smartDeduplicate(nodes, options);
        return result.nodes;
      }

      return deduplicateNodesUtil(nodes, options);
    } catch (error) {
      console.error('去重失败:', error);
      return nodes;
    }
  }

  /**
   * 节点重命名
   * @param {Object[]} nodes - 节点数组
   * @param {Object} options - 重命名选项
   * @returns {Object[]} 重命名后的节点数组
   */
  rename(nodes, options = {}) {
    try {
      if (!this.options.enableRename) {
        return nodes;
      }

      return renameNodesUtil(nodes, options);
    } catch (error) {
      console.error('重命名失败:', error);
      return nodes;
    }
  }

  /**
   * 一键处理：解析 -> 去重 -> 重命名 -> 转换
   * @param {string|Object} input - 输入内容
   * @param {string} outputFormat - 输出格式
   * @param {Object} options - 处理选项
   * @returns {string|Object} 处理后的内容
   */
  process(input, outputFormat, options = {}) {
    try {
      const {
        inputFormat = null,
        deduplicate = true,
        rename = true,
        deduplicateOptions = {},
        renameOptions = {},
        convertOptions = {}
      } = options;

      // 1. 解析输入
      let nodes = this.parse(input, inputFormat);
      console.log(`解析完成，共 ${nodes.length} 个节点`);

      if (nodes.length === 0) {
        return outputFormat === OutputFormats.JSON ? [] : '';
      }

      // 2. 去重
      if (deduplicate) {
        const originalCount = nodes.length;
        nodes = this.deduplicate(nodes, deduplicateOptions);
        console.log(`去重完成，移除 ${originalCount - nodes.length} 个重复节点`);
      }

      // 3. 重命名
      if (rename) {
        nodes = this.rename(nodes, renameOptions);
        console.log(`重命名完成`);
      }

      // 4. 转换格式
      const result = this.convert(nodes, outputFormat, convertOptions);
      console.log(`转换为 ${outputFormat} 格式完成`);

      return result;
    } catch (error) {
      console.error('处理失败:', error);
      return outputFormat === OutputFormats.JSON ? [] : '';
    }
  }

  /**
   * 批量处理多个输入
   * @param {Array} inputs - 输入数组
   * @param {string} outputFormat - 输出格式
   * @param {Object} options - 处理选项
   * @returns {string|Object} 合并处理后的内容
   */
  batchProcess(inputs, outputFormat, options = {}) {
    try {
      const allNodes = [];

      for (const input of inputs) {
        const nodes = this.parse(input.content, input.format);
        allNodes.push(...nodes);
      }

      console.log(`批量解析完成，共 ${allNodes.length} 个节点`);

      return this.process(allNodes, outputFormat, {
        ...options,
        inputFormat: OutputFormats.JSON // 已经是解析后的节点数组
      });
    } catch (error) {
      console.error('批量处理失败:', error);
      return outputFormat === OutputFormats.JSON ? [] : '';
    }
  }

  /**
   * 获取节点统计信息
   * @param {Object[]} nodes - 节点数组
   * @returns {Object} 统计信息
   */
  getStats(nodes) {
    if (!nodes || !Array.isArray(nodes)) {
      return { total: 0, types: {}, regions: {} };
    }

    const stats = {
      total: nodes.length,
      types: {},
      regions: {},
      valid: 0,
      invalid: 0
    };

    for (const node of nodes) {
      // 统计协议类型
      if (node.type) {
        stats.types[node.type] = (stats.types[node.type] || 0) + 1;
      }

      // 统计地区
      const region = detectRegion(node.name, node.server);
      stats.regions[region] = (stats.regions[region] || 0) + 1;

      // 统计有效性
      if (this.validateNode(node)) {
        stats.valid++;
      } else {
        stats.invalid++;
      }
    }

    return stats;
  }

  /**
   * 验证节点有效性
   * @param {Object} node - 节点信息
   * @returns {boolean} 是否有效
   */
  validateNode(node) {
    return !!(
      node &&
      node.type &&
      node.server &&
      node.port &&
      node.port > 0 &&
      node.port < 65536
    );
  }

  /**
   * 获取支持的协议类型
   * @returns {string[]} 支持的协议类型
   */
  getSupportedTypes() {
    return Object.values(ProxyTypes);
  }

  /**
   * 获取支持的输出格式
   * @returns {string[]} 支持的输出格式
   */
  getSupportedFormats() {
    return Object.values(OutputFormats);
  }
}

// 创建默认实例
export const converter = new ProxyConverter();

// 导出便捷函数
export const parseProxyUrls = converter.parse.bind(converter);
export const convertNodes = converter.convert.bind(converter);
export const deduplicateNodes = converter.deduplicate.bind(converter);
export const renameNodes = converter.rename.bind(converter);
export const processNodes = converter.process.bind(converter);

// 导出所有模块
export * from './parsers/index.js';
export * from './converters/index.js';
export * from './utils/index.js';
export * from './types.js';

// 默认导出
export default ProxyConverter;
