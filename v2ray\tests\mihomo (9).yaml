proxies:
  - cipher: aes-256-gcm
    name: "\U0001F1EF\U0001F1F5日本1 | ⬇️ 7.1MB/s"
    password: 9FUHILBF7J8FJOUP
    port: 18010
    server: ti3hyra4.slashdevslashnetslashtun.net
    type: ss
    udp: true
  - cipher: aes-128-gcm
    name: "\U0001F1F8\U0001F1EC新加坡1 | ⬇️ 9.8MB/s"
    password: f16163ec-3c35-4719-a19b-68c864cdc626
    port: 13038
    server: *************
    type: ss
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1ED\U0001F1F0香港1 | ⬇️ 6.6MB/s"
    password: RlzoEILU
    port: 50723
    server: **************
    skip-cert-verify: true
    sni: **************
    type: trojan
    udp: true
  - cipher: chacha20-ietf-poly1305
    name: "\U0001F1FA\U0001F1F8美国1 | ⬇️ 6.9MB/s"
    password: 92e0a2cd-f842-42b6-84ef-dd2da5c711ac
    port: 44223
    server: 03.kill704.win
    type: ss
    udp: true
  - alpn:
      - ''
    alterId: '0'
    cipher: auto
    name: "\U0001F1EF\U0001F1F5日本2 | ⬇️ 10.0MB/s"
    network: ws
    port: 80
    server: d9479ad0-2f0d-4dd0-253d-67606186a1ed.castlepeakhospital.moe
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 14a8a0db-e922-453a-af8d-843af8734b3b
    ws-opts:
      headers:
        Host: tms.dingtalk.com
      path: /
    xudp: true
  - cipher: aes-256-cfb
    name: "\U0001F1F8\U0001F1EC新加坡2 | ⬇️ 10.2MB/s"
    password: qawszxc123
    port: 443
    server: ************
    type: ss
    udp: true
  - cipher: xchacha20-ietf-poly1305
    name: "\U0001F1ED\U0001F1F0香港2 | ⬇️ 9.4MB/s"
    password: '@CfftfYVgp4gkMHMirH6@_C'
    port: 49758
    server: *************
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1ED\U0001F1F0香港3 | ⬇️ 8.2MB/s"
    password: R2F7VQ5ABAAJQIBH
    port: 15013
    server: 8tv68qhq.slashdevslashnetslashtun.net
    type: ss
    udp: true
  - cipher: aes-256-cfb
    name: "\U0001F1EF\U0001F1F5日本3 | ⬇️ 8.5MB/s"
    password: awsps0501
    port: 443
    server: **************
    type: ss
    udp: true
  - down: ''
    fingerprint: ''
    name: "\U0001F1F7\U0001F1FA俄罗斯1 | ⬇️ 7.3MB/s"
    obfs: ''
    obfs-password: ''
    password: dongtaiwang.com
    port: 30033
    server: hy2.694463.xyz
    skip-cert-verify: true
    sni: www.bing.com
    type: hysteria2
    up: ''
  - client-fingerprint: chrome
    name: "\U0001F1ED\U0001F1F0香港4 | ⬇️ 8.3MB/s"
    password: RlzoEILU
    port: 28296
    server: *************
    skip-cert-verify: true
    sni: cdn.egvra.cn
    type: trojan
    udp: true
  - down: ''
    fingerprint: ''
    name: "\U0001F1FA\U0001F1F8美国2 | ⬇️ 7.4MB/s"
    obfs: ''
    obfs-password: ''
    password: dongtaiwang.com
    port: 57773
    server: *************
    skip-cert-verify: true
    sni: apple.com
    type: hysteria2
    up: ''
  - client-fingerprint: chrome
    name: "\U0001F1F0\U0001F1F7韩国1 | ⬇️ 6.7MB/s"
    password: RlzoEILU
    port: 28548
    server: *************
    skip-cert-verify: true
    sni: cdn.egvra.cn
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1F0\U0001F1F7韩国2 | ⬇️ 9.6MB/s"
    password: RlzoEILU
    port: 28910
    server: *************
    skip-cert-verify: true
    sni: cdn.egvra.cn
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1E6\U0001F1FA澳大利亚1 | ⬇️ 9.6MB/s"
    password: RlzoEILU
    port: 11641
    server: *************
    skip-cert-verify: true
    sni: *************
    type: trojan
    udp: true
  - alpn:
      - ''
    alterId: '0'
    cipher: auto
    name: "\U0001F1ED\U0001F1F0香港5 | ⬇️ 9.0MB/s"
    network: ws
    port: 80
    server: 0b17b6cf-2c23-f38e-114f-8f1f3bcd06a3.castlepeakhospital.moe
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 14a8a0db-e922-453a-af8d-843af8734b3b
    ws-opts:
      headers:
        Host: tms.dingtalk.com
      path: /
    xudp: true
  - client-fingerprint: chrome
    name: "\U0001F1F3\U0001F1F1荷兰1 | ⬇️ 6.8MB/s"
    password: RlzoEILU
    port: 8565
    server: *************
    skip-cert-verify: true
    sni: *************
    type: trojan
    udp: true
  - alpn:
      - ''
    alterId: '0'
    cipher: auto
    name: "\U0001F1FA\U0001F1F8美国3 | ⬇️ 6.4MB/s"
    network: ws
    port: 20012
    server: 2fea6447-81c4-2d7d-d592-a4f729209777.7head.icu
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 14a8a0db-e922-453a-af8d-843af8734b3b
    ws-opts:
      headers:
        Host: tms.dingtalk.com
      path: /
    xudp: true
  - client-fingerprint: chrome
    name: "\U0001F1F0\U0001F1F7韩国3 | ⬇️ 7.9MB/s"
    password: RlzoEILU
    port: 47655
    server: *************
    skip-cert-verify: true
    sni: *************
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1F3\U0001F1F1荷兰2 | ⬇️ 8.5MB/s"
    password: RlzoEILU
    port: 33097
    server: *************
    skip-cert-verify: true
    sni: *************
    type: trojan
    udp: true
  - alpn:
      - ''
    alterId: '0'
    cipher: auto
    name: "\U0001F1F8\U0001F1EC新加坡3 | ⬇️ 11.6MB/s"
    network: ws
    port: 80
    server: 0989a4f1-856a-4d0b-530b-aed9bf289ad8.castlepeakhospital.moe
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 14a8a0db-e922-453a-af8d-843af8734b3b
    ws-opts:
      headers:
        Host: tms.dingtalk.com
      path: /
    xudp: true
  - alpn:
      - ''
    alterId: '0'
    cipher: auto
    name: "\U0001F1F9\U0001F1FC台湾1 | ⬇️ 6.2MB/s"
    network: ws
    port: 80
    server: 205928ec-61f8-1f90-8438-1b912b849b80.castlepeakhospital.moe
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 14a8a0db-e922-453a-af8d-843af8734b3b
    ws-opts:
      headers:
        Host: tms.dingtalk.com
      path: /
    xudp: true
  - client-fingerprint: chrome
    name: "\U0001F1ED\U0001F1F0香港6 | ⬇️ 9.5MB/s"
    password: RlzoEILU
    port: 3754
    server: *************
    skip-cert-verify: true
    sni: *************
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1F0\U0001F1F7韩国4 | ⬇️ 10.9MB/s"
    password: RlzoEILU
    port: 44907
    server: *************
    skip-cert-verify: true
    sni: *************
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1F3\U0001F1F1荷兰3 | ⬇️ 9.2MB/s"
    password: RlzoEILU
    port: 15407
    server: *************
    skip-cert-verify: true
    sni: *************
    type: trojan
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1FA\U0001F1F8美国4 | ⬇️ 8.1MB/s"
    password: P1lrnsJwO4
    port: 50631
    server: ************
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1ED\U0001F1F0香港7 | ⬇️ 6.2MB/s"
    password: 9MUA3J02RL6G9NB6
    port: 15007
    server: ti3hyra4.slashdevslashnetslashtun.net
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1E8\U0001F1E6加拿大1 | ⬇️ 7.2MB/s"
    password: 9IFWX2G53HT0ZFD5
    port: 20026
    server: *************
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1F8\U0001F1EC新加坡4 | ⬇️ 9.3MB/s"
    password: CIU43ZEX3BQ3VS9F
    port: 16003
    server: ti3hyra4.slashdevslashnetslashtun.net
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1EF\U0001F1F5日本4 | ⬇️ 7.8MB/s"
    password: IFZ122XFFWS7SFVN
    port: 18003
    server: ti3hyra4.slashdevslashnetslashtun.net
    type: ss
    udp: true
  - down: ''
    fingerprint: ''
    name: "\U0001F1FA\U0001F1F8美国5 | ⬇️ 7.1MB/s"
    obfs: ''
    obfs-password: ''
    password: 6c510073-4ca8-423b-87a5-a6d73c0ca557
    port: 43999
    server: jiangzhixjp.54264944.xyz
    skip-cert-verify: true
    sni: jiangzhixjp.54264944.xyz
    type: hysteria2
    up: ''
  - cipher: aes-256-gcm
    name: "\U0001F1F8\U0001F1EC新加坡5 | ⬇️ 9.2MB/s"
    password: L0JM8AWELGJYDSUE
    port: 16006
    server: 8tv68qhq.slashdevslashnetslashtun.net
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1F8\U0001F1EC新加坡6 | ⬇️ 9.1MB/s"
    password: 8M4NQQML45SQ5GNO
    port: 16013
    server: ti3hyra4.slashdevslashnetslashtun.net
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1EF\U0001F1F5日本5 | ⬇️ 8.2MB/s"
    password: W6QW1HQ85JDNZS33
    port: 18002
    server: 8tv68qhq.slashdevslashnetslashtun.net
    type: ss
    udp: true
  - down: ''
    fingerprint: ''
    name: "\U0001F1FA\U0001F1F8美国6 | ⬇️ 8.9MB/s"
    obfs: ''
    obfs-password: ''
    password: b8bd42a9-551f-419d-b70d-4aefdd2cb074
    port: 443
    server: **************
    skip-cert-verify: true
    sni: www.bing.com
    type: hysteria2
    up: ''
  - down: ''
    fingerprint: ''
    name: "\U0001F1FA\U0001F1F8美国7 | ⬇️ 8.0MB/s"
    obfs: ''
    obfs-password: ''
    password: 92b2a3d4-f353-11ef-b714-f23c93136cb3
    port: 1443
    server: 50528787-swb8g0-sxscwg-63bp.la.shifen.uk
    skip-cert-verify: false
    sni: 50528787-swb8g0-sxscwg-63bp.la.shifen.uk
    type: hysteria2
    up: ''
  - down: ''
    fingerprint: ''
    name: "\U0001F1FA\U0001F1F8美国8 | ⬇️ 7.9MB/s"
    obfs: ''
    obfs-password: ''
    password: b6b95866-4bf9-11ee-95b5-f23c93136cb3
    port: 1443
    server: e8798f42-sw9ds0-t0kcfe-5ywi.la.shifen.uk
    skip-cert-verify: false
    sni: e8798f42-sw9ds0-t0kcfe-5ywi.la.shifen.uk
    type: hysteria2
    up: ''
  - down: ''
    fingerprint: ''
    name: "\U0001F1FA\U0001F1F8美国9 | ⬇️ 8.0MB/s"
    obfs: ''
    obfs-password: ''
    password: a346c669-6374-11ef-bc6a-f23c9313b177
    port: 1443
    server: 5f374286-svtc00-tdex4g-6qyv.la.shifen.uk
    skip-cert-verify: true
    sni: 5f374286-svtc00-tdex4g-6qyv.la.shifen.uk
    type: hysteria2
    up: ''
  - down: ''
    fingerprint: ''
    name: "\U0001F1FA\U0001F1F8美国10 | ⬇️ 7.4MB/s"
    obfs: ''
    obfs-password: ''
    password: 2058bdd6-1505-11f0-a035-f23c95b6f51d
    port: 1443
    server: 7da41e11-sw9ds0-szcxa1-dmd8.la.shifen.uk
    skip-cert-verify: false
    sni: 7da41e11-sw9ds0-szcxa1-dmd8.la.shifen.uk
    type: hysteria2
    up: ''
  - down: ''
    fingerprint: ''
    name: "\U0001F1FA\U0001F1F8美国11 | ⬇️ 9.0MB/s"
    obfs: ''
    obfs-password: ''
    password: be8cc8f6-0c6a-11f0-a5a3-f23c93141fad
    port: 1443
    server: a2fff907-sw9ds0-sxkcyq-b4pt.la.shifen.uk
    skip-cert-verify: false
    sni: a2fff907-sw9ds0-sxkcyq-b4pt.la.shifen.uk
    type: hysteria2
    up: ''
  - down: ''
    fingerprint: ''
    name: "\U0001F1FA\U0001F1F8美国12 | ⬇️ 7.8MB/s"
    obfs: ''
    obfs-password: ''
    password: 16955d72-1794-11f0-a035-f23c95b6f51d
    port: 1443
    server: 34a15372-svlxc0-sw77c8-dnss.la.shifen.uk
    skip-cert-verify: true
    sni: 34a15372-svlxc0-sw77c8-dnss.la.shifen.uk
    type: hysteria2
    up: ''
  - down: ''
    fingerprint: ''
    name: "\U0001F1FA\U0001F1F8美国13 | ⬇️ 7.2MB/s"
    obfs: ''
    obfs-password: ''
    password: b8bd42a9-551f-419d-b70d-4aefdd2cb074
    port: 443
    server: **************
    skip-cert-verify: true
    sni: www.bing.com
    type: hysteria2
    up: ''
  - down: ''
    fingerprint: ''
    name: "\U0001F1FA\U0001F1F8美国14 | ⬇️ 8.1MB/s"
    obfs: ''
    obfs-password: ''
    password: 16955d72-1794-11f0-a035-f23c95b6f51d
    port: 1443
    server: 2e54cb70-swd340-sxs14h-dnss.la.shifen.uk
    skip-cert-verify: false
    sni: 2e54cb70-swd340-sxs14h-dnss.la.shifen.uk
    type: hysteria2
    up: ''
  - down: ''
    fingerprint: ''
    name: "\U0001F1FA\U0001F1F8美国15 | ⬇️ 7.3MB/s"
    obfs: ''
    obfs-password: ''
    password: bafc1f54-7a2d-11ef-9f82-f23c93136cb3
    port: 1443
    server: b9cd5106-sw9ds0-sxihu3-an44.la.shifen.uk
    skip-cert-verify: false
    sni: b9cd5106-sw9ds0-sxihu3-an44.la.shifen.uk
    type: hysteria2
    up: ''
  - down: ''
    fingerprint: ''
    name: "\U0001F1FA\U0001F1F8美国16 | ⬇️ 11.0MB/s"
    obfs: ''
    obfs-password: ''
    password: b8bd42a9-551f-419d-b70d-4aefdd2cb074
    port: 443
    server: **************
    skip-cert-verify: true
    sni: www.bing.com
    type: hysteria2
    up: ''
  - down: ''
    fingerprint: ''
    name: "\U0001F1FA\U0001F1F8美国17 | ⬇️ 8.3MB/s"
    obfs: ''
    obfs-password: ''
    password: 16955d72-1794-11f0-a035-f23c95b6f51d
    port: 1443
    server: 9b614a41-swb8g0-sxs14h-dnss.la.shifen.uk
    skip-cert-verify: false
    sni: 9b614a41-swb8g0-sxs14h-dnss.la.shifen.uk
    type: hysteria2
    up: ''
  - down: ''
    fingerprint: ''
    name: "\U0001F1FA\U0001F1F8美国18 | ⬇️ 7.8MB/s"
    obfs: ''
    obfs-password: ''
    password: 92b2a3d4-f353-11ef-b714-f23c93136cb3
    port: 1443
    server: 9e4d0b01-swd340-sxscwg-63bp.la.shifen.uk
    skip-cert-verify: false
    sni: 9e4d0b01-swd340-sxscwg-63bp.la.shifen.uk
    type: hysteria2
    up: ''
  - down: ''
    fingerprint: ''
    name: "\U0001F1FA\U0001F1F8美国19 | ⬇️ 10.9MB/s"
    obfs: ''
    obfs-password: ''
    password: b8bd42a9-551f-419d-b70d-4aefdd2cb074
    port: 443
    server: *************
    skip-cert-verify: true
    sni: www.bing.com
    type: hysteria2
    up: ''
  - down: ''
    fingerprint: ''
    name: "\U0001F1EB\U0001F1F7法国1 | ⬇️ 6.2MB/s"
    obfs: ''
    obfs-password: ''
    password: dongtaiwang.com
    port: 31180
    server: *************
    skip-cert-verify: true
    sni: apple.com
    type: hysteria2
    up: ''
  - cipher: aes-128-gcm
    name: "\U0001F1ED\U0001F1F0香港8 | ⬇️ 9.8MB/s"
    password: sadujij!@diQojd1254
    port: 49759
    server: *************
    type: ss
    udp: true
  - name: "\U0001F1EF\U0001F1F5日本6 | ⬇️ 12.0MB/s"
    password: 7669a04d-a459-4c96-bc0e-fde51cb984f3
    port: 12032
    server: cn2.cdn.xfltd-cdn.top
    skip-cert-verify: true
    sni: cn2.cdn.xfltd-cdn.top
    type: trojan
    udp: true
  - name: "\U0001F1F0\U0001F1F7韩国5 | ⬇️ 9.4MB/s"
    password: 7669a04d-a459-4c96-bc0e-fde51cb984f3
    port: 12041
    server: cn2.cdn.xfltd-cdn.top
    skip-cert-verify: true
    sni: cn2.cdn.xfltd-cdn.top
    type: trojan
    udp: true
  - name: "\U0001F1EF\U0001F1F5日本7 | ⬇️ 10.6MB/s"
    password: 7669a04d-a459-4c96-bc0e-fde51cb984f3
    port: 12035
    server: cn2.cdn.xfltd-cdn.top
    skip-cert-verify: true
    sni: cn2.cdn.xfltd-cdn.top
    type: trojan
    udp: true
  - name: "\U0001F1EF\U0001F1F5日本8 | ⬇️ 8.2MB/s"
    password: 7669a04d-a459-4c96-bc0e-fde51cb984f3
    port: 12031
    server: cn2.cdn.xfltd-cdn.top
    skip-cert-verify: true
    sni: cn2.cdn.xfltd-cdn.top
    type: trojan
    udp: true
  - name: "\U0001F1F8\U0001F1EC新加坡7 | ⬇️ 11.5MB/s"
    password: 7669a04d-a459-4c96-bc0e-fde51cb984f3
    port: 12024
    server: cn2.cdn.xfltd-cdn.top
    skip-cert-verify: true
    sni: cn2.cdn.xfltd-cdn.top
    type: trojan
    udp: true
  - name: "\U0001F1F8\U0001F1EC新加坡8 | ⬇️ 6.4MB/s"
    password: 7669a04d-a459-4c96-bc0e-fde51cb984f3
    port: 12022
    server: cn2.cdn.xfltd-cdn.top
    skip-cert-verify: true
    sni: cn2.cdn.xfltd-cdn.top
    type: trojan
    udp: true
  - cipher: aes-256-cfb
    name: "\U0001F1EF\U0001F1F5日本9 | ⬇️ 7.5MB/s"
    password: awsps0501
    port: 443
    server: **************
    type: ss
    udp: true
  - cipher: aes-256-cfb
    name: "\U0001F1F0\U0001F1F7韩国6 | ⬇️ 11.4MB/s"
    password: yijian0503
    port: 443
    server: ************
    type: ss
    udp: true
  - name: "\U0001F1F8\U0001F1EC新加坡9 | ⬇️ 9.7MB/s"
    password: 7669a04d-a459-4c96-bc0e-fde51cb984f3
    port: 12021
    server: cn2.cdn.xfltd-cdn.top
    skip-cert-verify: true
    sni: cn2.cdn.xfltd-cdn.top
    type: trojan
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1ED\U0001F1F0香港9 | ⬇️ 6.3MB/s"
    password: e04ae67d4e4cd165
    port: 2019
    server: **************
    type: ss
    udp: true
  - name: "\U0001F300其他1-TH | ⬇️ 7.3MB/s"
    password: 7669a04d-a459-4c96-bc0e-fde51cb984f3
    port: 12076
    server: cn2.cdn.xfltd-cdn.top
    skip-cert-verify: true
    sni: cn2.cdn.xfltd-cdn.top
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1FA\U0001F1F8美国20 | ⬇️ 8.0MB/s"
    network: ws
    port: 443
    server: dmit.jhyl.bid
    tls: true
    type: vless
    udp: true
    uuid: 0cc14bae-0703-4c2d-e9de-ed4672eadd30
    ws-opts:
      headers:
        Host: dmit.jhyl.bid
        User-Agent: >-
          Mozilla/5.0 (Windows NT 10.0) AppleWebKit/537.36 (KHTML, like Gecko)
          Chrome/70.0.3538.102 Safari/537.36
      path: /download
    xudp: true
    servername: dmit.jhyl.bid
  - cipher: aes-256-gcm
    name: "\U0001F1FA\U0001F1F8美国21 | ⬇️ 9.1MB/s"
    password: c86d483c-431f-41df-bb6b-c1dcebfc7401
    port: 20010
    server: *************
    type: ss
    udp: true
  - down: ''
    fingerprint: ''
    name: "\U0001F1FA\U0001F1F8美国22 | ⬇️ 8.1MB/s"
    obfs: ''
    obfs-password: ''
    password: nfsn666
    port: 8888
    server: sj-arm.nfsn666.gq
    skip-cert-verify: true
    sni: sj-arm.nfsn666.gq
    type: hysteria2
    up: ''
  - client-fingerprint: chrome
    name: "\U0001F1FA\U0001F1F8美国23 | ⬇️ 6.3MB/s"
    password: 7669a04d-a459-4c96-bc0e-fde51cb984f3
    port: 12004
    server: cn1.cdn.xfltd-cdn.top
    skip-cert-verify: true
    sni: cdn.alibaba.com
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1EF\U0001F1F5日本10 | ⬇️ 10.0MB/s"
    password: nktaqlk-1O8bEfVXgIhUvYc_
    port: 443
    server: starlink-tko5.2513142.xyz
    skip-cert-verify: true
    sni: www.cloudflare.com
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1F8\U0001F1EC新加坡10 | ⬇️ 9.1MB/s"
    password: sH-dNCXpq8RiI_PeL6Mr4lMT
    port: 443
    server: starlink-sgp5.2513142.xyz
    skip-cert-verify: true
    sni: www.cloudflare.com
    type: trojan
    udp: true
  - name: "\U0001F1FA\U0001F1F8美国24 | ⬇️ 9.9MB/s"
    network: ws
    port: 8880
    server: **************
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
        User-Agent: >-
          Mozilla/5.0 (Linux; Android 7.0; SM-G570M Build/NRD90M)
          AppleWebKit/537.36 (KHTML, like Gecko) Chrome/69.0.3497.100 Mobile
          Safari/537.36
      path: "/Telegram\U0001F1E8\U0001F1F3 @pgkj666 /?ed=2560"
    xudp: true
  - client-fingerprint: chrome
    name: "\U0001F300其他2-未识别 | ⬇️ 10.3MB/s"
    password: nktaqlk-1O8bEfVXgIhUvYc_
    port: 443
    server: starlink-tko6.2513142.xyz
    skip-cert-verify: true
    sni: www.cloudflare.com
    type: trojan
    udp: true
  - client-fingerprint: random
    name: "\U0001F1FA\U0001F1F8美国25 | ⬇️ 8.7MB/s"
    network: ws
    port: 443
    server: *************
    tls: true
    type: vless
    udp: true
    uuid: ad32ab40-a469-4ce6-a1da-63c6cae6c546
    ws-opts:
      headers:
        Host: qifei.876566.xyz
        User-Agent: >-
          Mozilla/5.0 (Linux; Android 5.1.1; A37fw Build/LMY47V)
          AppleWebKit/537.36 (KHTML, like Gecko) Chrome/62.0.3202.84 Mobile
          Safari/537.36
      path: /?ed=2560
    xudp: true
    servername: qifei.876566.xyz
  - cipher: aes-256-gcm
    name: "\U0001F300其他3-未识别 | ⬇️ 8.4MB/s"
    password: c86d483c-431f-41df-bb6b-c1dcebfc7401
    port: 29015
    server: *************
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1EF\U0001F1F5日本11 | ⬇️ 8.7MB/s"
    password: c86d483c-431f-41df-bb6b-c1dcebfc7401
    port: 19002
    server: *************
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1F9\U0001F1FC台湾2 | ⬇️ 10.0MB/s"
    password: c86d483c-431f-41df-bb6b-c1dcebfc7401
    port: 17001
    server: *************
    type: ss
    udp: true
  - down: ''
    fingerprint: ''
    name: "\U0001F1FA\U0001F1F8美国26 | ⬇️ 6.9MB/s"
    obfs: ''
    obfs-password: ''
    password: 6e9cdc20-b92a-11ef-973a-f23c913c8d2b
    port: 8443
    server: 1fec14d5-swrwg0-sww7b0-1qwp5.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 1fec14d5-swrwg0-sww7b0-1qwp5.hy2.gotochinatown.net
    type: hysteria2
    up: ''
  - down: ''
    fingerprint: ''
    name: "\U0001F1EB\U0001F1F7法国2 | ⬇️ 6.3MB/s"
    obfs: ''
    obfs-password: ''
    password: 6c510073-4ca8-423b-87a5-a6d73c0ca557
    port: 43999
    server: jiangzhifg.54264944.xyz
    skip-cert-verify: true
    sni: jiangzhifr.54264944.xyz
    type: hysteria2
    up: ''
  - down: ''
    fingerprint: ''
    name: "\U0001F1FA\U0001F1F8美国27 | ⬇️ 7.2MB/s"
    obfs: ''
    obfs-password: ''
    password: 6c510073-4ca8-423b-87a5-a6d73c0ca557
    port: 43999
    server: jiangzhidb.54264944.xyz
    skip-cert-verify: true
    sni: jiangzhidb.54264944.xyz
    type: hysteria2
    up: ''
  - down: ''
    fingerprint: ''
    name: "\U0001F1FA\U0001F1F8美国28 | ⬇️ 6.1MB/s"
    obfs: ''
    obfs-password: ''
    password: 48173CB0-68B5-4CAB-9587-FB7CCDE206A7
    port: 5790
    server: us3.dexlos.com
    skip-cert-verify: false
    sni: us3.dexlos.com
    type: hysteria2
    up: ''
  - cipher: aes-256-gcm
    name: "\U0001F1E8\U0001F1E6加拿大2 | ⬇️ 6.5MB/s"
    password: 3557YN81P52GXJEE
    port: 20026
    server: *************
    type: ss
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1FA\U0001F1F8美国29 | ⬇️ 9.1MB/s"
    password: 3lSlAcxDSNpeKOyZ3pIlyYRC7328C3X93Aa8SFOgDX4D7An0zYSRDRuaRE8lCpCASE6Bu
    port: 443
    server: ************
    skip-cert-verify: false
    sni: theory.wireshop.net
    type: trojan
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1F8\U0001F1EC新加坡11 | ⬇️ 6.6MB/s"
    password: O8WKA9YNWXIPLZHE
    port: 16011
    server: ti3hyra4.slashdevslashnetslashtun.net
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1E8\U0001F1E6加拿大3 | ⬇️ 6.6MB/s"
    password: OJOW52P84GC951ML
    port: 20035
    server: **************
    type: ss
    udp: true
  - down: ''
    fingerprint: ''
    name: "\U0001F1FA\U0001F1F8美国30 | ⬇️ 6.0MB/s"
    obfs: ''
    obfs-password: ''
    password: 48173CB0-68B5-4CAB-9587-FB7CCDE206A7
    port: 7605
    server: us4.dexlos.com
    skip-cert-verify: false
    sni: us4.dexlos.com
    type: hysteria2
    up: ''
  - cipher: aes-256-gcm
    name: "\U0001F1ED\U0001F1F0香港10 | ⬇️ 9.3MB/s"
    password: 2HZGM3TWD6433RJ2
    port: 15014
    server: 8tv68qhq.slashdevslashnetslashtun.net
    type: ss
    udp: true
  - down: ''
    fingerprint: ''
    name: "\U0001F1FA\U0001F1F8美国31 | ⬇️ 7.8MB/s"
    obfs: ''
    obfs-password: ''
    password: 796ec552-f8e4-43c7-ac2f-5c2e668074de
    port: 30003
    server: qyxjp2.qy1357.top
    skip-cert-verify: true
    sni: qyxjp2.qy1357.top
    type: hysteria2
    up: ''
  - cipher: aes-256-gcm
    name: "\U0001F1E8\U0001F1E6加拿大4 | ⬇️ 6.0MB/s"
    password: 9Y8KM6VPSPGMG80I
    port: 20032
    server: ************
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1E8\U0001F1E6加拿大5 | ⬇️ 6.5MB/s"
    password: 9IHUC79NEREGCCVR
    port: 20025
    server: **************
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1F8\U0001F1EC新加坡12 | ⬇️ 7.4MB/s"
    password: A0KMY7WT9NQR3X3F
    port: 16004
    server: ti3hyra4.slashdevslashnetslashtun.net
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F300其他4-GR | ⬇️ 6.6MB/s"
    password: KVR39BSSX1S7IH3D
    port: 20004
    server: **************
    type: ss
    udp: true
  - down: ''
    fingerprint: ''
    name: "\U0001F1EC\U0001F1E7英国1 | ⬇️ 6.6MB/s"
    obfs: ''
    obfs-password: ''
    password: 5415e20c-dbe6-46ff-9cc1-1c092184d801
    port: 10000
    server: yingguo1.959555.xyz
    skip-cert-verify: true
    sni: yingguo1.959555.xyz
    type: hysteria2
    up: ''
  - down: ''
    fingerprint: ''
    name: "\U0001F1FA\U0001F1F8美国32 | ⬇️ 6.8MB/s"
    obfs: ''
    obfs-password: ''
    password: 48173CB0-68B5-4CAB-9587-FB7CCDE206A7
    port: 5192
    server: us3.dexlos.com
    skip-cert-verify: false
    sni: us3.dexlos.com
    type: hysteria2
    up: ''
  - down: ''
    fingerprint: ''
    name: "\U0001F1EF\U0001F1F5日本12 | ⬇️ 11.8MB/s"
    obfs: ''
    obfs-password: ''
    password: 48173CB0-68B5-4CAB-9587-FB7CCDE206A7
    port: 5347
    server: jp3.dexlos.com
    skip-cert-verify: false
    sni: jp3.dexlos.com
    type: hysteria2
    up: ''
  - down: ''
    fingerprint: ''
    name: "\U0001F1FA\U0001F1F8美国33 | ⬇️ 6.4MB/s"
    obfs: ''
    obfs-password: ''
    password: 48173CB0-68B5-4CAB-9587-FB7CCDE206A7
    port: 9166
    server: us5.dexlos.com
    skip-cert-verify: false
    sni: us5.dexlos.com
    type: hysteria2
    up: ''
  - down: ''
    fingerprint: ''
    name: "\U0001F1EB\U0001F1F7法国3 | ⬇️ 6.9MB/s"
    obfs: ''
    obfs-password: ''
    password: d946ec38-583a-41e5-b747-227bed0fb503
    port: 43999
    server: jiangzhifg.54264944.xyz
    skip-cert-verify: true
    sni: jiangzhifr.54264944.xyz
    type: hysteria2
    up: ''
  - cipher: aes-256-gcm
    name: "\U0001F1F8\U0001F1EC新加坡13 | ⬇️ 7.1MB/s"
    password: G7DZ7DUSG9VI6CCN
    port: 16009
    server: ti3hyra4.slashdevslashnetslashtun.net
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1ED\U0001F1F0香港11 | ⬇️ 6.4MB/s"
    password: U3NE5UAS2AO9RWDV
    port: 15015
    server: ti3hyra4.slashdevslashnetslashtun.net
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1E8\U0001F1E6加拿大6 | ⬇️ 6.3MB/s"
    password: GU5F7BLIKNTOW6G2
    port: 20030
    server: **************
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1ED\U0001F1F0香港12 | ⬇️ 7.3MB/s"
    password: NANGHYCW70BLYMX7
    port: 15016
    server: ti3hyra4.slashdevslashnetslashtun.net
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1ED\U0001F1F0香港13 | ⬇️ 7.2MB/s"
    password: GFT5YY53X9S4I4VA
    port: 15012
    server: 8tv68qhq.slashdevslashnetslashtun.net
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1E8\U0001F1E6加拿大7 | ⬇️ 8.7MB/s"
    password: UKSUZR1V7NDI8Z79
    port: 20030
    server: **************
    type: ss
    udp: true
  - down: ''
    fingerprint: ''
    name: "\U0001F1FA\U0001F1F8美国34 | ⬇️ 5.9MB/s"
    obfs: ''
    obfs-password: ''
    password: d010926e-0311-4924-a013-b84fbae430f9
    port: 30003
    server: qymg.qy1357.top
    skip-cert-verify: true
    sni: qymg.qy1357.top
    type: hysteria2
    up: ''
  - client-fingerprint: chrome
    flow: xtls-rprx-vision
    name: "\U0001F1F8\U0001F1EC新加坡14 | ⬇️ 9.0MB/s"
    network: tcp
    port: 20230
    reality-opts:
      public-key: 1cs7mxEcoVKwcYepAnKgqHAFhRxPv6aO3tv7lNhwLDQ
      short-id: 46bf6ea0
    server: sg002.421421.xyz
    tls: true
    type: vless
    udp: true
    uuid: 0bfa2050-b165-4156-859c-70f36d300dce
    xudp: true
    servername: www.nvidia.com
  - client-fingerprint: chrome
    flow: xtls-rprx-vision
    http-opts:
      headers: {}
      path:
        - /
    name: "\U0001F1E9\U0001F1EA德国1 | ⬇️ 7.0MB/s"
    network: tcp
    port: 443
    server: *************
    tls: true
    type: vless
    udp: true
    uuid: b8bd42a9-551f-419d-b70d-4aefdd2cb074
    xudp: true
    servername: yfnl1.xn--4gq62f52gppi29k.com
  - down: ''
    fingerprint: ''
    name: "\U0001F1FA\U0001F1F8美国35 | ⬇️ 7.0MB/s"
    obfs: ''
    obfs-password: ''
    password: nfsn666
    port: 8888
    server: ************
    skip-cert-verify: true
    sni: sj-arm.nfsn666.gq
    type: hysteria2
    up: ''
  - client-fingerprint: chrome
    grpc-opts:
      grpc-service-name: >-
        CANALTELEGRAM--jet_config--TELEGRAM--jet_config--CANALTELEGRAM--jet_config--TELEGRAM--jet_config--CANALTELEGRAM--jet_config
    name: "\U0001F1EC\U0001F1E7英国2 | ⬇️ 7.7MB/s"
    network: grpc
    port: 2030
    reality-opts:
      public-key: YWfCdTnr4FAOMYTY2dLrMtQUokyxOGpPhYEEszPj20E
      short-id: 7fe29733
    server: *************
    tls: true
    type: vless
    udp: true
    uuid: ad5e479a-d0f6-4809-902c-e74f5404336c
    xudp: true
    servername: refersion.com
  - down: ''
    fingerprint: ''
    name: "\U0001F1EC\U0001F1E7英国3 | ⬇️ 6.3MB/s"
    obfs: ''
    obfs-password: ''
    password: nfsn666
    port: 8888
    server: ld-arm.nfsn666.gq
    skip-cert-verify: true
    sni: ld-arm.nfsn666.gq
    type: hysteria2
    up: ''
  - down: ''
    fingerprint: ''
    name: "\U0001F1E9\U0001F1EA德国2 | ⬇️ 6.4MB/s"
    obfs: ''
    obfs-password: ''
    password: 75e98355-345b-4413-8001-************
    port: 44001
    server: **************
    skip-cert-verify: true
    sni: ''
    type: hysteria2
    up: ''
    disable-sni: true
  - down: ''
    fingerprint: ''
    name: "\U0001F1EB\U0001F1F7法国4 | ⬇️ 6.0MB/s"
    obfs: ''
    obfs-password: ''
    password: 5415e20c-dbe6-46ff-9cc1-1c092184d801
    port: 10000
    server: faguo.959555.xyz
    skip-cert-verify: true
    sni: faguo.959555.xyz
    type: hysteria2
    up: ''
  - client-fingerprint: chrome
    flow: xtls-rprx-vision
    name: "\U0001F1EF\U0001F1F5日本13 | ⬇️ 12.9MB/s"
    network: tcp
    port: 20230
    reality-opts:
      public-key: lbOfuIKCBPcQH4AEnwnPw1LNxWrl-Bul6KU99H240Fc
      short-id: abae4722
    server: jp004.421421.xyz
    tls: true
    type: vless
    udp: true
    uuid: 0bfa2050-b165-4156-859c-70f36d300dce
    xudp: true
    servername: www.nvidia.com
  - down: ''
    fingerprint: ''
    name: "\U0001F1FA\U0001F1F8美国36 | ⬇️ 6.0MB/s"
    obfs: ''
    obfs-password: ''
    password: 0da8651e-e1f6-11ec-bd7c-f23c913c8d2b
    port: 8443
    server: 479aad99-swd340-tcinla-hrtf.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 479aad99-swd340-tcinla-hrtf.hy2.gotochinatown.net
    type: hysteria2
    up: ''
  - client-fingerprint: chrome
    flow: xtls-rprx-vision
    name: "\U0001F1FA\U0001F1F8美国37 | ⬇️ 8.9MB/s"
    network: tcp
    port: 8443
    reality-opts:
      public-key: 4Qekb9y1dqO8hvRzVSGeSRNyhko_gqpeWD94zrLCvjs
      short-id: 5488b0e7
    server: ************
    tls: true
    type: vless
    udp: true
    uuid: 06121b89-607b-44c9-9c01-cc2fc6a7321d
    xudp: true
    servername: www.yahoo.com
  - down: ''
    fingerprint: ''
    name: "\U0001F1EF\U0001F1F5日本14 | ⬇️ 12.0MB/s"
    obfs: ''
    obfs-password: ''
    password: 48173CB0-68B5-4CAB-9587-FB7CCDE206A7
    port: 9197
    server: jp5.dexlos.com
    skip-cert-verify: false
    sni: jp5.dexlos.com
    type: hysteria2
    up: ''
  - cipher: aes-256-gcm
    name: "\U0001F1ED\U0001F1F0香港14 | ⬇️ 9.1MB/s"
    password: ZUSRFLOFK26OO0X5
    port: 15015
    server: ti3hyra4.slashdevslashnetslashtun.net
    type: ss
    udp: true
  - down: ''
    fingerprint: ''
    name: "\U0001F1FA\U0001F1F8美国38 | ⬇️ 7.7MB/s"
    obfs: ''
    obfs-password: ''
    password: d010926e-0311-4924-a013-b84fbae430f9
    port: 30003
    server: qyhg.qy1357.top
    skip-cert-verify: true
    sni: qyhg.qy1357.top
    type: hysteria2
    up: ''
  - cipher: aes-256-gcm
    name: "\U0001F1F8\U0001F1EC新加坡15 | ⬇️ 6.9MB/s"
    password: LUPCT9VGXN2HELH2
    port: 16015
    server: ti3hyra4.slashdevslashnetslashtun.net
    type: ss
    udp: true
  - down: ''
    fingerprint: ''
    name: "\U0001F1FA\U0001F1F8美国39 | ⬇️ 6.4MB/s"
    obfs: ''
    obfs-password: ''
    password: 869a0163-456f-4c06-bd4a-2376e4563eae
    port: 30003
    server: qyxjp2.qy1357.top
    skip-cert-verify: true
    sni: qyxjp2.qy1357.top
    type: hysteria2
    up: ''
  - down: ''
    fingerprint: ''
    name: "\U0001F1FA\U0001F1F8美国40 | ⬇️ 6.2MB/s"
    obfs: ''
    obfs-password: ''
    password: 65a43b6e-19c5-4fab-b960-d110a07d66a4
    port: 3234
    server: **************
    skip-cert-verify: true
    sni: ''
    type: hysteria2
    up: ''
    disable-sni: true
  - down: ''
    fingerprint: ''
    name: "\U0001F1FA\U0001F1F8美国41 | ⬇️ 5.9MB/s"
    obfs: ''
    obfs-password: ''
    password: 41a11f83
    port: 60000
    server: *************
    skip-cert-verify: true
    sni: www.bing.com
    type: hysteria2
    up: ''
port: 7890
socks-port: 7891
redir-port: 7892
mixed-port: 7893
tproxy-port: 7894
ipv6: false
allow-lan: true
unified-delay: true
tcp-concurrent: true
geodata-mode: false
geodata-loader: standard
geo-auto-update: true
geo-update-interval: 48
geox-url:
  geoip: 'https://testingcf.jsdelivr.net/gh/MetaCubeX/meta-rules-dat@release/geoip.dat'
  geosite: >-
    https://testingcf.jsdelivr.net/gh/MetaCubeX/meta-rules-dat@release/geosite.dat
  mmdb: >-
    https://testingcf.jsdelivr.net/gh/MetaCubeX/meta-rules-dat@release/country.mmdb
  asn: >-
    https://github.com/xishang0128/geoip/releases/download/latest/GeoLite2-ASN.mmdb
profile:
  store-selected: true
  store-fake-ip: true
sniffer:
  enable: true
  sniff:
    HTTP:
      ports:
        - 80
        - 8080-8880
      override-destination: true
    TLS:
      ports:
        - 443
        - 8443
    QUIC:
      ports:
        - 443
        - 8443
  force-domain:
    - +.v2ex.com
  skip-domain:
    - Mijia Cloud
    - dlg.io.mi.com
    - +.push.apple.com
    - +.apple.com
dns:
  enable: true
  listen: '0.0.0.0:1053'
  ipv6: false
  respect-rules: true
  enhanced-mode: fake-ip
  fake-ip-range: ********/8
  fake-ip-filter-mode: blacklist
  fake-ip-filter:
    - +.lan
    - +.local
    - 'geosite:private'
    - 'geosite:cn'
  default-nameserver:
    - *********
    - ************
  proxy-server-nameserver:
    - *********
    - ************
  nameserver:
    - *********
    - ************
  nameserver-policy:
    'rule-set:private_domain,cn_domain':
      - *********
      - ************
    'rule-set:geolocation-!cn':
      - 'https://dns.cloudflare.com/dns-query'
      - 'https://dns.google/dns-query'
pr:
  type: select
  proxies:
    - "\U0001F680 节点选择"
    - "\U0001F1ED\U0001F1F0 香港负载均衡"
    - "\U0001F1EF\U0001F1F5 日本负载均衡"
    - "\U0001F1F0\U0001F1F7 韩国负载均衡"
    - "\U0001F1F8\U0001F1EC 新加坡负载均衡"
    - "\U0001F1FA\U0001F1F8 美国负载均衡"
    - "\U0001F1ED\U0001F1F0 香港自动"
    - "\U0001F1EF\U0001F1F5 日本自动"
    - "\U0001F1F0\U0001F1F7 韩国自动"
    - "\U0001F1F8\U0001F1EC 新加坡自动"
    - "\U0001F1FA\U0001F1F8 美国自动"
    - ♻️ 自动选择
    - "\U0001F1ED\U0001F1F0 香港节点"
    - "\U0001F1EF\U0001F1F5 日本节点"
    - "\U0001F1F0\U0001F1F7 韩国节点"
    - "\U0001F1F8\U0001F1EC 新加坡节点"
    - "\U0001F1FA\U0001F1F8 美国节点"
    - "\U0001F310 全部节点"
proxy-groups:
  - name: "\U0001F310 全部节点"
    type: select
    include-all: true
  - name: "\U0001F680 节点选择"
    type: select
    proxies:
      - "\U0001F1ED\U0001F1F0 香港负载均衡"
      - "\U0001F1EF\U0001F1F5 日本负载均衡"
      - "\U0001F1F0\U0001F1F7 韩国负载均衡"
      - "\U0001F1F8\U0001F1EC 新加坡负载均衡"
      - "\U0001F1FA\U0001F1F8 美国负载均衡"
      - "\U0001F1ED\U0001F1F0 香港自动"
      - "\U0001F1EF\U0001F1F5 日本自动"
      - "\U0001F1F0\U0001F1F7 韩国自动"
      - "\U0001F1F8\U0001F1EC 新加坡自动"
      - "\U0001F1FA\U0001F1F8 美国自动"
      - ♻️ 自动选择
      - "\U0001F1ED\U0001F1F0 香港节点"
      - "\U0001F1EF\U0001F1F5 日本节点"
      - "\U0001F1F0\U0001F1F7 韩国节点"
      - "\U0001F1F8\U0001F1EC 新加坡节点"
      - "\U0001F1FA\U0001F1F8 美国节点"
      - "\U0001F310 全部节点"
  - name: "\U0001F4AC ChatGPT"
    type: select
    proxies:
      - "\U0001F680 节点选择"
      - "\U0001F1ED\U0001F1F0 香港负载均衡"
      - "\U0001F1EF\U0001F1F5 日本负载均衡"
      - "\U0001F1F0\U0001F1F7 韩国负载均衡"
      - "\U0001F1F8\U0001F1EC 新加坡负载均衡"
      - "\U0001F1FA\U0001F1F8 美国负载均衡"
      - "\U0001F1ED\U0001F1F0 香港自动"
      - "\U0001F1EF\U0001F1F5 日本自动"
      - "\U0001F1F0\U0001F1F7 韩国自动"
      - "\U0001F1F8\U0001F1EC 新加坡自动"
      - "\U0001F1FA\U0001F1F8 美国自动"
      - ♻️ 自动选择
      - "\U0001F1ED\U0001F1F0 香港节点"
      - "\U0001F1EF\U0001F1F5 日本节点"
      - "\U0001F1F0\U0001F1F7 韩国节点"
      - "\U0001F1F8\U0001F1EC 新加坡节点"
      - "\U0001F1FA\U0001F1F8 美国节点"
      - "\U0001F310 全部节点"
  - name: "\U0001F4FA YouTube"
    type: select
    proxies:
      - "\U0001F680 节点选择"
      - "\U0001F1ED\U0001F1F0 香港负载均衡"
      - "\U0001F1EF\U0001F1F5 日本负载均衡"
      - "\U0001F1F0\U0001F1F7 韩国负载均衡"
      - "\U0001F1F8\U0001F1EC 新加坡负载均衡"
      - "\U0001F1FA\U0001F1F8 美国负载均衡"
      - "\U0001F1ED\U0001F1F0 香港自动"
      - "\U0001F1EF\U0001F1F5 日本自动"
      - "\U0001F1F0\U0001F1F7 韩国自动"
      - "\U0001F1F8\U0001F1EC 新加坡自动"
      - "\U0001F1FA\U0001F1F8 美国自动"
      - ♻️ 自动选择
      - "\U0001F1ED\U0001F1F0 香港节点"
      - "\U0001F1EF\U0001F1F5 日本节点"
      - "\U0001F1F0\U0001F1F7 韩国节点"
      - "\U0001F1F8\U0001F1EC 新加坡节点"
      - "\U0001F1FA\U0001F1F8 美国节点"
      - "\U0001F310 全部节点"
  - name: "\U0001F3B5 TikTok"
    type: select
    proxies:
      - "\U0001F680 节点选择"
      - "\U0001F1ED\U0001F1F0 香港负载均衡"
      - "\U0001F1EF\U0001F1F5 日本负载均衡"
      - "\U0001F1F0\U0001F1F7 韩国负载均衡"
      - "\U0001F1F8\U0001F1EC 新加坡负载均衡"
      - "\U0001F1FA\U0001F1F8 美国负载均衡"
      - "\U0001F1ED\U0001F1F0 香港自动"
      - "\U0001F1EF\U0001F1F5 日本自动"
      - "\U0001F1F0\U0001F1F7 韩国自动"
      - "\U0001F1F8\U0001F1EC 新加坡自动"
      - "\U0001F1FA\U0001F1F8 美国自动"
      - ♻️ 自动选择
      - "\U0001F1ED\U0001F1F0 香港节点"
      - "\U0001F1EF\U0001F1F5 日本节点"
      - "\U0001F1F0\U0001F1F7 韩国节点"
      - "\U0001F1F8\U0001F1EC 新加坡节点"
      - "\U0001F1FA\U0001F1F8 美国节点"
      - "\U0001F310 全部节点"
  - name: "\U0001F3AC NETFLIX"
    type: select
    proxies:
      - "\U0001F680 节点选择"
      - "\U0001F1ED\U0001F1F0 香港负载均衡"
      - "\U0001F1EF\U0001F1F5 日本负载均衡"
      - "\U0001F1F0\U0001F1F7 韩国负载均衡"
      - "\U0001F1F8\U0001F1EC 新加坡负载均衡"
      - "\U0001F1FA\U0001F1F8 美国负载均衡"
      - "\U0001F1ED\U0001F1F0 香港自动"
      - "\U0001F1EF\U0001F1F5 日本自动"
      - "\U0001F1F0\U0001F1F7 韩国自动"
      - "\U0001F1F8\U0001F1EC 新加坡自动"
      - "\U0001F1FA\U0001F1F8 美国自动"
      - ♻️ 自动选择
      - "\U0001F1ED\U0001F1F0 香港节点"
      - "\U0001F1EF\U0001F1F5 日本节点"
      - "\U0001F1F0\U0001F1F7 韩国节点"
      - "\U0001F1F8\U0001F1EC 新加坡节点"
      - "\U0001F1FA\U0001F1F8 美国节点"
      - "\U0001F310 全部节点"
  - name: "\U0001F1ED\U0001F1F0 香港节点"
    type: select
    include-all: true
    filter: (?i)港|hk|hongkong|hong kong
  - name: "\U0001F1EF\U0001F1F5 日本节点"
    type: select
    include-all: true
    filter: (?i)日|jp|japan
  - name: "\U0001F1F0\U0001F1F7 韩国节点"
    type: select
    include-all: true
    filter: (?i)韩|kr|korea
  - name: "\U0001F1F8\U0001F1EC 新加坡节点"
    type: select
    include-all: true
    filter: (?i)新|狮|sg|singapore|新加坡
  - name: "\U0001F1FA\U0001F1F8 美国节点"
    type: select
    include-all: true
    filter: (?i)美|us|unitedstates|united states
  - name: "\U0001F1ED\U0001F1F0 香港自动"
    type: url-test
    include-all: true
    tolerance: 20
    interval: 300
    filter: (?=.*(港|HK|(?i)Hong))^((?!(台|日|韩|新|深|美)).)*$
  - name: "\U0001F1EF\U0001F1F5 日本自动"
    type: url-test
    include-all: true
    tolerance: 20
    interval: 300
    filter: (?=.*(日|JP|(?i)Japan))^((?!(港|台|韩|新|美)).)*$
  - name: "\U0001F1F0\U0001F1F7 韩国自动"
    type: url-test
    include-all: true
    tolerance: 20
    interval: 300
    filter: (?=.*(韩|KR|(?i)Korea))^((?!(港|台|日|新|美)).)*$
  - name: "\U0001F1F8\U0001F1EC 新加坡自动"
    type: url-test
    include-all: true
    tolerance: 20
    interval: 300
    filter: (?=.*(新|狮|SG|(?i)Singapore))^((?!(港|台|日|韩|美)).)*$
  - name: "\U0001F1FA\U0001F1F8 美国自动"
    type: url-test
    include-all: true
    tolerance: 20
    interval: 300
    filter: (?=.*(美|US|(?i)States|America))^((?!(港|台|日|韩|新)).)*$
  - name: ♻️ 自动选择
    type: url-test
    include-all: true
    tolerance: 20
    interval: 300
    filter: ^((?!(直连)).)*$
  - name: "\U0001F1ED\U0001F1F0 香港负载均衡"
    type: load-balance
    include-all: true
    interval: 60
    lazy: true
    url: 'http://www.google.com/blank.html'
    disable-udp: false
    strategy: round-robin
    timeout: 2000
    max-failed-times: 3
    filter: (?=.*(港|HK|(?i)Hong))^((?!(台|日|韩|新|深|美)).)*$
  - name: "\U0001F1EF\U0001F1F5 日本负载均衡"
    type: load-balance
    include-all: true
    interval: 60
    lazy: true
    url: 'http://www.google.com/blank.html'
    disable-udp: false
    strategy: round-robin
    timeout: 2000
    max-failed-times: 3
    filter: (?=.*(日|JP|(?i)Japan))^((?!(港|台|韩|新|美)).)*$
  - name: "\U0001F1F0\U0001F1F7 韩国负载均衡"
    type: load-balance
    include-all: true
    interval: 60
    lazy: true
    url: 'http://www.google.com/blank.html'
    disable-udp: false
    strategy: round-robin
    timeout: 2000
    max-failed-times: 3
    filter: (?=.*(韩|KR|(?i)Korea))^((?!(港|台|日|新|美)).)*$
  - name: "\U0001F1F8\U0001F1EC 新加坡负载均衡"
    type: load-balance
    include-all: true
    interval: 60
    lazy: true
    url: 'http://www.google.com/blank.html'
    disable-udp: false
    strategy: round-robin
    timeout: 2000
    max-failed-times: 3
    filter: (?=.*(新|狮|SG|(?i)Singapore))^((?!(港|台|日|韩|美)).)*$
  - name: "\U0001F1FA\U0001F1F8 美国负载均衡"
    type: load-balance
    include-all: true
    interval: 60
    lazy: true
    url: 'http://www.google.com/blank.html'
    disable-udp: false
    strategy: round-robin
    timeout: 2000
    max-failed-times: 3
    filter: (?=.*(美|US|(?i)States|America))^((?!(港|台|日|韩|新)).)*$
  - name: "\U0001F3AF 全球直连"
    type: select
    proxies:
      - DIRECT
      - "\U0001F680 节点选择"
  - name: "\U0001F420 漏网之鱼"
    type: select
    proxies:
      - "\U0001F680 节点选择"
      - "\U0001F1ED\U0001F1F0 香港负载均衡"
      - "\U0001F1EF\U0001F1F5 日本负载均衡"
      - "\U0001F1F0\U0001F1F7 韩国负载均衡"
      - "\U0001F1F8\U0001F1EC 新加坡负载均衡"
      - "\U0001F1FA\U0001F1F8 美国负载均衡"
      - "\U0001F1ED\U0001F1F0 香港自动"
      - "\U0001F1EF\U0001F1F5 日本自动"
      - "\U0001F1F0\U0001F1F7 韩国自动"
      - "\U0001F1F8\U0001F1EC 新加坡自动"
      - "\U0001F1FA\U0001F1F8 美国自动"
      - ♻️ 自动选择
      - "\U0001F1ED\U0001F1F0 香港节点"
      - "\U0001F1EF\U0001F1F5 日本节点"
      - "\U0001F1F0\U0001F1F7 韩国节点"
      - "\U0001F1F8\U0001F1EC 新加坡节点"
      - "\U0001F1FA\U0001F1F8 美国节点"
      - "\U0001F310 全部节点"
rules:
  - 'RULE-SET,BanAD,REJECT'
  - 'RULE-SET,BanProgramAD,REJECT'
  - 'RULE-SET,adobe,REJECT'
  - "RULE-SET,youtube_domain,\U0001F4FA YouTube"
  - 'RULE-SET,tencent,DIRECT'
  - 'RULE-SET,private_domain,DIRECT'
  - 'RULE-SET,TencentVideo,DIRECT'
  - "RULE-SET,apple_domain,\U0001F3AF 全球直连"
  - "RULE-SET,ai,\U0001F4AC ChatGPT"
  - "RULE-SET,Spotify,\U0001F680 节点选择"
  - "RULE-SET,github_domain,\U0001F680 节点选择"
  - "RULE-SET,google_domain,\U0001F680 节点选择"
  - "RULE-SET,onedrive_domain,\U0001F3AF 全球直连"
  - "RULE-SET,microsoft_domain,\U0001F3AF 全球直连"
  - "RULE-SET,tiktok_domain,\U0001F3B5 TikTok"
  - "RULE-SET,speedtest_domain,\U0001F680 节点选择"
  - "RULE-SET,telegram_domain,\U0001F680 节点选择"
  - "RULE-SET,netflix_domain,\U0001F3AC NETFLIX"
  - "RULE-SET,Netflix,\U0001F3AC NETFLIX"
  - "RULE-SET,paypal_domain,\U0001F680 节点选择"
  - "RULE-SET,geolocation-!cn,\U0001F680 节点选择"
  - "RULE-SET,cn_domain,\U0001F3AF 全球直连"
  - "RULE-SET,google_ip,\U0001F680 节点选择,no-resolve"
  - "RULE-SET,netflix_ip,\U0001F3AC NETFLIX,no-resolve"
  - "RULE-SET,telegram_ip,\U0001F680 节点选择,no-resolve"
  - "RULE-SET,cn_ip,\U0001F3AF 全球直连"
  - "RULE-SET,proxylite,\U0001F680 节点选择"
  - "MATCH,\U0001F420 漏网之鱼"
rule-anchor:
  ip:
    type: http
    interval: 86400
    behavior: ipcidr
    format: mrs
  domain:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
  qcy:
    type: http
    interval: 86400
    behavior: domain
    format: text
  class:
    type: http
    interval: 86400
    behavior: classical
    format: text
rule-providers:
  BanAD:
    type: http
    interval: 86400
    behavior: classical
    format: text
    url: 'https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/Clash/BanAD.list'
  tencent:
    type: http
    interval: 86400
    behavior: classical
    format: text
    url: >-
      https://cdn.jsdelivr.net/gh/blackmatrix7/ios_rule_script@master/rule/Shadowrocket/Tencent/Tencent.list
  TencentVideo:
    type: http
    interval: 86400
    behavior: classical
    format: text
    url: >-
      https://cdn.jsdelivr.net/gh/blackmatrix7/ios_rule_script@master/rule/Shadowrocket/TencentVideo/TencentVideo.list
  BanProgramAD:
    type: http
    interval: 86400
    behavior: classical
    format: text
    url: >-
      https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/Clash/BanProgramAD.list
  adobe:
    type: http
    interval: 86400
    behavior: classical
    format: text
    url: 'https://raw.githubusercontent.com/cmliu/ACL4SSR/main/Clash/adobe.list'
  private_domain:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/private.mrs
  ai:
    type: http
    interval: 86400
    behavior: classical
    format: text
    url: >-
      https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/a98edee7cd64d64cb58ce26d63d2dbd2475575ad/rule/Clash/OpenAI/OpenAI.list
  Spotify:
    type: http
    interval: 86400
    behavior: classical
    format: text
    url: >-
      https://cdn.jsdelivr.net/gh/blackmatrix7/ios_rule_script@release/rule/Shadowrocket/Spotify/Spotify.list
  Netflix:
    type: http
    interval: 86400
    behavior: classical
    format: text
    url: >-
      https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/a98edee7cd64d64cb58ce26d63d2dbd2475575ad/rule/Clash/Netflix/Netflix.list
  youtube_domain:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/youtube.mrs
  google_domain:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/google.mrs
  github_domain:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/github.mrs
  telegram_domain:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/telegram.mrs
  netflix_domain:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/netflix.mrs
  paypal_domain:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/paypal.mrs
  onedrive_domain:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/onedrive.mrs
  microsoft_domain:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/microsoft.mrs
  apple_domain:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/apple-cn.mrs
  speedtest_domain:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/ookla-speedtest.mrs
  tiktok_domain:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/tiktok.mrs
  gfw_domain:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/gfw.mrs
  geolocation-!cn:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/geolocation-!cn.mrs
  cn_domain:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/cn.mrs
  proxylite:
    type: http
    interval: 86400
    behavior: classical
    format: text
    url: >-
      https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/refs/heads/master/rule/Clash/ProxyLite/ProxyLite.list
  cn_ip:
    type: http
    interval: 86400
    behavior: ipcidr
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geoip/cn.mrs
  google_ip:
    type: http
    interval: 86400
    behavior: ipcidr
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geoip/google.mrs
  telegram_ip:
    type: http
    interval: 86400
    behavior: ipcidr
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geoip/telegram.mrs
  netflix_ip:
    type: http
    interval: 86400
    behavior: ipcidr
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geoip/netflix.mrs
