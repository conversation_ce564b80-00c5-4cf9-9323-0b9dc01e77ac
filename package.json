{"name": "sub-store-front-end", "version": "2.15.33", "private": true, "scripts": {"dev": "vite --host", "dev:production": "vite --host --mode production", "build": "vue-tsc --noEmit && vite build", "preview": "vite preview --host", "changelog": "conventional-changelog -p cli -i CHANGELOG.md -s"}, "dependencies": {"@codemirror/autocomplete": "^6.12.0", "@codemirror/buildhelper": "^1.0.1", "@codemirror/commands": "^6.3.3", "@codemirror/language": "^6.10.1", "@codemirror/state": "^6.4.0", "@codemirror/view": "^6.23.1", "@fortawesome/fontawesome-svg-core": "^6.1.1", "@fortawesome/free-regular-svg-icons": "^6.1.1", "@fortawesome/free-solid-svg-icons": "^6.1.1", "@fortawesome/vue-fontawesome": "^3.0.1", "@lezer/common": "^1.2.1", "@lezer/highlight": "^1.2.0", "@lezer/javascript": "^1.4.13", "@nutui/nutui": "^3.3.8", "@replit/codemirror-indentation-markers": "^6.5.0", "@vueuse/core": "^8.9.2", "@vueuse/integrations": "^8.9.2", "axios": "^0.27.2", "codemirror": "^6.0.1", "crelt": "^1.0.6", "dayjs": "^1.11.3", "js-beautify": "^1.15.1", "js-yaml": "^4.1.0", "marked": "^7.0.4", "modern-css-reset": "^1.4.0", "monaco-editor": "^0.33.0", "pinia": "^2.0.14", "qrcode": "^1.5.0", "semver": "^7.5.4", "vite-plugin-compression": "^0.5.1", "vue": "^3.2.37", "vue-clipboard3": "^2.0.0", "vue-i18n": "^9.2.0-beta.36", "vue-router": "^4.0.16", "vue3-toastify": "^0.2.0", "vuedraggable": "^4.1.0"}, "devDependencies": {"@antfu/eslint-config": "^0.40.2", "@commitlint/cli": "^17.0.3", "@commitlint/config-angular": "^17.0.3", "@commitlint/config-conventional": "^17.0.3", "@types/js-yaml": "^4.0.5", "@types/node": "^18.0.0", "@vitejs/plugin-legacy": "^2.0.1", "@vitejs/plugin-vue": "^3.1.1", "consola": "^2.15.3", "eslint": "^8.47.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "eslint-plugin-simple-import-sort": "^10.0.0", "fast-glob": "^3.2.11", "husky": "^8.0.1", "sass": "^1.53.0", "terser": "^5.19.3", "typescript": "^4.7.4", "vconsole": "^3.15.1", "vite": "^3.2.7", "vite-plugin-monaco-editor": "^1.1.0", "vite-plugin-pwa": "^0.16.4", "vite-plugin-style-import": "^2.0.0", "vite-plugin-svg-icons": "^2.0.1", "vue-tsc": "^0.35.2", "workbox-window": "^7.0.0"}}