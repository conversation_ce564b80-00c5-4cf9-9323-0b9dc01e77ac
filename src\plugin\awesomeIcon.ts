import { library } from '@fortawesome/fontawesome-svg-core';

import {
  faAnglesRight,
  faAngleRight,
  faArrowRotateRight,
  faArrowUpRightFromSquare,
  faBan,
  faCheck,
  faCircleQuestion,
  faCircleXmark,
  faXmark,
  faClone,
  faCloudArrowDown,
  faCloudArrowUp,
  faCode,
  faEye,
  faEllipsis,
  faFloppyDisk,
  faGrip,
  faLanguage,
  faToggleOn,
  faToggleOff,
  faLocationArrow,
  faPaste,
  faPenNib,
  faPenToSquare,
  faPlus,
  faTrashCan,
  faEraser,
  faT,
  faICursor,
  faFileImport,
  faFileExport,
  faShareNodes,
  faLink,
  faSquareArrowUpRight
} from '@fortawesome/free-solid-svg-icons';

library.add(faFileImport);
library.add(faFileExport);
library.add(faToggleOn);
library.add(faToggleOff);
library.add(faLanguage);
library.add(faCheck);
library.add(faPlus);
library.add(faArrowUpRightFromSquare);
library.add(faTrashCan);
library.add(faPenNib);
library.add(faClone);
library.add(faEye);
library.add(faGrip);
library.add(faCircleQuestion);
library.add(faFloppyDisk);
library.add(faCircleXmark);
library.add(faXmark);
library.add(faLocationArrow);
library.add(faCode);
library.add(faArrowRotateRight);
library.add(faAnglesRight);
library.add(faAngleRight);
library.add(faCloudArrowDown);
library.add(faCloudArrowUp);
library.add(faBan);
library.add(faPenToSquare);
library.add(faPaste);
library.add(faEraser);
library.add(faT);
library.add(faICursor);
library.add(faShareNodes);
library.add(faLink);
library.add(faSquareArrowUpRight);
library.add(faEllipsis);
