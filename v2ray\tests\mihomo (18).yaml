proxies:
  - cipher: aes-256-gcm
    name: "\U0001F1ED\U0001F1F0香港1 | ⬇️ 7.8MB/s"
    password: 3030b609-f36f-4fd1-a4ef-a6e653780536
    port: 16007
    server: ************
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1F9\U0001F1FC台湾1 | ⬇️ 8.3MB/s"
    password: 3030b609-f36f-4fd1-a4ef-a6e653780536
    port: 17004
    server: *************
    type: ss
    udp: true
  - alpn:
      - ''
    alterId: '0'
    cipher: auto
    name: "\U0001F1EF\U0001F1F5日本1 | ⬇️ 13.4MB/s"
    network: ws
    port: 19700
    server: **************
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: db1b5367-92d7-4337-90c6-b3b9955d02ba
    ws-opts:
      headers:
        Host: tms.dingtalk.com
      path: /
    xudp: true
  - down: ''
    fingerprint: ''
    name: "\U0001F1EF\U0001F1F5日本2 | ⬇️ 7.9MB/s"
    obfs: ''
    obfs-password: ''
    password: 0A1DDFE6-A16A-43FA-A6E3-70EDE4EA61C3
    port: 7270
    server: jp4.dexlos.com
    skip-cert-verify: false
    sni: jp4.dexlos.com
    type: hysteria2
    up: ''
  - down: ''
    fingerprint: ''
    name: "\U0001F1F7\U0001F1FA俄罗斯1 | ⬇️ 8.9MB/s"
    obfs: ''
    obfs-password: ''
    password: dongtaiwang.com
    port: 30033
    server: hy2.694463.xyz
    skip-cert-verify: true
    sni: www.bing.com
    type: hysteria2
    up: ''
  - down: ''
    fingerprint: ''
    name: "\U0001F1FA\U0001F1F8美国1 | ⬇️ 8.7MB/s"
    obfs: ''
    obfs-password: ''
    password: a346c669-6374-11ef-bc6a-f23c9313b177
    port: 1443
    server: 5f374286-svtc00-tdex4g-6qyv.la.shifen.uk
    skip-cert-verify: true
    sni: 5f374286-svtc00-tdex4g-6qyv.la.shifen.uk
    type: hysteria2
    up: ''
  - down: ''
    fingerprint: ''
    name: "\U0001F1FA\U0001F1F8美国2 | ⬇️ 10.9MB/s"
    obfs: ''
    obfs-password: ''
    password: dongtaiwang.com
    port: 57773
    server: *************
    skip-cert-verify: true
    sni: apple.com
    type: hysteria2
    up: ''
  - cipher: aes-128-gcm
    name: "\U0001F1F8\U0001F1EC新加坡1 | ⬇️ 10.5MB/s"
    password: f16163ec-3c35-4719-a19b-68c864cdc626
    port: 13038
    server: *************
    type: ss
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1FA\U0001F1F8美国3 | ⬇️ 8.1MB/s"
    network: ws
    port: 443
    server: dmit.jhyl.bid
    tls: true
    type: vless
    udp: true
    uuid: 0cc14bae-0703-4c2d-e9de-ed4672eadd30
    ws-opts:
      headers:
        Host: dmit.jhyl.bid
        User-Agent: >-
          Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like
          Gecko) Chrome/50.0.2661.102 Safari/537.36
      path: /download
    xudp: true
    servername: dmit.jhyl.bid
  - down: ''
    fingerprint: ''
    name: "\U0001F1FA\U0001F1F8美国4 | ⬇️ 8.2MB/s"
    obfs: ''
    obfs-password: ''
    password: nfsn666
    port: 8888
    server: sj-arm.nfsn666.gq
    skip-cert-verify: true
    sni: sj-arm.nfsn666.gq
    type: hysteria2
    up: ''
  - down: ''
    fingerprint: ''
    name: "\U0001F1FA\U0001F1F8美国5 | ⬇️ 8.0MB/s"
    obfs: ''
    obfs-password: ''
    password: nfsn666
    port: 8888
    server: ************
    skip-cert-verify: true
    sni: sj-arm.nfsn666.gq
    type: hysteria2
    up: ''
  - client-fingerprint: chrome
    name: "\U0001F1E6\U0001F1FA澳大利亚1 | ⬇️ 9.6MB/s"
    password: RlzoEILU
    port: 40252
    server: **************
    skip-cert-verify: true
    sni: **************
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1EF\U0001F1F5日本3 | ⬇️ 10.9MB/s"
    password: 7669a04d-a459-4c96-bc0e-fde51cb984f3
    port: 12032
    server: cn2.cdn.xfltd-cdn.top
    skip-cert-verify: true
    sni: cn2.cdn.xfltd-cdn.top
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1EF\U0001F1F5日本4 | ⬇️ 9.9MB/s"
    password: 7669a04d-a459-4c96-bc0e-fde51cb984f3
    port: 12035
    server: cn2.cdn.xfltd-cdn.top
    skip-cert-verify: true
    sni: cn2.cdn.xfltd-cdn.top
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1F0\U0001F1F7韩国1 | ⬇️ 9.1MB/s"
    password: 7669a04d-a459-4c96-bc0e-fde51cb984f3
    port: 12041
    server: cn2.cdn.xfltd-cdn.top
    skip-cert-verify: true
    sni: cn2.cdn.xfltd-cdn.top
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1F8\U0001F1EC新加坡2 | ⬇️ 9.7MB/s"
    password: 7669a04d-a459-4c96-bc0e-fde51cb984f3
    port: 12025
    server: cn2.cdn.xfltd-cdn.top
    skip-cert-verify: true
    sni: cn2.cdn.xfltd-cdn.top
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1ED\U0001F1F0香港2 | ⬇️ 12.0MB/s"
    password: 2c605663-b89a-5734-a9d6-97d4743d72cf
    port: 8313
    server: dozo01.flztjc.top
    skip-cert-verify: false
    sni: hk-13-568.flztjc.net
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1FA\U0001F1F8美国6 | ⬇️ 9.4MB/s"
    password: 7669a04d-a459-4c96-bc0e-fde51cb984f3
    port: 12004
    server: cn1.cdn.xfltd-cdn.top
    skip-cert-verify: true
    sni: cdn.alibaba.com
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F300其他1-ID | ⬇️ 8.5MB/s"
    password: 7669a04d-a459-4c96-bc0e-fde51cb984f3
    port: 12061
    server: cn2.cdn.xfltd-cdn.top
    skip-cert-verify: true
    sni: cn2.cdn.xfltd-cdn.top
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1ED\U0001F1F0香港3 | ⬇️ 12.2MB/s"
    password: 2c605663-b89a-5734-a9d6-97d4743d72cf
    port: 8313
    server: **************
    skip-cert-verify: true
    sni: **************
    type: trojan
    udp: true
  - client-fingerprint: chrome
    flow: xtls-rprx-vision
    name: "\U0001F1FA\U0001F1F8美国7 | ⬇️ 8.7MB/s"
    network: tcp
    port: 8443
    reality-opts:
      public-key: 4Qekb9y1dqO8hvRzVSGeSRNyhko_gqpeWD94zrLCvjs
      short-id: 5488b0e7
    server: sj-arm.nfsn666.gq
    tls: true
    type: vless
    udp: true
    uuid: 06121b89-607b-44c9-9c01-cc2fc6a7321d
    xudp: true
    servername: www.yahoo.com
  - name: "\U0001F1FA\U0001F1F8美国8 | ⬇️ 9.8MB/s"
    network: ws
    port: 8880
    server: **************
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
        User-Agent: >-
          Mozilla/5.0 (Windows NT 5.1; Win64; x64) AppleWebKit/537.36 (KHTML,
          like Gecko) Chrome/55.0.2883.87 Safari/537.36
      path: /?ed=2560
    xudp: true
  - alpn:
      - h3
      - h2
      - http/1.1
    client-fingerprint: chrome
    name: "\U0001F1FA\U0001F1F8美国9 | ⬇️ 8.6MB/s"
    network: ws
    port: 443
    server: ************
    tls: true
    type: vless
    udp: true
    uuid: e3820a55-ff58-4e42-ac82-df9a9376e699
    ws-opts:
      headers:
        Host: madcity2.777999.lol
        User-Agent: >-
          Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML,
          like Gecko) Chrome/62.0.3202.89 Safari/537.36
      path: /?ed=2560
    xudp: true
    servername: madcity2.777999.lol
  - cipher: aes-256-gcm
    name: "\U0001F1F8\U0001F1EC新加坡3 | ⬇️ 9.8MB/s"
    password: c86d483c-431f-41df-bb6b-c1dcebfc7401
    port: 18002
    server: *************
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1FA\U0001F1F8美国10 | ⬇️ 8.3MB/s"
    password: c86d483c-431f-41df-bb6b-c1dcebfc7401
    port: 20010
    server: *************
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1F9\U0001F1FC台湾2 | ⬇️ 8.3MB/s"
    password: c86d483c-431f-41df-bb6b-c1dcebfc7401
    port: 17007
    server: *************
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1ED\U0001F1F0香港4 | ⬇️ 9.4MB/s"
    password: c86d483c-431f-41df-bb6b-c1dcebfc7401
    port: 16014
    server: ************
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1F9\U0001F1FC台湾3 | ⬇️ 10.0MB/s"
    password: c86d483c-431f-41df-bb6b-c1dcebfc7401
    port: 17001
    server: *************
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1ED\U0001F1F0香港5 | ⬇️ 10.4MB/s"
    password: 3030b609-f36f-4fd1-a4ef-a6e653780536
    port: 16002
    server: *************
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1F8\U0001F1EC新加坡4 | ⬇️ 9.0MB/s"
    password: 3030b609-f36f-4fd1-a4ef-a6e653780536
    port: 18004
    server: *************
    type: ss
    udp: true
  - alpn:
      - ''
    alterId: '0'
    cipher: auto
    name: "\U0001F300其他2-未识别 | ⬇️ 12.5MB/s"
    network: ws
    port: 20000
    server: 2fea6447-81c4-2d7d-d592-a4f729209777.7head.icu
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 14a8a0db-e922-453a-af8d-843af8734b3b
    ws-opts:
      headers:
        Host: tms.dingtalk.com
      path: /
    xudp: true
  - alpn:
      - ''
    alterId: '0'
    cipher: auto
    name: "\U0001F300其他3-未识别 | ⬇️ 7.8MB/s"
    network: ws
    port: 80
    server: 445522e0-a147-66c0-6b59-becb865f30a5.castlepeakhospital.moe
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 14a8a0db-e922-453a-af8d-843af8734b3b
    ws-opts:
      headers:
        Host: tms.dingtalk.com
      path: /
    xudp: true
  - alpn:
      - ''
    alterId: '0'
    cipher: auto
    name: "\U0001F300其他4-未识别 | ⬇️ 9.3MB/s"
    network: ws
    port: 80
    server: 0989a4f1-856a-4d0b-530b-aed9bf289ad8.castlepeakhospital.moe
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 14a8a0db-e922-453a-af8d-843af8734b3b
    ws-opts:
      headers:
        Host: tms.dingtalk.com
      path: /
    xudp: true
  - alpn:
      - ''
    alterId: '0'
    cipher: auto
    name: "\U0001F1F8\U0001F1EC新加坡5 | ⬇️ 15.5MB/s"
    network: ws
    port: 49102
    server: free-relay.themars.top
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 781807c4-37df-4da3-9942-c6e82032399a
    ws-opts:
      headers:
        Host: www.cctv.com
      path: /cctv1.m3u8
    xudp: true
  - alpn:
      - ''
    alterId: '0'
    cipher: auto
    name: "\U0001F300其他5-未识别 | ⬇️ 13.9MB/s"
    network: ws
    port: 80
    server: d9479ad0-2f0d-4dd0-253d-67606186a1ed.castlepeakhospital.moe
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 21c90669-193d-49d3-8e37-f5c1462eb134
    ws-opts:
      headers:
        Host: tms.dingtalk.com
      path: /
    xudp: true
  - client-fingerprint: chrome
    name: "\U0001F1ED\U0001F1F0香港6 | ⬇️ 12.6MB/s"
    password: RlzoEILU
    port: 45569
    server: **************
    skip-cert-verify: true
    sni: cdn.egvra.cn
    type: trojan
    udp: true
  - alpn:
      - ''
    alterId: '0'
    cipher: auto
    name: "\U0001F1EC\U0001F1E7英国1 | ⬇️ 8.8MB/s"
    network: ws
    port: 20032
    server: 2fea6447-81c4-2d7d-d592-a4f729209777.7head.icu
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 14a8a0db-e922-453a-af8d-843af8734b3b
    ws-opts:
      headers:
        Host: tms.dingtalk.com
      path: /
    xudp: true
  - alpn:
      - ''
    alterId: '0'
    cipher: auto
    name: "\U0001F1E9\U0001F1EA德国1 | ⬇️ 9.1MB/s"
    network: ws
    port: 20030
    server: 2fea6447-81c4-2d7d-d592-a4f729209777.7head.icu
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 14a8a0db-e922-453a-af8d-843af8734b3b
    ws-opts:
      headers:
        Host: tms.dingtalk.com
      path: /
    xudp: true
  - name: "\U0001F1F8\U0001F1EC新加坡6 | ⬇️ 10.5MB/s"
    obfs: ''
    obfs-password: ''
    password: hf96oOugMgvkOAlVykIA0EKHk
    port: 31667
    server: **************
    skip-cert-verify: true
    sni: bing.com
    type: hysteria2
  - name: "\U0001F1EF\U0001F1F5日本5 | ⬇️ 9.9MB/s"
    obfs: ''
    obfs-password: ''
    password: 82762002-40f5-4775-a9ad-3da97a2772f7
    port: 20000
    server: jp01.poke-mon.xyz
    skip-cert-verify: true
    sni: www.bing.com
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国11 | ⬇️ 8.4MB/s"
    password: aed1cc24-351d-11ef-ba52-f23c9164ca5d
    port: 15229
    server: a28054aa-swtr40-sy9vy9-1rfon.cu.plebai.net
    type: trojan
    udp: true
    sni: a28054aa-swtr40-sy9vy9-1rfon.cu.plebai.net
  - cipher: dummy
    name: "\U0001F1FA\U0001F1F8美国12 | ⬇️ 13.2MB/s"
    obfs: plain
    obfs-param: cbcdc8198.microsoft.com
    password: mantouyun888
    port: 44005
    protocol: auth_chain_a
    protocol-param: '8198:CsJt8BBrKFOSrjv2'
    server: 88gg.mt.mt5888.top
    type: ssr
    udp: true
  - name: "\U0001F1FA\U0001F1F8美国13 | ⬇️ 7.9MB/s"
    password: 31ede2d8-3f22-11ef-b023-f23c9164ca5d
    port: 15229
    server: 863f62ce-swtr40-szcku7-153sp.cu.plebai.net
    skip-cert-verify: true
    sni: 863f62ce-swtr40-szcku7-153sp.cu.plebai.net
    type: trojan
    udp: true
  - name: "\U0001F1FA\U0001F1F8美国14 | ⬇️ 8.2MB/s"
    password: 6e9cdc20-b92a-11ef-973a-f23c913c8d2b
    port: 15229
    server: e068bf84-swxgg0-sww7b0-1qwp5.cu.plebai.net
    type: trojan
    udp: true
    sni: e068bf84-swxgg0-sww7b0-1qwp5.cu.plebai.net
  - name: "\U0001F1E9\U0001F1EA德国2 | ⬇️ 10.6MB/s"
    network: ws
    port: 8880
    server: ***********
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /TelegramU0001F1E8U0001F1F3 @MxlShare @WangCai2 /?ed=2560
    servername: reedfree8mahsang2.redorg.ir
  - name: "\U0001F1E9\U0001F1EA德国3 | ⬇️ 9.6MB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /?ed=2560
  - name: "\U0001F1FA\U0001F1F8美国15 | ⬇️ 7.8MB/s"
    password: 591ac730-bd6f-11ed-a8bf-f23c91cfbbc9
    port: 15229
    server: eddfd42e-swtr40-sy3zfb-ezjz.cu.plebai.net
    type: trojan
    udp: true
    sni: eddfd42e-swtr40-sy3zfb-ezjz.cu.plebai.net
  - client-fingerprint: chrome
    grpc-opts:
      grpc-mode: gun
      grpc-service-name: >-
        CANALTELEGRAM--jet_config--TELEGRAM--jet_config--CANALTELEGRAM--jet_config--TELEGRAM--jet_config--CANALTELEGRAM--jet_config--TELEGRAM--jet_config--CANALTELEGRAM--jet_config--TELEGRAM--jet_config
    name: "\U0001F1EB\U0001F1F7法国1 | ⬇️ 8.4MB/s"
    network: grpc
    port: 1210
    reality-opts:
      public-key: YWfCdTnr4FAOMYTY2dLrMtQUokyxOGpPhYEEszPj20E
      short-id: 7fe29733
    server: ns8.esfahansiman.com
    tls: true
    type: vless
    udp: true
    uuid: 4ccaab94-9fb3-44ec-8caf-178cee94424f
    servername: ubuntu.com
  - name: "\U0001F1FA\U0001F1F8美国16 | ⬇️ 8.0MB/s"
    password: 3d7182ce-8dcc-11ef-a3f6-f23c9164ca5d
    port: 15229
    server: f22a91be-swvls0-thdi1k-19yro.cu.plebai.net
    type: trojan
    udp: true
    sni: f22a91be-swvls0-thdi1k-19yro.cu.plebai.net
  - cipher: chacha20-ietf-poly1305
    name: "\U0001F1FA\U0001F1F8美国17 | ⬇️ 9.9MB/s"
    password: 0fbed6c9-0fb4-4ec3-8582-f569f8d773d5
    port: 23340
    server: ***************
    type: ss
    udp: true
  - client-fingerprint: chrome
    grpc-opts:
      grpc-mode: gun
      grpc-service-name: tj
    name: "\U0001F1FA\U0001F1F8美国18 | ⬇️ 8.1MB/s"
    network: grpc
    port: 20230
    reality-opts:
      public-key: Bq_RQyPwxp7zTYywC37jLxmcjV9npNN6H6_1d_R6GT0
      short-id: c9916730
    server: us002.421421.xyz
    tls: true
    type: vless
    udp: true
    uuid: 5a81db08-7af3-49a4-b089-b7a5b2c24c38
    servername: www.nvidia.com
  - name: "\U0001F1FA\U0001F1F8美国19 | ⬇️ 7.9MB/s"
    password: bb85e074-b0c2-11ea-ad28-f23c913c8d2b
    port: 15229
    server: 684f1982-swvls0-sx9mz9-m0b9.cu.plebai.net
    type: trojan
    udp: true
    sni: 684f1982-swvls0-sx9mz9-m0b9.cu.plebai.net
  - name: "\U0001F1FA\U0001F1F8美国20 | ⬇️ 15.7MB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /?ed=2560
    servername: reedfree8mahsang2.redorg.ir
  - name: "\U0001F1E9\U0001F1EA德国4 | ⬇️ 9.9MB/s"
    network: ws
    port: 8880
    server: ***********
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: "/Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
  - auth: 5cc61971-19b5-454a-a933-abb628ce7b80
    down: 200
    name: "\U0001F1F0\U0001F1F7韩国2 | ⬇️ 8.4MB/s"
    password: 5cc61971-19b5-454a-a933-abb628ce7b80
    port: 30308
    ports: 30301-30399
    server: hg.yuanbaojc.site
    skip-cert-verify: false
    sni: hg.yuanbaojc.site
    type: hysteria2
    udp: true
    up: 100
  - auth: 5cc61971-19b5-454a-a933-abb628ce7b80
    down: 200
    name: "\U0001F1F0\U0001F1F7韩国3 | ⬇️ 8.7MB/s"
    password: 5cc61971-19b5-454a-a933-abb628ce7b80
    port: 30350
    ports: 30301-30399
    server: hg.yuanbaojc.site
    skip-cert-verify: false
    sni: hg.yuanbaojc.site
    type: hysteria2
    udp: true
    up: 100
  - name: "\U0001F1FA\U0001F1F8美国21 | ⬇️ 11.6MB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: "/Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
  - auth: 5cc61971-19b5-454a-a933-abb628ce7b80
    down: 1000
    name: "\U0001F1E9\U0001F1EA德国5 | ⬇️ 8.0MB/s"
    password: 5cc61971-19b5-454a-a933-abb628ce7b80
    port: 30334
    ports: 30301-30399
    server: dg.yuanbaojc.site
    skip-cert-verify: false
    sni: dg.yuanbaojc.site
    type: hysteria2
    udp: true
    up: 1000
  - alterId: 0
    cipher: auto
    name: "\U0001F1F0\U0001F1F7韩国4 | ⬇️ 12.0MB/s"
    port: 17232
    server: b79d3418-sux8g0-swe4j1-1rfon.cm5.p5pv.com
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: aed1cc24-351d-11ef-ba52-f23c9164ca5d
  - name: "\U0001F1F0\U0001F1F7韩国5 | ⬇️ 15.8MB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: "/Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
  - cipher: dummy
    name: "\U0001F300其他6-SC | ⬇️ 11.2MB/s"
    obfs: plain
    obfs-param: cbcdc8198.microsoft.com
    password: mantouyun888
    port: 41115
    protocol: auth_chain_a
    protocol-param: '8198:CsJt8BBrKFOSrjv2'
    server: 9988.mt.mt5888.top
    type: ssr
    udp: true
  - cipher: dummy
    name: "\U0001F1FA\U0001F1F8美国22 | ⬇️ 13.9MB/s"
    obfs: plain
    obfs-param: cbcdc8198.microsoft.com
    password: mantouyun888
    port: 44008
    protocol: auth_chain_a
    protocol-param: '8198:CsJt8BBrKFOSrjv2'
    server: 88gg.mt.mt5888.top
    type: ssr
    udp: true
  - name: "\U0001F1ED\U0001F1F0香港7 | ⬇️ 17.4MB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: "/Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
  - auth: 5cc61971-19b5-454a-a933-abb628ce7b80
    down: 200
    name: "\U0001F1F0\U0001F1F7韩国6 | ⬇️ 8.3MB/s"
    password: 5cc61971-19b5-454a-a933-abb628ce7b80
    port: 30365
    ports: 30301-30399
    server: hg.yuanbaojc.site
    skip-cert-verify: false
    sni: hg.yuanbaojc.site
    type: hysteria2
    udp: true
    up: 100
  - name: "\U0001F1FA\U0001F1F8美国23 | ⬇️ 8.0MB/s"
    password: 861726bb-c61a-b0e0-99fa-5ddfdcf21902
    port: 15229
    server: 3aadf07e-swvls0-ta5nd4-e06r.cu.plebai.net
    type: trojan
    udp: true
    sni: 3aadf07e-swvls0-ta5nd4-e06r.cu.plebai.net
  - name: "\U0001F1FA\U0001F1F8美国24 | ⬇️ 8.0MB/s"
    password: 0da8651e-e1f6-11ec-bd7c-f23c913c8d2b
    port: 15229
    server: 19727a29-swtr40-tcinla-hrtf.cu.plebai.net
    type: trojan
    udp: true
    sni: 19727a29-swtr40-tcinla-hrtf.cu.plebai.net
  - alterId: 0
    cipher: auto
    name: "\U0001F1FA\U0001F1F8美国25 | ⬇️ 8.6MB/s"
    network: ws
    port: 22056
    server: **************
    tls: false
    type: vmess
    udp: true
    uuid: 595a346d-078c-4bb3-b426-f9a8b350eab8
    ws-opts:
      path: /
  - name: "\U0001F1FA\U0001F1F8美国26 | ⬇️ 13.8MB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: "/Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
  - alpn:
      - h3
    auth-str: dongtaiwang.com
    auth_str: dongtaiwang.com
    down: 100
    name: "\U0001F1EB\U0001F1F7法国2 | ⬇️ 8.2MB/s"
    port: 14241
    protocol: udp
    server: ************
    skip-cert-verify: true
    sni: apple.com
    type: hysteria
    udp: true
    up: 100
  - auth: 5cc61971-19b5-454a-a933-abb628ce7b80
    down: 200
    name: "\U0001F1F0\U0001F1F7韩国7 | ⬇️ 8.3MB/s"
    password: 5cc61971-19b5-454a-a933-abb628ce7b80
    port: 30306
    ports: 30301-30399
    server: hg.yuanbaojc.site
    skip-cert-verify: false
    sni: hg.yuanbaojc.site
    type: hysteria2
    udp: true
    up: 100
  - alterId: 2
    cipher: auto
    name: "\U0001F1ED\U0001F1F0香港8 | ⬇️ 14.4MB/s"
    network: ws
    port: 459
    server: 688f6e4a-swxgg0-syb15h-8caj.hkt.east.wctype.com
    tls: false
    type: vmess
    udp: true
    uuid: a67a6c18-3f6d-11ef-ab9c-f23c9313b177
    ws-opts:
      headers:
        Host: a605477178.m.ctrip.com
      path: /
  - name: "\U0001F1F8\U0001F1EC新加坡7 | ⬇️ 10.2MB/s"
    network: ws
    port: 80
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 438f9559-1671-45cf-9d2c-338fe6766acf
    ws-opts:
      headers:
        Host: 14.sahanwickramasinghe.shop
      path: /
  - client-fingerprint: chrome
    name: "\U0001F1F8\U0001F1EC新加坡8 | ⬇️ 10.3MB/s"
    password: 82762002-40f5-4775-a9ad-3da97a2772f7
    port: 55015
    server: sssg03.521pokemon.com
    skip-cert-verify: true
    sni: **************
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1F3\U0001F1F1荷兰1 | ⬇️ 7.9MB/s"
    password: 7669a04d-a459-4c96-bc0e-fde51cb984f3
    port: 12074
    server: cn2.cdn.xfltd-cdn.top
    skip-cert-verify: true
    sni: cdn.alibaba.com
    type: trojan
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1F8\U0001F1EC新加坡9 | ⬇️ 9.9MB/s"
    password: 9acfc574-acc3-4c2b-ab3b-491d43a6eb83
    port: 21114
    server: okanc.node-is.green
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1F8\U0001F1EC新加坡10 | ⬇️ 9.3MB/s"
    password: 9acfc574-acc3-4c2b-ab3b-491d43a6eb83
    port: 21115
    server: okanc.node-is.green
    type: ss
    udp: true
  - name: "\U0001F1F3\U0001F1F1荷兰2 | ⬇️ 18.7MB/s"
    network: ws
    port: 8880
    server: **************
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
        User-Agent: >-
          Mozilla/5.0 (Windows NT 6.2; WOW64) AppleWebKit/537.36 (KHTML, like
          Gecko) Chrome/51.0.2704.106 Safari/537.36
      path: "/Telegram\U0001F1E8\U0001F1F3 @pgkj666 /?ed=2560"
    xudp: true
  - name: "\U0001F1FA\U0001F1F8美国27 | ⬇️ 13.8MB/s"
    network: ws
    port: 8880
    server: **************
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
        User-Agent: >-
          Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML,
          like Gecko) Chrome/65.0.3325.181 Safari/537.36
      path: "/Telegram\U0001F1E8\U0001F1F3 @pgkj666 /?ed=2560"
    xudp: true
  - alpn:
      - ''
    alterId: '0'
    cipher: auto
    name: "\U0001F1F0\U0001F1F7韩国8 | ⬇️ 11.7MB/s"
    network: tcp
    port: 17232
    server: 4924b4e1-sux8g0-sxlqgq-1spnr.cm5.p5pv.com
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 127e3f92-f714-11ef-bbb0-f23c91cfbbc9
    xudp: true
  - name: "\U0001F1F8\U0001F1EC新加坡11 | ⬇️ 14.5MB/s"
    network: ws
    port: 80
    server: **************
    type: vless
    udp: true
    uuid: 438f9559-1671-45cf-9d2c-338fe6766acf
    ws-opts:
      headers:
        Host: 14.sahanwickramasinghe.shop
        User-Agent: >-
          Mozilla/5.0 (Windows NT 6.1) AppleWebKit/537.36 (KHTML, like Gecko)
          Chrome/70.0.3538.110 Safari/537.36
      path: /
    xudp: true
  - alterId: 0
    cipher: auto
    name: "\U0001F1F8\U0001F1EC新加坡12 | ⬇️ 12.7MB/s"
    network: ws
    port: 443
    server: tls.08.node-for-bigairport.win
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: c69374da-2208-4cbd-b81e-cdf88b5e7f53
    ws-opts:
      headers:
        Host: tls.08.node-for-bigairport.win
      path: /
  - auth: 5cc61971-19b5-454a-a933-abb628ce7b80
    down: 200
    name: "\U0001F1F0\U0001F1F7韩国9 | ⬇️ 8.0MB/s"
    password: 5cc61971-19b5-454a-a933-abb628ce7b80
    port: 30321
    ports: 30301-30399
    server: hg.yuanbaojc.site
    skip-cert-verify: false
    sni: hg.yuanbaojc.site
    type: hysteria2
    udp: true
    up: 100
  - auth: 5cc61971-19b5-454a-a933-abb628ce7b80
    down: 1000
    name: "\U0001F1FA\U0001F1F8美国28 | ⬇️ 8.1MB/s"
    password: 5cc61971-19b5-454a-a933-abb628ce7b80
    port: 30396
    ports: 30301-30399
    server: mg.yuanbaojc.site
    skip-cert-verify: false
    sni: mg.yuanbaojc.site
    type: hysteria2
    udp: true
    up: 1000
  - alterId: 2
    cipher: auto
    name: "\U0001F1FA\U0001F1F8美国29 | ⬇️ 10.3MB/s"
    network: ws
    port: 30829
    server: v29.heduian.link
    skip-cert-verify: true
    tls: false
    type: vmess
    uuid: cbb3f877-d1fb-344c-87a9-d153bffd5484
    ws-opts:
      headers:
        Host: v29.heduian.link
      path: /oooo
    servername: v29.heduian.link
  - name: "\U0001F1ED\U0001F1F0香港9 | ⬇️ 10.2MB/s"
    network: ws
    port: 8880
    server: *************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /?ed=2560
  - alterId: 0
    cipher: auto
    name: "\U0001F1FA\U0001F1F8美国30 | ⬇️ 8.1MB/s"
    network: ws
    port: 22056
    server: **************
    tls: false
    type: vmess
    udp: true
    uuid: 2260c6f3-e170-408d-832f-4c1217d3f1ed
    ws-opts:
      path: /
  - name: "\U0001F1FA\U0001F1F8美国31 | ⬇️ 8.0MB/s"
    password: 91365a7a-46d7-11ee-a8b9-f23c9164ca5d
    port: 15229
    server: 77a2be97-swvls0-t3o6u7-1osdm.cu.plebai.net
    type: trojan
    udp: true
    sni: 77a2be97-swvls0-t3o6u7-1osdm.cu.plebai.net
  - alterId: 0
    cipher: auto
    name: "\U0001F1FA\U0001F1F8美国32 | ⬇️ 8.6MB/s"
    network: ws
    port: 22056
    server: **************
    tls: false
    type: vmess
    udp: true
    uuid: e2c75ffe-a803-46eb-9ad2-fedcc56ca0b6
    ws-opts:
      path: /
  - cipher: dummy
    name: "\U0001F1F8\U0001F1EC新加坡13 | ⬇️ 12.1MB/s"
    obfs: plain
    obfs-param: cbcdc8198.microsoft.com
    password: mantouyun888
    port: 44003
    protocol: auth_chain_a
    protocol-param: '8198:CsJt8BBrKFOSrjv2'
    server: 88gg.mt.mt5888.top
    type: ssr
    udp: true
  - auth: dcccacba-fa44-11ef-8400-f23c9164ca5d
    name: "\U0001F1FA\U0001F1F8美国33 | ⬇️ 9.6MB/s"
    password: dcccacba-fa44-11ef-8400-f23c9164ca5d
    port: 8443
    server: d465b594-swvls0-sx0fe4-1j6h0.hy2.gotochinatown.net
    type: hysteria2
    udp: true
    sni: d465b594-swvls0-sx0fe4-1j6h0.hy2.gotochinatown.net
  - alterId: 2
    cipher: auto
    name: "\U0001F1ED\U0001F1F0香港10 | ⬇️ 14.3MB/s"
    network: ws
    port: 459
    server: 9d6c2f7e-swvls0-syb15h-8caj.hkt.east.wctype.com
    tls: false
    type: vmess
    udp: true
    uuid: a67a6c18-3f6d-11ef-ab9c-f23c9313b177
    ws-opts:
      headers:
        Host: a605477178.m.ctrip.com
      path: /
  - name: "\U0001F1FA\U0001F1F8美国34 | ⬇️ 9.4MB/s"
    password: d4018e28-e328-11ed-98a7-f23c913c8d2b
    port: 15229
    server: 036c81df-swtr40-sx3h07-1g8k0.cu.plebai.net
    type: trojan
    udp: true
    sni: 036c81df-swtr40-sx3h07-1g8k0.cu.plebai.net
  - name: "\U0001F1FA\U0001F1F8美国35 | ⬇️ 9.5MB/s"
    obfs-opts:
      host: icloud.com
      mode: http
    port: 45768
    psk: SRjsBEEEvNNCL69o
    server: *************
    type: snell
    version: 3
  - name: "\U0001F1E9\U0001F1EA德国6 | ⬇️ 14.0MB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: "/Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
  - name: "\U0001F1FA\U0001F1F8美国36 | ⬇️ 8.3MB/s"
    password: 34f0cf42-0f8a-11ec-a8bf-f23c91cfbbc9
    port: 15229
    server: ccab8269-swxgg0-szdere-155d9.cu.plebai.net
    type: trojan
    udp: true
    sni: ccab8269-swxgg0-szdere-155d9.cu.plebai.net
  - name: "\U0001F1FA\U0001F1F8美国37 | ⬇️ 7.9MB/s"
    password: 336501b6-51d2-11ee-a993-f23c9164ca5d
    port: 15229
    server: 0e1a59af-swxgg0-t8kd6j-1c9em.cu.plebai.net
    type: trojan
    udp: true
    sni: 0e1a59af-swxgg0-t8kd6j-1c9em.cu.plebai.net
  - name: "\U0001F1FA\U0001F1F8美国38 | ⬇️ 8.3MB/s"
    password: 788328ee-d49f-11ef-bd97-f23c9164ca5d
    port: 15229
    server: 3238ec22-swxgg0-tfnoge-1luqs.cu.plebai.net
    type: trojan
    udp: true
    sni: 3238ec22-swxgg0-tfnoge-1luqs.cu.plebai.net
  - name: "\U0001F1E9\U0001F1EA德国7 | ⬇️ 15.2MB/s"
    network: ws
    port: 8880
    server: *************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /Telegram@ShadowProxy66/?ed=2560
  - auth: 869a0163-456f-4c06-bd4a-2376e4563eae
    name: "\U0001F1FA\U0001F1F8美国39 | ⬇️ 7.8MB/s"
    password: 869a0163-456f-4c06-bd4a-2376e4563eae
    port: 33003
    server: qydg.qy1357.top
    skip-cert-verify: true
    sni: qydg.qy1357.top
    type: hysteria2
    udp: true
  - name: "\U0001F1E9\U0001F1EA德国8 | ⬇️ 11.8MB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: "/Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
  - name: "\U0001F1F3\U0001F1F1荷兰3 | ⬇️ 9.9MB/s"
    network: ws
    port: 8880
    server: ***********
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /TelegramU0001F1E8U0001F1F3 @MxlShare @WangCai2 /?ed=2560
    servername: reedfree8mahsang2.redorg.ir
  - name: "\U0001F1FA\U0001F1F8美国40 | ⬇️ 12.8MB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /TelegramU0001F1E8U0001F1F3 @MxlShare @WangCai2 /?ed=2560
    servername: reedfree8mahsang2.redorg.ir
  - auth: 336501b6-51d2-11ee-a993-f23c9164ca5d
    name: "\U0001F1FA\U0001F1F8美国41 | ⬇️ 8.8MB/s"
    password: 336501b6-51d2-11ee-a993-f23c9164ca5d
    port: 8443
    server: 4f88735f-swxgg0-t8kd6j-1c9em.hy2.gotochinatown.net
    type: hysteria2
    udp: true
    sni: 4f88735f-swxgg0-t8kd6j-1c9em.hy2.gotochinatown.net
  - name: "\U0001F1F3\U0001F1F1荷兰4 | ⬇️ 15.3MB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /TelegramU0001F1E8U0001F1F3 @MxlShare @WangCai2 /?ed=2560
    servername: reedfree8mahsang2.redorg.ir
port: 7890
socks-port: 7891
redir-port: 7892
mixed-port: 7893
tproxy-port: 7894
ipv6: false
allow-lan: true
unified-delay: true
tcp-concurrent: true
geodata-mode: false
geodata-loader: standard
geo-auto-update: true
geo-update-interval: 48
geox-url:
  geoip: 'https://testingcf.jsdelivr.net/gh/MetaCubeX/meta-rules-dat@release/geoip.dat'
  geosite: >-
    https://testingcf.jsdelivr.net/gh/MetaCubeX/meta-rules-dat@release/geosite.dat
  mmdb: >-
    https://testingcf.jsdelivr.net/gh/MetaCubeX/meta-rules-dat@release/country.mmdb
  asn: >-
    https://github.com/xishang0128/geoip/releases/download/latest/GeoLite2-ASN.mmdb
profile:
  store-selected: true
  store-fake-ip: true
sniffer:
  enable: true
  sniff:
    HTTP:
      ports:
        - 80
        - 8080-8880
      override-destination: true
    TLS:
      ports:
        - 443
        - 8443
    QUIC:
      ports:
        - 443
        - 8443
  force-domain:
    - +.v2ex.com
  skip-domain:
    - Mijia Cloud
    - dlg.io.mi.com
    - +.push.apple.com
    - +.apple.com
dns:
  enable: true
  listen: '0.0.0.0:1053'
  ipv6: false
  respect-rules: true
  enhanced-mode: fake-ip
  fake-ip-range: ********/8
  fake-ip-filter-mode: blacklist
  fake-ip-filter:
    - +.lan
    - +.local
    - 'geosite:private'
    - 'geosite:cn'
  default-nameserver:
    - *********
    - ************
  proxy-server-nameserver:
    - *********
    - ************
  nameserver:
    - *********
    - ************
  nameserver-policy:
    'rule-set:private_domain,cn_domain':
      - *********
      - ************
    'rule-set:geolocation-!cn':
      - 'https://dns.cloudflare.com/dns-query'
      - 'https://dns.google/dns-query'
pr:
  type: select
  proxies:
    - "\U0001F680 节点选择"
    - "\U0001F1ED\U0001F1F0 香港负载均衡"
    - "\U0001F1EF\U0001F1F5 日本负载均衡"
    - "\U0001F1F0\U0001F1F7 韩国负载均衡"
    - "\U0001F1F8\U0001F1EC 新加坡负载均衡"
    - "\U0001F1FA\U0001F1F8 美国负载均衡"
    - "\U0001F1ED\U0001F1F0 香港自动"
    - "\U0001F1EF\U0001F1F5 日本自动"
    - "\U0001F1F0\U0001F1F7 韩国自动"
    - "\U0001F1F8\U0001F1EC 新加坡自动"
    - "\U0001F1FA\U0001F1F8 美国自动"
    - ♻️ 自动选择
    - "\U0001F1ED\U0001F1F0 香港节点"
    - "\U0001F1EF\U0001F1F5 日本节点"
    - "\U0001F1F0\U0001F1F7 韩国节点"
    - "\U0001F1F8\U0001F1EC 新加坡节点"
    - "\U0001F1FA\U0001F1F8 美国节点"
    - "\U0001F310 全部节点"
proxy-groups:
  - name: "\U0001F310 全部节点"
    type: select
    include-all: true
  - name: "\U0001F680 节点选择"
    type: select
    proxies:
      - "\U0001F1ED\U0001F1F0 香港负载均衡"
      - "\U0001F1EF\U0001F1F5 日本负载均衡"
      - "\U0001F1F0\U0001F1F7 韩国负载均衡"
      - "\U0001F1F8\U0001F1EC 新加坡负载均衡"
      - "\U0001F1FA\U0001F1F8 美国负载均衡"
      - "\U0001F1ED\U0001F1F0 香港自动"
      - "\U0001F1EF\U0001F1F5 日本自动"
      - "\U0001F1F0\U0001F1F7 韩国自动"
      - "\U0001F1F8\U0001F1EC 新加坡自动"
      - "\U0001F1FA\U0001F1F8 美国自动"
      - ♻️ 自动选择
      - "\U0001F1ED\U0001F1F0 香港节点"
      - "\U0001F1EF\U0001F1F5 日本节点"
      - "\U0001F1F0\U0001F1F7 韩国节点"
      - "\U0001F1F8\U0001F1EC 新加坡节点"
      - "\U0001F1FA\U0001F1F8 美国节点"
      - "\U0001F310 全部节点"
  - name: "\U0001F4AC ChatGPT"
    type: select
    proxies:
      - "\U0001F680 节点选择"
      - "\U0001F1ED\U0001F1F0 香港负载均衡"
      - "\U0001F1EF\U0001F1F5 日本负载均衡"
      - "\U0001F1F0\U0001F1F7 韩国负载均衡"
      - "\U0001F1F8\U0001F1EC 新加坡负载均衡"
      - "\U0001F1FA\U0001F1F8 美国负载均衡"
      - "\U0001F1ED\U0001F1F0 香港自动"
      - "\U0001F1EF\U0001F1F5 日本自动"
      - "\U0001F1F0\U0001F1F7 韩国自动"
      - "\U0001F1F8\U0001F1EC 新加坡自动"
      - "\U0001F1FA\U0001F1F8 美国自动"
      - ♻️ 自动选择
      - "\U0001F1ED\U0001F1F0 香港节点"
      - "\U0001F1EF\U0001F1F5 日本节点"
      - "\U0001F1F0\U0001F1F7 韩国节点"
      - "\U0001F1F8\U0001F1EC 新加坡节点"
      - "\U0001F1FA\U0001F1F8 美国节点"
      - "\U0001F310 全部节点"
  - name: "\U0001F4FA YouTube"
    type: select
    proxies:
      - "\U0001F680 节点选择"
      - "\U0001F1ED\U0001F1F0 香港负载均衡"
      - "\U0001F1EF\U0001F1F5 日本负载均衡"
      - "\U0001F1F0\U0001F1F7 韩国负载均衡"
      - "\U0001F1F8\U0001F1EC 新加坡负载均衡"
      - "\U0001F1FA\U0001F1F8 美国负载均衡"
      - "\U0001F1ED\U0001F1F0 香港自动"
      - "\U0001F1EF\U0001F1F5 日本自动"
      - "\U0001F1F0\U0001F1F7 韩国自动"
      - "\U0001F1F8\U0001F1EC 新加坡自动"
      - "\U0001F1FA\U0001F1F8 美国自动"
      - ♻️ 自动选择
      - "\U0001F1ED\U0001F1F0 香港节点"
      - "\U0001F1EF\U0001F1F5 日本节点"
      - "\U0001F1F0\U0001F1F7 韩国节点"
      - "\U0001F1F8\U0001F1EC 新加坡节点"
      - "\U0001F1FA\U0001F1F8 美国节点"
      - "\U0001F310 全部节点"
  - name: "\U0001F3B5 TikTok"
    type: select
    proxies:
      - "\U0001F680 节点选择"
      - "\U0001F1ED\U0001F1F0 香港负载均衡"
      - "\U0001F1EF\U0001F1F5 日本负载均衡"
      - "\U0001F1F0\U0001F1F7 韩国负载均衡"
      - "\U0001F1F8\U0001F1EC 新加坡负载均衡"
      - "\U0001F1FA\U0001F1F8 美国负载均衡"
      - "\U0001F1ED\U0001F1F0 香港自动"
      - "\U0001F1EF\U0001F1F5 日本自动"
      - "\U0001F1F0\U0001F1F7 韩国自动"
      - "\U0001F1F8\U0001F1EC 新加坡自动"
      - "\U0001F1FA\U0001F1F8 美国自动"
      - ♻️ 自动选择
      - "\U0001F1ED\U0001F1F0 香港节点"
      - "\U0001F1EF\U0001F1F5 日本节点"
      - "\U0001F1F0\U0001F1F7 韩国节点"
      - "\U0001F1F8\U0001F1EC 新加坡节点"
      - "\U0001F1FA\U0001F1F8 美国节点"
      - "\U0001F310 全部节点"
  - name: "\U0001F3AC NETFLIX"
    type: select
    proxies:
      - "\U0001F680 节点选择"
      - "\U0001F1ED\U0001F1F0 香港负载均衡"
      - "\U0001F1EF\U0001F1F5 日本负载均衡"
      - "\U0001F1F0\U0001F1F7 韩国负载均衡"
      - "\U0001F1F8\U0001F1EC 新加坡负载均衡"
      - "\U0001F1FA\U0001F1F8 美国负载均衡"
      - "\U0001F1ED\U0001F1F0 香港自动"
      - "\U0001F1EF\U0001F1F5 日本自动"
      - "\U0001F1F0\U0001F1F7 韩国自动"
      - "\U0001F1F8\U0001F1EC 新加坡自动"
      - "\U0001F1FA\U0001F1F8 美国自动"
      - ♻️ 自动选择
      - "\U0001F1ED\U0001F1F0 香港节点"
      - "\U0001F1EF\U0001F1F5 日本节点"
      - "\U0001F1F0\U0001F1F7 韩国节点"
      - "\U0001F1F8\U0001F1EC 新加坡节点"
      - "\U0001F1FA\U0001F1F8 美国节点"
      - "\U0001F310 全部节点"
  - name: "\U0001F1ED\U0001F1F0 香港节点"
    type: select
    include-all: true
    filter: (?i)港|hk|hongkong|hong kong
  - name: "\U0001F1EF\U0001F1F5 日本节点"
    type: select
    include-all: true
    filter: (?i)日|jp|japan
  - name: "\U0001F1F0\U0001F1F7 韩国节点"
    type: select
    include-all: true
    filter: (?i)韩|kr|korea
  - name: "\U0001F1F8\U0001F1EC 新加坡节点"
    type: select
    include-all: true
    filter: (?i)新|狮|sg|singapore|新加坡
  - name: "\U0001F1FA\U0001F1F8 美国节点"
    type: select
    include-all: true
    filter: (?i)美|us|unitedstates|united states
  - name: "\U0001F1ED\U0001F1F0 香港自动"
    type: url-test
    include-all: true
    tolerance: 20
    interval: 300
    filter: (?=.*(港|HK|(?i)Hong))^((?!(台|日|韩|新|深|美)).)*$
  - name: "\U0001F1EF\U0001F1F5 日本自动"
    type: url-test
    include-all: true
    tolerance: 20
    interval: 300
    filter: (?=.*(日|JP|(?i)Japan))^((?!(港|台|韩|新|美)).)*$
  - name: "\U0001F1F0\U0001F1F7 韩国自动"
    type: url-test
    include-all: true
    tolerance: 20
    interval: 300
    filter: (?=.*(韩|KR|(?i)Korea))^((?!(港|台|日|新|美)).)*$
  - name: "\U0001F1F8\U0001F1EC 新加坡自动"
    type: url-test
    include-all: true
    tolerance: 20
    interval: 300
    filter: (?=.*(新|狮|SG|(?i)Singapore))^((?!(港|台|日|韩|美)).)*$
  - name: "\U0001F1FA\U0001F1F8 美国自动"
    type: url-test
    include-all: true
    tolerance: 20
    interval: 300
    filter: (?=.*(美|US|(?i)States|America))^((?!(港|台|日|韩|新)).)*$
  - name: ♻️ 自动选择
    type: url-test
    include-all: true
    tolerance: 20
    interval: 300
    filter: ^((?!(直连)).)*$
  - name: "\U0001F1ED\U0001F1F0 香港负载均衡"
    type: load-balance
    include-all: true
    interval: 60
    lazy: true
    url: 'http://www.google.com/blank.html'
    disable-udp: false
    strategy: round-robin
    timeout: 2000
    max-failed-times: 3
    filter: (?=.*(港|HK|(?i)Hong))^((?!(台|日|韩|新|深|美)).)*$
  - name: "\U0001F1EF\U0001F1F5 日本负载均衡"
    type: load-balance
    include-all: true
    interval: 60
    lazy: true
    url: 'http://www.google.com/blank.html'
    disable-udp: false
    strategy: round-robin
    timeout: 2000
    max-failed-times: 3
    filter: (?=.*(日|JP|(?i)Japan))^((?!(港|台|韩|新|美)).)*$
  - name: "\U0001F1F0\U0001F1F7 韩国负载均衡"
    type: load-balance
    include-all: true
    interval: 60
    lazy: true
    url: 'http://www.google.com/blank.html'
    disable-udp: false
    strategy: round-robin
    timeout: 2000
    max-failed-times: 3
    filter: (?=.*(韩|KR|(?i)Korea))^((?!(港|台|日|新|美)).)*$
  - name: "\U0001F1F8\U0001F1EC 新加坡负载均衡"
    type: load-balance
    include-all: true
    interval: 60
    lazy: true
    url: 'http://www.google.com/blank.html'
    disable-udp: false
    strategy: round-robin
    timeout: 2000
    max-failed-times: 3
    filter: (?=.*(新|狮|SG|(?i)Singapore))^((?!(港|台|日|韩|美)).)*$
  - name: "\U0001F1FA\U0001F1F8 美国负载均衡"
    type: load-balance
    include-all: true
    interval: 60
    lazy: true
    url: 'http://www.google.com/blank.html'
    disable-udp: false
    strategy: round-robin
    timeout: 2000
    max-failed-times: 3
    filter: (?=.*(美|US|(?i)States|America))^((?!(港|台|日|韩|新)).)*$
  - name: "\U0001F3AF 全球直连"
    type: select
    proxies:
      - DIRECT
      - "\U0001F680 节点选择"
  - name: "\U0001F420 漏网之鱼"
    type: select
    proxies:
      - "\U0001F680 节点选择"
      - "\U0001F1ED\U0001F1F0 香港负载均衡"
      - "\U0001F1EF\U0001F1F5 日本负载均衡"
      - "\U0001F1F0\U0001F1F7 韩国负载均衡"
      - "\U0001F1F8\U0001F1EC 新加坡负载均衡"
      - "\U0001F1FA\U0001F1F8 美国负载均衡"
      - "\U0001F1ED\U0001F1F0 香港自动"
      - "\U0001F1EF\U0001F1F5 日本自动"
      - "\U0001F1F0\U0001F1F7 韩国自动"
      - "\U0001F1F8\U0001F1EC 新加坡自动"
      - "\U0001F1FA\U0001F1F8 美国自动"
      - ♻️ 自动选择
      - "\U0001F1ED\U0001F1F0 香港节点"
      - "\U0001F1EF\U0001F1F5 日本节点"
      - "\U0001F1F0\U0001F1F7 韩国节点"
      - "\U0001F1F8\U0001F1EC 新加坡节点"
      - "\U0001F1FA\U0001F1F8 美国节点"
      - "\U0001F310 全部节点"
rules:
  - 'RULE-SET,BanAD,REJECT'
  - 'RULE-SET,BanProgramAD,REJECT'
  - 'RULE-SET,adobe,REJECT'
  - "RULE-SET,youtube_domain,\U0001F4FA YouTube"
  - 'RULE-SET,tencent,DIRECT'
  - 'RULE-SET,private_domain,DIRECT'
  - 'RULE-SET,TencentVideo,DIRECT'
  - "RULE-SET,apple_domain,\U0001F3AF 全球直连"
  - "RULE-SET,ai,\U0001F4AC ChatGPT"
  - "RULE-SET,Spotify,\U0001F680 节点选择"
  - "RULE-SET,github_domain,\U0001F680 节点选择"
  - "RULE-SET,google_domain,\U0001F680 节点选择"
  - "RULE-SET,onedrive_domain,\U0001F3AF 全球直连"
  - "RULE-SET,microsoft_domain,\U0001F3AF 全球直连"
  - "RULE-SET,tiktok_domain,\U0001F3B5 TikTok"
  - "RULE-SET,speedtest_domain,\U0001F680 节点选择"
  - "RULE-SET,telegram_domain,\U0001F680 节点选择"
  - "RULE-SET,netflix_domain,\U0001F3AC NETFLIX"
  - "RULE-SET,Netflix,\U0001F3AC NETFLIX"
  - "RULE-SET,paypal_domain,\U0001F680 节点选择"
  - "RULE-SET,geolocation-!cn,\U0001F680 节点选择"
  - "RULE-SET,cn_domain,\U0001F3AF 全球直连"
  - "RULE-SET,google_ip,\U0001F680 节点选择,no-resolve"
  - "RULE-SET,netflix_ip,\U0001F3AC NETFLIX,no-resolve"
  - "RULE-SET,telegram_ip,\U0001F680 节点选择,no-resolve"
  - "RULE-SET,cn_ip,\U0001F3AF 全球直连"
  - "RULE-SET,proxylite,\U0001F680 节点选择"
  - "MATCH,\U0001F420 漏网之鱼"
rule-anchor:
  ip:
    type: http
    interval: 86400
    behavior: ipcidr
    format: mrs
  domain:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
  qcy:
    type: http
    interval: 86400
    behavior: domain
    format: text
  class:
    type: http
    interval: 86400
    behavior: classical
    format: text
rule-providers:
  BanAD:
    type: http
    interval: 86400
    behavior: classical
    format: text
    url: 'https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/Clash/BanAD.list'
  tencent:
    type: http
    interval: 86400
    behavior: classical
    format: text
    url: >-
      https://cdn.jsdelivr.net/gh/blackmatrix7/ios_rule_script@master/rule/Shadowrocket/Tencent/Tencent.list
  TencentVideo:
    type: http
    interval: 86400
    behavior: classical
    format: text
    url: >-
      https://cdn.jsdelivr.net/gh/blackmatrix7/ios_rule_script@master/rule/Shadowrocket/TencentVideo/TencentVideo.list
  BanProgramAD:
    type: http
    interval: 86400
    behavior: classical
    format: text
    url: >-
      https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/Clash/BanProgramAD.list
  adobe:
    type: http
    interval: 86400
    behavior: classical
    format: text
    url: 'https://raw.githubusercontent.com/cmliu/ACL4SSR/main/Clash/adobe.list'
  private_domain:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/private.mrs
  ai:
    type: http
    interval: 86400
    behavior: classical
    format: text
    url: >-
      https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/a98edee7cd64d64cb58ce26d63d2dbd2475575ad/rule/Clash/OpenAI/OpenAI.list
  Spotify:
    type: http
    interval: 86400
    behavior: classical
    format: text
    url: >-
      https://cdn.jsdelivr.net/gh/blackmatrix7/ios_rule_script@release/rule/Shadowrocket/Spotify/Spotify.list
  Netflix:
    type: http
    interval: 86400
    behavior: classical
    format: text
    url: >-
      https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/a98edee7cd64d64cb58ce26d63d2dbd2475575ad/rule/Clash/Netflix/Netflix.list
  youtube_domain:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/youtube.mrs
  google_domain:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/google.mrs
  github_domain:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/github.mrs
  telegram_domain:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/telegram.mrs
  netflix_domain:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/netflix.mrs
  paypal_domain:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/paypal.mrs
  onedrive_domain:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/onedrive.mrs
  microsoft_domain:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/microsoft.mrs
  apple_domain:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/apple-cn.mrs
  speedtest_domain:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/ookla-speedtest.mrs
  tiktok_domain:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/tiktok.mrs
  gfw_domain:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/gfw.mrs
  geolocation-!cn:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/geolocation-!cn.mrs
  cn_domain:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/cn.mrs
  proxylite:
    type: http
    interval: 86400
    behavior: classical
    format: text
    url: >-
      https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/refs/heads/master/rule/Clash/ProxyLite/ProxyLite.list
  cn_ip:
    type: http
    interval: 86400
    behavior: ipcidr
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geoip/cn.mrs
  google_ip:
    type: http
    interval: 86400
    behavior: ipcidr
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geoip/google.mrs
  telegram_ip:
    type: http
    interval: 86400
    behavior: ipcidr
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geoip/telegram.mrs
  netflix_ip:
    type: http
    interval: 86400
    behavior: ipcidr
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geoip/netflix.mrs
