#!/usr/bin/env node

/**
 * 代理节点转换工具演示脚本
 */

import { ProxyConverter } from './src/index.js';
import { OutputFormats } from './src/types.js';

// 演示数据
const demoUrls = [
  'ss://<EMAIL>:8388#香港节点1',
  'ss://<EMAIL>:8389#HK-Node-2',
  'vmess://eyJ2IjoiMiIsInBzIjoi5pel5pys6IqC54K5IiwiYWRkIjoianAuZXhhbXBsZS5jb20iLCJwb3J0IjoiODA4MCIsImlkIjoiMTIzNDU2NzgtYWJjZC0xMjM0LWFiY2QtMTIzNDU2Nzg5YWJjIiwiYWlkIjoiMCIsInNjeSI6ImF1dG8iLCJuZXQiOiJ0Y3AiLCJ0eXBlIjoibm9uZSIsImhvc3QiOiIiLCJwYXRoIjoiIiwidGxzIjoiIn0=',
  'vless://<EMAIL>:443?encryption=none&security=tls&type=ws&host=us.example.com&path=/ws#美国节点1',
  'trojan://<EMAIL>:443?security=tls&type=tcp&sni=sg.example.com#Singapore-Node',
  // 添加一个重复节点用于演示去重
  'ss://<EMAIL>:8388#香港重复节点',
];

function printSeparator(title) {
  console.log('\n' + '='.repeat(50));
  console.log(`  ${title}`);
  console.log('='.repeat(50));
}

function printSubTitle(title) {
  console.log(`\n📌 ${title}`);
  console.log('-'.repeat(30));
}

async function runDemo() {
  console.log('🚀 代理节点转换工具演示');
  console.log('基于 Sub-Store 项目开发的节点转换工具');
  
  const converter = new ProxyConverter();

  try {
    printSeparator('步骤 1: 解析代理节点');
    
    console.log('输入的代理URL:');
    demoUrls.forEach((url, index) => {
      console.log(`  ${index + 1}. ${url.substring(0, 60)}...`);
    });

    const nodes = converter.parse(demoUrls);
    
    printSubTitle('解析结果');
    console.log(`成功解析 ${nodes.length} 个节点:`);
    nodes.forEach((node, index) => {
      console.log(`  ${index + 1}. ${node.name} (${node.type}) - ${node.server}:${node.port}`);
    });

    printSeparator('步骤 2: 节点去重');
    
    const originalCount = nodes.length;
    const deduplicatedNodes = converter.deduplicate(nodes, { smart: true });
    
    console.log(`原始节点数: ${originalCount}`);
    console.log(`去重后节点数: ${deduplicatedNodes.length}`);
    console.log(`移除重复节点: ${originalCount - deduplicatedNodes.length}`);
    
    if (deduplicatedNodes.length < originalCount) {
      printSubTitle('去重后的节点');
      deduplicatedNodes.forEach((node, index) => {
        console.log(`  ${index + 1}. ${node.name} (${node.type})`);
      });
    }

    printSeparator('步骤 3: 节点重命名');
    
    const renamedNodes = converter.rename(deduplicatedNodes, {
      template: '{flag} {region} {index:3}',
      groupByRegion: true,
      startIndex: 1
    });
    
    printSubTitle('重命名结果 (格式: 国旗Emoji 地区中文名 三位数序号)');
    renamedNodes.forEach((node, index) => {
      console.log(`  ${index + 1}. ${node.name} (原名: ${node.originalName || '无'})`);
    });

    printSeparator('步骤 4: 格式转换');
    
    // 转换为 Clash 格式
    printSubTitle('转换为 Clash 配置');
    const clashConfig = converter.convert(renamedNodes, OutputFormats.CLASH);
    console.log(`生成 Clash 配置，包含 ${clashConfig.proxies.length} 个代理节点`);
    console.log('代理节点列表:');
    clashConfig.proxies.forEach((proxy, index) => {
      console.log(`  ${index + 1}. ${proxy.name} (${proxy.type})`);
    });

    // 转换为 Base64 订阅
    printSubTitle('转换为 Base64 订阅');
    const base64Subscription = converter.convert(renamedNodes, OutputFormats.BASE64);
    console.log(`Base64 订阅长度: ${base64Subscription.length} 字符`);
    console.log(`Base64 内容预览: ${base64Subscription.substring(0, 100)}...`);

    // 转换为 URL 列表
    printSubTitle('转换为 URL 列表');
    const urlList = converter.convert(renamedNodes, OutputFormats.URL);
    const urls = urlList.split('\n').filter(url => url.trim());
    console.log(`生成 ${urls.length} 个代理URL:`);
    urls.forEach((url, index) => {
      console.log(`  ${index + 1}. ${url.substring(0, 60)}...`);
    });

    printSeparator('步骤 5: 一键处理演示');
    
    console.log('使用一键处理功能，自动完成: 解析 → 去重 → 重命名 → 转换');
    const oneClickResult = converter.process(demoUrls, OutputFormats.CLASH, {
      deduplicate: true,
      rename: true,
      deduplicateOptions: { smart: true },
      renameOptions: { 
        template: '{flag} {region} {index:3}',
        groupByRegion: true 
      }
    });
    
    console.log(`一键处理完成！生成包含 ${oneClickResult.proxies.length} 个节点的 Clash 配置`);

    printSeparator('步骤 6: 统计信息');
    
    const stats = converter.getStats(renamedNodes);
    console.log('节点统计信息:');
    console.log(`  总节点数: ${stats.total}`);
    console.log(`  有效节点: ${stats.valid}`);
    console.log(`  无效节点: ${stats.invalid}`);
    
    console.log('\n协议类型分布:');
    Object.entries(stats.types).forEach(([type, count]) => {
      console.log(`  ${type}: ${count} 个`);
    });
    
    console.log('\n地区分布:');
    Object.entries(stats.regions).forEach(([region, count]) => {
      console.log(`  ${region}: ${count} 个`);
    });

    printSeparator('演示完成');
    
    console.log('🎉 代理节点转换工具演示完成！');
    console.log('\n主要功能:');
    console.log('  ✅ 支持多种代理协议 (SS, VMess, VLESS, Trojan 等)');
    console.log('  ✅ 智能节点去重');
    console.log('  ✅ 统一节点重命名 (国旗 + 地区 + 序号)');
    console.log('  ✅ 多格式转换 (Clash, Base64, URL, JSON)');
    console.log('  ✅ 一键批量处理');
    console.log('  ✅ 详细统计信息');
    
    console.log('\n使用方法:');
    console.log('  npm install    # 安装依赖');
    console.log('  npm start      # 运行主程序');
    console.log('  npm test       # 运行测试');
    console.log('  node demo.js   # 运行演示');

  } catch (error) {
    console.error('\n❌ 演示过程中发生错误:', error);
    console.error('错误详情:', error.stack);
  }
}

// 运行演示
runDemo();
