proxies:
  - down: ''
    fingerprint: ''
    name: "\U0001F1ED\U0001F1F0香港1 | ⬇️ 13.2MB/s"
    obfs: ''
    obfs-password: ''
    password: b751e995-b24c-4f30-8425-05db0b9eac45
    port: 8022
    server: hkhkt.ccwink.cc
    skip-cert-verify: true
    sni: hkhkt.ccwink.cc
    type: hysteria2
    up: ''
  - down: ''
    fingerprint: ''
    name: "\U0001F1ED\U0001F1F0香港2 | ⬇️ 13.5MB/s"
    obfs: ''
    obfs-password: ''
    password: b751e995-b24c-4f30-8425-05db0b9eac45
    port: 8021
    server: hkhkt.ccwink.cc
    skip-cert-verify: true
    sni: hkhkt.ccwink.cc
    type: hysteria2
    up: ''
  - cipher: aes-256-gcm
    name: "\U0001F1ED\U0001F1F0香港3 | ⬇️ 8.8MB/s"
    password: c86d483c-431f-41df-bb6b-c1dcebfc7401
    port: 16006
    server: *************
    type: ss
    udp: true
  - down: ''
    fingerprint: ''
    name: "\U0001F1FA\U0001F1F8美国1 | ⬇️ 8.0MB/s"
    obfs: ''
    obfs-password: ''
    password: b8bd42a9-551f-419d-b70d-4aefdd2cb074
    port: 443
    server: *************
    skip-cert-verify: true
    sni: www.bing.com
    type: hysteria2
    up: ''
  - name: "\U0001F1EF\U0001F1F5日本1 | ⬇️ 9.1MB/s"
    password: 7669a04d-a459-4c96-bc0e-fde51cb984f3
    port: 12035
    server: cn2.cdn.xfltd-cdn.top
    skip-cert-verify: true
    sni: cn2.cdn.xfltd-cdn.top
    type: trojan
    udp: true
  - name: "\U0001F1F0\U0001F1F7韩国1 | ⬇️ 9.7MB/s"
    password: RlzoEILU
    port: 47655
    server: *************
    skip-cert-verify: true
    type: trojan
    udp: true
  - name: "\U0001F1FA\U0001F1F8美国2 | ⬇️ 9.9MB/s"
    password: 2b1ed981-6547-4094-998b-06a3323d6f6c
    port: 59599
    server: xd-js.timiwc.com
    skip-cert-verify: true
    sni: xd-js.timiwc.com
    type: trojan
    udp: true
  - cipher: chacha20-ietf-poly1305
    name: "\U0001F1FA\U0001F1F8美国3 | ⬇️ 12.4MB/s"
    password: ed6c8eea-8503-43e1-9563-0a355b0edcac
    port: 42023
    server: eepl1.dhh114514.christmas
    type: ss
    udp: true
  - name: "\U0001F1F8\U0001F1EC新加坡1 | ⬇️ 10.7MB/s"
    password: 7669a04d-a459-4c96-bc0e-fde51cb984f3
    port: 12025
    server: cn2.cdn.xfltd-cdn.top
    skip-cert-verify: true
    sni: cn2.cdn.xfltd-cdn.top
    type: trojan
    udp: true
  - name: "\U0001F1ED\U0001F1F0香港4 | ⬇️ 10.4MB/s"
    password: RlzoEILU
    port: 28296
    server: *************
    skip-cert-verify: true
    type: trojan
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1ED\U0001F1F0香港5 | ⬇️ 11.3MB/s"
    password: ed6c8eea-8503-43e1-9563-0a355b0edcac
    port: 42097
    server: eepl2.d-h-h.de
    type: ss
    udp: true
  - cipher: chacha20-ietf-poly1305
    name: "\U0001F1F0\U0001F1F7韩国2 | ⬇️ 12.7MB/s"
    password: ed6c8eea-8503-43e1-9563-0a355b0edcac
    port: 42010
    server: eepl2.d-h-h.de
    type: ss
    udp: true
  - cipher: chacha20-ietf-poly1305
    name: "\U0001F1FA\U0001F1F8美国4 | ⬇️ 8.2MB/s"
    password: ed6c8eea-8503-43e1-9563-0a355b0edcac
    port: 42022
    server: eepl1.dhh114514.christmas
    type: ss
    udp: true
  - cipher: chacha20-ietf-poly1305
    name: "\U0001F1ED\U0001F1F0香港6 | ⬇️ 13.2MB/s"
    password: ed6c8eea-8503-43e1-9563-0a355b0edcac
    port: 42099
    server: eepl1.dhh114514.christmas
    type: ss
    udp: true
  - cipher: chacha20-ietf-poly1305
    name: "\U0001F1EF\U0001F1F5日本2 | ⬇️ 9.6MB/s"
    password: f87772ed-cef9-444a-a8e8-bcf299c850ec
    port: 23335
    server: link.karleynetwork.xyz
    type: ss
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1F0\U0001F1F7韩国3 | ⬇️ 12.0MB/s"
    password: RlzoEILU
    port: 28548
    server: *************
    skip-cert-verify: true
    sni: cdn.egvra.cn
    type: trojan
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1F8\U0001F1EC新加坡2 | ⬇️ 10.6MB/s"
    password: c86d483c-431f-41df-bb6b-c1dcebfc7401
    port: 18002
    server: *************
    type: ss
    udp: true
  - cipher: aes-256-cfb
    name: "\U0001F1F8\U0001F1EC新加坡3 | ⬇️ 7.9MB/s"
    password: qawszxc123
    port: 443
    server: ************
    type: ss
    udp: true
  - cipher: aes-128-gcm
    name: "\U0001F1F8\U0001F1EC新加坡4 | ⬇️ 12.0MB/s"
    password: 763bf612-4c66-4fd4-b54b-5349bdea6bca
    port: 570
    server: aisalayer-b.upperlay.xyz
    type: ss
    udp: true
  - cipher: aes-128-gcm
    name: "\U0001F1F8\U0001F1EC新加坡5 | ⬇️ 10.2MB/s"
    password: 763bf612-4c66-4fd4-b54b-5349bdea6bca
    port: 568
    server: aisalayer-a.upperlay.xyz
    type: ss
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1ED\U0001F1F0香港7 | ⬇️ 10.4MB/s"
    password: 7669a04d-a459-4c96-bc0e-fde51cb984f3
    port: 12003
    server: cn1.cdn.xfltd-cdn.top
    skip-cert-verify: true
    sni: cdn.alibaba.com
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1ED\U0001F1F0香港8 | ⬇️ 11.4MB/s"
    password: 7669a04d-a459-4c96-bc0e-fde51cb984f3
    port: 12007
    server: cn1.cdn.xfltd-cdn.top
    skip-cert-verify: true
    sni: cdn.alibaba.com
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1ED\U0001F1F0香港9 | ⬇️ 10.1MB/s"
    password: 7669a04d-a459-4c96-bc0e-fde51cb984f3
    port: 12006
    server: cn1.cdn.xfltd-cdn.top
    skip-cert-verify: true
    sni: cdn.alibaba.com
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1FA\U0001F1F8美国5 | ⬇️ 10.3MB/s"
    password: 7669a04d-a459-4c96-bc0e-fde51cb984f3
    port: 12004
    server: cn1.cdn.xfltd-cdn.top
    skip-cert-verify: true
    sni: cdn.alibaba.com
    type: trojan
    udp: true
  - cipher: chacha20-ietf-poly1305
    name: "\U0001F1F9\U0001F1FC台湾1 | ⬇️ 13.7MB/s"
    password: ed6c8eea-8503-43e1-9563-0a355b0edcac
    port: 42029
    server: eepl2.d-h-h.de
    type: ss
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1EE\U0001F1F3印度1 | ⬇️ 9.3MB/s"
    password: 7669a04d-a459-4c96-bc0e-fde51cb984f3
    port: 12072
    server: cn2.cdn.xfltd-cdn.top
    skip-cert-verify: true
    sni: cdn.alibaba.com
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1E6\U0001F1FA澳大利亚1 | ⬇️ 11.1MB/s"
    password: 7669a04d-a459-4c96-bc0e-fde51cb984f3
    port: 12068
    server: cn2.cdn.xfltd-cdn.top
    skip-cert-verify: true
    sni: cdn.alibaba.com
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1ED\U0001F1F0香港10 | ⬇️ 10.3MB/s"
    password: RlzoEILU
    port: 39689
    server: *************
    skip-cert-verify: true
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1ED\U0001F1F0香港11 | ⬇️ 10.6MB/s"
    password: RlzoEILU
    port: 3754
    server: *************
    skip-cert-verify: true
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1E6\U0001F1FA澳大利亚2 | ⬇️ 11.2MB/s"
    password: RlzoEILU
    port: 11641
    server: *************
    skip-cert-verify: true
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1E6\U0001F1FA澳大利亚3 | ⬇️ 11.3MB/s"
    password: RlzoEILU
    port: 40252
    server: **************
    skip-cert-verify: true
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1F3\U0001F1F1荷兰1 | ⬇️ 9.1MB/s"
    password: RlzoEILU
    port: 8565
    server: *************
    skip-cert-verify: true
    type: trojan
    udp: true
  - alterId: 64
    cipher: auto
    name: "\U0001F1EF\U0001F1F5日本3 | ⬇️ 8.1MB/s"
    network: ws
    port: 1443
    server: sslvpn.51job.com
    skip-cert-verify: true
    tls: true
    type: vmess
    uuid: a6a0d901-67e9-460a-90b5-634c5c4f9782
    ws-opts:
      headers:
        Host: centos7
      path: /634c5c4f9782
    servername: centos7
  - down: ''
    fingerprint: ''
    name: "\U0001F1FA\U0001F1F8美国6 | ⬇️ 8.0MB/s"
    obfs: ''
    obfs-password: ''
    password: 92b2a3d4-f353-11ef-b714-f23c93136cb3
    port: 1443
    server: 50528787-swb8g0-sxscwg-63bp.la.shifen.uk
    skip-cert-verify: false
    sni: 50528787-swb8g0-sxscwg-63bp.la.shifen.uk
    type: hysteria2
    up: ''
  - down: ''
    fingerprint: ''
    name: "\U0001F1FA\U0001F1F8美国7 | ⬇️ 9.0MB/s"
    obfs: ''
    obfs-password: ''
    password: 16955d72-1794-11f0-a035-f23c95b6f51d
    port: 1443
    server: 2ef15a2a-svi800-sw77c8-dnss.la.shifen.uk
    skip-cert-verify: false
    sni: 2ef15a2a-svi800-sw77c8-dnss.la.shifen.uk
    type: hysteria2
    up: ''
  - down: ''
    fingerprint: ''
    name: "\U0001F1FA\U0001F1F8美国8 | ⬇️ 10.8MB/s"
    obfs: ''
    obfs-password: ''
    password: b8bd42a9-551f-419d-b70d-4aefdd2cb074
    port: 443
    server: **************
    skip-cert-verify: true
    sni: www.bing.com
    type: hysteria2
    up: ''
  - alpn:
      - h3
      - h2
      - http/1.1
    client-fingerprint: chrome
    name: "\U0001F1E9\U0001F1EA德国1 | ⬇️ 8.6MB/s"
    network: ws
    port: 8443
    server: ************
    tls: true
    type: vless
    udp: true
    uuid: 0b193b9c-6ad3-41a2-83c0-d3de9b86b70e
    ws-opts:
      headers:
        Host: frbn.musics-fa.ru
        User-Agent: >-
          Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like
          Gecko) Chrome/36.0.1985.143 Safari/537.36
      path: >-
        /telegram----------------v2ryNG01----v2ryNG01----v2ryNG01---v2ryNG01---------------v2ryNG01---v2ryNG01--v2ryNG01----v2ryNG01----v2ryNG01---v2ryNG01---v2ryNG01---v2ryNG01v2ryNG01----v2ryNG01----v2ryNG01---v2ryNG01---v2ryNG01---v2ryNG01?ed=2048
    xudp: true
    servername: frbn.musics-fa.ru
  - cipher: aes-256-gcm
    name: "\U0001F1FA\U0001F1F8美国9 | ⬇️ 9.1MB/s"
    password: 52d2c388-5458-470d-b253-f0e0f5833283
    port: 601
    server: **************
    type: ss
    udp: true
  - cipher: xchacha20-ietf-poly1305
    name: "\U0001F1ED\U0001F1F0香港12 | ⬇️ 12.1MB/s"
    password: '@CfftfYVgp4gkMHMirH6@_C'
    port: 49758
    server: *************
    type: ss
    udp: true
  - cipher: aes-128-gcm
    name: "\U0001F1F8\U0001F1EC新加坡6 | ⬇️ 10.6MB/s"
    password: f16163ec-3c35-4719-a19b-68c864cdc626
    port: 13038
    server: *************
    type: ss
    udp: true
  - cipher: chacha20-ietf-poly1305
    name: "\U0001F1FA\U0001F1F8美国10 | ⬇️ 13.5MB/s"
    password: 92e0a2cd-f842-42b6-84ef-dd2da5c711ac
    port: 44223
    server: 03.kill704.win
    type: ss
    udp: true
  - name: "\U0001F1EF\U0001F1F5日本4 | ⬇️ 9.4MB/s"
    password: 7669a04d-a459-4c96-bc0e-fde51cb984f3
    port: 12034
    server: cn2.cdn.xfltd-cdn.top
    skip-cert-verify: true
    sni: cn2.cdn.xfltd-cdn.top
    type: trojan
    udp: true
  - name: "\U0001F1ED\U0001F1F0香港13 | ⬇️ 11.1MB/s"
    password: RlzoEILU
    port: 48959
    server: **************
    skip-cert-verify: true
    type: trojan
    udp: true
  - name: "\U0001F1F0\U0001F1F7韩国4 | ⬇️ 10.7MB/s"
    password: RlzoEILU
    port: 44907
    server: *************
    skip-cert-verify: true
    type: trojan
    udp: true
  - name: "\U0001F1EF\U0001F1F5日本5 | ⬇️ 10.2MB/s"
    password: 7669a04d-a459-4c96-bc0e-fde51cb984f3
    port: 12031
    server: cn2.cdn.xfltd-cdn.top
    skip-cert-verify: true
    sni: cn2.cdn.xfltd-cdn.top
    type: trojan
    udp: true
  - name: "\U0001F1F3\U0001F1F1荷兰2 | ⬇️ 8.6MB/s"
    password: RlzoEILU
    port: 15407
    server: *************
    skip-cert-verify: true
    type: trojan
    udp: true
  - cipher: aes-256-cfb
    name: "\U0001F1F0\U0001F1F7韩国5 | ⬇️ 10.7MB/s"
    password: yijian0503
    port: 443
    server: **********
    type: ss
    udp: true
  - cipher: chacha20-ietf-poly1305
    name: "\U0001F1F0\U0001F1F7韩国6 | ⬇️ 13.9MB/s"
    password: ed6c8eea-8503-43e1-9563-0a355b0edcac
    port: 42010
    server: eepl1.dhh114514.christmas
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1ED\U0001F1F0香港14 | ⬇️ 9.4MB/s"
    password: e04ae67d4e4cd165
    port: 2019
    server: **************
    type: ss
    udp: true
  - alterId: '0'
    cipher: auto
    name: "\U0001F1F3\U0001F1F1荷兰3 | ⬇️ 8.3MB/s"
    network: ws
    port: 80
    server: tiamo1.tiamocloud.us.kg
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 1a78222f-78d1-4661-8b2d-ac131de82310
    ws-opts:
      headers:
        Host: tiamo1.tiamocloud.us.kg
      path: /
    xudp: true
  - client-fingerprint: chrome
    name: "\U0001F1ED\U0001F1F0香港15 | ⬇️ 11.0MB/s"
    password: RlzoEILU
    port: 34041
    server: **************
    skip-cert-verify: true
    sni: cdn.egvra.cn
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1ED\U0001F1F0香港16 | ⬇️ 10.3MB/s"
    password: RlzoEILU
    port: 50723
    server: **************
    skip-cert-verify: true
    type: trojan
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F300其他1-MY | ⬇️ 10.5MB/s"
    password: b8fa2e30-a210-4d8b-bb1e-bf42ea9c9e7e
    port: 29010
    server: ************
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1EF\U0001F1F5日本6 | ⬇️ 8.1MB/s"
    password: b8fa2e30-a210-4d8b-bb1e-bf42ea9c9e7e
    port: 19001
    server: *************
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1F9\U0001F1FC台湾2 | ⬇️ 9.1MB/s"
    password: 324970cf-e758-44d2-982d-32983fad93bb
    port: 17003
    server: ************
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F300其他2-VN | ⬇️ 10.7MB/s"
    password: b8fa2e30-a210-4d8b-bb1e-bf42ea9c9e7e
    port: 29001
    server: *************
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1F9\U0001F1FC台湾3 | ⬇️ 8.7MB/s"
    password: b8fa2e30-a210-4d8b-bb1e-bf42ea9c9e7e
    port: 17010
    server: *************
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1ED\U0001F1F0香港17 | ⬇️ 11.7MB/s"
    password: b8fa2e30-a210-4d8b-bb1e-bf42ea9c9e7e
    port: 16003
    server: *************
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1ED\U0001F1F0香港18 | ⬇️ 11.4MB/s"
    password: b8fa2e30-a210-4d8b-bb1e-bf42ea9c9e7e
    port: 16009
    server: *************
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1ED\U0001F1F0香港19 | ⬇️ 10.7MB/s"
    password: c38ab133-f18f-4537-8dc8-e2e2c2c24e18
    port: 16002
    server: *************
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1F8\U0001F1EC新加坡7 | ⬇️ 7.9MB/s"
    password: b8fa2e30-a210-4d8b-bb1e-bf42ea9c9e7e
    port: 18010
    server: ************
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1ED\U0001F1F0香港20 | ⬇️ 11.1MB/s"
    password: 49cdeea8-97dd-402a-bf8f-961cb59123a7
    port: 16002
    server: ************
    type: ss
    udp: true
  - cipher: aes-256-cfb
    name: "\U0001F1F8\U0001F1EC新加坡8 | ⬇️ 11.8MB/s"
    password: qawszxc123
    port: 443
    server: *************
    type: ss
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1F8\U0001F1EC新加坡9 | ⬇️ 11.1MB/s"
    password: nktaqlk-1O8bEfVXgIhUvYc_
    port: 443
    server: starlink-sgp5.2513142.xyz
    skip-cert-verify: true
    sni: www.cloudflare.com
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1F8\U0001F1EC新加坡10 | ⬇️ 8.2MB/s"
    password: sH-dNCXpq8RiI_PeL6Mr4lMT
    port: 443
    server: starlink-sgp5.2513142.xyz
    skip-cert-verify: true
    sni: www.cloudflare.com
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1F8\U0001F1EC新加坡11 | ⬇️ 10.3MB/s"
    password: sH-dNCXpq8RiI_PeL6Mr4lMT
    port: 443
    server: starlink-sgp6.2513142.xyz
    skip-cert-verify: true
    sni: www.cloudflare.com
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F300其他3-未识别 | ⬇️ 9.8MB/s"
    password: nktaqlk-1O8bEfVXgIhUvYc_
    port: 443
    server: starlink-tko5.2513142.xyz
    skip-cert-verify: true
    sni: www.cloudflare.com
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1ED\U0001F1F0香港21 | ⬇️ 11.8MB/s"
    password: 7669a04d-a459-4c96-bc0e-fde51cb984f3
    port: 12002
    server: cn1.cdn.xfltd-cdn.top
    skip-cert-verify: true
    sni: cdn.alibaba.com
    type: trojan
    udp: true
  - cipher: aes-128-gcm
    name: "\U0001F1ED\U0001F1F0香港22 | ⬇️ 12.0MB/s"
    password: ed6c8eea-8503-43e1-9563-0a355b0edcac
    port: 51001
    server: cm1.d-h-h.in
    type: ss
    udp: true
  - cipher: chacha20-ietf-poly1305
    name: "\U0001F1EF\U0001F1F5日本7 | ⬇️ 14.5MB/s"
    password: ed6c8eea-8503-43e1-9563-0a355b0edcac
    port: 42031
    server: eepl1.dhh114514.christmas
    type: ss
    udp: true
  - cipher: chacha20-ietf-poly1305
    name: "\U0001F1EF\U0001F1F5日本8 | ⬇️ 15.2MB/s"
    password: ed6c8eea-8503-43e1-9563-0a355b0edcac
    port: 42031
    server: eepl2.d-h-h.de
    type: ss
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1FA\U0001F1F8美国11 | ⬇️ 10.6MB/s"
    password: 7669a04d-a459-4c96-bc0e-fde51cb984f3
    port: 12051
    server: cn2.cdn.xfltd-cdn.top
    skip-cert-verify: true
    sni: cdn.alibaba.com
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1EC\U0001F1E7英国1 | ⬇️ 11.9MB/s"
    password: 7669a04d-a459-4c96-bc0e-fde51cb984f3
    port: 12069
    server: cn2.cdn.xfltd-cdn.top
    skip-cert-verify: true
    sni: cdn.alibaba.com
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1E9\U0001F1EA德国2 | ⬇️ 9.9MB/s"
    password: ed6c8eea-8503-43e1-9563-0a355b0edcac
    port: 41019
    server: cm1.d-h-h.in
    skip-cert-verify: true
    sni: v1-de1.776688.best
    type: trojan
    udp: true
  - alpn:
      - ''
    alterId: '0'
    cipher: auto
    name: "\U0001F300其他4-未识别 | ⬇️ 11.1MB/s"
    network: ws
    port: 20046
    server: 2fea6447-81c4-2d7d-d592-a4f729209777.7head.icu
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 14a8a0db-e922-453a-af8d-843af8734b3b
    ws-opts:
      headers:
        Host: tms.dingtalk.com
      path: /
    xudp: true
  - cipher: chacha20-ietf-poly1305
    name: "\U0001F300其他5-SE | ⬇️ 11.6MB/s"
    password: ed6c8eea-8503-43e1-9563-0a355b0edcac
    port: 42015
    server: eepl1.dhh114514.christmas
    type: ss
    udp: true
  - name: "\U0001F1FA\U0001F1F8美国12 | ⬇️ 11.8MB/s"
    network: ws
    port: 8880
    server: **************
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
        User-Agent: >-
          Mozilla/5.0 (Linux; Android 8.0.0; ANE-LX3 Build/HUAWEIANE-LX3)
          AppleWebKit/537.36 (KHTML, like Gecko) Chrome/69.0.3497.100 Mobile
          Safari/537.36
      path: "/Telegram\U0001F1E8\U0001F1F3 @pgkj666 /?ed=2560"
    xudp: true
  - alpn:
      - ''
    alterId: '0'
    cipher: auto
    name: "\U0001F300其他6-未识别 | ⬇️ 9.4MB/s"
    network: ws
    port: 33443
    server: tls.12.node-for-bigairport.win
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: b71f9e84-86c9-49c4-b5f4-b33b35ee7410
    ws-opts:
      headers:
        Host: tls.12.node-for-bigairport.win
      path: /
    xudp: true
  - client-fingerprint: chrome
    name: "\U0001F1F8\U0001F1EC新加坡12 | ⬇️ 11.1MB/s"
    password: RlzoEILU
    port: 17166
    server: **************
    skip-cert-verify: true
    sni: cdn.egvra.cn
    type: trojan
    udp: true
port: 7890
socks-port: 7891
redir-port: 7892
mixed-port: 7893
tproxy-port: 7894
ipv6: false
allow-lan: true
unified-delay: true
tcp-concurrent: true
geodata-mode: false
geodata-loader: standard
geo-auto-update: true
geo-update-interval: 48
geox-url:
  geoip: 'https://testingcf.jsdelivr.net/gh/MetaCubeX/meta-rules-dat@release/geoip.dat'
  geosite: >-
    https://testingcf.jsdelivr.net/gh/MetaCubeX/meta-rules-dat@release/geosite.dat
  mmdb: >-
    https://testingcf.jsdelivr.net/gh/MetaCubeX/meta-rules-dat@release/country.mmdb
  asn: >-
    https://github.com/xishang0128/geoip/releases/download/latest/GeoLite2-ASN.mmdb
profile:
  store-selected: true
  store-fake-ip: true
sniffer:
  enable: true
  sniff:
    HTTP:
      ports:
        - 80
        - 8080-8880
      override-destination: true
    TLS:
      ports:
        - 443
        - 8443
    QUIC:
      ports:
        - 443
        - 8443
  force-domain:
    - +.v2ex.com
  skip-domain:
    - Mijia Cloud
    - dlg.io.mi.com
    - +.push.apple.com
    - +.apple.com
dns:
  enable: true
  listen: '0.0.0.0:1053'
  ipv6: false
  respect-rules: true
  enhanced-mode: fake-ip
  fake-ip-range: ********/8
  fake-ip-filter-mode: blacklist
  fake-ip-filter:
    - +.lan
    - +.local
    - 'geosite:private'
    - 'geosite:cn'
  default-nameserver:
    - *********
    - ************
  proxy-server-nameserver:
    - *********
    - ************
  nameserver:
    - *********
    - ************
  nameserver-policy:
    'rule-set:private_domain,cn_domain':
      - *********
      - ************
    'rule-set:geolocation-!cn':
      - 'https://dns.cloudflare.com/dns-query'
      - 'https://dns.google/dns-query'
pr:
  type: select
  proxies:
    - "\U0001F680 节点选择"
    - "\U0001F1ED\U0001F1F0 香港负载均衡"
    - "\U0001F1EF\U0001F1F5 日本负载均衡"
    - "\U0001F1F0\U0001F1F7 韩国负载均衡"
    - "\U0001F1F8\U0001F1EC 新加坡负载均衡"
    - "\U0001F1FA\U0001F1F8 美国负载均衡"
    - "\U0001F1ED\U0001F1F0 香港自动"
    - "\U0001F1EF\U0001F1F5 日本自动"
    - "\U0001F1F0\U0001F1F7 韩国自动"
    - "\U0001F1F8\U0001F1EC 新加坡自动"
    - "\U0001F1FA\U0001F1F8 美国自动"
    - ♻️ 自动选择
    - "\U0001F1ED\U0001F1F0 香港节点"
    - "\U0001F1EF\U0001F1F5 日本节点"
    - "\U0001F1F0\U0001F1F7 韩国节点"
    - "\U0001F1F8\U0001F1EC 新加坡节点"
    - "\U0001F1FA\U0001F1F8 美国节点"
    - "\U0001F310 全部节点"
proxy-groups:
  - name: "\U0001F310 全部节点"
    type: select
    include-all: true
  - name: "\U0001F680 节点选择"
    type: select
    proxies:
      - "\U0001F1ED\U0001F1F0 香港负载均衡"
      - "\U0001F1EF\U0001F1F5 日本负载均衡"
      - "\U0001F1F0\U0001F1F7 韩国负载均衡"
      - "\U0001F1F8\U0001F1EC 新加坡负载均衡"
      - "\U0001F1FA\U0001F1F8 美国负载均衡"
      - "\U0001F1ED\U0001F1F0 香港自动"
      - "\U0001F1EF\U0001F1F5 日本自动"
      - "\U0001F1F0\U0001F1F7 韩国自动"
      - "\U0001F1F8\U0001F1EC 新加坡自动"
      - "\U0001F1FA\U0001F1F8 美国自动"
      - ♻️ 自动选择
      - "\U0001F1ED\U0001F1F0 香港节点"
      - "\U0001F1EF\U0001F1F5 日本节点"
      - "\U0001F1F0\U0001F1F7 韩国节点"
      - "\U0001F1F8\U0001F1EC 新加坡节点"
      - "\U0001F1FA\U0001F1F8 美国节点"
      - "\U0001F310 全部节点"
  - name: "\U0001F4AC ChatGPT"
    type: select
    proxies:
      - "\U0001F680 节点选择"
      - "\U0001F1ED\U0001F1F0 香港负载均衡"
      - "\U0001F1EF\U0001F1F5 日本负载均衡"
      - "\U0001F1F0\U0001F1F7 韩国负载均衡"
      - "\U0001F1F8\U0001F1EC 新加坡负载均衡"
      - "\U0001F1FA\U0001F1F8 美国负载均衡"
      - "\U0001F1ED\U0001F1F0 香港自动"
      - "\U0001F1EF\U0001F1F5 日本自动"
      - "\U0001F1F0\U0001F1F7 韩国自动"
      - "\U0001F1F8\U0001F1EC 新加坡自动"
      - "\U0001F1FA\U0001F1F8 美国自动"
      - ♻️ 自动选择
      - "\U0001F1ED\U0001F1F0 香港节点"
      - "\U0001F1EF\U0001F1F5 日本节点"
      - "\U0001F1F0\U0001F1F7 韩国节点"
      - "\U0001F1F8\U0001F1EC 新加坡节点"
      - "\U0001F1FA\U0001F1F8 美国节点"
      - "\U0001F310 全部节点"
  - name: "\U0001F4FA YouTube"
    type: select
    proxies:
      - "\U0001F680 节点选择"
      - "\U0001F1ED\U0001F1F0 香港负载均衡"
      - "\U0001F1EF\U0001F1F5 日本负载均衡"
      - "\U0001F1F0\U0001F1F7 韩国负载均衡"
      - "\U0001F1F8\U0001F1EC 新加坡负载均衡"
      - "\U0001F1FA\U0001F1F8 美国负载均衡"
      - "\U0001F1ED\U0001F1F0 香港自动"
      - "\U0001F1EF\U0001F1F5 日本自动"
      - "\U0001F1F0\U0001F1F7 韩国自动"
      - "\U0001F1F8\U0001F1EC 新加坡自动"
      - "\U0001F1FA\U0001F1F8 美国自动"
      - ♻️ 自动选择
      - "\U0001F1ED\U0001F1F0 香港节点"
      - "\U0001F1EF\U0001F1F5 日本节点"
      - "\U0001F1F0\U0001F1F7 韩国节点"
      - "\U0001F1F8\U0001F1EC 新加坡节点"
      - "\U0001F1FA\U0001F1F8 美国节点"
      - "\U0001F310 全部节点"
  - name: "\U0001F3B5 TikTok"
    type: select
    proxies:
      - "\U0001F680 节点选择"
      - "\U0001F1ED\U0001F1F0 香港负载均衡"
      - "\U0001F1EF\U0001F1F5 日本负载均衡"
      - "\U0001F1F0\U0001F1F7 韩国负载均衡"
      - "\U0001F1F8\U0001F1EC 新加坡负载均衡"
      - "\U0001F1FA\U0001F1F8 美国负载均衡"
      - "\U0001F1ED\U0001F1F0 香港自动"
      - "\U0001F1EF\U0001F1F5 日本自动"
      - "\U0001F1F0\U0001F1F7 韩国自动"
      - "\U0001F1F8\U0001F1EC 新加坡自动"
      - "\U0001F1FA\U0001F1F8 美国自动"
      - ♻️ 自动选择
      - "\U0001F1ED\U0001F1F0 香港节点"
      - "\U0001F1EF\U0001F1F5 日本节点"
      - "\U0001F1F0\U0001F1F7 韩国节点"
      - "\U0001F1F8\U0001F1EC 新加坡节点"
      - "\U0001F1FA\U0001F1F8 美国节点"
      - "\U0001F310 全部节点"
  - name: "\U0001F3AC NETFLIX"
    type: select
    proxies:
      - "\U0001F680 节点选择"
      - "\U0001F1ED\U0001F1F0 香港负载均衡"
      - "\U0001F1EF\U0001F1F5 日本负载均衡"
      - "\U0001F1F0\U0001F1F7 韩国负载均衡"
      - "\U0001F1F8\U0001F1EC 新加坡负载均衡"
      - "\U0001F1FA\U0001F1F8 美国负载均衡"
      - "\U0001F1ED\U0001F1F0 香港自动"
      - "\U0001F1EF\U0001F1F5 日本自动"
      - "\U0001F1F0\U0001F1F7 韩国自动"
      - "\U0001F1F8\U0001F1EC 新加坡自动"
      - "\U0001F1FA\U0001F1F8 美国自动"
      - ♻️ 自动选择
      - "\U0001F1ED\U0001F1F0 香港节点"
      - "\U0001F1EF\U0001F1F5 日本节点"
      - "\U0001F1F0\U0001F1F7 韩国节点"
      - "\U0001F1F8\U0001F1EC 新加坡节点"
      - "\U0001F1FA\U0001F1F8 美国节点"
      - "\U0001F310 全部节点"
  - name: "\U0001F1ED\U0001F1F0 香港节点"
    type: select
    include-all: true
    filter: (?i)港|hk|hongkong|hong kong
  - name: "\U0001F1EF\U0001F1F5 日本节点"
    type: select
    include-all: true
    filter: (?i)日|jp|japan
  - name: "\U0001F1F0\U0001F1F7 韩国节点"
    type: select
    include-all: true
    filter: (?i)韩|kr|korea
  - name: "\U0001F1F8\U0001F1EC 新加坡节点"
    type: select
    include-all: true
    filter: (?i)新|狮|sg|singapore|新加坡
  - name: "\U0001F1FA\U0001F1F8 美国节点"
    type: select
    include-all: true
    filter: (?i)美|us|unitedstates|united states
  - name: "\U0001F1ED\U0001F1F0 香港自动"
    type: url-test
    include-all: true
    tolerance: 20
    interval: 300
    filter: (?=.*(港|HK|(?i)Hong))^((?!(台|日|韩|新|深|美)).)*$
  - name: "\U0001F1EF\U0001F1F5 日本自动"
    type: url-test
    include-all: true
    tolerance: 20
    interval: 300
    filter: (?=.*(日|JP|(?i)Japan))^((?!(港|台|韩|新|美)).)*$
  - name: "\U0001F1F0\U0001F1F7 韩国自动"
    type: url-test
    include-all: true
    tolerance: 20
    interval: 300
    filter: (?=.*(韩|KR|(?i)Korea))^((?!(港|台|日|新|美)).)*$
  - name: "\U0001F1F8\U0001F1EC 新加坡自动"
    type: url-test
    include-all: true
    tolerance: 20
    interval: 300
    filter: (?=.*(新|狮|SG|(?i)Singapore))^((?!(港|台|日|韩|美)).)*$
  - name: "\U0001F1FA\U0001F1F8 美国自动"
    type: url-test
    include-all: true
    tolerance: 20
    interval: 300
    filter: (?=.*(美|US|(?i)States|America))^((?!(港|台|日|韩|新)).)*$
  - name: ♻️ 自动选择
    type: url-test
    include-all: true
    tolerance: 20
    interval: 300
    filter: ^((?!(直连)).)*$
  - name: "\U0001F1ED\U0001F1F0 香港负载均衡"
    type: load-balance
    include-all: true
    interval: 60
    lazy: true
    url: 'http://www.google.com/blank.html'
    disable-udp: false
    strategy: round-robin
    timeout: 2000
    max-failed-times: 3
    filter: (?=.*(港|HK|(?i)Hong))^((?!(台|日|韩|新|深|美)).)*$
  - name: "\U0001F1EF\U0001F1F5 日本负载均衡"
    type: load-balance
    include-all: true
    interval: 60
    lazy: true
    url: 'http://www.google.com/blank.html'
    disable-udp: false
    strategy: round-robin
    timeout: 2000
    max-failed-times: 3
    filter: (?=.*(日|JP|(?i)Japan))^((?!(港|台|韩|新|美)).)*$
  - name: "\U0001F1F0\U0001F1F7 韩国负载均衡"
    type: load-balance
    include-all: true
    interval: 60
    lazy: true
    url: 'http://www.google.com/blank.html'
    disable-udp: false
    strategy: round-robin
    timeout: 2000
    max-failed-times: 3
    filter: (?=.*(韩|KR|(?i)Korea))^((?!(港|台|日|新|美)).)*$
  - name: "\U0001F1F8\U0001F1EC 新加坡负载均衡"
    type: load-balance
    include-all: true
    interval: 60
    lazy: true
    url: 'http://www.google.com/blank.html'
    disable-udp: false
    strategy: round-robin
    timeout: 2000
    max-failed-times: 3
    filter: (?=.*(新|狮|SG|(?i)Singapore))^((?!(港|台|日|韩|美)).)*$
  - name: "\U0001F1FA\U0001F1F8 美国负载均衡"
    type: load-balance
    include-all: true
    interval: 60
    lazy: true
    url: 'http://www.google.com/blank.html'
    disable-udp: false
    strategy: round-robin
    timeout: 2000
    max-failed-times: 3
    filter: (?=.*(美|US|(?i)States|America))^((?!(港|台|日|韩|新)).)*$
  - name: "\U0001F3AF 全球直连"
    type: select
    proxies:
      - DIRECT
      - "\U0001F680 节点选择"
  - name: "\U0001F420 漏网之鱼"
    type: select
    proxies:
      - "\U0001F680 节点选择"
      - "\U0001F1ED\U0001F1F0 香港负载均衡"
      - "\U0001F1EF\U0001F1F5 日本负载均衡"
      - "\U0001F1F0\U0001F1F7 韩国负载均衡"
      - "\U0001F1F8\U0001F1EC 新加坡负载均衡"
      - "\U0001F1FA\U0001F1F8 美国负载均衡"
      - "\U0001F1ED\U0001F1F0 香港自动"
      - "\U0001F1EF\U0001F1F5 日本自动"
      - "\U0001F1F0\U0001F1F7 韩国自动"
      - "\U0001F1F8\U0001F1EC 新加坡自动"
      - "\U0001F1FA\U0001F1F8 美国自动"
      - ♻️ 自动选择
      - "\U0001F1ED\U0001F1F0 香港节点"
      - "\U0001F1EF\U0001F1F5 日本节点"
      - "\U0001F1F0\U0001F1F7 韩国节点"
      - "\U0001F1F8\U0001F1EC 新加坡节点"
      - "\U0001F1FA\U0001F1F8 美国节点"
      - "\U0001F310 全部节点"
rules:
  - 'RULE-SET,BanAD,REJECT'
  - 'RULE-SET,BanProgramAD,REJECT'
  - 'RULE-SET,adobe,REJECT'
  - "RULE-SET,youtube_domain,\U0001F4FA YouTube"
  - 'RULE-SET,tencent,DIRECT'
  - 'RULE-SET,private_domain,DIRECT'
  - 'RULE-SET,TencentVideo,DIRECT'
  - "RULE-SET,apple_domain,\U0001F3AF 全球直连"
  - "RULE-SET,ai,\U0001F4AC ChatGPT"
  - "RULE-SET,Spotify,\U0001F680 节点选择"
  - "RULE-SET,github_domain,\U0001F680 节点选择"
  - "RULE-SET,google_domain,\U0001F680 节点选择"
  - "RULE-SET,onedrive_domain,\U0001F3AF 全球直连"
  - "RULE-SET,microsoft_domain,\U0001F3AF 全球直连"
  - "RULE-SET,tiktok_domain,\U0001F3B5 TikTok"
  - "RULE-SET,speedtest_domain,\U0001F680 节点选择"
  - "RULE-SET,telegram_domain,\U0001F680 节点选择"
  - "RULE-SET,netflix_domain,\U0001F3AC NETFLIX"
  - "RULE-SET,Netflix,\U0001F3AC NETFLIX"
  - "RULE-SET,paypal_domain,\U0001F680 节点选择"
  - "RULE-SET,geolocation-!cn,\U0001F680 节点选择"
  - "RULE-SET,cn_domain,\U0001F3AF 全球直连"
  - "RULE-SET,google_ip,\U0001F680 节点选择,no-resolve"
  - "RULE-SET,netflix_ip,\U0001F3AC NETFLIX,no-resolve"
  - "RULE-SET,telegram_ip,\U0001F680 节点选择,no-resolve"
  - "RULE-SET,cn_ip,\U0001F3AF 全球直连"
  - "RULE-SET,proxylite,\U0001F680 节点选择"
  - "MATCH,\U0001F420 漏网之鱼"
rule-anchor:
  ip:
    type: http
    interval: 86400
    behavior: ipcidr
    format: mrs
  domain:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
  qcy:
    type: http
    interval: 86400
    behavior: domain
    format: text
  class:
    type: http
    interval: 86400
    behavior: classical
    format: text
rule-providers:
  BanAD:
    type: http
    interval: 86400
    behavior: classical
    format: text
    url: 'https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/Clash/BanAD.list'
  tencent:
    type: http
    interval: 86400
    behavior: classical
    format: text
    url: >-
      https://cdn.jsdelivr.net/gh/blackmatrix7/ios_rule_script@master/rule/Shadowrocket/Tencent/Tencent.list
  TencentVideo:
    type: http
    interval: 86400
    behavior: classical
    format: text
    url: >-
      https://cdn.jsdelivr.net/gh/blackmatrix7/ios_rule_script@master/rule/Shadowrocket/TencentVideo/TencentVideo.list
  BanProgramAD:
    type: http
    interval: 86400
    behavior: classical
    format: text
    url: >-
      https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/Clash/BanProgramAD.list
  adobe:
    type: http
    interval: 86400
    behavior: classical
    format: text
    url: 'https://raw.githubusercontent.com/cmliu/ACL4SSR/main/Clash/adobe.list'
  private_domain:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/private.mrs
  ai:
    type: http
    interval: 86400
    behavior: classical
    format: text
    url: >-
      https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/a98edee7cd64d64cb58ce26d63d2dbd2475575ad/rule/Clash/OpenAI/OpenAI.list
  Spotify:
    type: http
    interval: 86400
    behavior: classical
    format: text
    url: >-
      https://cdn.jsdelivr.net/gh/blackmatrix7/ios_rule_script@release/rule/Shadowrocket/Spotify/Spotify.list
  Netflix:
    type: http
    interval: 86400
    behavior: classical
    format: text
    url: >-
      https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/a98edee7cd64d64cb58ce26d63d2dbd2475575ad/rule/Clash/Netflix/Netflix.list
  youtube_domain:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/youtube.mrs
  google_domain:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/google.mrs
  github_domain:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/github.mrs
  telegram_domain:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/telegram.mrs
  netflix_domain:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/netflix.mrs
  paypal_domain:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/paypal.mrs
  onedrive_domain:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/onedrive.mrs
  microsoft_domain:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/microsoft.mrs
  apple_domain:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/apple-cn.mrs
  speedtest_domain:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/ookla-speedtest.mrs
  tiktok_domain:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/tiktok.mrs
  gfw_domain:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/gfw.mrs
  geolocation-!cn:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/geolocation-!cn.mrs
  cn_domain:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/cn.mrs
  proxylite:
    type: http
    interval: 86400
    behavior: classical
    format: text
    url: >-
      https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/refs/heads/master/rule/Clash/ProxyLite/ProxyLite.list
  cn_ip:
    type: http
    interval: 86400
    behavior: ipcidr
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geoip/cn.mrs
  google_ip:
    type: http
    interval: 86400
    behavior: ipcidr
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geoip/google.mrs
  telegram_ip:
    type: http
    interval: 86400
    behavior: ipcidr
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geoip/telegram.mrs
  netflix_ip:
    type: http
    interval: 86400
    behavior: ipcidr
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geoip/netflix.mrs
