proxies:
  - name: "🇸🇬 新加坡 001"
    type: hysteria2
    server: **************
    port: 31667
    password: "hf96oOugMgvkOAlVykIA0EKHk"
    sni: "bing.com"
    udp: true

  - name: "🇸🇬 新加坡 002"
    type: ss
    server: *************
    port: 13038
    cipher: "aes-128-gcm"
    password: "f16163ec-3c35-4719-a19b-68c864cdc626"
    udp: true

  - name: "🇸🇬 新加坡 003"
    type: trojan
    server: cn2.cdn.xfltd-cdn.top
    port: 12021
    password: "7669a04d-a459-4c96-bc0e-fde51cb984f3"
    network: tcp
    sni: "cn2.cdn.xfltd-cdn.top"
    skip-cert-verify: true
    udp: true

  - name: "🇸🇬 新加坡 004"
    type: ss
    server: ti3hyra4.slashdevslashnetslashtun.net
    port: 16015
    cipher: "aes-256-gcm"
    password: "RFUKD9DMSTSXGAJ8"
    udp: true

  - name: "🇸🇬 新加坡 005"
    type: trojan
    server: cn2.cdn.xfltd-cdn.top
    port: 12024
    password: "7669a04d-a459-4c96-bc0e-fde51cb984f3"
    network: tcp
    sni: "cn2.cdn.xfltd-cdn.top"
    skip-cert-verify: true
    udp: true

  - name: "🇸🇬 新加坡 006"
    type: hysteria2
    server: starlink-sgp.2513142.xyz
    port: 443
    password: "V3bDL91w89Uh5n65tXawOrHt"
    sni: "www.cloudflare.com"
    udp: true

  - name: "🇯🇵 日本 001"
    type: hysteria2
    server: jp4.dexlos.com
    port: 7270
    password: "0A1DDFE6-A16A-43FA-A6E3-70EDE4EA61C3"
    sni: "jp4.dexlos.com"
    udp: true

  - name: "🇯🇵 日本 002"
    type: hysteria2
    server: jp5.dexlos.com
    port: 9517
    password: "0A1DDFE6-A16A-43FA-A6E3-70EDE4EA61C3"
    sni: "jp5.dexlos.com"
    udp: true

  - name: "🇯🇵 日本 003"
    type: trojan
    server: ***********
    port: 443
    password: "CXCu72eya8wFeRySSFpDz3CN6jBYac5OIl3q0gSl8xZOADY3EK94pxZanDA3RT"
    network: tcp
    sni: "broker.superpokemon.com"
    skip-cert-verify: true
    udp: true

  - name: "🇯🇵 日本 004"
    type: vless
    server: jp002.421421.xyz
    port: 20230
    uuid: "f08d6a6b-a0b3-410e-a0ba-eae71b521904"
    network: tcp
    tls: true
    servername: "www.nvidia.com"
    reality:
      enabled: true
      public-key: "yNOemxnmT-HQ2HiR9fdHSN2B8HpHsRyMIsir_iAlaHE"
      short-id: "5c3c9f11"
    udp: true

  - name: "🌐 其他 001"
    type: ssr
    server: 88gg.mt.mt5888.top
    port: 44005
    cipher: "dummy"
    password: "mantouyun888"
    protocol: "auth_chain_a"
    obfs: "plain"
    obfs-param: "cbcdc8198.microsoft.com"
    udp: true

  - name: "🌐 其他 002"
    type: ssr
    server: 9988.mt.mt5888.top
    port: 41115
    cipher: "dummy"
    password: "mantouyun888"
    protocol: "auth_chain_a"
    obfs: "plain"
    obfs-param: "cbcdc8198.microsoft.com"
    udp: true

  - name: "🌐 其他 003"
    type: ssr
    server: 88gg.mt.mt5888.top
    port: 44003
    cipher: "dummy"
    password: "mantouyun888"
    protocol: "auth_chain_a"
    obfs: "plain"
    obfs-param: "cbcdc8198.microsoft.com"
    udp: true

  - name: "🌐 其他 004"
    type: ssr
    server: 88gg.mt.mt5888.top
    port: 44006
    cipher: "dummy"
    password: "mantouyun888"
    protocol: "auth_chain_a"
    obfs: "plain"
    obfs-param: "cbcdc8198.microsoft.com"
    udp: true

  - name: "🌐 其他 005"
    type: ss
    server: *************
    port: 17007
    cipher: "aes-256-gcm"
    password: "c86d483c-431f-41df-bb6b-c1dcebfc7401"
    udp: true

  - name: "🌐 其他 006"
    type: ssr
    server: aieq.mt.mt5888.top
    port: 24001
    cipher: "dummy"
    password: "mantouyun888"
    protocol: "auth_chain_a"
    obfs: "plain"
    obfs-param: "cbcdc8198.microsoft.com"
    udp: true

  - name: "🌐 其他 007"
    type: ssr
    server: 9988.mt.mt5888.top
    port: 41114
    cipher: "dummy"
    password: "mantouyun888"
    protocol: "auth_chain_a"
    obfs: "plain"
    obfs-param: "cbcdc8198.microsoft.com"
    udp: true

  - name: "🌐 其他 008"
    type: ssr
    server: 9988.mt.mt5888.top
    port: 44005
    cipher: "dummy"
    password: "mantouyun888"
    protocol: "auth_chain_a"
    obfs: "plain"
    obfs-param: "cbcdc8198.microsoft.com"
    udp: true

  - name: "🇭🇰 香港 001"
    type: vmess
    server: 688f6e4a-swxgg0-syb15h-8caj.hkt.east.wctype.com
    port: 459
    uuid: "a67a6c18-3f6d-11ef-ab9c-f23c9313b177"
    alterId: 2
    cipher: "auto"
    network: ws
    ws-opts:
      path: "/"
      headers:
        Host: "a605477178.m.ctrip.com"
    udp: true

  - name: "🇭🇰 香港 002"
    type: vmess
    server: 9d6c2f7e-swvls0-syb15h-8caj.hkt.east.wctype.com
    port: 459
    uuid: "a67a6c18-3f6d-11ef-ab9c-f23c9313b177"
    alterId: 2
    cipher: "auto"
    network: ws
    ws-opts:
      path: "/"
      headers:
        Host: "a605477178.m.ctrip.com"
    udp: true

  - name: "🇭🇰 香港 003"
    type: ss
    server: *************
    port: 49759
    cipher: "aes-128-gcm"
    password: "sadujij!@diQojd1254"
    udp: true

  - name: "🇭🇰 香港 004"
    type: trojan
    server: cn1.cdn.xfltd-cdn.top
    port: 12003
    password: "7669a04d-a459-4c96-bc0e-fde51cb984f3"
    network: tcp
    sni: "cdn.alibaba.com"
    skip-cert-verify: true
    udp: true

  - name: "🇭🇰 香港 005"
    type: trojan
    server: cn1.cdn.xfltd-cdn.top
    port: 12006
    password: "7669a04d-a459-4c96-bc0e-fde51cb984f3"
    network: tcp
    sni: "cdn.alibaba.com"
    skip-cert-verify: true
    udp: true

  - name: "🇭🇰 香港 006"
    type: trojan
    server: cn1.cdn.xfltd-cdn.top
    port: 12005
    password: "7669a04d-a459-4c96-bc0e-fde51cb984f3"
    network: tcp
    sni: "cdn.alibaba.com"
    skip-cert-verify: true
    udp: true

  - name: "🇭🇰 香港 007"
    type: vmess
    server: f77b7ed3-swtr40-syb15h-8caj.hkt.east.wctype.com
    port: 459
    uuid: "a67a6c18-3f6d-11ef-ab9c-f23c9313b177"
    alterId: 2
    cipher: "auto"
    network: ws
    ws-opts:
      path: "/"
      headers:
        Host: "a605477178.m.ctrip.com"
    udp: true

  - name: "🇭🇰 香港 008"
    type: trojan
    server: *************
    port: 46861
    password: "RlzoEILU"
    network: tcp
    sni: "*************"
    skip-cert-verify: true
    udp: true

  - name: "🇭🇰 香港 009"
    type: trojan
    server: cn1.cdn.xfltd-cdn.top
    port: 12007
    password: "7669a04d-a459-4c96-bc0e-fde51cb984f3"
    network: tcp
    sni: "cdn.alibaba.com"
    skip-cert-verify: true
    udp: true

  - name: "🇭🇰 香港 010"
    type: trojan
    server: **************
    port: 50723
    password: "RlzoEILU"
    network: tcp
    sni: "**************"
    skip-cert-verify: true
    udp: true

  - name: "🇭🇰 香港 011"
    type: trojan
    server: *************
    port: 3754
    password: "RlzoEILU"
    network: tcp
    sni: "*************"
    skip-cert-verify: true
    udp: true

  - name: "🇭🇰 香港 012"
    type: vmess
    server: e512a77b-swzb40-syfewh-64gc.hkt.east.wctype.com
    port: 459
    uuid: "d720a490-d56b-11ef-a1cb-f23c9313b177"
    alterId: 2
    cipher: "auto"
    network: ws
    ws-opts:
      path: "/"
      headers:
        Host: "a605477178.m.ctrip.com"
    udp: true

  - name: "🇭🇰 香港 013"
    type: vmess
    server: c0d86f90-swzb40-syb15h-8caj.hkt.east.wctype.com
    port: 459
    uuid: "a67a6c18-3f6d-11ef-ab9c-f23c9313b177"
    alterId: 2
    cipher: "auto"
    network: ws
    ws-opts:
      path: "/"
      headers:
        Host: "a605477178.m.ctrip.com"
    udp: true

  - name: "🇰🇷 韩国 001"
    type: trojan
    server: *************
    port: 47655
    password: "RlzoEILU"
    network: tcp
    sni: "*************"
    skip-cert-verify: true
    udp: true

  - name: "🇰🇷 韩国 002"
    type: trojan
    server: *************
    port: 28910
    password: "RlzoEILU"
    network: tcp
    sni: "cdn.egvra.cn"
    skip-cert-verify: true
    udp: true

  - name: "🇺🇸 美国 001"
    type: vless
    server: **************
    port: 8880
    uuid: "53fa8faf-ba4b-4322-9c69-a3e5b1555049"
    network: ws
    udp: true

  - name: "🇺🇸 美国 002"
    type: vless
    server: ************
    port: 8880
    uuid: "53fa8faf-ba4b-4322-9c69-a3e5b1555049"
    network: ws
    udp: true

  - name: "🇺🇸 美国 003"
    type: ss
    server: *************
    port: 20010
    cipher: "aes-256-gcm"
    password: "c86d483c-431f-41df-bb6b-c1dcebfc7401"
    udp: true

  - name: "🇨🇦 加拿大 001"
    type: vmess
    server: 06809388-302a-29e7-ec15-006f5353d530.castlepeakhospital.moe
    port: 80
    uuid: "14a8a0db-e922-453a-af8d-843af8734b3b"
    alterId: 0
    cipher: "auto"
    network: ws
    ws-opts:
      path: "/"
      headers:
        Host: "tms.dingtalk.com"
    udp: true

