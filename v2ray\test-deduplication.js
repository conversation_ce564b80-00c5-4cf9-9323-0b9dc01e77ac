/**
 * 测试去重合并功能
 */

import { ProxyConverter } from './src/index.js';
import fs from 'fs';
import path from 'path';

async function testDeduplication() {
  console.log('🧪 测试去重合并功能');
  console.log('==================================================');

  const converter = new ProxyConverter();
  const testsDir = './tests';

  // 获取所有base64文件
  const files = fs.readdirSync(testsDir)
    .filter(file => file.endsWith('.txt'))
    .sort();

  console.log(`📁 找到 ${files.length} 个文件:`);
  files.forEach((file, index) => {
    console.log(`  ${index + 1}. ${file}`);
  });

  let allNodes = [];
  let totalOriginalNodes = 0;

  console.log('\n📊 逐个文件分析:');
  console.log('==================================================');

  // 逐个处理文件
  for (let i = 0; i < files.length; i++) {
    const file = files[i];
    const filePath = path.join(testsDir, file);

    try {
      const content = fs.readFileSync(filePath, 'utf8').trim();

      if (!content) {
        console.log(`${i + 1}. ${file}: 空文件`);
        continue;
      }

      // 解析节点
      const nodes = converter.parse(content, 'base64');
      const nodeCount = nodes ? nodes.length : 0;
      totalOriginalNodes += nodeCount;

      console.log(`${i + 1}. ${file}: ${nodeCount} 个节点`);

      if (nodes && nodes.length > 0) {
        allNodes = allNodes.concat(nodes);

        // 显示前3个节点的基本信息
        const sampleNodes = nodes.slice(0, 3);
        sampleNodes.forEach((node, idx) => {
          console.log(`   ${idx + 1}. ${node.name || 'Unknown'} (${node.type}) - ${node.server}:${node.port}`);
        });
        if (nodes.length > 3) {
          console.log(`   ... 还有 ${nodes.length - 3} 个节点`);
        }
      }

    } catch (error) {
      console.log(`${i + 1}. ${file}: 解析失败 - ${error.message}`);
    }

    console.log('');
  }

  console.log('📈 合并前统计:');
  console.log(`  总文件数: ${files.length}`);
  console.log(`  总节点数: ${totalOriginalNodes}`);
  console.log(`  合并后节点数: ${allNodes.length}`);

  // 执行去重
  console.log('\n🔄 执行去重...');
  const uniqueNodes = converter.deduplicate(allNodes, {
    strategy: 'server-port-type',
    smart: true
  });

  console.log('📊 去重后统计:');
  console.log(`  去重前: ${allNodes.length} 个节点`);
  console.log(`  去重后: ${uniqueNodes.length} 个节点`);
  console.log(`  移除重复: ${allNodes.length - uniqueNodes.length} 个节点`);

  // 分析重复节点
  console.log('\n🔍 重复节点分析:');
  const duplicateMap = new Map();

  allNodes.forEach(node => {
    const key = `${node.server}:${node.port}:${node.type}`;
    if (duplicateMap.has(key)) {
      duplicateMap.set(key, duplicateMap.get(key) + 1);
    } else {
      duplicateMap.set(key, 1);
    }
  });

  const duplicates = Array.from(duplicateMap.entries())
    .filter(([key, count]) => count > 1)
    .sort((a, b) => b[1] - a[1]);

  console.log(`  发现 ${duplicates.length} 组重复节点:`);
  duplicates.slice(0, 10).forEach(([key, count]) => {
    console.log(`    ${key}: ${count} 次`);
  });

  if (duplicates.length > 10) {
    console.log(`    ... 还有 ${duplicates.length - 10} 组重复节点`);
  }

  // 协议分布统计
  console.log('\n📋 协议分布:');
  const protocolStats = {};
  uniqueNodes.forEach(node => {
    protocolStats[node.type] = (protocolStats[node.type] || 0) + 1;
  });

  Object.entries(protocolStats)
    .sort((a, b) => b[1] - a[1])
    .forEach(([protocol, count]) => {
      console.log(`  ${protocol}: ${count} 个`);
    });

  // 地区分布统计
  console.log('\n🌍 地区分布 (前10):');
  const regionStats = {};
  uniqueNodes.forEach(node => {
    // 简单的地区识别
    let region = 'OTHER';
    if (node.name) {
      if (node.name.includes('香港') || node.name.includes('HK')) region = 'HK';
      else if (node.name.includes('美国') || node.name.includes('US')) region = 'US';
      else if (node.name.includes('日本') || node.name.includes('JP')) region = 'JP';
      else if (node.name.includes('新加坡') || node.name.includes('SG')) region = 'SG';
      else if (node.name.includes('台湾') || node.name.includes('TW')) region = 'TW';
      else if (node.name.includes('韩国') || node.name.includes('KR')) region = 'KR';
    }
    regionStats[region] = (regionStats[region] || 0) + 1;
  });

  Object.entries(regionStats)
    .sort((a, b) => b[1] - a[1])
    .slice(0, 10)
    .forEach(([region, count]) => {
      console.log(`  ${region}: ${count} 个`);
    });

  // 尝试不同的去重策略
  console.log('\n🔬 测试不同去重策略:');
  console.log('==================================================');

  const strategies = [
    { name: 'server-port', strategy: 'server-port' },
    { name: 'server-port-type', strategy: 'server-port-type' },
    { name: 'full', strategy: 'full' },
    { name: 'v2rayn-like', strategy: 'v2rayn-like' }
  ];

  for (const { name, strategy } of strategies) {
    const testNodes = converter.deduplicate(allNodes, { strategy });
    console.log(`${name}: ${testNodes.length} 个节点 (移除 ${allNodes.length - testNodes.length} 个)`);
  }

  // 检查解析失败的文件
  console.log('\n🔍 检查解析失败情况:');
  console.log('==================================================');

  let parseFailures = 0;
  let emptyFiles = 0;

  for (let i = 0; i < files.length; i++) {
    const file = files[i];
    const filePath = path.join(testsDir, file);

    try {
      const content = fs.readFileSync(filePath, 'utf8').trim();

      if (!content) {
        emptyFiles++;
        console.log(`空文件: ${file}`);
        continue;
      }

      // 尝试手动解析base64
      try {
        const decoded = atob(content);
        const lines = decoded.split('\n').filter(line => line.trim());
        console.log(`${file}: 原始内容 ${lines.length} 行`);

        // 检查每行是否是有效的代理URL
        let validUrls = 0;
        for (const line of lines) {
          if (line.startsWith('ss://') || line.startsWith('vmess://') ||
              line.startsWith('trojan://') || line.startsWith('vless://') ||
              line.startsWith('hysteria2://') || line.startsWith('ssr://')) {
            validUrls++;
          }
        }
        console.log(`  有效代理URL: ${validUrls} 个`);

      } catch (e) {
        console.log(`${file}: Base64解码失败`);
      }

    } catch (error) {
      parseFailures++;
      console.log(`解析失败: ${file} - ${error.message}`);
    }
  }

  console.log(`\n解析失败文件: ${parseFailures} 个`);
  console.log(`空文件: ${emptyFiles} 个`);

  console.log('\n🎯 结论:');
  console.log(`  期望节点数: 509`);
  console.log(`  实际节点数: ${uniqueNodes.length}`);
  console.log(`  差异: ${509 - uniqueNodes.length}`);

  if (uniqueNodes.length !== 509) {
    console.log('\n❌ 节点数量不匹配，可能的原因:');
    console.log('  1. 某些文件解析失败');
    console.log('  2. 去重算法过于激进');
    console.log('  3. 文件格式问题');
    console.log('  4. 期望数量计算错误');

    // 建议使用不同的去重策略
    const serverPortNodes = converter.deduplicate(allNodes, { strategy: 'server-port' });
    console.log(`\n💡 建议: 使用 'server-port' 策略可得到 ${serverPortNodes.length} 个节点`);
  } else {
    console.log('\n✅ 节点数量匹配！');
  }

  return {
    totalFiles: files.length,
    totalOriginalNodes,
    mergedNodes: allNodes.length,
    uniqueNodes: uniqueNodes.length,
    duplicatesRemoved: allNodes.length - uniqueNodes.length
  };
}

// 运行测试
testDeduplication().catch(console.error);
