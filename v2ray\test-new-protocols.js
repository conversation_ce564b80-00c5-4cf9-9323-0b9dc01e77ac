#!/usr/bin/env node

/**
 * 测试新协议支持
 */

import { ProxyConverter } from './src/index.js';
import { OutputFormats } from './src/types.js';

// 测试新协议的URL
const testUrls = [
  // SSR
  'ssr://MTkyLjE2OC4xLjE6ODM4ODphdXRoX2FlczEyOF9tZDU6YWVzLTI1Ni1jZmI6dGxzMS4yX3RpY2tldF9hdXRoOllXVnpMVEkxTmkxblkyMD0vP29iZnNwYXJhbT1ZV1Z6TFRJMU5pMW5ZMjA9JnByb3RvcGFyYW09JnJlbWFya3M9VTFOUzZJZXE1TGl1Jmdyb3VwPQ==',
  
  // Hysteria2
  'hysteria2://<EMAIL>:443?obfs=salamander&obfs-password=obfspass&sni=hy2.example.com&up=100&down=200#Hysteria2节点',
  
  // Snell
  'snell://<EMAIL>:6160?version=4&obfs=http&obfs-host=www.bing.com#Snell节点'
];

async function testNewProtocols() {
  console.log('🧪 测试新协议支持');
  console.log('='.repeat(50));
  
  const converter = new ProxyConverter();

  try {
    // 测试解析
    console.log('📝 测试解析新协议URL:');
    const nodes = converter.parse(testUrls);
    console.log(`✅ 成功解析 ${nodes.length} 个节点\n`);
    
    nodes.forEach((node, index) => {
      console.log(`${index + 1}. ${node.name}`);
      console.log(`   协议: ${node.type}`);
      console.log(`   服务器: ${node.server}:${node.port}`);
      console.log(`   详细信息:`, JSON.stringify(node, null, 2));
      console.log();
    });

    if (nodes.length === 0) {
      console.log('⚠️ 没有解析到任何节点');
      return;
    }

    // 测试重命名
    console.log('🏷️ 测试节点重命名:');
    const renamedNodes = converter.rename(nodes);
    renamedNodes.forEach((node, index) => {
      console.log(`${index + 1}. ${node.name} (原名: ${node.originalName})`);
    });
    console.log();

    // 测试转换为Clash格式
    console.log('⚙️ 测试转换为Clash格式:');
    const clashConfig = converter.convert(renamedNodes, OutputFormats.CLASH);
    console.log(`✅ 生成Clash配置，包含 ${clashConfig.proxies?.length || 0} 个代理`);
    
    if (clashConfig.proxies) {
      clashConfig.proxies.forEach((proxy, index) => {
        console.log(`${index + 1}. ${proxy.name} (${proxy.type})`);
        console.log(`   详细配置:`, JSON.stringify(proxy, null, 2));
      });
    }
    console.log();

    // 测试生成URL
    console.log('🔗 测试生成代理URL:');
    const urlList = converter.convert(renamedNodes, OutputFormats.URL);
    const urls = urlList.split('\n').filter(url => url.trim());
    console.log(`✅ 生成 ${urls.length} 个代理URL:`);
    urls.forEach((url, index) => {
      console.log(`${index + 1}. ${url.substring(0, 80)}...`);
    });
    console.log();

    // 测试统计信息
    console.log('📊 测试统计信息:');
    const stats = converter.getStats(renamedNodes);
    console.log('节点统计:');
    console.log(`  总数: ${stats.total}`);
    console.log(`  有效: ${stats.valid}`);
    console.log(`  无效: ${stats.invalid}`);
    console.log('协议分布:');
    Object.entries(stats.types).forEach(([type, count]) => {
      console.log(`  ${type}: ${count}`);
    });
    console.log('地区分布:');
    Object.entries(stats.regions).forEach(([region, count]) => {
      console.log(`  ${region}: ${count}`);
    });

    console.log('\n🎉 新协议测试完成！');

  } catch (error) {
    console.error('❌ 测试失败:', error);
    console.error('错误详情:', error.stack);
  }
}

// 运行测试
testNewProtocols();
