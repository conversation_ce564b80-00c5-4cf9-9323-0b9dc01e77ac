---
date: 2024-12-15
---

### 阶段性更新汇总

此处的更新可能不完整不及时

Telegram 频道 https://t.me/cool_scripts 内有每次更新的详细说明

#### 核心功能增强

1. 协议支持

- 新增支持 Egern 输出

- 支持 VLESS mKcp

- 支持 Juicity, SSH 等新协议

- 支持 Trojan, VMess, VLESS httpupgrade

2. 解析优化

- 修复各类 URI 解析问题(SS, VMess, Trojan等)

- 改进域名解析功能,支持自定义 EDNS 和 DoH

- 优化无效节点处理逻辑

3. 代理与策略

- 新增全局代理/策略设置

- 支持文件和链接设置代理/策略

- 支持 direct 类型节点

#### 功能改进

1. 缓存与性能

- 默认缓存阈值调整为 1024KB

- 默认超时调整为 8000ms

- 优化乐观缓存逻辑

2. 订阅管理

- 支持订阅流量信息自定义

- 改进订阅刷新机制

- 支持定时处理订阅功能

3. 安全性

- 支持 JWT 和 token 管理

- 支持自定义 share token

- 支持 insecure 模式不验证证书

#### UI/UX 改进

- 新增图标仓库功能

- 支持标签管理和排序

- 改进分享功能,支持二维码

- 优化界面样式和交互

#### 其他

- 增加更多的兼容性支持

- 修复各类 bug

- 完善文档和说明

- 支持更多的配置选项

这些更新显著提升了软件的功能完整性、稳定性和用户体验。
