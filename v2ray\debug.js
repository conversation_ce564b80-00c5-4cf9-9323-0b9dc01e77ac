/**
 * 调试脚本
 */

// 首先导入兼容性修复
import './src/utils/index.js';

import { parseShadowsocksUrl } from './src/parsers/shadowsocks.js';
import { parseProxyUrl, parseProxyUrls } from './src/parsers/index.js';

// 测试单个SS URL
const testUrl = 'ss://<EMAIL>:8388#香港节点1';
const testUrls = [
  'ss://<EMAIL>:8388#香港节点1',
  'ss://<EMAIL>:8389#HK-Node-2'
];

console.log('=== 基础测试 ===');
console.log('测试URL:', testUrl);
console.log('atob 函数存在:', typeof atob !== 'undefined');
console.log('btoa 函数存在:', typeof btoa !== 'undefined');

// 测试 base64 解码
try {
  const testBase64 = 'YWVzLTI1Ni1nY206cGFzc3dvcmQ';
  const decoded = atob(testBase64);
  console.log('Base64 解码测试:', decoded);
} catch (error) {
  console.error('Base64 解码失败:', error);
}

console.log('\n=== 单个解析器测试 ===');
// 测试单个解析函数
try {
  const result = parseShadowsocksUrl(testUrl);
  console.log('单个解析器结果:', result);
} catch (error) {
  console.error('单个解析器失败:', error);
}

console.log('\n=== 统一入口测试 ===');
// 测试统一入口
try {
  const result = parseProxyUrl(testUrl);
  console.log('统一入口单个解析结果:', result);
} catch (error) {
  console.error('统一入口单个解析失败:', error);
}

// 测试多个URL解析
try {
  const results = parseProxyUrls(testUrls);
  console.log('多个URL解析结果:', results);
  console.log('解析到的节点数:', results.length);
} catch (error) {
  console.error('多个URL解析失败:', error);
}

console.log('\n=== 主入口测试 ===');
// 测试主入口
try {
  const { ProxyConverter } = await import('./src/index.js');
  const converter = new ProxyConverter();

  const nodes = converter.parse(testUrls);
  console.log('主入口解析结果:', nodes);
  console.log('主入口解析到的节点数:', nodes.length);

  if (nodes.length > 0) {
    console.log('第一个节点详情:', nodes[0]);
  }
} catch (error) {
  console.error('主入口测试失败:', error);
}
