<?xml version="1.0" encoding="UTF-8"?>
<svg width="129.907939px" height="130px" viewBox="0 0 129.907939 130" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>logo</title>
    <defs>
        <linearGradient x1="100%" y1="50%" x2="0%" y2="50%" id="linearGradient-1">
            <stop stop-color="#93EEFE" offset="0%"></stop>
            <stop stop-color="#A1FCC6" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="100%" y1="50%" x2="0%" y2="50%" id="linearGradient-2">
            <stop stop-color="#93EEFE" offset="0%"></stop>
            <stop stop-color="#A1FCC6" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="100%" y1="50%" x2="0%" y2="50%" id="linearGradient-3">
            <stop stop-color="#93EEFE" offset="0%"></stop>
            <stop stop-color="#A1FCC6" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="logo" transform="translate(-31.3706, -31.2416)">
            <polygon id="路径" transform="translate(96, 96) scale(-1, 1) rotate(-180) translate(-96, -96)" points="1.42108547e-14 96 1.42108547e-14 2.84217094e-14 96 2.84217094e-14 192 2.84217094e-14 192 96 192 192 96 192 1.42108547e-14 192"></polygon>
            <path d="M103.692088,67.0275809 C110.428883,46.6190563 90.812922,26.2105316 70.4043974,32.3529031 C65.252731,33.9380312 57.7233723,39.8822616 47.0237575,50.5818765 L31.3706172,66.4331579 L35.7297196,70.7922602 L40.0888219,75.1513626 L55.5438212,59.8945043 C68.2248463,47.2134793 71.9895255,44.6376461 77.3393329,44.6376461 C91.6054861,44.6376461 96.5590116,60.8852094 85.6612556,71.3866832 L79.7170252,77.1327728 L84.0761275,81.6900161 L88.4352299,86.2472594 L94.9738835,79.906747 C98.5404217,76.5383496 102.503242,70.5941192 103.692088,67.0275809 Z" id="路径" fill="url(#linearGradient-1)" fill-rule="nonzero" transform="translate(68.2067, 58.7446) scale(-1, 1) translate(-68.2067, -58.7446)"></path>
            <path d="M87.2101,108.994139 L75.7179211,97.3195274 L81.4640106,91.3752969 L87.2101,85.4310664 L82.8509977,81.071964 L78.4918953,76.5147207 L68.1885625,87.0161945 L57.6870886,97.3195274 L67.5941394,107.226578 C72.9439469,112.576386 77.8974723,117.133629 78.6900364,117.133629 C79.0863183,117.133629 81.9263396,114.420466 87.2101,108.994139 Z" id="路径" fill-opacity="0.96" fill="#FFFFFF" fill-rule="nonzero" transform="translate(72.4486, 96.8242) scale(-1, 1) rotate(-180) translate(-72.4486, -96.8242)"></path>
            <path d="M134.565804,108.994139 L123.073625,97.3195274 L128.819715,91.3752969 L134.565804,85.4310664 L130.206702,81.071964 L125.847599,76.5147207 L115.544266,87.0161945 L105.042792,97.3195274 L114.949843,107.226578 C120.299651,112.576386 125.253177,117.133629 126.045741,117.133629 C126.442022,117.133629 129.282043,114.420466 134.565804,108.994139 Z" id="路径" fill-opacity="0.96" fill="#FFFFFF" fill-rule="nonzero" transform="translate(119.8043, 96.8242) rotate(-180) translate(-119.8043, -96.8242)"></path>
            <path d="M50.7521531,145.984709 C43.8172175,138.059068 43.0246535,129.340864 48.7707429,123.594774 C54.9131144,117.452402 64.6220243,118.443107 71.5569598,125.576184 L77.3030492,131.520414 L81.8602926,127.161312 L86.417536,122.80221 L80.0770234,116.263556 C67.7922805,103.582532 51.7428582,102.789968 40.0525382,114.678428 C28.1640772,126.368748 28.7585003,141.625606 41.4395254,154.702913 L47.5818969,161.241567 L52.1391402,156.882465 L56.4982426,152.721504 L50.7521531,145.984709 Z" id="路径" fill="url(#linearGradient-2)" fill-rule="nonzero" transform="translate(58.9675, 133.7387) scale(-1, 1) rotate(-180) translate(-58.9675, -133.7387)"></path>
            <path d="M125.466293,70.9904013 C118.531358,63.0647605 117.738794,54.3465558 123.484884,48.6004664 C129.627255,42.458095 139.336165,43.4487999 146.2711,50.5818765 L152.01719,56.5261071 L156.574432,52.1670047 L161.131676,47.8079024 L154.791163,41.2692488 C142.50642,28.5882238 126.456998,27.7956597 114.766678,39.6841207 C102.878218,51.3744407 103.472641,66.6312988 116.153666,79.708606 L122.296037,86.2472594 L126.853281,81.8881571 L131.212383,77.7271958 L125.466293,70.9904013 Z" id="路径" fill="url(#linearGradient-3)" fill-rule="nonzero" transform="translate(133.6817, 58.7444) scale(-1, 1) translate(-133.6817, -58.7444)"></path>
            <path d="M159.927853,142.021433 C166.664647,121.612909 147.048687,101.204384 126.640162,107.346756 C121.488496,108.931884 113.959137,114.876115 103.259522,125.57573 L87.6063821,141.427011 L91.9654844,145.786113 L96.3245868,150.145215 L111.779586,134.888357 C124.460611,122.207333 128.22529,119.631498 133.575098,119.631498 C147.841251,119.631498 152.794777,135.879062 141.897021,146.380536 L135.952791,152.126626 L140.311893,156.683869 L144.670995,161.241113 L151.209648,154.9006 C154.776186,151.532203 158.739007,145.587972 159.927853,142.021433 Z" id="路径" fill="url(#linearGradient-1)" fill-rule="nonzero" transform="translate(124.4425, 133.7385) scale(-1, 1) rotate(-180) translate(-124.4425, -133.7385)"></path>
        </g>
    </g>
</svg>