/**
 * 测试Base64解析过程是否有节点丢失
 */

import { ProxyConverter } from './src/index.js';
import fs from 'fs';
import path from 'path';

async function testBase64Parsing() {
  console.log('🧪 测试Base64解析过程');
  console.log('==================================================');
  
  const converter = new ProxyConverter();
  const testsDir = './tests';
  
  // 获取所有base64文件
  const files = fs.readdirSync(testsDir)
    .filter(file => file.endsWith('.txt'))
    .sort();
  
  console.log(`📁 找到 ${files.length} 个文件\n`);
  
  let totalOriginalUrls = 0;
  let totalParsedNodes = 0;
  let totalParseFailures = 0;
  
  // 逐个检查文件
  for (let i = 0; i < files.length; i++) {
    const file = files[i];
    const filePath = path.join(testsDir, file);
    
    console.log(`📄 ${i + 1}. ${file}`);
    console.log('─'.repeat(50));
    
    try {
      const content = fs.readFileSync(filePath, 'utf8').trim();
      
      if (!content) {
        console.log('❌ 空文件\n');
        continue;
      }
      
      // 手动解码Base64
      let decodedContent;
      try {
        decodedContent = atob(content);
      } catch (e) {
        console.log('❌ Base64解码失败\n');
        continue;
      }
      
      // 分析原始URL
      const lines = decodedContent.split('\n').filter(line => line.trim());
      const validUrls = lines.filter(line => {
        const trimmed = line.trim();
        return trimmed.startsWith('ss://') || 
               trimmed.startsWith('vmess://') || 
               trimmed.startsWith('trojan://') || 
               trimmed.startsWith('vless://') ||
               trimmed.startsWith('hysteria2://') || 
               trimmed.startsWith('ssr://') ||
               trimmed.startsWith('snell://');
      });
      
      console.log(`📊 原始分析:`);
      console.log(`  总行数: ${lines.length}`);
      console.log(`  有效代理URL: ${validUrls.length}`);
      
      // 统计协议分布
      const protocolCount = {};
      validUrls.forEach(url => {
        const protocol = url.split('://')[0];
        protocolCount[protocol] = (protocolCount[protocol] || 0) + 1;
      });
      
      console.log(`  协议分布: ${Object.entries(protocolCount).map(([k,v]) => `${k}(${v})`).join(', ')}`);
      
      // 使用我们的解析器解析
      const nodes = converter.parse(content, 'base64');
      const parsedCount = nodes ? nodes.length : 0;
      
      console.log(`🔄 解析结果:`);
      console.log(`  解析到的节点: ${parsedCount}`);
      console.log(`  解析成功率: ${((parsedCount / validUrls.length) * 100).toFixed(1)}%`);
      
      // 检查是否有解析失败的URL
      if (parsedCount < validUrls.length) {
        const failedCount = validUrls.length - parsedCount;
        console.log(`❌ 解析失败: ${failedCount} 个节点`);
        totalParseFailures += failedCount;
        
        // 尝试逐个解析找出失败的URL
        console.log(`🔍 检查失败的URL:`);
        let successCount = 0;
        for (let j = 0; j < Math.min(validUrls.length, 5); j++) {
          const url = validUrls[j];
          try {
            const singleNode = converter.parse(url, 'url');
            if (singleNode && singleNode.length > 0) {
              successCount++;
            } else {
              console.log(`  ❌ 解析失败: ${url.substring(0, 50)}...`);
            }
          } catch (error) {
            console.log(`  ❌ 解析异常: ${url.substring(0, 50)}... - ${error.message}`);
          }
        }
        if (validUrls.length > 5) {
          console.log(`  ... 还有 ${validUrls.length - 5} 个URL未检查`);
        }
      } else {
        console.log(`✅ 解析完全成功`);
      }
      
      totalOriginalUrls += validUrls.length;
      totalParsedNodes += parsedCount;
      
    } catch (error) {
      console.log(`❌ 文件处理失败: ${error.message}`);
    }
    
    console.log('');
  }
  
  console.log('📈 总体统计:');
  console.log('==================================================');
  console.log(`总文件数: ${files.length}`);
  console.log(`原始有效URL总数: ${totalOriginalUrls}`);
  console.log(`解析成功节点总数: ${totalParsedNodes}`);
  console.log(`解析失败节点总数: ${totalParseFailures}`);
  console.log(`总体解析成功率: ${((totalParsedNodes / totalOriginalUrls) * 100).toFixed(2)}%`);
  
  if (totalParseFailures > 0) {
    console.log(`\n❌ 发现 ${totalParseFailures} 个节点在解析过程中丢失！`);
    console.log(`这可能解释了为什么最终节点数少于期望的509个。`);
  } else {
    console.log(`\n✅ 所有节点都成功解析，没有在解析过程中丢失。`);
  }
  
  // 计算期望的去重后节点数
  console.log(`\n🎯 节点数量分析:`);
  console.log(`原始节点总数: ${totalOriginalUrls}`);
  console.log(`解析后节点数: ${totalParsedNodes}`);
  console.log(`解析丢失: ${totalOriginalUrls - totalParsedNodes}`);
  
  // 如果解析没有丢失，那么问题可能在去重算法
  if (totalParseFailures === 0) {
    console.log(`\n💡 建议:`);
    console.log(`由于解析过程没有丢失节点，509个节点的差异可能来自:`);
    console.log(`1. v2rayN使用了不同的去重策略`);
    console.log(`2. v2rayN可能保留了某些我们认为重复的节点`);
    console.log(`3. 节点名称或其他字段的差异导致去重结果不同`);
  }
  
  return {
    totalFiles: files.length,
    totalOriginalUrls,
    totalParsedNodes,
    totalParseFailures,
    parseSuccessRate: (totalParsedNodes / totalOriginalUrls) * 100
  };
}

// 运行测试
testBase64Parsing().catch(console.error);
