proxies:
  - name: "🇭🇰 香港 001"
    type: ss
    server: *************
    port: 49758
    cipher: xchacha20-ietf-poly1305
    password: @CfftfYVgp4gkMHMirH6@_C
    udp: true

  - name: "🇭🇰 香港 002"
    type: ss
    server: *************
    port: 49759
    cipher: aes-128-gcm
    password: sadujij!@diQojd1254
    udp: true

  - name: "🇭🇰 香港 003"
    type: trojan
    server: *************
    port: 8313
    password: 2c605663-b89a-5734-a9d6-97d4743d72cf
    network: tcp
    sni: *************
    skip-cert-verify: true
    udp: true

  - name: "🇭🇰 香港 004"
    type: trojan
    server: dozo01.flztjc.top
    port: 8313
    password: 2c605663-b89a-5734-a9d6-97d4743d72cf
    network: tcp
    sni: dozo01.flztjc.top
    skip-cert-verify: true
    udp: true

  - name: "🇭🇰 香港 005"
    type: ss
    server: 346855.blhao0o.dpdns.org
    port: 12008
    cipher: aes-128-gcm
    password: 6c252c76-08b9-423f-9a9f-673c5536adba
    udp: true

  - name: "🇭🇰 香港 006"
    type: ss
    server: 346855.blhao0o.dpdns.org
    port: 12003
    cipher: aes-128-gcm
    password: 3695c2e5-5b43-4679-92c9-631b625d34f0
    udp: true

  - name: "🇭🇰 香港 007"
    type: ss
    server: *************
    port: 16003
    cipher: aes-256-gcm
    password: 3e0c78da-8bfb-4b1f-b1ad-de8b073f693e
    udp: true

  - name: "🇭🇰 香港 008"
    type: trojan
    server: **************
    port: 8313
    password: 2c605663-b89a-5734-a9d6-97d4743d72cf
    network: tcp
    sni: **************
    skip-cert-verify: true
    udp: true

  - name: "🇭🇰 香港 009"
    type: ss
    server: 8tv68qhq.slashdevslashnetslashtun.net
    port: 15010
    cipher: aes-256-gcm
    password: ENYGONDU94UW1G6X
    udp: true

  - name: "🇭🇰 香港 010"
    type: trojan
    server: **************
    port: 39001
    password: b6200af42ccadea353f5b5856dd20d70
    network: tcp
    sni: **************
    skip-cert-verify: true
    udp: true

  - name: "🇭🇰 香港 011"
    type: ss
    server: 346855.blhao0o.dpdns.org
    port: 12005
    cipher: aes-128-gcm
    password: 6c252c76-08b9-423f-9a9f-673c5536adba
    udp: true

  - name: "🇭🇰 香港 012"
    type: trojan
    server: gzyd.cg.xxality.cn
    port: 9900
    password: 09e8b39a-4c09-4df0-a50c-af3bef01567e
    network: tcp
    sni: gzyd.cg.xxality.cn
    skip-cert-verify: true
    udp: true

  - name: "🇭🇰 香港 013"
    type: ss
    server: *************
    port: 16007
    cipher: aes-256-gcm
    password: 54c16ea4-746f-4deb-81c6-1a8b8eb0fa75
    udp: true

  - name: "🇭🇰 香港 014"
    type: ss
    server: 8tv68qhq.slashdevslashnetslashtun.net
    port: 15007
    cipher: aes-256-gcm
    password: 3X4XOU4JFE142PW8
    udp: true

  - name: "🇭🇰 香港 015"
    type: ss
    server: *************
    port: 47351
    cipher: aes-256-gcm
    password: w64NwrzCssO7wpXCqQFOw5HDlsK8VMK+w7vCjMK9B8OrPsOxZMKHLmLDnDPDjMOfayTChQ=
    udp: true

  - name: "🇭🇰 香港 016"
    type: ss
    server: ************
    port: 16014
    cipher: aes-256-gcm
    password: c86d483c-431f-41df-bb6b-c1dcebfc7401
    udp: true

  - name: "🇭🇰 香港 017"
    type: ss
    server: *************
    port: 16005
    cipher: aes-256-gcm
    password: c86d483c-431f-41df-bb6b-c1dcebfc7401
    udp: true

  - name: "🇭🇰 香港 018"
    type: ss
    server: *************
    port: 16008
    cipher: aes-256-gcm
    password: 49cdeea8-97dd-402a-bf8f-961cb59123a7
    udp: true

  - name: "🇯🇵 日本 001"
    type: ss
    server: 346855.blhao0o.dpdns.org
    port: 12014
    cipher: aes-128-gcm
    password: 6c252c76-08b9-423f-9a9f-673c5536adba
    udp: true

  - name: "🇰🇷 韩国 001"
    type: ss
    server: *************
    port: 4857
    cipher: aes-256-cfb
    password: qwerREWQ@@
    udp: true

  - name: "🇰🇷 韩国 002"
    type: ss
    server: *************
    port: 21001
    cipher: aes-256-gcm
    password: dd0109d4-63f0-42aa-99c4-6b9b1eefc5fe
    udp: true

  - name: "🇰🇷 韩国 003"
    type: ss
    server: ************
    port: 21001
    cipher: aes-256-gcm
    password: eedb3468-23c8-4bf4-bc54-968ca7dfd431
    udp: true

  - name: "🇰🇷 韩国 004"
    type: ss
    server: p080.panda001.net
    port: 36379
    cipher: aes-256-cfb
    password: qwerREWQ@@
    udp: true

  - name: "🇰🇷 韩国 005"
    type: ss
    server: p222.panda001.net
    port: 15098
    cipher: aes-256-cfb
    password: qwerREWQ@@
    udp: true

  - name: "🇰🇷 韩国 006"
    type: ss
    server: **********
    port: 443
    cipher: aes-256-cfb
    password: yijian0503
    udp: true

  - name: "🇰🇷 韩国 007"
    type: ss
    server: ***************
    port: 4652
    cipher: aes-256-cfb
    password: qwerREWQ@@
    udp: true

  - name: "🇰🇷 韩国 008"
    type: ss
    server: ************
    port: 21001
    cipher: aes-256-gcm
    password: 49cdeea8-97dd-402a-bf8f-961cb59123a7
    udp: true

  - name: "🇰🇷 韩国 009"
    type: ss
    server: p141.panda001.net
    port: 4652
    cipher: aes-256-cfb
    password: qwerREWQ@@
    udp: true

  - name: "🇰🇷 韩国 010"
    type: ss
    server: ************
    port: 443
    cipher: aes-256-cfb
    password: yijian0503
    udp: true

  - name: "🇰🇷 韩国 011"
    type: ss
    server: p227.panda004.net
    port: 4857
    cipher: aes-256-cfb
    password: qwerREWQ@@
    udp: true

  - name: "🇰🇷 韩国 012"
    type: ss
    server: *************
    port: 443
    cipher: aes-256-cfb
    password: yijian0503
    udp: true

  - name: "🇰🇷 韩国 013"
    type: ss
    server: *************
    port: 21001
    cipher: aes-256-gcm
    password: 54c16ea4-746f-4deb-81c6-1a8b8eb0fa75
    udp: true

  - name: "🇸🇬 新加坡 001"
    type: ss
    server: *************
    port: 18002
    cipher: aes-256-gcm
    password: c86d483c-431f-41df-bb6b-c1dcebfc7401
    udp: true

  - name: "🇸🇬 新加坡 002"
    type: ss
    server: 346855.blhao0o.dpdns.org
    port: 12022
    cipher: aes-128-gcm
    password: d4ee896e-9fb3-4155-bff7-0fd19ffeb44f
    udp: true

  - name: "🇸🇬 新加坡 003"
    type: ss
    server: *************
    port: 18005
    cipher: aes-256-gcm
    password: 49cdeea8-97dd-402a-bf8f-961cb59123a7
    udp: true

  - name: "🇹🇼 台湾 001"
    type: ss
    server: 346855.blhao0o.dpdns.org
    port: 12029
    cipher: aes-128-gcm
    password: d4ee896e-9fb3-4155-bff7-0fd19ffeb44f
    udp: true

  - name: "🇺🇸 美国 001"
    type: trojan
    server: 031ad25f-swin40-syl0kc-115d8.cu.plebai.net
    port: 15229
    password: 20482402-7f76-11ed-a8bf-f23c91cfbbc9
    network: tcp
    sni: 031ad25f-swin40-syl0kc-115d8.cu.plebai.net
    skip-cert-verify: true
    udp: true

  - name: "🇺🇸 美国 002"
    type: trojan
    server: starlink-sfo4.2513142.xyz
    port: 443
    password: ZIXQa-7pg0vPahuyi8bABhob
    network: tcp
    sni: starlink-sfo4.2513142.xyz
    skip-cert-verify: true
    udp: true

  - name: "🇺🇸 美国 003"
    type: trojan
    server: 8d609c20-swkhs0-sxf56z-1f596.cu.plebai.net
    port: 15229
    password: e3975a05-b89e-11ec-a50d-f23c91cfbbc9
    network: tcp
    sni: 8d609c20-swkhs0-sxf56z-1f596.cu.plebai.net
    skip-cert-verify: true
    udp: true

  - name: "🇺🇸 美国 004"
    type: trojan
    server: 93875ab1-swkhs0-sxuzn1-11p9g.cu.plebai.net
    port: 15229
    password: 384d1b42-655f-11ed-a8bf-f23c91cfbbc9
    network: tcp
    sni: 93875ab1-swkhs0-sxuzn1-11p9g.cu.plebai.net
    skip-cert-verify: true
    udp: true

  - name: "🇺🇸 美国 005"
    type: trojan
    server: *************
    port: 7815
    password: RlzoEILU
    network: tcp
    sni: *************
    skip-cert-verify: true
    udp: true

  - name: "🇺🇸 美国 006"
    type: trojan
    server: **************
    port: 21056
    password: 2b1ed981-6547-4094-998b-06a3323d6f6c
    network: tcp
    sni: **************
    skip-cert-verify: true
    udp: true

  - name: "🇺🇸 美国 007"
    type: ss
    server: 03.kill704.win
    port: 44223
    cipher: chacha20-ietf-poly1305
    password: 92e0a2cd-f842-42b6-84ef-dd2da5c711ac
    udp: true

  - name: "🇺🇸 美国 008"
    type: ss
    server: *************
    port: 20002
    cipher: aes-256-gcm
    password: c38ab133-f18f-4537-8dc8-e2e2c2c24e18
    udp: true

  - name: "🇺🇸 美国 009"
    type: trojan
    server: 79e7cfa7-swkhs0-sxe8ph-mubp.cu.plebai.net
    port: 15229
    password: 1bcadc88-4741-11ee-ae2e-f23c91cfbbc9
    network: tcp
    sni: 79e7cfa7-swkhs0-sxe8ph-mubp.cu.plebai.net
    skip-cert-verify: true
    udp: true

  - name: "🇺🇸 美国 010"
    type: ss
    server: ************
    port: 443
    cipher: aes-256-cfb
    password: awsps0501
    udp: true

  - name: "🇺🇸 美国 011"
    type: ss
    server: *************
    port: 20002
    cipher: aes-256-gcm
    password: 54c16ea4-746f-4deb-81c6-1a8b8eb0fa75
    udp: true

  - name: "🇺🇸 美国 012"
    type: trojan
    server: xd-js.timiwc.com
    port: 21332
    password: 2b1ed981-6547-4094-998b-06a3323d6f6c
    network: tcp
    sni: xd-js.timiwc.com
    skip-cert-verify: true
    udp: true

  - name: "🇺🇸 美国 013"
    type: ss
    server: ************
    port: 20001
    cipher: aes-256-gcm
    password: 49cdeea8-97dd-402a-bf8f-961cb59123a7
    udp: true

  - name: "🇺🇸 美国 014"
    type: ss
    server: ************
    port: 443
    cipher: aes-256-cfb
    password: awsps0501
    udp: true

  - name: "🇺🇸 美国 015"
    type: ss
    server: ************
    port: 20002
    cipher: aes-256-gcm
    password: 200c650a-4d7c-4180-b0ce-20093784902e
    udp: true

  - name: "🇺🇸 美国 016"
    type: ss
    server: *************
    port: 20010
    cipher: aes-256-gcm
    password: c86d483c-431f-41df-bb6b-c1dcebfc7401
    udp: true

  - name: "🇺🇸 美国 017"
    type: trojan
    server: b68f8dd6-swkhs0-szxh9w-46gc.cu.plebai.net
    port: 15229
    password: 225d1cca-d744-11ef-b790-f23c91cfbbc9
    network: tcp
    sni: b68f8dd6-swkhs0-szxh9w-46gc.cu.plebai.net
    skip-cert-verify: true
    udp: true

  - name: "🇺🇸 美国 018"
    type: trojan
    server: a5cd9dd480ede645c36df0c25900652d.v1.cac.node-is.green
    port: 44596
    password: c983a532-d8ee-4074-991c-c4a721178fdc
    network: tcp
    sni: a5cd9dd480ede645c36df0c25900652d.v1.cac.node-is.green
    skip-cert-verify: true
    udp: true

  - name: "🇺🇸 美国 019"
    type: trojan
    server: 2399bf322dc7a0362009dcf1709fbcae.us.in.node-is.green
    port: 41397
    password: c983a532-d8ee-4074-991c-c4a721178fdc
    network: tcp
    sni: 2399bf322dc7a0362009dcf1709fbcae.us.in.node-is.green
    skip-cert-verify: true
    udp: true

  - name: "🇺🇸 美国 020"
    type: ss
    server: ************
    port: 20007
    cipher: aes-256-gcm
    password: c86d483c-431f-41df-bb6b-c1dcebfc7401
    udp: true

  - name: "🇺🇸 美国 021"
    type: ss
    server: ***********
    port: 443
    cipher: aes-256-cfb
    password: awsps0501
    udp: true

  - name: "🇺🇸 美国 022"
    type: trojan
    server: xd-js.timiwc.com
    port: 59599
    password: 2b1ed981-6547-4094-998b-06a3323d6f6c
    network: tcp
    sni: xd-js.timiwc.com
    skip-cert-verify: true
    udp: true

  - name: "🇺🇸 美国 023"
    type: ss
    server: *************
    port: 20003
    cipher: aes-256-gcm
    password: b8fa2e30-a210-4d8b-bb1e-bf42ea9c9e7e
    udp: true

  - name: "🇺🇸 美国 024"
    type: ss
    server: *************
    port: 20002
    cipher: aes-256-gcm
    password: 3e0c78da-8bfb-4b1f-b1ad-de8b073f693e
    udp: true

  - name: "🇺🇸 美国 025"
    type: trojan
    server: 5708853d-swkhs0-tcselz-oua4.cu.plebai.net
    port: 15229
    password: 659990ba-650f-11ea-a7af-f23c9164ca5d
    network: tcp
    sni: 5708853d-swkhs0-tcselz-oua4.cu.plebai.net
    skip-cert-verify: true
    udp: true

  - name: "🇩🇪 德国 001"
    type: ss
    server: *************
    port: 28001
    cipher: aes-256-gcm
    password: d12c4dbf-d12a-4243-8a45-35eb3e00df4f
    udp: true

  - name: "🇩🇪 德国 002"
    type: ss
    server: *************
    port: 28001
    cipher: aes-256-gcm
    password: b8fa2e30-a210-4d8b-bb1e-bf42ea9c9e7e
    udp: true

  - name: "🇩🇪 德国 003"
    type: ss
    server: ************
    port: 28001
    cipher: aes-256-gcm
    password: c86d483c-431f-41df-bb6b-c1dcebfc7401
    udp: true

  - name: "🇩🇪 德国 004"
    type: trojan
    server: **************
    port: 21102
    password: 2b1ed981-6547-4094-998b-06a3323d6f6c
    network: tcp
    sni: **************
    skip-cert-verify: true
    udp: true

  - name: "🇩🇪 德国 005"
    type: trojan
    server: 534f4c0ab1b6fe06f3fba5d889946941.v1.cac.node-is.green
    port: 41619
    password: c983a532-d8ee-4074-991c-c4a721178fdc
    network: tcp
    sni: 534f4c0ab1b6fe06f3fba5d889946941.v1.cac.node-is.green
    skip-cert-verify: true
    udp: true

  - name: "🇳🇱 荷兰 001"
    type: ss
    server: ************
    port: 26001
    cipher: aes-256-gcm
    password: d12c4dbf-d12a-4243-8a45-35eb3e00df4f
    udp: true

  - name: "🇳🇱 荷兰 002"
    type: ss
    server: ***************
    port: 20000
    cipher: aes-256-gcm
    password: 4RZYY1R43M8H1A33
    udp: true

  - name: "🇨🇦 加拿大 001"
    type: ss
    server: *************
    port: 22002
    cipher: aes-256-gcm
    password: 200c650a-4d7c-4180-b0ce-20093784902e
    udp: true

  - name: "🇨🇦 加拿大 002"
    type: ss
    server: *************
    port: 22002
    cipher: aes-256-gcm
    password: 54c16ea4-746f-4deb-81c6-1a8b8eb0fa75
    udp: true

  - name: "🇲🇾 马来西亚 001"
    type: trojan
    server: **************
    port: 21181
    password: 2b1ed981-6547-4094-998b-06a3323d6f6c
    network: tcp
    sni: **************
    skip-cert-verify: true
    udp: true

  - name: "🇲🇾 马来西亚 002"
    type: trojan
    server: **************
    port: 21079
    password: 2b1ed981-6547-4094-998b-06a3323d6f6c
    network: tcp
    sni: **************
    skip-cert-verify: true
    udp: true

  - name: "🇲🇾 马来西亚 003"
    type: ss
    server: ************
    port: 29010
    cipher: aes-256-gcm
    password: b8fa2e30-a210-4d8b-bb1e-bf42ea9c9e7e
    udp: true

  - name: "🌐 其他 001"
    type: ss
    server: **************
    port: 20007
    cipher: aes-256-gcm
    password: S2FZOQ6S97FY3CJY
    udp: true

  - name: "🌐 其他 002"
    type: ss
    server: **************
    port: 20015
    cipher: aes-256-gcm
    password: QOI73J58ZCD799BR
    udp: true

