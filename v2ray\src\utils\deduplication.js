/**
 * 节点去重工具
 */

/**
 * 节点去重选项
 * @typedef {Object} DeduplicationOptions
 * @property {string} strategy - 去重策略: 'server-port', 'server-port-type', 'name', 'full'
 * @property {boolean} keepFirst - 是否保留第一个重复节点
 * @property {boolean} caseSensitive - 名称比较是否区分大小写
 */

/**
 * 去重策略枚举
 */
export const DeduplicationStrategy = {
  SERVER_PORT: 'server-port',           // 基于服务器地址和端口
  SERVER_PORT_TYPE: 'server-port-type', // 基于服务器地址、端口和协议类型
  NAME: 'name',                         // 基于节点名称
  FULL: 'full',                         // 完全匹配（所有关键字段）
  PRECISE: 'precise'                    // 精准匹配（协议特定的核心字段）
};

/**
 * 节点去重主函数
 * @param {Object[]} nodes - 节点数组
 * @param {DeduplicationOptions} options - 去重选项
 * @returns {Object[]} 去重后的节点数组
 */
export function deduplicateNodes(nodes, options = {}) {
  if (!nodes || !Array.isArray(nodes) || nodes.length === 0) {
    return [];
  }

  const {
    strategy = DeduplicationStrategy.PRECISE,
    keepFirst = true,
    caseSensitive = false
  } = options;

  const uniqueNodes = [];
  const seenKeys = new Set();

  for (const node of nodes) {
    const key = generateNodeKey(node, strategy, caseSensitive);

    if (!seenKeys.has(key)) {
      seenKeys.add(key);
      uniqueNodes.push(node);
    } else if (!keepFirst) {
      // 如果不保留第一个，则替换已存在的节点
      const existingIndex = uniqueNodes.findIndex(n =>
        generateNodeKey(n, strategy, caseSensitive) === key
      );
      if (existingIndex !== -1) {
        uniqueNodes[existingIndex] = node;
      }
    }
  }

  return uniqueNodes;
}

/**
 * 生成节点的唯一标识键
 * @param {Object} node - 节点信息
 * @param {string} strategy - 去重策略
 * @param {boolean} caseSensitive - 是否区分大小写
 * @returns {string} 唯一标识键
 */
function generateNodeKey(node, strategy, caseSensitive = false) {
  if (!node) return '';

  switch (strategy) {
    case DeduplicationStrategy.SERVER_PORT:
      return `${node.server}:${node.port}`;

    case DeduplicationStrategy.SERVER_PORT_TYPE:
      return `${node.server}:${node.port}:${node.type}`;

    case DeduplicationStrategy.NAME:
      const name = caseSensitive ? node.name : (node.name || '').toLowerCase();
      return name;

    case DeduplicationStrategy.FULL:
      return generateFullNodeKey(node);

    case DeduplicationStrategy.PRECISE:
      return generatePreciseNodeKey(node);

    default:
      return generateNodeKey(node, DeduplicationStrategy.SERVER_PORT_TYPE, caseSensitive);
  }
}

/**
 * 生成完整的节点标识键（高精度去重）
 * @param {Object} node - 节点信息
 * @returns {string} 完整标识键
 */
function generateFullNodeKey(node) {
  const keyParts = [
    node.server,
    node.port,
    node.type
  ];

  // 根据协议类型添加特定字段（高精度规则）
  switch (node.type) {
    case 'ss':
      // SS: 服务器+端口+密码+加密方法+插件
      keyParts.push(
        node.password,
        node.method || node.cipher,
        node.plugin || 'none',
        node.pluginOpts || ''
      );
      break;

    case 'ssr':
      // SSR: 服务器+端口+密码+加密方法+协议+混淆+协议参数+混淆参数
      keyParts.push(
        node.password,
        node.method || node.cipher,
        node.protocol || 'origin',
        node.obfs || 'plain',
        node.protocolParam || '',
        node.obfsParam || ''
      );
      break;

    case 'vmess':
      // VMess: 服务器+端口+UUID+alterId+加密+传输层+TLS+路径/Host
      keyParts.push(
        node.uuid,
        node.alterId || 0,
        node.cipher || 'auto',
        node.network || 'tcp'
      );
      // 传输层特定配置
      if (node.transport) {
        keyParts.push(
          node.transport.path || '',
          node.transport.host || '',
          node.transport.serviceName || ''
        );
      }
      // TLS配置
      if (node.tls) {
        keyParts.push(
          node.tls.enabled ? 'tls' : 'notls',
          node.tls.serverName || ''
        );
      }
      break;

    case 'vless':
      // VLESS: 服务器+端口+UUID+flow+加密+传输层+TLS+路径/Host
      keyParts.push(
        node.uuid,
        node.flow || 'none',
        node.encryption || 'none',
        node.network || 'tcp'
      );
      // 传输层特定配置
      if (node.transport) {
        keyParts.push(
          node.transport.path || '',
          node.transport.host || '',
          node.transport.serviceName || ''
        );
      }
      // TLS配置
      if (node.tls) {
        keyParts.push(
          node.tls.enabled ? 'tls' : 'notls',
          node.tls.serverName || ''
        );
      }
      // Reality配置
      if (node.reality) {
        keyParts.push(
          'reality',
          node.reality.publicKey || '',
          node.reality.shortId || ''
        );
      }
      break;

    case 'trojan':
      // Trojan: 服务器+端口+密码+传输层+TLS+路径/Host
      keyParts.push(
        node.password,
        node.network || 'tcp'
      );
      // 传输层特定配置
      if (node.transport) {
        keyParts.push(
          node.transport.path || '',
          node.transport.host || '',
          node.transport.serviceName || ''
        );
      }
      // TLS配置（Trojan默认使用TLS）
      if (node.tls) {
        keyParts.push(
          node.tls.serverName || '',
          node.tls.allowInsecure ? 'insecure' : 'secure'
        );
      }
      break;

    case 'hysteria':
      // Hysteria v1: 服务器+端口+认证+协议+混淆+TLS
      keyParts.push(
        node.password || node.auth || '',
        node.protocol || 'udp',
        node.obfs || 'none',
        node.tls?.serverName || '',
        node.up || '',
        node.down || ''
      );
      break;

    case 'hysteria2':
      // Hysteria2: 服务器+端口+认证+TLS+混淆+带宽
      keyParts.push(
        node.password || node.auth || '',
        node.tls?.serverName || '',
        node.obfs?.type || 'none',
        node.obfs?.password || '',
        node.up || '',
        node.down || ''
      );
      break;

    case 'tuic':
      // TUIC: 服务器+端口+UUID+密码+版本+拥塞控制
      keyParts.push(
        node.uuid || '',
        node.password || '',
        node.version || 5,
        node.congestion || 'cubic',
        node.udpRelayMode || 'native'
      );
      break;

    case 'snell':
      // Snell: 服务器+端口+密码+版本+混淆+TLS
      keyParts.push(
        node.password,
        node.version || 'v3',
        node.obfs?.type || 'none',
        node.obfs?.host || '',
        node.tls?.enabled ? 'tls' : 'notls'
      );
      break;

    default:
      // 对于未知协议，添加所有可能的关键字段
      if (node.password) keyParts.push(node.password);
      if (node.auth) keyParts.push(node.auth);
      if (node.uuid) keyParts.push(node.uuid);
      if (node.method) keyParts.push(node.method);
      if (node.network) keyParts.push(node.network);
      break;
  }

  return keyParts.filter(part => part !== undefined && part !== null && part !== '').join(':');
}

/**
 * 生成精准的节点标识键（平衡精度和实用性）
 * @param {Object} node - 节点信息
 * @returns {string} 精准标识键
 */
function generatePreciseNodeKey(node) {
  const keyParts = [
    node.server,
    node.port,
    node.type
  ];

  // 根据协议类型添加核心字段（精准规则）
  switch (node.type) {
    case 'ss':
      // SS: 服务器+端口+密码+加密方法（忽略插件差异，除非插件不同）
      keyParts.push(
        node.password,
        node.method || node.cipher
      );
      // 只有当插件存在且不为默认值时才加入
      if (node.plugin && node.plugin !== 'none') {
        keyParts.push(node.plugin);
      }
      break;

    case 'ssr':
      // SSR: 服务器+端口+密码+加密方法+协议+混淆（忽略参数的细微差异）
      keyParts.push(
        node.password,
        node.method || node.cipher,
        node.protocol || 'origin',
        node.obfs || 'plain'
      );
      break;

    case 'vmess':
      // VMess: 服务器+端口+UUID+传输层+TLS状态（忽略alterId和cipher的细微差异）
      keyParts.push(
        node.uuid,
        node.network || 'tcp'
      );
      // 传输层路径（对于ws/h2/grpc很重要）
      if (node.transport && (node.network === 'ws' || node.network === 'h2' || node.network === 'grpc')) {
        keyParts.push(
          node.transport.path || '',
          node.transport.host || ''
        );
      }
      // TLS状态
      keyParts.push(node.tls?.enabled ? 'tls' : 'notls');
      break;

    case 'vless':
      // VLESS: 服务器+端口+UUID+flow+传输层+TLS/Reality状态
      keyParts.push(
        node.uuid,
        node.flow || 'none',
        node.network || 'tcp'
      );
      // 传输层路径
      if (node.transport && (node.network === 'ws' || node.network === 'h2' || node.network === 'grpc')) {
        keyParts.push(
          node.transport.path || '',
          node.transport.host || ''
        );
      }
      // TLS/Reality状态
      if (node.reality) {
        keyParts.push('reality', node.reality.publicKey || '');
      } else {
        keyParts.push(node.tls?.enabled ? 'tls' : 'notls');
      }
      break;

    case 'trojan':
      // Trojan: 服务器+端口+密码+传输层（TLS是默认的）
      keyParts.push(
        node.password,
        node.network || 'tcp'
      );
      // 传输层路径
      if (node.transport && (node.network === 'ws' || node.network === 'h2' || node.network === 'grpc')) {
        keyParts.push(
          node.transport.path || '',
          node.transport.host || ''
        );
      }
      break;

    case 'hysteria':
      // Hysteria v1: 服务器+端口+认证+协议+混淆（忽略带宽差异）
      keyParts.push(
        node.password || node.auth || '',
        node.protocol || 'udp',
        node.obfs || 'none'
      );
      break;

    case 'hysteria2':
      // Hysteria2: 服务器+端口+认证+混淆（忽略带宽差异）
      keyParts.push(
        node.password || node.auth || '',
        node.obfs?.type || 'none'
      );
      // 只有当混淆密码存在时才加入
      if (node.obfs?.password) {
        keyParts.push(node.obfs.password);
      }
      break;

    case 'tuic':
      // TUIC: 服务器+端口+UUID+密码+版本（忽略拥塞控制差异）
      keyParts.push(
        node.uuid || '',
        node.password || '',
        node.version || 5
      );
      break;

    case 'snell':
      // Snell: 服务器+端口+密码+版本+混淆
      keyParts.push(
        node.password,
        node.version || 'v3',
        node.obfs?.type || 'none'
      );
      break;

    default:
      // 对于未知协议，使用基本字段
      if (node.password) keyParts.push(node.password);
      if (node.uuid) keyParts.push(node.uuid);
      if (node.method) keyParts.push(node.method);
      break;
  }

  return keyParts.filter(part => part !== undefined && part !== null && part !== '').join(':');
}

/**
 * 查找重复的节点
 * @param {Object[]} nodes - 节点数组
 * @param {DeduplicationOptions} options - 查找选项
 * @returns {Object} 重复节点信息
 */
export function findDuplicateNodes(nodes, options = {}) {
  if (!nodes || !Array.isArray(nodes) || nodes.length === 0) {
    return { duplicates: [], groups: [] };
  }

  const {
    strategy = DeduplicationStrategy.PRECISE,
    caseSensitive = false
  } = options;

  const groups = new Map();
  const duplicates = [];

  // 按键分组
  for (let i = 0; i < nodes.length; i++) {
    const node = nodes[i];
    const key = generateNodeKey(node, strategy, caseSensitive);

    if (!groups.has(key)) {
      groups.set(key, []);
    }

    groups.get(key).push({ node, index: i });
  }

  // 找出重复的组
  const duplicateGroups = [];
  for (const [key, group] of groups.entries()) {
    if (group.length > 1) {
      duplicateGroups.push({
        key,
        nodes: group,
        count: group.length
      });

      // 除了第一个节点外，其他都是重复的
      duplicates.push(...group.slice(1));
    }
  }

  return {
    duplicates: duplicates.map(item => item.index),
    groups: duplicateGroups,
    totalDuplicates: duplicates.length,
    uniqueCount: groups.size
  };
}

/**
 * 获取去重统计信息
 * @param {Object[]} originalNodes - 原始节点数组
 * @param {Object[]} deduplicatedNodes - 去重后的节点数组
 * @param {DeduplicationOptions} options - 去重选项
 * @returns {Object} 统计信息
 */
export function getDeduplicationStats(originalNodes, deduplicatedNodes, options = {}) {
  const duplicateInfo = findDuplicateNodes(originalNodes, options);

  return {
    original: originalNodes.length,
    deduplicated: deduplicatedNodes.length,
    removed: originalNodes.length - deduplicatedNodes.length,
    duplicateGroups: duplicateInfo.groups.length,
    totalDuplicates: duplicateInfo.totalDuplicates,
    strategy: options.strategy || DeduplicationStrategy.PRECISE
  };
}

/**
 * 智能去重（自动选择最佳策略）
 * @param {Object[]} nodes - 节点数组
 * @param {Object} options - 选项
 * @returns {Object} 去重结果
 */
export function smartDeduplicate(nodes, options = {}) {
  if (!nodes || !Array.isArray(nodes) || nodes.length === 0) {
    return { nodes: [], stats: null };
  }

  // 尝试不同的策略，按精度排序
  const strategies = [
    DeduplicationStrategy.PRECISE,    // 精准匹配（推荐）
    DeduplicationStrategy.FULL,       // 完全匹配
    DeduplicationStrategy.SERVER_PORT_TYPE, // 基础匹配
    DeduplicationStrategy.SERVER_PORT // 宽松匹配
  ];

  let bestResult = null;
  let bestStrategy = null;
  let maxRemoved = 0;

  for (const strategy of strategies) {
    const deduplicatedNodes = deduplicateNodes(nodes, { ...options, strategy });
    const removed = nodes.length - deduplicatedNodes.length;

    if (removed > maxRemoved) {
      maxRemoved = removed;
      bestResult = deduplicatedNodes;
      bestStrategy = strategy;
    }
  }

  const stats = getDeduplicationStats(nodes, bestResult || nodes, {
    ...options,
    strategy: bestStrategy
  });

  return {
    nodes: bestResult || nodes,
    stats,
    strategy: bestStrategy
  };
}

/**
 * 按协议类型分组去重
 * @param {Object[]} nodes - 节点数组
 * @param {DeduplicationOptions} options - 去重选项
 * @returns {Object[]} 去重后的节点数组
 */
export function deduplicateByType(nodes, options = {}) {
  if (!nodes || !Array.isArray(nodes) || nodes.length === 0) {
    return [];
  }

  // 按协议类型分组
  const typeGroups = new Map();
  for (const node of nodes) {
    const type = node.type || 'unknown';
    if (!typeGroups.has(type)) {
      typeGroups.set(type, []);
    }
    typeGroups.get(type).push(node);
  }

  // 对每个类型分别去重
  const deduplicatedNodes = [];
  for (const [type, typeNodes] of typeGroups.entries()) {
    const deduplicated = deduplicateNodes(typeNodes, options);
    deduplicatedNodes.push(...deduplicated);
  }

  return deduplicatedNodes;
}

/**
 * 自定义去重函数
 * @param {Object[]} nodes - 节点数组
 * @param {Function} keyGenerator - 自定义键生成函数
 * @param {boolean} keepFirst - 是否保留第一个
 * @returns {Object[]} 去重后的节点数组
 */
export function customDeduplicate(nodes, keyGenerator, keepFirst = true) {
  if (!nodes || !Array.isArray(nodes) || nodes.length === 0) {
    return [];
  }

  if (typeof keyGenerator !== 'function') {
    throw new Error('keyGenerator 必须是一个函数');
  }

  const uniqueNodes = [];
  const seenKeys = new Set();

  for (const node of nodes) {
    try {
      const key = keyGenerator(node);

      if (!seenKeys.has(key)) {
        seenKeys.add(key);
        uniqueNodes.push(node);
      } else if (!keepFirst) {
        const existingIndex = uniqueNodes.findIndex(n => keyGenerator(n) === key);
        if (existingIndex !== -1) {
          uniqueNodes[existingIndex] = node;
        }
      }
    } catch (error) {
      console.error('自定义键生成函数执行失败:', error);
      // 如果键生成失败，保留该节点
      uniqueNodes.push(node);
    }
  }

  return uniqueNodes;
}