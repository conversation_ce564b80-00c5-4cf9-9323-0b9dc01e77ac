/**
 * 节点去重工具
 */

/**
 * 节点去重选项
 * @typedef {Object} DeduplicationOptions
 * @property {string} strategy - 去重策略: 'server-port', 'server-port-type', 'name', 'full'
 * @property {boolean} keepFirst - 是否保留第一个重复节点
 * @property {boolean} caseSensitive - 名称比较是否区分大小写
 */

/**
 * 去重策略枚举
 */
export const DeduplicationStrategy = {
  SERVER_PORT: 'server-port',           // 基于服务器地址和端口
  SERVER_PORT_TYPE: 'server-port-type', // 基于服务器地址、端口和协议类型
  NAME: 'name',                         // 基于节点名称
  FULL: 'full',                         // 完全匹配（所有关键字段）
  V2RAYN_LIKE: 'v2rayn-like'           // 类似v2rayN的去重策略
};

/**
 * 节点去重主函数
 * @param {Object[]} nodes - 节点数组
 * @param {DeduplicationOptions} options - 去重选项
 * @returns {Object[]} 去重后的节点数组
 */
export function deduplicateNodes(nodes, options = {}) {
  if (!nodes || !Array.isArray(nodes) || nodes.length === 0) {
    return [];
  }

  const {
    strategy = DeduplicationStrategy.SERVER_PORT_TYPE,
    keepFirst = true,
    caseSensitive = false
  } = options;

  const uniqueNodes = [];
  const seenKeys = new Set();

  for (const node of nodes) {
    const key = generateNodeKey(node, strategy, caseSensitive);

    if (!seenKeys.has(key)) {
      seenKeys.add(key);
      uniqueNodes.push(node);
    } else if (!keepFirst) {
      // 如果不保留第一个，则替换已存在的节点
      const existingIndex = uniqueNodes.findIndex(n =>
        generateNodeKey(n, strategy, caseSensitive) === key
      );
      if (existingIndex !== -1) {
        uniqueNodes[existingIndex] = node;
      }
    }
  }

  return uniqueNodes;
}

/**
 * 生成节点的唯一标识键
 * @param {Object} node - 节点信息
 * @param {string} strategy - 去重策略
 * @param {boolean} caseSensitive - 是否区分大小写
 * @returns {string} 唯一标识键
 */
function generateNodeKey(node, strategy, caseSensitive = false) {
  if (!node) return '';

  switch (strategy) {
    case DeduplicationStrategy.SERVER_PORT:
      return `${node.server}:${node.port}`;

    case DeduplicationStrategy.SERVER_PORT_TYPE:
      return `${node.server}:${node.port}:${node.type}`;

    case DeduplicationStrategy.NAME:
      const name = caseSensitive ? node.name : (node.name || '').toLowerCase();
      return name;

    case DeduplicationStrategy.FULL:
      return generateFullNodeKey(node);

    case DeduplicationStrategy.V2RAYN_LIKE:
      return generateV2rayNLikeKey(node);

    default:
      return generateNodeKey(node, DeduplicationStrategy.SERVER_PORT_TYPE, caseSensitive);
  }
}

/**
 * 生成完整的节点标识键
 * @param {Object} node - 节点信息
 * @returns {string} 完整标识键
 */
function generateFullNodeKey(node) {
  const keyParts = [
    node.server,
    node.port,
    node.type
  ];

  // 根据协议类型添加特定字段
  switch (node.type) {
    case 'ss':
      keyParts.push(node.password, node.method || node.cipher);
      break;
    case 'ssr':
      keyParts.push(node.password, node.method, node.protocol, node.obfs);
      break;
    case 'vmess':
      keyParts.push(node.uuid, node.alterId, node.cipher, node.network);
      break;
    case 'vless':
      keyParts.push(node.uuid, node.flow, node.encryption, node.network);
      break;
    case 'trojan':
      keyParts.push(node.password, node.network);
      break;
    case 'hysteria2':
      keyParts.push(node.password, node.sni);
      break;
    case 'snell':
      keyParts.push(node.password, node.version, node.obfs);
      break;
    default:
      // 对于未知协议，添加所有可能的关键字段
      if (node.password) keyParts.push(node.password);
      if (node.uuid) keyParts.push(node.uuid);
      if (node.method) keyParts.push(node.method);
      break;
  }

  return keyParts.filter(part => part !== undefined && part !== null && part !== '').join(':');
}

/**
 * 查找重复的节点
 * @param {Object[]} nodes - 节点数组
 * @param {DeduplicationOptions} options - 查找选项
 * @returns {Object} 重复节点信息
 */
export function findDuplicateNodes(nodes, options = {}) {
  if (!nodes || !Array.isArray(nodes) || nodes.length === 0) {
    return { duplicates: [], groups: [] };
  }

  const {
    strategy = DeduplicationStrategy.SERVER_PORT_TYPE,
    caseSensitive = false
  } = options;

  const groups = new Map();
  const duplicates = [];

  // 按键分组
  for (let i = 0; i < nodes.length; i++) {
    const node = nodes[i];
    const key = generateNodeKey(node, strategy, caseSensitive);

    if (!groups.has(key)) {
      groups.set(key, []);
    }

    groups.get(key).push({ node, index: i });
  }

  // 找出重复的组
  const duplicateGroups = [];
  for (const [key, group] of groups.entries()) {
    if (group.length > 1) {
      duplicateGroups.push({
        key,
        nodes: group,
        count: group.length
      });

      // 除了第一个节点外，其他都是重复的
      duplicates.push(...group.slice(1));
    }
  }

  return {
    duplicates: duplicates.map(item => item.index),
    groups: duplicateGroups,
    totalDuplicates: duplicates.length,
    uniqueCount: groups.size
  };
}

/**
 * 获取去重统计信息
 * @param {Object[]} originalNodes - 原始节点数组
 * @param {Object[]} deduplicatedNodes - 去重后的节点数组
 * @param {DeduplicationOptions} options - 去重选项
 * @returns {Object} 统计信息
 */
export function getDeduplicationStats(originalNodes, deduplicatedNodes, options = {}) {
  const duplicateInfo = findDuplicateNodes(originalNodes, options);

  return {
    original: originalNodes.length,
    deduplicated: deduplicatedNodes.length,
    removed: originalNodes.length - deduplicatedNodes.length,
    duplicateGroups: duplicateInfo.groups.length,
    totalDuplicates: duplicateInfo.totalDuplicates,
    strategy: options.strategy || DeduplicationStrategy.SERVER_PORT_TYPE
  };
}

/**
 * 智能去重（自动选择最佳策略）
 * @param {Object[]} nodes - 节点数组
 * @param {Object} options - 选项
 * @returns {Object} 去重结果
 */
export function smartDeduplicate(nodes, options = {}) {
  if (!nodes || !Array.isArray(nodes) || nodes.length === 0) {
    return { nodes: [], stats: null };
  }

  // 尝试不同的策略，选择去重效果最好的
  const strategies = [
    DeduplicationStrategy.FULL,
    DeduplicationStrategy.SERVER_PORT_TYPE,
    DeduplicationStrategy.SERVER_PORT,
    DeduplicationStrategy.NAME
  ];

  let bestResult = null;
  let bestStrategy = null;
  let maxRemoved = 0;

  for (const strategy of strategies) {
    const deduplicatedNodes = deduplicateNodes(nodes, { ...options, strategy });
    const removed = nodes.length - deduplicatedNodes.length;

    if (removed > maxRemoved) {
      maxRemoved = removed;
      bestResult = deduplicatedNodes;
      bestStrategy = strategy;
    }
  }

  const stats = getDeduplicationStats(nodes, bestResult || nodes, {
    ...options,
    strategy: bestStrategy
  });

  return {
    nodes: bestResult || nodes,
    stats,
    strategy: bestStrategy
  };
}

/**
 * 按协议类型分组去重
 * @param {Object[]} nodes - 节点数组
 * @param {DeduplicationOptions} options - 去重选项
 * @returns {Object[]} 去重后的节点数组
 */
export function deduplicateByType(nodes, options = {}) {
  if (!nodes || !Array.isArray(nodes) || nodes.length === 0) {
    return [];
  }

  // 按协议类型分组
  const typeGroups = new Map();
  for (const node of nodes) {
    const type = node.type || 'unknown';
    if (!typeGroups.has(type)) {
      typeGroups.set(type, []);
    }
    typeGroups.get(type).push(node);
  }

  // 对每个类型分别去重
  const deduplicatedNodes = [];
  for (const [type, typeNodes] of typeGroups.entries()) {
    const deduplicated = deduplicateNodes(typeNodes, options);
    deduplicatedNodes.push(...deduplicated);
  }

  return deduplicatedNodes;
}

/**
 * 自定义去重函数
 * @param {Object[]} nodes - 节点数组
 * @param {Function} keyGenerator - 自定义键生成函数
 * @param {boolean} keepFirst - 是否保留第一个
 * @returns {Object[]} 去重后的节点数组
 */
export function customDeduplicate(nodes, keyGenerator, keepFirst = true) {
  if (!nodes || !Array.isArray(nodes) || nodes.length === 0) {
    return [];
  }

  if (typeof keyGenerator !== 'function') {
    throw new Error('keyGenerator 必须是一个函数');
  }

  const uniqueNodes = [];
  const seenKeys = new Set();

  for (const node of nodes) {
    try {
      const key = keyGenerator(node);

      if (!seenKeys.has(key)) {
        seenKeys.add(key);
        uniqueNodes.push(node);
      } else if (!keepFirst) {
        const existingIndex = uniqueNodes.findIndex(n => keyGenerator(n) === key);
        if (existingIndex !== -1) {
          uniqueNodes[existingIndex] = node;
        }
      }
    } catch (error) {
      console.error('自定义键生成函数执行失败:', error);
      // 如果键生成失败，保留该节点
      uniqueNodes.push(node);
    }
  }

  return uniqueNodes;
}

/**
 * 生成类似v2rayN的节点标识键
 * v2rayN可能只基于服务器地址和端口进行去重，对于相同服务器不同协议的节点可能保留
 * @param {Object} node - 节点信息
 * @returns {string} v2rayN风格标识键
 */
function generateV2rayNLikeKey(node) {
  // v2rayN可能使用更宽松的去重策略
  // 只有当服务器、端口、协议类型和主要认证信息都相同时才认为是重复
  const keyParts = [
    node.server,
    node.port,
    node.type
  ];

  // 只添加主要的认证信息，忽略一些次要参数
  switch (node.type) {
    case 'ss':
    case 'ssr':
      // 对于SS/SSR，只比较密码，忽略加密方法的细微差异
      if (node.password) keyParts.push(node.password);
      break;
    case 'vmess':
    case 'vless':
      // 对于VMess/VLESS，只比较UUID
      if (node.uuid) keyParts.push(node.uuid);
      break;
    case 'trojan':
      // 对于Trojan，只比较密码
      if (node.password) keyParts.push(node.password);
      break;
    case 'hysteria2':
      // 对于Hysteria2，比较密码
      if (node.password) keyParts.push(node.password);
      break;
    case 'snell':
      // 对于Snell，比较密码
      if (node.password) keyParts.push(node.password);
      break;
  }

  return keyParts.filter(part => part !== undefined && part !== null && part !== '').join(':');
}