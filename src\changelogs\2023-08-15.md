---
date: 2023-08-15
---

- 更新组件库 NutUI v3.3.8、Picker 问题修复
- 解决反复重启并发消耗资源、降低资源占用内存
- 移除：Monaco-Editor 组件

- 添加新主题、Simple 模式 主页与管理页
- 自定义后端 HostAPI 更多使用场景

- 优化侧滑返回容易失效的问题
- 修复左右滑动组件的时候 没有阻止会上下滑动 容易误触
- 修复拖动卡片的时会把快捷方式跟着拖动
- 脚本操作、正则操作、等平铺放置更容易添加操作
- 点击订阅左边的图标才会预览，防止误触预览节点
- 首页订阅页面：卡片左滑呼出快捷方式，可设置右滑呼出。ㅤ ㅤ
- 点击卡片空白处可关闭当前滑块。添加编辑方便修改

- 改进 Service Worker 通过将资源预缓存，更快、流畅地加载ㅤ ㅤ
- 网络连接稳定或不可用时仍能够访问程序
- 增加预览时候的 V2Ray 入口
- 新增长按卡片拖动排序，前端需 v2.14.6+ 后端 v2.14.13+
- 首页订阅页面图标默认为黑白，可自定义开启图标为彩色
