proxies:
  - cipher: aes-256-gcm
    name: "\U0001F1EF\U0001F1F5日本1 | ⬇️ 8.9MB/s"
    password: 3030b609-f36f-4fd1-a4ef-a6e653780536
    port: 19002
    server: ************
    type: ss
  - client-fingerprint: chrome
    name: "\U0001F1ED\U0001F1F0香港1 | ⬇️ 10.0MB/s"
    password: e2241e38-d5d4-4f60-afcd-bf7d6bfb6668
    port: 42204
    server: e5cf8bf8bd48d330b0509e4aaf1b57f5.v1.cac.node-is.green
    skip-cert-verify: true
    sni: hk14.bilibili.com
    type: trojan
    udp: true
  - down: ''
    fingerprint: ''
    name: "\U0001F1FA\U0001F1F8美国1 | ⬇️ 8.0MB/s"
    obfs: ''
    obfs-password: ''
    password: b8bd42a9-551f-419d-b70d-4aefdd2cb074
    port: 443
    server: **************
    skip-cert-verify: true
    sni: www.bing.com
    type: hysteria2
    up: ''
  - down: ''
    fingerprint: ''
    name: "\U0001F1FA\U0001F1F8美国2 | ⬇️ 10.1MB/s"
    obfs: ''
    obfs-password: ''
    password: b8bd42a9-551f-419d-b70d-4aefdd2cb074
    port: 443
    server: **************
    skip-cert-verify: true
    sni: www.bing.com
    type: hysteria2
    up: ''
  - down: ''
    fingerprint: ''
    name: "\U0001F1FA\U0001F1F8美国3 | ⬇️ 9.4MB/s"
    obfs: ''
    obfs-password: ''
    password: b8bd42a9-551f-419d-b70d-4aefdd2cb074
    port: 443
    server: *************
    skip-cert-verify: true
    sni: www.bing.com
    type: hysteria2
    up: ''
  - down: ''
    fingerprint: ''
    name: "\U0001F1FA\U0001F1F8美国4 | ⬇️ 8.5MB/s"
    obfs: ''
    obfs-password: ''
    password: b72ba5d5-2d5e-45b7-93b5-236d343baa7c
    port: 47262
    server: **************
    skip-cert-verify: true
    sni: www.bing.com
    type: hysteria2
    up: ''
  - name: "\U0001F1FA\U0001F1F8美国5 | ⬇️ 10.0MB/s"
    password: 2b1ed981-6547-4094-998b-06a3323d6f6c
    port: 59599
    server: xd-js.timiwc.com
    skip-cert-verify: true
    sni: k62.tudou211.com
    type: trojan
  - cipher: aes-256-gcm
    name: "\U0001F1F9\U0001F1FC台湾1 | ⬇️ 11.0MB/s"
    password: c86d483c-431f-41df-bb6b-c1dcebfc7401
    port: 17001
    server: *************
    type: ss
  - cipher: chacha20-ietf-poly1305
    name: "\U0001F1FA\U0001F1F8美国6 | ⬇️ 12.5MB/s"
    password: 92e0a2cd-f842-42b6-84ef-dd2da5c711ac
    port: 44223
    server: 03.kill704.win
    type: ss
    udp: true
  - name: "\U0001F1FA\U0001F1F8美国7 | ⬇️ 8.5MB/s"
    password: c5eb2c2c-bc1d-11ed-a8bf-f23c91cfbbc9
    port: 15229
    server: a6b099d1-swrwg0-swzxpi-44k4.cu.plebai.net
    skip-cert-verify: true
    sni: a6b099d1-swrwg0-swzxpi-44k4.cu.plebai.net
    type: trojan
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1ED\U0001F1F0香港2 | ⬇️ 9.3MB/s"
    password: c86d483c-431f-41df-bb6b-c1dcebfc7401
    port: 16002
    server: ************
    type: ss
    udp: true
  - alterId: 64
    cipher: auto
    name: "\U0001F1EF\U0001F1F5日本2 | ⬇️ 8.3MB/s"
    network: ws
    port: 1443
    server: sslvpn.51job.com
    skip-cert-verify: true
    tls: true
    type: vmess
    uuid: a6a0d901-67e9-460a-90b5-634c5c4f9782
    ws-opts:
      headers:
        Host: centos7
      path: /634c5c4f9782
    servername: centos7
  - cipher: aes-256-gcm
    name: "\U0001F1ED\U0001F1F0香港3 | ⬇️ 11.4MB/s"
    password: 3030b609-f36f-4fd1-a4ef-a6e653780536
    port: 16004
    server: ************
    type: ss
  - cipher: aes-256-gcm
    name: "\U0001F1ED\U0001F1F0香港4 | ⬇️ 11.1MB/s"
    password: 3030b609-f36f-4fd1-a4ef-a6e653780536
    port: 16002
    server: *************
    type: ss
  - cipher: aes-256-gcm
    name: "\U0001F1F8\U0001F1EC新加坡1 | ⬇️ 10.3MB/s"
    password: 3030b609-f36f-4fd1-a4ef-a6e653780536
    port: 18004
    server: *************
    type: ss
  - cipher: aes-256-gcm
    name: "\U0001F1FA\U0001F1F8美国8 | ⬇️ 8.7MB/s"
    password: 3030b609-f36f-4fd1-a4ef-a6e653780536
    port: 20004
    server: *************
    type: ss
  - cipher: aes-256-gcm
    name: "\U0001F1FA\U0001F1F8美国9 | ⬇️ 8.1MB/s"
    password: 3030b609-f36f-4fd1-a4ef-a6e653780536
    port: 20002
    server: *************
    type: ss
  - alterId: 2
    cipher: auto
    name: "\U0001F1FA\U0001F1F8美国10 | ⬇️ 8.2MB/s"
    network: ws
    port: 22641
    server: tk.hzlt.tkddns.xyz
    skip-cert-verify: true
    tls: true
    type: vmess
    uuid: 98e96c9f-4bb3-39d4-9a2c-fac04257f7c7
    ws-opts:
      headers:
        Host: zxjp-a.tkong.cc
      path: /
    servername: zxjp-a.tkong.cc
  - cipher: aes-256-gcm
    name: "\U0001F1F9\U0001F1FC台湾2 | ⬇️ 11.0MB/s"
    password: 3030b609-f36f-4fd1-a4ef-a6e653780536
    port: 17004
    server: *************
    type: ss
  - cipher: aes-256-gcm
    name: "\U0001F1F9\U0001F1FC台湾3 | ⬇️ 11.0MB/s"
    password: c86d483c-431f-41df-bb6b-c1dcebfc7401
    port: 17005
    server: ************
    type: ss
  - alterId: 0
    cipher: auto
    name: "\U0001F300其他1-未识别 | ⬇️ 11.6MB/s"
    network: ws
    port: 80
    server: 9351cca4-e594-cbfa-a6de-3c1ee2315694.castlepeakhospital.moe
    skip-cert-verify: true
    tls: false
    type: vmess
    uuid: 14a8a0db-e922-453a-af8d-843af8734b3b
    ws-opts:
      headers:
        Host: tms.dingtalk.com
      path: /
  - alterId: 0
    cipher: auto
    name: "\U0001F300其他2-未识别 | ⬇️ 9.5MB/s"
    network: ws
    port: 80
    server: 0b17b6cf-2c23-f38e-114f-8f1f3bcd06a3.castlepeakhospital.moe
    skip-cert-verify: true
    tls: false
    type: vmess
    uuid: 14a8a0db-e922-453a-af8d-843af8734b3b
    ws-opts:
      headers:
        Host: tms.dingtalk.com
      path: /
  - alterId: 0
    cipher: auto
    name: "\U0001F300其他3-未识别 | ⬇️ 10.2MB/s"
    network: ws
    port: 80
    server: 0989a4f1-856a-4d0b-530b-aed9bf289ad8.castlepeakhospital.moe
    skip-cert-verify: true
    tls: false
    type: vmess
    uuid: 14a8a0db-e922-453a-af8d-843af8734b3b
    ws-opts:
      headers:
        Host: tms.dingtalk.com
      path: /
  - alterId: 0
    cipher: auto
    name: "\U0001F300其他4-未识别 | ⬇️ 13.3MB/s"
    network: ws
    port: 80
    server: 445522e0-a147-66c0-6b59-becb865f30a5.castlepeakhospital.moe
    skip-cert-verify: true
    tls: false
    type: vmess
    uuid: 14a8a0db-e922-453a-af8d-843af8734b3b
    ws-opts:
      headers:
        Host: tms.dingtalk.com
      path: /
  - alterId: 0
    cipher: auto
    name: "\U0001F300其他5-未识别 | ⬇️ 10.0MB/s"
    network: ws
    port: 80
    server: 06809388-302a-29e7-ec15-006f5353d530.castlepeakhospital.moe
    skip-cert-verify: true
    tls: false
    type: vmess
    uuid: 14a8a0db-e922-453a-af8d-843af8734b3b
    ws-opts:
      headers:
        Host: tms.dingtalk.com
      path: /
  - alterId: 0
    cipher: auto
    name: "\U0001F1F8\U0001F1EC新加坡2 | ⬇️ 13.7MB/s"
    network: ws
    port: 39903
    server: free-relay.themars.top
    skip-cert-verify: true
    tls: false
    type: vmess
    uuid: 781807c4-37df-4da3-9942-c6e82032399a
    ws-opts:
      headers:
        Host: www.cctv.com
      path: /cctv1.m3u8
  - alterId: 0
    cipher: auto
    name: "\U0001F1FA\U0001F1F8美国11 | ⬇️ 12.5MB/s"
    network: ws
    port: 20012
    server: 2fea6447-81c4-2d7d-d592-a4f729209777.7head.icu
    skip-cert-verify: true
    tls: false
    type: vmess
    uuid: 14a8a0db-e922-453a-af8d-843af8734b3b
    ws-opts:
      headers:
        Host: tms.dingtalk.com
      path: /
  - alterId: 0
    cipher: auto
    name: "\U0001F1FA\U0001F1F8美国12 | ⬇️ 11.5MB/s"
    network: ws
    port: 20014
    server: 2fea6447-81c4-2d7d-d592-a4f729209777.7head.icu
    skip-cert-verify: true
    tls: false
    type: vmess
    uuid: 14a8a0db-e922-453a-af8d-843af8734b3b
    ws-opts:
      headers:
        Host: tms.dingtalk.com
      path: /
  - alterId: 0
    cipher: auto
    name: "\U0001F1F9\U0001F1FC台湾4 | ⬇️ 11.7MB/s"
    network: ws
    port: 80
    server: 85ef02dd-b03e-94d5-61f4-bb5a8d5f491f.castlepeakhospital.moe
    skip-cert-verify: true
    tls: false
    type: vmess
    uuid: 14a8a0db-e922-453a-af8d-843af8734b3b
    ws-opts:
      headers:
        Host: tms.dingtalk.com
      path: /
  - alterId: 0
    cipher: auto
    name: "\U0001F1F0\U0001F1F7韩国1 | ⬇️ 12.7MB/s"
    network: ws
    port: 20000
    server: 2fea6447-81c4-2d7d-d592-a4f729209777.7head.icu
    skip-cert-verify: true
    tls: false
    type: vmess
    uuid: 14a8a0db-e922-453a-af8d-843af8734b3b
    ws-opts:
      headers:
        Host: tms.dingtalk.com
      path: /
  - alterId: 0
    cipher: auto
    name: "\U0001F300其他6-未识别 | ⬇️ 10.0MB/s"
    network: ws
    port: 20030
    server: 2fea6447-81c4-2d7d-d592-a4f729209777.7head.icu
    skip-cert-verify: true
    tls: false
    type: vmess
    uuid: 14a8a0db-e922-453a-af8d-843af8734b3b
    ws-opts:
      headers:
        Host: tms.dingtalk.com
      path: /
  - alterId: 0
    cipher: auto
    name: "\U0001F300其他7-LU | ⬇️ 11.8MB/s"
    network: ws
    port: 20046
    server: 2fea6447-81c4-2d7d-d592-a4f729209777.7head.icu
    skip-cert-verify: true
    tls: false
    type: vmess
    uuid: 14a8a0db-e922-453a-af8d-843af8734b3b
    ws-opts:
      headers:
        Host: tms.dingtalk.com
      path: /
  - alterId: 0
    cipher: auto
    name: "\U0001F1EB\U0001F1EE芬兰1 | ⬇️ 10.2MB/s"
    network: ws
    port: 20036
    server: 2fea6447-81c4-2d7d-d592-a4f729209777.7head.icu
    skip-cert-verify: true
    tls: false
    type: vmess
    uuid: 14a8a0db-e922-453a-af8d-843af8734b3b
    ws-opts:
      headers:
        Host: tms.dingtalk.com
      path: /
  - alterId: 0
    cipher: auto
    name: "\U0001F1FA\U0001F1E6乌克兰1 | ⬇️ 11.9MB/s"
    network: ws
    port: 20024
    server: 2fea6447-81c4-2d7d-d592-a4f729209777.7head.icu
    skip-cert-verify: true
    tls: false
    type: vmess
    uuid: 14a8a0db-e922-453a-af8d-843af8734b3b
    ws-opts:
      headers:
        Host: tms.dingtalk.com
      path: /
  - alterId: 0
    cipher: auto
    name: "\U0001F300其他8-TH | ⬇️ 12.8MB/s"
    network: ws
    port: 20006
    server: 2fea6447-81c4-2d7d-d592-a4f729209777.7head.icu
    skip-cert-verify: true
    tls: false
    type: vmess
    uuid: 14a8a0db-e922-453a-af8d-843af8734b3b
    ws-opts:
      headers:
        Host: tms.dingtalk.com
      path: /
  - client-fingerprint: chrome
    name: "\U0001F1ED\U0001F1F0香港5 | ⬇️ 14.9MB/s"
    password: 2c605663-b89a-5734-a9d6-97d4743d72cf
    port: 8313
    server: **************
    skip-cert-verify: true
    type: trojan
    udp: true
  - name: "\U0001F1EB\U0001F1EE芬兰2 | ⬇️ 9.1MB/s"
    network: ws
    port: 8880
    server: ************
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
        User-Agent: >-
          Mozilla/5.0 (Linux; Android 6.0.1; SM-G532M Build/MMB29T)
          AppleWebKit/537.36 (KHTML, like Gecko) Chrome/55.0.2883.91 Mobile
          Safari/537.36
      path: "/Telegram\U0001F1E8\U0001F1F3 @pgkj666 /?ed=2560"
    xudp: true
  - name: "\U0001F1E9\U0001F1EA德国1 | ⬇️ 9.9MB/s"
    network: ws
    port: 8880
    server: ***********
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
        User-Agent: >-
          Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like
          Gecko) Chrome/51.0.2704.106 Safari/537.36
      path: "/Telegram\U0001F1E8\U0001F1F3 @pgkj666 /?ed=2560"
    xudp: true
  - name: "\U0001F1FA\U0001F1F8美国13 | ⬇️ 8.8MB/s"
    network: ws
    port: 8880
    server: ************
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
        User-Agent: >-
          Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML,
          like Gecko) Chrome/55.0.2883.87 Safari/537.36
      path: "/Telegram\U0001F1E8\U0001F1F3 @pgkj666 /?ed=2560"
    xudp: true
  - name: "\U0001F1F0\U0001F1F7韩国2 | ⬇️ 12.6MB/s"
    network: ws
    port: 8880
    server: ************
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
        User-Agent: >-
          Mozilla/5.0 (Windows NT 6.0; Win64; x64) AppleWebKit/537.36 (KHTML,
          like Gecko) Chrome/55.0.2883.87 Safari/537.36
      path: "/Telegram\U0001F1E8\U0001F1F3 @pgkj666 /?ed=2560"
    xudp: true
  - down: ''
    fingerprint: ''
    name: "\U0001F1F0\U0001F1F7韩国3 | ⬇️ 9.5MB/s"
    obfs: ''
    obfs-password: ''
    password: 75e98355-345b-4413-8001-************
    port: 44005
    server: **************
    skip-cert-verify: true
    sni: www.bing.com
    type: hysteria2
    up: ''
  - down: ''
    fingerprint: ''
    name: "\U0001F1EF\U0001F1F5日本3 | ⬇️ 7.9MB/s"
    obfs: ''
    obfs-password: ''
    password: 0A1DDFE6-A16A-43FA-A6E3-70EDE4EA61C3
    port: 7270
    server: jp4.dexlos.com
    skip-cert-verify: false
    sni: jp4.dexlos.com
    type: hysteria2
    up: ''
  - client-fingerprint: chrome
    name: "\U0001F1ED\U0001F1F0香港6 | ⬇️ 8.5MB/s"
    password: e2241e38-d5d4-4f60-afcd-bf7d6bfb6668
    port: 44446
    server: 4c48724e1f11cf0912ab5b04e0a2391b.v1.cac.node-is.green
    skip-cert-verify: true
    sni: hk13.bilibili.com
    type: trojan
    udp: true
  - alterId: '0'
    cipher: auto
    name: "\U0001F300其他9-未识别 | ⬇️ 13.1MB/s"
    network: ws
    port: 20012
    server: 2fea6447-81c4-2d7d-d592-a4f729209777.7head.icu
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 21c90669-193d-49d3-8e37-f5c1462eb134
    ws-opts:
      headers:
        Host: tms.dingtalk.com
      path: /
    xudp: true
  - name: "\U0001F1E9\U0001F1EA德国2 | ⬇️ 12.5MB/s"
    network: ws
    port: 8880
    server: ***********
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
        User-Agent: >-
          Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like
          Gecko) Chrome/50.0.2661.94 Safari/537.36
      path: /?ed=2560
    xudp: true
    servername: reedfree8mahsang2.redorg.ir
  - name: "\U0001F300其他10-未识别 | ⬇️ 10.2MB/s"
    network: ws
    port: 80
    server: *************
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
        User-Agent: >-
          Mozilla/5.0 (Windows NT 6.1) AppleWebKit/537.36 (KHTML, like Gecko)
          Chrome/70.0.3538.77 Safari/537.36
      path: '/Telegram:@vpnAndroid2/?ed=2560'
    xudp: true
    servername: reedfree8mahsang2.redorg.ir
port: 7890
socks-port: 7891
redir-port: 7892
mixed-port: 7893
tproxy-port: 7894
ipv6: false
allow-lan: true
unified-delay: true
tcp-concurrent: true
geodata-mode: false
geodata-loader: standard
geo-auto-update: true
geo-update-interval: 48
geox-url:
  geoip: 'https://testingcf.jsdelivr.net/gh/MetaCubeX/meta-rules-dat@release/geoip.dat'
  geosite: >-
    https://testingcf.jsdelivr.net/gh/MetaCubeX/meta-rules-dat@release/geosite.dat
  mmdb: >-
    https://testingcf.jsdelivr.net/gh/MetaCubeX/meta-rules-dat@release/country.mmdb
  asn: >-
    https://github.com/xishang0128/geoip/releases/download/latest/GeoLite2-ASN.mmdb
profile:
  store-selected: true
  store-fake-ip: true
sniffer:
  enable: true
  sniff:
    HTTP:
      ports:
        - 80
        - 8080-8880
      override-destination: true
    TLS:
      ports:
        - 443
        - 8443
    QUIC:
      ports:
        - 443
        - 8443
  force-domain:
    - +.v2ex.com
  skip-domain:
    - Mijia Cloud
    - dlg.io.mi.com
    - +.push.apple.com
    - +.apple.com
dns:
  enable: true
  listen: '0.0.0.0:1053'
  ipv6: false
  respect-rules: true
  enhanced-mode: fake-ip
  fake-ip-range: ********/8
  fake-ip-filter-mode: blacklist
  fake-ip-filter:
    - +.lan
    - +.local
    - 'geosite:private'
    - 'geosite:cn'
  default-nameserver:
    - *********
    - ************
  proxy-server-nameserver:
    - *********
    - ************
  nameserver:
    - *********
    - ************
  nameserver-policy:
    'rule-set:private_domain,cn_domain':
      - *********
      - ************
    'rule-set:geolocation-!cn':
      - 'https://dns.cloudflare.com/dns-query'
      - 'https://dns.google/dns-query'
pr:
  type: select
  proxies:
    - "\U0001F680 节点选择"
    - "\U0001F1ED\U0001F1F0 香港负载均衡"
    - "\U0001F1EF\U0001F1F5 日本负载均衡"
    - "\U0001F1F0\U0001F1F7 韩国负载均衡"
    - "\U0001F1F8\U0001F1EC 新加坡负载均衡"
    - "\U0001F1FA\U0001F1F8 美国负载均衡"
    - "\U0001F1ED\U0001F1F0 香港自动"
    - "\U0001F1EF\U0001F1F5 日本自动"
    - "\U0001F1F0\U0001F1F7 韩国自动"
    - "\U0001F1F8\U0001F1EC 新加坡自动"
    - "\U0001F1FA\U0001F1F8 美国自动"
    - ♻️ 自动选择
    - "\U0001F1ED\U0001F1F0 香港节点"
    - "\U0001F1EF\U0001F1F5 日本节点"
    - "\U0001F1F0\U0001F1F7 韩国节点"
    - "\U0001F1F8\U0001F1EC 新加坡节点"
    - "\U0001F1FA\U0001F1F8 美国节点"
    - "\U0001F310 全部节点"
proxy-groups:
  - name: "\U0001F310 全部节点"
    type: select
    include-all: true
  - name: "\U0001F680 节点选择"
    type: select
    proxies:
      - "\U0001F1ED\U0001F1F0 香港负载均衡"
      - "\U0001F1EF\U0001F1F5 日本负载均衡"
      - "\U0001F1F0\U0001F1F7 韩国负载均衡"
      - "\U0001F1F8\U0001F1EC 新加坡负载均衡"
      - "\U0001F1FA\U0001F1F8 美国负载均衡"
      - "\U0001F1ED\U0001F1F0 香港自动"
      - "\U0001F1EF\U0001F1F5 日本自动"
      - "\U0001F1F0\U0001F1F7 韩国自动"
      - "\U0001F1F8\U0001F1EC 新加坡自动"
      - "\U0001F1FA\U0001F1F8 美国自动"
      - ♻️ 自动选择
      - "\U0001F1ED\U0001F1F0 香港节点"
      - "\U0001F1EF\U0001F1F5 日本节点"
      - "\U0001F1F0\U0001F1F7 韩国节点"
      - "\U0001F1F8\U0001F1EC 新加坡节点"
      - "\U0001F1FA\U0001F1F8 美国节点"
      - "\U0001F310 全部节点"
  - name: "\U0001F4AC ChatGPT"
    type: select
    proxies:
      - "\U0001F680 节点选择"
      - "\U0001F1ED\U0001F1F0 香港负载均衡"
      - "\U0001F1EF\U0001F1F5 日本负载均衡"
      - "\U0001F1F0\U0001F1F7 韩国负载均衡"
      - "\U0001F1F8\U0001F1EC 新加坡负载均衡"
      - "\U0001F1FA\U0001F1F8 美国负载均衡"
      - "\U0001F1ED\U0001F1F0 香港自动"
      - "\U0001F1EF\U0001F1F5 日本自动"
      - "\U0001F1F0\U0001F1F7 韩国自动"
      - "\U0001F1F8\U0001F1EC 新加坡自动"
      - "\U0001F1FA\U0001F1F8 美国自动"
      - ♻️ 自动选择
      - "\U0001F1ED\U0001F1F0 香港节点"
      - "\U0001F1EF\U0001F1F5 日本节点"
      - "\U0001F1F0\U0001F1F7 韩国节点"
      - "\U0001F1F8\U0001F1EC 新加坡节点"
      - "\U0001F1FA\U0001F1F8 美国节点"
      - "\U0001F310 全部节点"
  - name: "\U0001F4FA YouTube"
    type: select
    proxies:
      - "\U0001F680 节点选择"
      - "\U0001F1ED\U0001F1F0 香港负载均衡"
      - "\U0001F1EF\U0001F1F5 日本负载均衡"
      - "\U0001F1F0\U0001F1F7 韩国负载均衡"
      - "\U0001F1F8\U0001F1EC 新加坡负载均衡"
      - "\U0001F1FA\U0001F1F8 美国负载均衡"
      - "\U0001F1ED\U0001F1F0 香港自动"
      - "\U0001F1EF\U0001F1F5 日本自动"
      - "\U0001F1F0\U0001F1F7 韩国自动"
      - "\U0001F1F8\U0001F1EC 新加坡自动"
      - "\U0001F1FA\U0001F1F8 美国自动"
      - ♻️ 自动选择
      - "\U0001F1ED\U0001F1F0 香港节点"
      - "\U0001F1EF\U0001F1F5 日本节点"
      - "\U0001F1F0\U0001F1F7 韩国节点"
      - "\U0001F1F8\U0001F1EC 新加坡节点"
      - "\U0001F1FA\U0001F1F8 美国节点"
      - "\U0001F310 全部节点"
  - name: "\U0001F3B5 TikTok"
    type: select
    proxies:
      - "\U0001F680 节点选择"
      - "\U0001F1ED\U0001F1F0 香港负载均衡"
      - "\U0001F1EF\U0001F1F5 日本负载均衡"
      - "\U0001F1F0\U0001F1F7 韩国负载均衡"
      - "\U0001F1F8\U0001F1EC 新加坡负载均衡"
      - "\U0001F1FA\U0001F1F8 美国负载均衡"
      - "\U0001F1ED\U0001F1F0 香港自动"
      - "\U0001F1EF\U0001F1F5 日本自动"
      - "\U0001F1F0\U0001F1F7 韩国自动"
      - "\U0001F1F8\U0001F1EC 新加坡自动"
      - "\U0001F1FA\U0001F1F8 美国自动"
      - ♻️ 自动选择
      - "\U0001F1ED\U0001F1F0 香港节点"
      - "\U0001F1EF\U0001F1F5 日本节点"
      - "\U0001F1F0\U0001F1F7 韩国节点"
      - "\U0001F1F8\U0001F1EC 新加坡节点"
      - "\U0001F1FA\U0001F1F8 美国节点"
      - "\U0001F310 全部节点"
  - name: "\U0001F3AC NETFLIX"
    type: select
    proxies:
      - "\U0001F680 节点选择"
      - "\U0001F1ED\U0001F1F0 香港负载均衡"
      - "\U0001F1EF\U0001F1F5 日本负载均衡"
      - "\U0001F1F0\U0001F1F7 韩国负载均衡"
      - "\U0001F1F8\U0001F1EC 新加坡负载均衡"
      - "\U0001F1FA\U0001F1F8 美国负载均衡"
      - "\U0001F1ED\U0001F1F0 香港自动"
      - "\U0001F1EF\U0001F1F5 日本自动"
      - "\U0001F1F0\U0001F1F7 韩国自动"
      - "\U0001F1F8\U0001F1EC 新加坡自动"
      - "\U0001F1FA\U0001F1F8 美国自动"
      - ♻️ 自动选择
      - "\U0001F1ED\U0001F1F0 香港节点"
      - "\U0001F1EF\U0001F1F5 日本节点"
      - "\U0001F1F0\U0001F1F7 韩国节点"
      - "\U0001F1F8\U0001F1EC 新加坡节点"
      - "\U0001F1FA\U0001F1F8 美国节点"
      - "\U0001F310 全部节点"
  - name: "\U0001F1ED\U0001F1F0 香港节点"
    type: select
    include-all: true
    filter: (?i)港|hk|hongkong|hong kong
  - name: "\U0001F1EF\U0001F1F5 日本节点"
    type: select
    include-all: true
    filter: (?i)日|jp|japan
  - name: "\U0001F1F0\U0001F1F7 韩国节点"
    type: select
    include-all: true
    filter: (?i)韩|kr|korea
  - name: "\U0001F1F8\U0001F1EC 新加坡节点"
    type: select
    include-all: true
    filter: (?i)新|狮|sg|singapore|新加坡
  - name: "\U0001F1FA\U0001F1F8 美国节点"
    type: select
    include-all: true
    filter: (?i)美|us|unitedstates|united states
  - name: "\U0001F1ED\U0001F1F0 香港自动"
    type: url-test
    include-all: true
    tolerance: 20
    interval: 300
    filter: (?=.*(港|HK|(?i)Hong))^((?!(台|日|韩|新|深|美)).)*$
  - name: "\U0001F1EF\U0001F1F5 日本自动"
    type: url-test
    include-all: true
    tolerance: 20
    interval: 300
    filter: (?=.*(日|JP|(?i)Japan))^((?!(港|台|韩|新|美)).)*$
  - name: "\U0001F1F0\U0001F1F7 韩国自动"
    type: url-test
    include-all: true
    tolerance: 20
    interval: 300
    filter: (?=.*(韩|KR|(?i)Korea))^((?!(港|台|日|新|美)).)*$
  - name: "\U0001F1F8\U0001F1EC 新加坡自动"
    type: url-test
    include-all: true
    tolerance: 20
    interval: 300
    filter: (?=.*(新|狮|SG|(?i)Singapore))^((?!(港|台|日|韩|美)).)*$
  - name: "\U0001F1FA\U0001F1F8 美国自动"
    type: url-test
    include-all: true
    tolerance: 20
    interval: 300
    filter: (?=.*(美|US|(?i)States|America))^((?!(港|台|日|韩|新)).)*$
  - name: ♻️ 自动选择
    type: url-test
    include-all: true
    tolerance: 20
    interval: 300
    filter: ^((?!(直连)).)*$
  - name: "\U0001F1ED\U0001F1F0 香港负载均衡"
    type: load-balance
    include-all: true
    interval: 60
    lazy: true
    url: 'http://www.google.com/blank.html'
    disable-udp: false
    strategy: round-robin
    timeout: 2000
    max-failed-times: 3
    filter: (?=.*(港|HK|(?i)Hong))^((?!(台|日|韩|新|深|美)).)*$
  - name: "\U0001F1EF\U0001F1F5 日本负载均衡"
    type: load-balance
    include-all: true
    interval: 60
    lazy: true
    url: 'http://www.google.com/blank.html'
    disable-udp: false
    strategy: round-robin
    timeout: 2000
    max-failed-times: 3
    filter: (?=.*(日|JP|(?i)Japan))^((?!(港|台|韩|新|美)).)*$
  - name: "\U0001F1F0\U0001F1F7 韩国负载均衡"
    type: load-balance
    include-all: true
    interval: 60
    lazy: true
    url: 'http://www.google.com/blank.html'
    disable-udp: false
    strategy: round-robin
    timeout: 2000
    max-failed-times: 3
    filter: (?=.*(韩|KR|(?i)Korea))^((?!(港|台|日|新|美)).)*$
  - name: "\U0001F1F8\U0001F1EC 新加坡负载均衡"
    type: load-balance
    include-all: true
    interval: 60
    lazy: true
    url: 'http://www.google.com/blank.html'
    disable-udp: false
    strategy: round-robin
    timeout: 2000
    max-failed-times: 3
    filter: (?=.*(新|狮|SG|(?i)Singapore))^((?!(港|台|日|韩|美)).)*$
  - name: "\U0001F1FA\U0001F1F8 美国负载均衡"
    type: load-balance
    include-all: true
    interval: 60
    lazy: true
    url: 'http://www.google.com/blank.html'
    disable-udp: false
    strategy: round-robin
    timeout: 2000
    max-failed-times: 3
    filter: (?=.*(美|US|(?i)States|America))^((?!(港|台|日|韩|新)).)*$
  - name: "\U0001F3AF 全球直连"
    type: select
    proxies:
      - DIRECT
      - "\U0001F680 节点选择"
  - name: "\U0001F420 漏网之鱼"
    type: select
    proxies:
      - "\U0001F680 节点选择"
      - "\U0001F1ED\U0001F1F0 香港负载均衡"
      - "\U0001F1EF\U0001F1F5 日本负载均衡"
      - "\U0001F1F0\U0001F1F7 韩国负载均衡"
      - "\U0001F1F8\U0001F1EC 新加坡负载均衡"
      - "\U0001F1FA\U0001F1F8 美国负载均衡"
      - "\U0001F1ED\U0001F1F0 香港自动"
      - "\U0001F1EF\U0001F1F5 日本自动"
      - "\U0001F1F0\U0001F1F7 韩国自动"
      - "\U0001F1F8\U0001F1EC 新加坡自动"
      - "\U0001F1FA\U0001F1F8 美国自动"
      - ♻️ 自动选择
      - "\U0001F1ED\U0001F1F0 香港节点"
      - "\U0001F1EF\U0001F1F5 日本节点"
      - "\U0001F1F0\U0001F1F7 韩国节点"
      - "\U0001F1F8\U0001F1EC 新加坡节点"
      - "\U0001F1FA\U0001F1F8 美国节点"
      - "\U0001F310 全部节点"
rules:
  - 'RULE-SET,BanAD,REJECT'
  - 'RULE-SET,BanProgramAD,REJECT'
  - 'RULE-SET,adobe,REJECT'
  - "RULE-SET,youtube_domain,\U0001F4FA YouTube"
  - 'RULE-SET,tencent,DIRECT'
  - 'RULE-SET,private_domain,DIRECT'
  - 'RULE-SET,TencentVideo,DIRECT'
  - "RULE-SET,apple_domain,\U0001F3AF 全球直连"
  - "RULE-SET,ai,\U0001F4AC ChatGPT"
  - "RULE-SET,Spotify,\U0001F680 节点选择"
  - "RULE-SET,github_domain,\U0001F680 节点选择"
  - "RULE-SET,google_domain,\U0001F680 节点选择"
  - "RULE-SET,onedrive_domain,\U0001F3AF 全球直连"
  - "RULE-SET,microsoft_domain,\U0001F3AF 全球直连"
  - "RULE-SET,tiktok_domain,\U0001F3B5 TikTok"
  - "RULE-SET,speedtest_domain,\U0001F680 节点选择"
  - "RULE-SET,telegram_domain,\U0001F680 节点选择"
  - "RULE-SET,netflix_domain,\U0001F3AC NETFLIX"
  - "RULE-SET,Netflix,\U0001F3AC NETFLIX"
  - "RULE-SET,paypal_domain,\U0001F680 节点选择"
  - "RULE-SET,geolocation-!cn,\U0001F680 节点选择"
  - "RULE-SET,cn_domain,\U0001F3AF 全球直连"
  - "RULE-SET,google_ip,\U0001F680 节点选择,no-resolve"
  - "RULE-SET,netflix_ip,\U0001F3AC NETFLIX,no-resolve"
  - "RULE-SET,telegram_ip,\U0001F680 节点选择,no-resolve"
  - "RULE-SET,cn_ip,\U0001F3AF 全球直连"
  - "RULE-SET,proxylite,\U0001F680 节点选择"
  - "MATCH,\U0001F420 漏网之鱼"
rule-anchor:
  ip:
    type: http
    interval: 86400
    behavior: ipcidr
    format: mrs
  domain:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
  qcy:
    type: http
    interval: 86400
    behavior: domain
    format: text
  class:
    type: http
    interval: 86400
    behavior: classical
    format: text
rule-providers:
  BanAD:
    type: http
    interval: 86400
    behavior: classical
    format: text
    url: 'https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/Clash/BanAD.list'
  tencent:
    type: http
    interval: 86400
    behavior: classical
    format: text
    url: >-
      https://cdn.jsdelivr.net/gh/blackmatrix7/ios_rule_script@master/rule/Shadowrocket/Tencent/Tencent.list
  TencentVideo:
    type: http
    interval: 86400
    behavior: classical
    format: text
    url: >-
      https://cdn.jsdelivr.net/gh/blackmatrix7/ios_rule_script@master/rule/Shadowrocket/TencentVideo/TencentVideo.list
  BanProgramAD:
    type: http
    interval: 86400
    behavior: classical
    format: text
    url: >-
      https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/Clash/BanProgramAD.list
  adobe:
    type: http
    interval: 86400
    behavior: classical
    format: text
    url: 'https://raw.githubusercontent.com/cmliu/ACL4SSR/main/Clash/adobe.list'
  private_domain:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/private.mrs
  ai:
    type: http
    interval: 86400
    behavior: classical
    format: text
    url: >-
      https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/a98edee7cd64d64cb58ce26d63d2dbd2475575ad/rule/Clash/OpenAI/OpenAI.list
  Spotify:
    type: http
    interval: 86400
    behavior: classical
    format: text
    url: >-
      https://cdn.jsdelivr.net/gh/blackmatrix7/ios_rule_script@release/rule/Shadowrocket/Spotify/Spotify.list
  Netflix:
    type: http
    interval: 86400
    behavior: classical
    format: text
    url: >-
      https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/a98edee7cd64d64cb58ce26d63d2dbd2475575ad/rule/Clash/Netflix/Netflix.list
  youtube_domain:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/youtube.mrs
  google_domain:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/google.mrs
  github_domain:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/github.mrs
  telegram_domain:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/telegram.mrs
  netflix_domain:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/netflix.mrs
  paypal_domain:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/paypal.mrs
  onedrive_domain:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/onedrive.mrs
  microsoft_domain:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/microsoft.mrs
  apple_domain:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/apple-cn.mrs
  speedtest_domain:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/ookla-speedtest.mrs
  tiktok_domain:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/tiktok.mrs
  gfw_domain:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/gfw.mrs
  geolocation-!cn:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/geolocation-!cn.mrs
  cn_domain:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/cn.mrs
  proxylite:
    type: http
    interval: 86400
    behavior: classical
    format: text
    url: >-
      https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/refs/heads/master/rule/Clash/ProxyLite/ProxyLite.list
  cn_ip:
    type: http
    interval: 86400
    behavior: ipcidr
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geoip/cn.mrs
  google_ip:
    type: http
    interval: 86400
    behavior: ipcidr
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geoip/google.mrs
  telegram_ip:
    type: http
    interval: 86400
    behavior: ipcidr
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geoip/telegram.mrs
  netflix_ip:
    type: http
    interval: 86400
    behavior: ipcidr
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geoip/netflix.mrs
