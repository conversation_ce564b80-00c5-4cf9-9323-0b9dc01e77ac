# URL解码修复说明

## 问题描述

在转换URL格式和Base64格式代理节点时，发现有些URL编码没有被正确转换。例如：

```yaml
password: "M2JmZjZkMWQyOGE0Yjg2NQ%3D%3D%3AYjc1MWU5OTUtYjI0Yy00Zg%3D%3D"
```

其中的`%3D`应该解码为`=`，这是Base64编码中常见的填充字符。

## 修复内容

### 1. 新增URL解码工具函数

在 `src/utils/index.js` 中添加了以下函数：

- `safeDecodeURIComponent(str)` - 安全的URL解码函数
- `hasUrlEncoding(str)` - 检测字符串是否包含URL编码
- `smartUrlDecode(str)` - 智能URL解码，只对包含URL编码的字符串进行解码

### 2. 修复Shadowsocks解析器

在 `src/parsers/shadowsocks.js` 中：

- 导入 `smartUrlDecode` 函数
- 在解析密码时应用URL解码
- 在解析节点名称时应用URL解码

### 3. 修复Trojan解析器

在 `src/parsers/trojan.js` 中：

- 导入 `smartUrlDecode` 函数
- 在解析密码时应用URL解码
- 在解析节点名称时应用URL解码

### 4. 修复ShadowsocksR解析器

在 `src/parsers/shadowsocksr.js` 中：

- 导入 `smartUrlDecode` 函数
- 在解析密码时应用URL解码
- 在解析参数时应用URL解码

## 修复效果

### 修复前
```yaml
password: "M2JmZjZkMWQyOGE0Yjg2NQ%3D%3D%3AYjc1MWU5OTUtYjI0Yy00Zg%3D%3D"
```

### 修复后
```yaml
password: "M2JmZjZkMWQyOGE0Yjg2NQ==:Yjc1MWU5OTUtYjI0Yy00Zg=="
```

## 测试验证

创建了 `test-url-decode.js` 测试文件，验证了以下场景：

### 1. URL解码功能测试
- ✅ 正常字符串 - 保持不变
- ✅ URL编码的Base64 - 正确解码`%3D`为`=`
- ✅ 包含特殊字符 - 正确解码`%40`为`@`，`%23`为`#`
- ✅ 中文字符 - 正确解码中文URL编码

### 2. Shadowsocks URL解析测试
- ✅ 密码URL解码正确
- ✅ 节点名称URL解码正确

### 3. Trojan URL解析测试
- ✅ 密码URL解码正确
- ✅ 节点名称URL解码正确

## 支持的URL编码字符

修复后的工具支持解码以下URL编码字符：

- `%3D` → `=` (Base64填充字符)
- `%40` → `@` (邮箱符号)
- `%23` → `#` (井号)
- `%3A` → `:` (冒号)
- `%2F` → `/` (斜杠)
- `%3F` → `?` (问号)
- `%26` → `&` (和号)
- 以及所有其他标准URL编码字符

## 安全性

- 使用 `try-catch` 包装解码操作，确保解码失败时不会崩溃
- 只对包含URL编码模式的字符串进行解码，避免不必要的处理
- 保持向后兼容性，不影响已经正确的节点配置

## 影响范围

此修复影响以下协议的解析：

1. **Shadowsocks (SS)** - 密码和节点名称的URL解码
2. **Trojan** - 密码和节点名称的URL解码
3. **ShadowsocksR (SSR)** - 密码、参数和节点名称的URL解码

其他协议（VMess、VLESS等）主要使用JSON格式，通常不受URL编码问题影响。

## 使用方法

修复已自动集成到现有的处理流程中，用户无需额外操作：

```bash
# 处理包含URL编码的订阅文件
npm run process

# 或使用交互式菜单
npm run menu
```

修复后的工具会自动检测并正确解码URL编码的字符，确保生成的配置文件格式正确。
