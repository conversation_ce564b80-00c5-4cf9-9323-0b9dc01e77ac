<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8" />
  <meta name="version" content="__SUB_STORE_FRONT_END_VERSION__">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
  <!-- <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate" />
  <meta http-equiv="Pragma" content="no-cache" /> -->
  <meta http-equiv="Expires" content="0" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta name="viewport"
    content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
  <title>Sub Store</title>
  <meta name="description" content="A sub-converter running in a Progressive Web App" />
  <link rel="icon" href="/favicon.svg" type="image/svg+xml" />
  <link rel="alternate icon" href="/favicon.ico" type="image/png" sizes="16x16" />
  <link rel="apple-touch-icon" href="/apple-touch-icon.png" sizes="180x180" />
  <link rel="mask-icon" href="/favicon.svg" color="#FFFFFF" />
  <meta id="theme__color" name="theme-color" content="#000" />
  <link rel="manifest" href="/manifests.json" />
  <style>
    html {
      width: 100%;
      height: 100vh;
      overflow-x: hidden;
      overflow-y: auto;
    }

    body {
      background: #f2f2f2;
    }

    @media (prefers-color-scheme: dark) {
      body {
        background: #000;
      }
    }
  </style>
</head>

<body>
  <div id="app"></div>
  <div style='z-index:9999;position: relative' id='ztop'></div>
  <script type="module" async src="/src/splash.ts"></script>
  <noscript>
    <strong>We're sorry but <%= htmlWebpackPlugin.options.title %> doesn't work
        properly without JavaScript enabled. Please enable it to
        continue.</strong>
  </noscript>
</body>

</html>
