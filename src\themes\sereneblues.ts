export default {
  meta: {
    name: '静谧蓝',
    author: 'Keywos',
    label: 'dark',
    extend: 'dark',
  },
  colors: {
    // 全局高亮色
    'primary-color': '#6f9cc5',
    'primary-color-end': '#8389c8',
    'second-color': '#c5565aad',
    'third-color': '#70b496',

    'danger-color': '#c16058',
    'succeed-color': '#49bb88',

    // icon色
    'icon-nav-bar-right': '#5b5f67',
    'unimportant-icon-color': '#FFFFFF34',

    // 组件色
    'status-bar-background-color': '#353642',
    'background-color': '#353642',
    'nav-bar-color': '#353642',
    'tab-bar-color': '#353642cf',
    'popup-color': '#444654',
    'divider-color': '#FFFFFF08',
    'card-color': '#444654',
    'dialog-color': '#444654',
    'switch-close-background-color': '#FFFFFF14',
    'switch-active-background-color': '#478EF2',
    'compare-item-background-color': '#444654',
    'picker-mask-near-color': '#353642cf',
    'picker-mask-far-color': '#353642',

    // 文字色
    'primary-text-color': '#bcbac1',
    'second-text-color': '#FFFFFFBB',
    'comment-text-color': '#FFFFFF88',
    'lowest-text-color': '#FFFFFF36',

    // 其他
    'img-brightness': '100',
    'nav-bar-blur': '16px',
    'tab-bar-blur': '16px',
    'sticky-title-blur': '16px',

    'compare-tag-text-color': '#FFFFFF88',
    'compare-tag-background-color': '#FFFFFF22',
  },
};