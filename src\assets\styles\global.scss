// remove default styles
body {
  outline: none;
  -webkit-tap-highlight-color: transparent;
  min-height: 100% !important;
}

a {
  color: inherit;
  text-decoration: none;
}

textarea,
input,
button {
  font-family: inherit;
  color: inherit;
}

ul,
ol {
  list-style: none;
  padding-inline-start: 0;
}

table {
  border-collapse: collapse;
  border-spacing: 0;
}

img {
  pointer-events: none;
}

*::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

*::-webkit-scrollbar-corner {
  background-color: transparent;
}

/* Light mode */
@media (prefers-color-scheme: light) {
  *::-webkit-scrollbar-thumb {
    background: #c0c1c58f;
    border-radius: 5px;
  }

  *::-webkit-scrollbar-thumb:hover {
    background: #c0c1c550;
  }
}

/* Dark mode */
@media (prefers-color-scheme: dark) {
  *::-webkit-scrollbar-thumb {
    background: #c0c1c550;
    border-radius: 5px;
  }

  *::-webkit-scrollbar-thumb:hover {
    background: #c0c1c58f;
  }
}

//collapse
.nut-collapse-item {
  .collapse-item.collapse-item {
    background-color: var(--card-color);

    &::after {
      border-color: var(--divider-color);
    }
  }

  .collapse-wrapper {
    background-color: var(--card-color);
  }
}

// tab 切换面板
.tab-panel-wrapper {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translateX(-50%) translateY(-50%);
  z-index: 1100;
  max-height: 95vh;
  max-width: 80vw;
  width: 80vw;
  border-radius: 8px;
  overflow: auto;
  padding-bottom: 24px;
}
.nut-button {
  margin-right: 10px;
}

// 顶部粘性定位标题样式
.sticky-title-wrapperse {
  position: sticky;
  display: block;
  z-index: 1;
  top: 0;
  font-size: 16px;
  font-weight: bold;
  line-height: 2;
}
// .nut-icon-order:before {
//   content: "\e697";
// }
// .nut-icon-edit:before {
//   content: "\e667";
// }
// .nut-icon-find:before {
//   content: "\e661";
// }

.nut-icon-shop:before {
  content: "\e688";
  cursor: pointer;
}
.nut-icon-tips:before {
  content: "\e671";
  cursor: pointer;
}
.nut-icon-category:before {
  font-size: 20px;
  content: "\e662";
}
.nut-icon-issue:before {
  content: '\e6b1';
}
.nut-icon-ask:before {
  content: '\e670';
}
.nut-icon-setting:before {
  content: '\e681';
}
.nut-icon-left:before {
  content: '\e6c9';
}
.nut-icon-rect-left:before {
  content: '\e6c9';
}

.nut-icon-right:before {
  content: '\e6cb';
  color: #666;
}
.nut-icon-rect-right:before {
  content: '\e6cb'; // \2003 占位符
  // color: var(--lowest-text-color); // 更多设置
}
.nut-icon-rect-down:before {
  content: "\e6cc";
}

.nut-icon-triangle-down:before {
  content: "\eb6d";
}

.nut-icon-triangle-up:before {
  content: "\eb6e";
}
.nut-icon-arrow-right2:before {
  content: '\e6a9';
}
.nut-icon-arrow-right:before {
  content: '\e6a3';
}
.nut-icon-link:before {
  content: '\e67c'; // link
}
.nut-icon-refresh2:before {
  content: '\e682'; // sync
}
.nut-icon-refresh:before {
  content: '\e690';
}
.nut-icon-close-little:before {
  content: '\e6aa';
}
.nut-icon-close:before {
  content: '\e674';
}
.nut-icon-mask-close:before {
  content: '\e6cd';
}
.nut-icon-circle-close:before {
  content: '\e699';
}
.nut-icon-checked:before {
  content: '\e601';
}

.nut-icon-checklist:before {
  content: '\e6c8';
}

.nut-icon-check-disabled:before {
  content: '\e6c7';
}

.nut-icon-check-normal:before {
  content: '\e6c5';
}

.nut-icon-check-checked:before {
  content: '\e6c6';
}

.nut-icon-check-checked:before {
  content: '\e6c6';
}

.nut-icon-loading:before {
  content: '\e6c4';
}

.nut-icon-loading1:before {
  content: '\e614';
}

// keyfrom nutui scss
.nut-icon {
  display: inline-block;
  position: relative;
  width: $icon-width;
  height: $icon-height;
  line-height: $icon-line-height;
  text-align: right;

  &::before {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }

  &__img {
    width: $icon-width;
    height: $icon-height;
    object-fit: contain;
  }
  &-loading,
  &-loading1 {
    display: inline-block;
    animation: rotation 1s infinite linear;
  }

  --animate-duration: 1s;
  --animate-delay: 0s;

  &-am-infinite {
    -webkit-animation-iteration-count: infinite;
    -webkit-animation-direction: alternate;
    animation-iteration-count: infinite;
    animation-direction: alternate;
  }
  &-am-jump {
    -webkit-animation-name: nutJumpOne;
    -webkit-animation-duration: var(--animate-duration);
    -webkit-animation-timing-function: ease;
    -webkit-animation-delay: var(--animate-delay);

    animation-name: nutJumpOne;
    animation-duration: var(--animate-duration);
    animation-timing-function: ease;
    animation-delay: var(--animate-delay);

    &.nut-icon-am-infinite {
      -webkit-animation-name: nutJump;
      animation-name: nutJump;
    }
  }

  &-am-rotate {
    -webkit-animation-name: rotation;
    -webkit-animation-duration: var(--animate-duration);
    -webkit-animation-timing-function: linear;
    -webkit-animation-delay: var(--animate-delay);

    animation-name: rotation;
    animation-duration: var(--animate-duration);
    animation-timing-function: linear;
    animation-delay: var(--animate-delay);

    &.nut-icon-am-infinite {
      -webkit-animation-direction: normal;
      animation-direction: normal;
    }
  }
  &-am-blink {
    -webkit-animation-name: nutBlink;
    -webkit-animation-duration: var(--animate-duration);
    -webkit-animation-timing-function: ease-in-out;
    -webkit-animation-delay: var(--animate-delay);

    animation-name: nutBlink;
    animation-duration: var(--animate-duration);
    animation-timing-function: linear;
    animation-delay: var(--animate-delay);
  }
  &-am-breathe {
    -webkit-animation-name: nutBreathe;
    -webkit-animation-duration: var(--animate-duration);
    -webkit-animation-timing-function: ease-in-out;
    -webkit-animation-delay: var(--animate-delay);

    animation-name: nutBreathe;
    animation-duration: var(--animate-duration);
    animation-timing-function: ease-in-out;
    animation-delay: var(--animate-delay);
  }
  &-am-flash {
    -webkit-animation-name: nutFlash;
    -webkit-animation-duration: var(--animate-duration);
    -webkit-animation-timing-function: ease-in-out;
    -webkit-animation-delay: var(--animate-delay);

    animation-name: nutFlash;
    animation-duration: var(--animate-duration);
    animation-timing-function: ease-in-out;
    animation-delay: var(--animate-delay);
  }
  &-am-bounce {
    -webkit-animation-name: nutBounce;
    -webkit-animation-duration: var(--animate-duration);
    -webkit-animation-timing-function: ease-in-out;
    -webkit-animation-delay: var(--animate-delay);
    animation-name: nutBounce;
    animation-duration: var(--animate-duration);
    animation-timing-function: ease-in-out;
    animation-delay: var(--animate-delay);

    &.nut-icon-am-infinite {
      -webkit-animation-direction: normal;
      animation-direction: normal;
    }
  }

  &-am-shake {
    -webkit-animation-name: nutShake;
    -webkit-animation-duration: var(--animate-duration);
    -webkit-animation-timing-function: ease-in-out;
    -webkit-animation-delay: var(--animate-delay);

    animation-name: nutShake;
    animation-duration: var(--animate-duration);
    animation-timing-function: ease-in-out;
    animation-delay: var(--animate-delay);
  }
}
