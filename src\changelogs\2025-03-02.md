---
date: 2025-03-02
---

### 阶段性更新汇总

此处的更新可能不完整不及时

Telegram 频道 https://t.me/cool_scripts 内有每次更新的详细说明

功能新增
	
  •	区域过滤和协议过滤支持保留模式和过滤模式(后端需 >= 2.17.0, 前端需 >= 2.15.0)
	
  •	订阅管理
	
  •	支持环境变量 SUB_STORE_PRODUCE_CRON 在后台定时处理订阅。
	
  •	订阅支持开关 passThroughUA 透传请求的 User-Agent。
	
  •	组合订阅支持手动设置流量信息，可使用链接，响应内容即为流量信息。
	
  •	远程订阅支持透传请求的 User-Agent。
	
  •	协议支持
	
  •	sing-box 及 Egern 支持 anytls 协议。
	
  •	Egern 和 Stash 可根据 User-Agent 自动包含官方/商店版/未续费订阅不支持的协议。
	
  •	Egern 正式支持 Shadowsocks 2022。
	
  •	Loon 正式支持 Shadowsocks 2022 和 Shadow-TLS。
	
  •	Surge 默认开启 Shadowsocks 2022。
	
  •	sing-box 及 Egern 支持 Hysteria2 端口跳跃。
	
  •	VLESS 支持 spx 参数；Trojan 支持 REALITY/XHTTP 结合使用。
	
  •	Proxy URI Scheme 支持省略端口号（HTTP 默认 80，TLS 默认 443）。
	
  •	Shadowrocket 的 Shadowsocks 输入支持 Shadow TLS 参数。
	
  •	Egern 支持 prev_hop 前置代理。
	
  •	Mihomo 配置
	
  •	Mihomo 配置支持 覆写 多次使用。
	
  •	Mihomo 配置的 Snell 版本 < 3 时，强制去除 udp 字段以防止内核报错。
	
  •	Mihomo 配置文件支持 流量信息链接 设置。

优化改进
	
  •	解析 & 兼容性
	
  •	修复 Shadowsocks URI 解析逻辑，支持 Shadow TLS plugin。
	
  •	UUID 仅辅助判断，不直接过滤；VMess/VLESS 校验 UUID。
	
  •	兼容 v2rayN 非标 TUIC URI，并支持更多 TUIC URI 字段。
	
  •	Egern 增加默认 SNI。
	
  •	Loon 排除 XTLS。
	
  •	界面优化
	
  •	预览界面：
	
  •	复制分享链接优化，新增一键复制按钮。
	
  •	订阅管理界面顶部标签栏始终显示，增加 PWA 判断。
	
  •	修复宽屏设备下节点信息面板二维码样式问题。
	
  •	文件管理
	
  •	target 名称适配大小写和别名。
	
  •	Mihomo 配置中订阅名称选取交互优化。
	
  •	订阅流量信息去除空字段，增强兼容性。

修复
	
  •	修复 Surge 输入的 tfo。
	
  •	修复 Loon ip-mode 逻辑。
	
  •	修复 Egern VMess tcp 传输层问题。
	
  •	修复 TUIC URI 解析问题。
	
  •	修复 组合订阅 透传 User-Agent 逻辑。
	
  •	修复 Base64 解码合法性判断。
	
  •	修复 Clash Pre-processor 逻辑。
	
  •	修复 短 ID 正则匹配 问题。
	
  •	修复 代理 App 版 target 参数为空的情况。

其他
	
  •	geo 数据更新。
	
  •	README 文档调整。
	
  •	pnpm 依赖更新，构建方式调整（使用 esbuild）。
	
  •	GitHub Actions 流水线优化。

这一版本包含了大量新增特性和修复，建议所有用户尽快更新！ 🚀