{
  "extends": [
    "@antfu",
    "prettier"
  ],
  "plugins": [
    "simple-import-sort",
    "prettier"
  ],
  "rules": {
    "simple-import-sort/imports": "error",
    "simple-import-sort/exports": "error",
    "antfu/top-level-function": "off",
    "antfu/if-newline": "off",
    "vue/component-tags-order": [
      "error",
      {
        "order": [
          "template",
          "script",
          "style"
        ]
      }
    ],
    "@typescript-eslint/consistent-type-definitions": [
      "error",
      "type"
    ],
    "@typescript-eslint/semi": [
      "error",
      "always"
    ],
    "prettier/prettier": ["error", {
      "htmlWhitespaceSensitivity": "ignore" // 根据显示样式决定 html 要不要折行
    }]
  }
}