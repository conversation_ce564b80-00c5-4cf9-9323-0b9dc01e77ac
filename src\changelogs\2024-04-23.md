---
date: 2024-04-23
---

### 阶段性更新汇总

此处的更新可能不完整不及时

Telegram 频道 https://t.me/cool_scripts 内有每次更新的详细说明

- 处理手动删除 Gist 之后, Sub-Store 侧重新同步的逻辑

- QX 输出正式支持 VLESS

- 节点操作支持单项展开/收起

- 支持 Loon SOCKS5/SOCKS5-TLS

- 支持 WireGuard URI 输入和输出

- 支持 dialer-proxy, detour

- fancy-characters 增加 modifier-letter(小写没有 q, 用 ᵠ 替代. 大写缺的太多, 用小写替代)

- 订阅支持输出哪吒探针兼容响应, 网络监控接口(Loon/Surge 可输出节点延迟)

- 节点操作增加收起/展开按钮, 方便处理多个操作

- 远程订阅 URL 新增参数 validCheck 将检查订阅有效期和剩余流量

- 增加脚本说明文档链接

- HTML 中增加 version meta

- 支持导出操作的数据到剪贴板, 从剪贴板数据导入操作

- 设置展示订阅进度

- 增加提示文案(部分浏览器上 HTTPS 前端无法访问本地 HTTP 后端)

- 恢复数据后 重新加载页面

- 旗帜操作(支持更多选项)

- 协议类型筛选支持 SSH

- 增加 User-Agent 说明文案

- 预览界面显示保留/过滤节点数量并可跳转

- 预览界面增加端口

- 域名解析新增 IP4P, 支持禁用缓存

- 增加下载缓存阈值

- 文件预览界面增加复制预览内容按钮

- 订阅列表的流量信息兼容远程和本地合并的情况, 排除设置了不查询订阅信息的链接

- 支持显示剩余重置天数

- 支持自定义订阅流量信息

- 通过代理/节点/策略获取订阅 现已支持 Surge, Loon, Stash, Shadowrocket, QX, Node.js

- 支持设置查询远程订阅流量信息时的 User-Agent

- 支持收起/展开订阅

- 订阅支持标签分组

- 组合订阅中的子订阅支持分组筛选(仅支持筛选, 未实现动态绑定一个分组的所有订阅)

- 支持参数 hideExpire 隐藏到期时间

- 订阅链接支持 `showRemaining` 参数, 此时将显示剩余流量而不是已用流量

- 支持设置并在远程订阅失败时读取最近一次成功的缓存
