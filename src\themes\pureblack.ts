export default {
  meta: {
    name: 'Pure Black',
    author: 'Keywos',
    label: 'dark',
    extend: 'dark',
  },
  colors: {
    // 全局高亮色
    'primary-color': '#6f9cc5',
    'primary-color-end': '#8389c8',
    'second-color': '#c5565a',
    'third-color': '#70b496',

    'danger-color': '#c16058',
    'succeed-color': '#49bb88',

    // icon色
    'icon-nav-bar-right': '#606266',
    'unimportant-icon-color': '#FFFFFF34',

    // 组件色
    'status-bar-background-color': '#000',
    'background-color': '#000',
    'nav-bar-color': '#000000',
    'tab-bar-color': '#00000063',
    'popup-color': '#121212',
    'divider-color': '#FFFFFF08',
    'card-color': '#242427',
    // 'card-color': '#0e0e0f',
    'dialog-color': '#242427',
    'switch-close-background-color': '#FFFFFF14',
    'switch-active-background-color': '#478EF2',
    'compare-item-background-color': '#202020',
    'picker-mask-near-color': '#12121248',
    'picker-mask-far-color': '#121212',
    // nut-input nut-input-readonly nut-input-border nut-input-text
    // 文字色
    'nut-input-readonly': '#bcbac1',
    'nut-input': '#bcbac1',
    'nut-input-text': '#bcbac1',


    'primary-text-color': '#bcbac1',
    'second-text-color': '#FFFFFFBB',
    'comment-text-color': '#FFFFFF88',
    'lowest-text-color': '#FFFFFF36',

    // 其他
    'img-brightness': '100',
    'nav-bar-blur': '16px',
    'tab-bar-blur': '16px',
    'sticky-title-blur': '16px',

    'compare-tag-text-color': '#FFFFFF88',
    'compare-tag-background-color': '#FFFFFF22',
  },
};