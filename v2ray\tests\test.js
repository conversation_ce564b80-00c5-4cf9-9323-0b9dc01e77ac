/**
 * 代理节点转换工具测试
 */

import { ProxyConverter } from '../src/index.js';
import { OutputFormats } from '../src/types.js';

// 测试数据
const testUrls = [
  'ss://YWVzLTI1Ni1nY206cGFzc3dvcmQ@***********:8388#香港节点1',
  'ss://YWVzLTI1Ni1nY206cGFzc3dvcmQ@***********:8388#香港节点2',
  'vmess://eyJ2IjoiMiIsInBzIjoi5pel5pys6IqC54K5IiwiYWRkIjoiMTkyLjE2OC4xLjMiLCJwb3J0IjoiODA4MCIsImlkIjoiMTIzNDU2NzgtYWJjZC0xMjM0LWFiY2QtMTIzNDU2Nzg5YWJjIiwiYWlkIjoiMCIsInNjeSI6ImF1dG8iLCJuZXQiOiJ0Y3AiLCJ0eXBlIjoibm9uZSIsImhvc3QiOiIiLCJwYXRoIjoiIiwidGxzIjoiIn0=',
  'vless://12345678-abcd-1234-abcd-123456789abc@***********:443?encryption=none&security=tls&type=ws&host=example.com&path=/path#美国节点1',
  'trojan://password123@***********:443?security=tls&type=tcp&sni=example.com#新加坡节点1',
  // 重复节点（用于测试去重）
  'ss://YWVzLTI1Ni1nY206cGFzc3dvcmQ@***********:8388#香港节点重复',
];

const testBase64 = 'c3M6Ly9ZV1Z6TFRJMU5pMW5ZMjA2Y0dGemMzZHZjbVE9QDE5Mi4xNjguMS4xOjgzODgjJUU5JUE2JTk5JUU2JUI4JUFGJUU4JThBJTgyJUU3JTgyJUI5MQpzczovL1lXVnpMVEkxTmkxblkyMDZjR0Z6YzNkdmNtUT1AMTI3LjAuMC4xOjgzODgjJUU5JUE2JTk5JUU2JUI4JUFGJUU4JThBJTgyJUU3JTgyJUI5Mg==';

async function runTests() {
  console.log('🚀 开始测试代理节点转换工具\n');

  const converter = new ProxyConverter();

  try {
    // 测试1: 解析代理URL
    console.log('📝 测试1: 解析代理URL');
    const nodes = converter.parse(testUrls);
    console.log(`解析结果: ${nodes.length} 个节点`);
    nodes.forEach((node, index) => {
      console.log(`  ${index + 1}. ${node.name} (${node.type}) - ${node.server}:${node.port}`);
    });
    console.log('✅ 解析测试完成\n');

    // 测试2: 节点去重
    console.log('🔄 测试2: 节点去重');
    const originalCount = nodes.length;
    const deduplicatedNodes = converter.deduplicate(nodes);
    console.log(`去重前: ${originalCount} 个节点`);
    console.log(`去重后: ${deduplicatedNodes.length} 个节点`);
    console.log(`移除: ${originalCount - deduplicatedNodes.length} 个重复节点`);
    console.log('✅ 去重测试完成\n');

    // 测试3: 节点重命名
    console.log('🏷️ 测试3: 节点重命名');
    const renamedNodes = converter.rename(deduplicatedNodes);
    console.log('重命名结果:');
    renamedNodes.forEach((node, index) => {
      console.log(`  ${index + 1}. ${node.name} (原名: ${node.originalName || '无'})`);
    });
    console.log('✅ 重命名测试完成\n');

    // 测试4: 转换为Clash格式
    console.log('⚙️ 测试4: 转换为Clash格式');
    const clashConfig = converter.convert(renamedNodes, OutputFormats.CLASH);
    console.log(`Clash配置生成完成，包含 ${clashConfig.proxies?.length || 0} 个代理`);
    console.log('代理列表:');
    clashConfig.proxies?.forEach((proxy, index) => {
      console.log(`  ${index + 1}. ${proxy.name} (${proxy.type})`);
    });
    console.log('✅ Clash转换测试完成\n');

    // 测试5: 转换为Base64订阅
    console.log('📄 测试5: 转换为Base64订阅');
    const base64Subscription = converter.convert(renamedNodes, OutputFormats.BASE64);
    console.log(`Base64订阅生成完成，长度: ${base64Subscription.length} 字符`);
    console.log(`Base64内容预览: ${base64Subscription.substring(0, 100)}...`);
    console.log('✅ Base64转换测试完成\n');

    // 测试6: 转换为URL列表
    console.log('🔗 测试6: 转换为URL列表');
    const urlList = converter.convert(renamedNodes, OutputFormats.URL);
    const urls = urlList.split('\n').filter(url => url.trim());
    console.log(`URL列表生成完成，包含 ${urls.length} 个URL`);
    urls.forEach((url, index) => {
      console.log(`  ${index + 1}. ${url.substring(0, 50)}...`);
    });
    console.log('✅ URL转换测试完成\n');

    // 测试7: 一键处理
    console.log('🎯 测试7: 一键处理');
    const processedResult = converter.process(testUrls, OutputFormats.CLASH, {
      deduplicate: true,
      rename: true,
      deduplicateOptions: { smart: true },
      renameOptions: { groupByRegion: true }
    });
    console.log(`一键处理完成，生成Clash配置包含 ${processedResult.proxies?.length || 0} 个代理`);
    console.log('✅ 一键处理测试完成\n');

    // 测试8: Base64订阅解析
    console.log('📥 测试8: Base64订阅解析');
    const base64Nodes = converter.parse(testBase64, OutputFormats.BASE64);
    console.log(`Base64订阅解析完成，包含 ${base64Nodes.length} 个节点`);
    base64Nodes.forEach((node, index) => {
      console.log(`  ${index + 1}. ${node.name} (${node.type}) - ${node.server}:${node.port}`);
    });
    console.log('✅ Base64解析测试完成\n');

    // 测试9: 获取统计信息
    console.log('📊 测试9: 获取统计信息');
    const stats = converter.getStats(renamedNodes);
    console.log('节点统计信息:');
    console.log(`  总数: ${stats.total}`);
    console.log(`  有效: ${stats.valid}`);
    console.log(`  无效: ${stats.invalid}`);
    console.log('  协议分布:');
    Object.entries(stats.types).forEach(([type, count]) => {
      console.log(`    ${type}: ${count}`);
    });
    console.log('  地区分布:');
    Object.entries(stats.regions).forEach(([region, count]) => {
      console.log(`    ${region}: ${count}`);
    });
    console.log('✅ 统计信息测试完成\n');

    // 测试10: 批量处理
    console.log('📦 测试10: 批量处理');
    const batchInputs = [
      { content: testUrls.slice(0, 3), format: OutputFormats.URL },
      { content: testBase64, format: OutputFormats.BASE64 }
    ];
    const batchResult = converter.batchProcess(batchInputs, OutputFormats.JSON);
    console.log(`批量处理完成，合并后包含 ${batchResult.length} 个节点`);
    console.log('✅ 批量处理测试完成\n');

    console.log('🎉 所有测试完成！');

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
  }
}

// 运行测试
runTests();
