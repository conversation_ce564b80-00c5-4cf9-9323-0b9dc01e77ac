/**
 * VMess 协议解析器
 */

import { ProxyTypes } from '../types.js';

/**
 * 解析 VMess URL
 * 支持格式: vmess://base64(json)
 * @param {string} url - VMess URL
 * @returns {Object|null} 解析后的节点信息
 */
export function parseVMessUrl(url) {
  try {
    if (!url.startsWith('vmess://')) {
      return null;
    }

    // 移除协议前缀并解码base64
    const base64Content = url.slice(8);
    let jsonString;
    try {
      jsonString = atob(base64Content);
    } catch (e) {
      // 如果atob失败，使用Buffer方式
      jsonString = Buffer.from(base64Content, 'base64').toString('utf8');
    }
    const config = JSON.parse(jsonString);

    return {
      type: ProxyTypes.VMESS,
      name: config.ps || `${config.add}:${config.port}`,
      server: config.add,
      port: parseInt(config.port),
      uuid: config.id,
      alterId: parseInt(config.aid) || 0,
      cipher: config.scy || 'auto',
      network: config.net || 'tcp',
      tls: {
        enabled: config.tls === 'tls' || config.tls === '1' || config.tls === 1 || config.tls === true,
        serverName: config.sni || config.host || ''
      },
      transport: parseTransport(config)
    };
  } catch (error) {
    console.error('解析 VMess URL 失败:', error);
    return null;
  }
}

/**
 * 生成 VMess URL
 * @param {Object} node - 节点信息
 * @returns {string} VMess URL
 */
export function generateVMessUrl(node) {
  try {
    const config = {
      v: '2',
      ps: node.name,
      add: node.server,
      port: node.port.toString(),
      id: node.uuid,
      aid: node.alterId.toString(),
      scy: node.cipher,
      net: node.network,
      type: 'none',
      host: '',
      path: '',
      tls: node.tls?.enabled ? 'tls' : '',
      sni: node.tls?.serverName || ''
    };

    // 添加传输层配置
    if (node.transport) {
      switch (node.network) {
        case 'ws':
          config.host = node.transport.headers?.Host || '';
          config.path = node.transport.path || '/';
          break;
        case 'h2':
          config.host = node.transport.host || '';
          config.path = node.transport.path || '/';
          break;
        case 'grpc':
          config.path = node.transport.serviceName || '';
          config.type = node.transport.mode || 'gun';
          break;
      }
    }

    const jsonString = JSON.stringify(config);

    try {
      const base64Content = btoa(jsonString);
      return `vmess://${base64Content}`;
    } catch (error) {
      // 如果btoa失败，使用Buffer方式
      console.warn('btoa编码失败，使用Buffer方式:', error.message);
      try {
        const base64Content = Buffer.from(jsonString, 'utf8').toString('base64');
        return `vmess://${base64Content}`;
      } catch (bufferError) {
        console.error('Buffer编码也失败:', bufferError.message);
        return null;
      }
    }
  } catch (error) {
    console.error('生成 VMess URL 失败:', error);
    return null;
  }
}

/**
 * 转换为 Clash 格式
 * @param {Object} node - 节点信息
 * @returns {Object} Clash 格式节点
 */
export function toClashFormat(node) {
  const clashNode = {
    name: node.name,
    type: 'vmess',
    server: node.server,
    port: node.port,
    uuid: node.uuid,
    alterId: node.alterId,
    cipher: node.cipher,
    network: node.network
  };

  // TLS 配置
  if (node.tls?.enabled) {
    clashNode.tls = true;
    if (node.tls.serverName) {
      clashNode.servername = node.tls.serverName;
    }
  }

  // 传输层配置
  if (node.transport) {
    switch (node.network) {
      case 'ws':
        clashNode['ws-opts'] = {
          path: node.transport.path || '/',
          headers: node.transport.headers || {}
        };
        break;
      case 'h2':
        clashNode['h2-opts'] = {
          host: node.transport.host ? [node.transport.host] : [],
          path: node.transport.path || '/'
        };
        break;
      case 'grpc':
        clashNode['grpc-opts'] = {
          'grpc-service-name': node.transport.serviceName || ''
        };
        break;
    }
  }

  return clashNode;
}

/**
 * 从 Clash 格式解析
 * @param {Object} clashNode - Clash 格式节点
 * @returns {Object} 标准节点格式
 */
export function fromClashFormat(clashNode) {
  const node = {
    type: ProxyTypes.VMESS,
    name: clashNode.name,
    server: clashNode.server,
    port: clashNode.port,
    uuid: clashNode.uuid,
    alterId: clashNode.alterId || 0,
    cipher: clashNode.cipher || 'auto',
    network: clashNode.network || 'tcp',
    tls: {
      enabled: !!clashNode.tls,
      serverName: clashNode.servername || ''
    },
    transport: {}
  };

  // 解析传输层配置
  switch (node.network) {
    case 'ws':
      if (clashNode['ws-opts']) {
        node.transport = {
          path: clashNode['ws-opts'].path || '/',
          headers: clashNode['ws-opts'].headers || {}
        };
      }
      break;
    case 'h2':
      if (clashNode['h2-opts']) {
        node.transport = {
          host: clashNode['h2-opts'].host?.[0] || '',
          path: clashNode['h2-opts'].path || '/'
        };
      }
      break;
    case 'grpc':
      if (clashNode['grpc-opts']) {
        node.transport = {
          serviceName: clashNode['grpc-opts']['grpc-service-name'] || ''
        };
      }
      break;
  }

  return node;
}

/**
 * 解析传输层配置
 * @param {Object} config - VMess 配置
 * @returns {Object} 传输层配置
 */
function parseTransport(config) {
  const transport = {};

  switch (config.net) {
    case 'ws':
      transport.path = config.path || '/';
      transport.headers = {};
      if (config.host) {
        transport.headers.Host = config.host;
      }
      break;
    case 'h2':
      transport.host = config.host || '';
      transport.path = config.path || '/';
      break;
    case 'grpc':
      transport.serviceName = config.path || '';
      transport.mode = config.type || 'gun';
      break;
  }

  return transport;
}

/**
 * 验证节点配置
 * @param {Object} node - 节点信息
 * @returns {boolean} 是否有效
 */
export function validateNode(node) {
  return !!(
    node.server &&
    node.port &&
    node.uuid &&
    node.port > 0 &&
    node.port < 65536 &&
    /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(node.uuid)
  );
}
