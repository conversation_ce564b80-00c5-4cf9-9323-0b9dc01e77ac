#!/usr/bin/env node

/**
 * 交互式代理节点处理菜单
 * 提供多种处理选项的交互式界面
 */

import readline from 'readline';
import { mergeYamlFiles, mergeBase64Files, mergeUrlFiles, scanAndCategorizeFiles, ensureOutputDir } from './merge-files.js';

// 创建readline接口
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

/**
 * 显示主菜单
 */
function showMainMenu() {
  console.clear();
  console.log('🚀 代理节点处理工具 - 交互式菜单');
  console.log('='.repeat(60));
  console.log('');
  console.log('📋 请选择操作:');
  console.log('');
  console.log('  1️⃣  处理所有文件 (自动转换格式)');
  console.log('  2️⃣  合并YAML文件 (去重后生成单个YAML文件)');
  console.log('  3️⃣  合并Base64文件 (去重后生成单个Base64文件)');
  console.log('  4️⃣  合并URL文件 (去重后生成单个URL文件)');
  console.log('  5️⃣  智能合并所有文件 (按格式分类合并)');
  console.log('  6️⃣  查看文件统计信息');
  console.log('  0️⃣  退出程序');
  console.log('');
  console.log('='.repeat(60));
}

/**
 * 获取用户输入
 */
function getUserInput(prompt) {
  return new Promise((resolve) => {
    rl.question(prompt, (answer) => {
      resolve(answer.trim());
    });
  });
}

/**
 * 暂停等待用户按键
 */
function waitForKeyPress() {
  return new Promise((resolve) => {
    rl.question('\n按回车键继续...', () => {
      resolve();
    });
  });
}

/**
 * 处理所有文件
 */
async function processAllFilesMenu() {
  console.clear();
  console.log('📁 处理所有文件');
  console.log('='.repeat(40));

  try {
    // 动态导入处理文件模块
    const { processAllFiles } = await import('./process-files.js');
    await processAllFiles();
  } catch (error) {
    console.error('❌ 处理失败:', error.message);
  }

  await waitForKeyPress();
}

/**
 * 查看文件统计
 */
async function showFileStats() {
  console.clear();
  console.log('📊 文件统计信息');
  console.log('='.repeat(40));

  const categories = scanAndCategorizeFiles();

  console.log(`📂 扫描目录: ./tests`);
  console.log('');
  console.log('📋 文件分类统计:');
  console.log(`  🟡 YAML文件: ${categories.yaml.length} 个`);
  categories.yaml.forEach((file, index) => {
    console.log(`     ${index + 1}. ${file.file}`);
  });

  console.log(`  🟢 Base64文件: ${categories.base64.length} 个`);
  categories.base64.forEach((file, index) => {
    console.log(`     ${index + 1}. ${file.file}`);
  });

  console.log(`  🔵 URL文件: ${categories.url.length} 个`);
  categories.url.forEach((file, index) => {
    console.log(`     ${index + 1}. ${file.file}`);
  });

  console.log(`  ⚪ 未知格式: ${categories.unknown.length} 个`);
  categories.unknown.forEach((file, index) => {
    console.log(`     ${index + 1}. ${file.file}`);
  });

  console.log('');
  console.log(`📈 总计: ${categories.yaml.length + categories.base64.length + categories.url.length + categories.unknown.length} 个文件`);

  await waitForKeyPress();
}

/**
 * 智能合并所有文件
 */
async function smartMergeAllFiles() {
  console.clear();
  console.log('🧠 智能合并所有文件');
  console.log('='.repeat(40));

  ensureOutputDir();
  const categories = scanAndCategorizeFiles();

  console.log('📋 将按格式分类进行合并:');
  console.log(`  🟡 YAML文件: ${categories.yaml.length} 个`);
  console.log(`  🟢 Base64文件: ${categories.base64.length} 个`);
  console.log(`  🔵 URL文件: ${categories.url.length} 个`);
  console.log('');

  const confirm = await getUserInput('确认开始智能合并? (y/N): ');
  if (confirm.toLowerCase() !== 'y' && confirm.toLowerCase() !== 'yes') {
    console.log('❌ 操作已取消');
    await waitForKeyPress();
    return;
  }

  try {
    // 合并YAML文件
    if (categories.yaml.length > 0) {
      await mergeYamlFiles(categories.yaml);
    }

    // 合并Base64文件
    if (categories.base64.length > 0) {
      await mergeBase64Files(categories.base64);
    }

    // 合并URL文件
    if (categories.url.length > 0) {
      await mergeUrlFiles(categories.url);
    }

    console.log('\n🎉 智能合并完成！');

  } catch (error) {
    console.error('❌ 合并失败:', error.message);
  }

  await waitForKeyPress();
}

/**
 * 合并指定格式的文件
 */
async function mergeSpecificFormat(format) {
  console.clear();
  console.log(`📄 合并${format.toUpperCase()}文件`);
  console.log('='.repeat(40));

  ensureOutputDir();
  const categories = scanAndCategorizeFiles();
  const files = categories[format];

  if (files.length === 0) {
    console.log(`⚠️ 没有找到${format.toUpperCase()}格式的文件`);
    await waitForKeyPress();
    return;
  }

  console.log(`📋 找到 ${files.length} 个${format.toUpperCase()}文件:`);
  files.forEach((file, index) => {
    console.log(`  ${index + 1}. ${file.file}`);
  });
  console.log('');

  const confirm = await getUserInput(`确认合并这 ${files.length} 个文件? (y/N): `);
  if (confirm.toLowerCase() !== 'y' && confirm.toLowerCase() !== 'yes') {
    console.log('❌ 操作已取消');
    await waitForKeyPress();
    return;
  }

  try {
    switch (format) {
      case 'yaml':
        await mergeYamlFiles(files);
        break;
      case 'base64':
        await mergeBase64Files(files);
        break;
      case 'url':
        await mergeUrlFiles(files);
        break;
    }
  } catch (error) {
    console.error('❌ 合并失败:', error.message);
  }

  await waitForKeyPress();
}

/**
 * 主程序循环
 */
async function main() {
  while (true) {
    showMainMenu();

    const choice = await getUserInput('请输入选项 (0-6): ');

    switch (choice) {
      case '1':
        await processAllFilesMenu();
        break;
      case '2':
        await mergeSpecificFormat('yaml');
        break;
      case '3':
        await mergeSpecificFormat('base64');
        break;
      case '4':
        await mergeSpecificFormat('url');
        break;
      case '5':
        await smartMergeAllFiles();
        break;
      case '6':
        await showFileStats();
        break;
      case '0':
        console.log('\n👋 感谢使用！再见！');
        rl.close();
        process.exit(0);
        break;
      default:
        console.log('\n❌ 无效选项，请重新选择');
        await waitForKeyPress();
        break;
    }
  }
}

// 启动程序
main().catch(error => {
  console.error('❌ 程序运行失败:', error);
  rl.close();
  process.exit(1);
});
