# 🚀 代理节点转换工具

一个功能强大的代理节点格式转换和管理工具，支持多种协议和格式的智能转换、去重、合并和重命名。

## ✨ 主要功能

### 🔄 格式转换
- **支持格式**: Clash YAML、Base64订阅、URL列表、JSON
- **智能转换**: 自动检测输入格式，避免重复格式转换
- **多协议支持**: SS、SSR、VMess、VLESS、Trojan、Hysteria2、Snell

### 🔧 节点处理
- **智能去重**: 基于服务器、端口、协议的智能去重算法
- **统一重命名**: 国旗 + 地区 + 序号格式 (如: 🇭🇰 香港 001)
- **特殊字符处理**: 自动为URL和Base64格式的特殊字符添加引号

### 📁 文件合并
- **YAML文件合并**: 多个YAML文件去重后合并为单个YAML文件
- **Base64文件合并**: 多个Base64文件去重后合并为单个Base64文件
- **URL文件合并**: 多个URL文件去重后合并为单个URL文件
- **智能合并**: 自动按格式分类并合并所有文件

### 🎯 交互式操作
- **菜单界面**: 友好的交互式命令行界面
- **手动选择**: 支持手动选择要处理的文件和操作
- **实时统计**: 显示详细的节点统计信息

## 🛠️ 支持的协议

| 协议 | 解析 | 生成 | Clash转换 | 说明 |
|------|------|------|-----------|------|
| **Shadowsocks (SS)** | ✅ | ✅ | ✅ | 完整支持 |
| **ShadowsocksR (SSR)** | ✅ | ✅ | ✅ | 新增支持 |
| **VMess** | ✅ | ✅ | ✅ | 完整支持 |
| **VLESS** | ✅ | ✅ | ✅ | 完整支持 |
| **Trojan** | ✅ | ✅ | ✅ | 完整支持 |
| **Hysteria2** | ✅ | ✅ | ✅ | 新增支持 |
| **Snell** | ✅ | ✅ | ✅ | 新增支持 |

## 📦 安装和使用

### 环境要求
- Node.js 16+
- npm 或 yarn

### 安装依赖
```bash
npm install
```

### 🎮 使用方法

#### 1. 交互式菜单 (推荐)
```bash
npm run menu
```

提供以下选项：
- 1️⃣ 处理所有文件 (自动转换格式)
- 2️⃣ 合并YAML文件 (去重后生成单个YAML文件)
- 3️⃣ 合并Base64文件 (去重后生成单个Base64文件)
- 4️⃣ 合并URL文件 (去重后生成单个URL文件)
- 5️⃣ 智能合并所有文件 (按格式分类合并)
- 6️⃣ 查看文件统计信息
- 0️⃣ 退出程序

#### 2. 命令行操作

**处理所有文件**
```bash
npm run process
```

**处理指定文件**
```bash
npm run process-file filename.txt
```

**快速合并操作**
```bash
# 合并所有YAML文件
npm run merge-yaml

# 合并所有Base64文件
npm run merge-base64

# 合并所有URL文件
npm run merge-url
```

## 📂 文件结构

### 输入目录
将订阅文件放在 `tests/` 目录中：
```
tests/
├── mihomo.yaml          # Clash配置文件
├── base64.txt           # Base64订阅文件
├── 合并节点.txt         # URL列表文件
└── ...                  # 其他订阅文件
```

### 输出目录
处理后的文件保存在 `output/` 目录中：
```
output/
├── base64_clash.yaml           # 转换后的Clash配置
├── base64_urls.txt             # 转换后的URL列表
├── base64_nodes.json           # JSON格式节点数据
├── merged_yaml_nodes.yaml      # 合并的YAML文件
├── merged_base64_nodes.txt     # 合并的Base64文件
├── merged_url_nodes.txt        # 合并的URL文件
└── ...                         # 其他输出文件
```

## ✨ 新功能特性

### 🚫 避免重复格式转换
- **Base64格式** 的节点不再转换成Base64格式文件
- **YAML格式** 的节点不再转换成YAML格式文件
- **URL格式** 的节点不再转换成URL格式文件

### 🔤 特殊字符处理
对于URL和Base64格式的节点，当password等字段以特殊字符开头时，自动添加引号：
```yaml
# 优化前
password: @CfftfYVgp4gkMHMirH6@_C

# 优化后
password: "@CfftfYVgp4gkMHMirH6@_C"
```

### 📊 智能文件分类
自动识别和分类不同格式的文件：
- 🟡 YAML文件 (.yaml, .yml)
- 🟢 Base64文件 (单行Base64编码)
- 🔵 URL文件 (包含代理URL的文本文件)

## 🎯 使用示例

### 示例1: 处理单个文件
```bash
# 处理Base64订阅文件
npm run process-file base64.txt

# 输出文件:
# - base64_clash.yaml (Clash配置)
# - base64_urls.txt (URL列表)
# - base64_nodes.json (JSON数据)
# 注意: 不会生成 base64_base64.txt (避免重复)
```

### 示例2: 智能合并所有文件
```bash
npm run menu
# 选择 5 (智能合并所有文件)

# 自动生成:
# - merged_yaml_nodes.yaml (所有YAML文件合并)
# - merged_base64_nodes.txt (所有Base64文件合并)
# - merged_url_nodes.txt (所有URL文件合并)
```

### 示例3: 查看文件统计
```bash
npm run menu
# 选择 6 (查看文件统计信息)

# 显示:
# - 文件分类统计
# - 节点数量统计
# - 协议分布统计
# - 地区分布统计
```

## 📈 更新日志

### v1.2.0 (最新)
- ✅ 新增 Hysteria2、SSR、Snell 协议支持
- ✅ 添加文件合并功能
- ✅ 实现交互式菜单界面
- ✅ 优化避免重复格式转换
- ✅ 改进特殊字符处理
- ✅ 修复编码问题

## 📄 许可证

MIT License

---

**享受使用代理节点转换工具！** 🎉
