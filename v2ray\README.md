# 代理节点转换工具

一个功能强大的代理节点格式转换工具，支持多种代理协议的格式转换、节点去重和重命名。

## 功能特性

- 🔄 **格式转换**: 支持URL、Clash、Base64等格式之间的相互转换
- 🗑️ **节点去重**: 智能识别并去除重复的代理节点
- 🏷️ **节点重命名**: 统一格式为"国旗Emoji 地区中文名 三位数序号"
- 🌐 **多协议支持**: 支持SS、SSR、VMess、VLESS、Trojan、Hysteria等主流协议

## 支持的协议

- Shadowsocks (SS)
- ShadowsocksR (SSR) 
- VMess
- VLESS
- Trojan
- Hysteria
- Hysteria2
- TUIC
- WireGuard

## 支持的格式

### 输入格式
- 单行代理URL
- Base64编码的订阅
- Clash YAML配置
- JSON格式配置

### 输出格式
- Clash YAML
- 代理URL列表
- Base64编码订阅
- JSON格式

## 安装使用

```bash
# 安装依赖
npm install

# 运行程序
npm start

# 开发模式
npm run dev

# 运行测试
npm test
```

## 项目结构

```
v2ray/
├── src/
│   ├── parsers/          # 各协议解析器
│   ├── converters/       # 格式转换器
│   ├── utils/           # 工具函数
│   └── index.js         # 主入口
├── tests/               # 测试文件
└── package.json
```

## 使用示例

```javascript
import { ProxyConverter } from './src/index.js';

const converter = new ProxyConverter();

// 解析代理URL
const nodes = converter.parseProxyUrls([
  'ss://YWVzLTI1Ni1nY206cGFzc3dvcmQ@***********:8388#Test'
]);

// 转换为Clash格式
const clashConfig = converter.toClash(nodes);

// 节点去重
const uniqueNodes = converter.deduplicateNodes(nodes);

// 节点重命名
const renamedNodes = converter.renameNodes(nodes);
```

## 许可证

MIT License
