{"name": "proxy-node-converter", "version": "1.0.0", "description": "代理节点格式转换工具", "main": "src/index.js", "type": "module", "scripts": {"start": "node demo.js", "test": "node tests/test.js", "dev": "node --watch demo.js", "lib": "node src/index.js", "process": "node process-files.js", "process-file": "node process-files.js"}, "keywords": ["proxy", "v2ray", "clash", "shadowsocks", "trojan", "converter"], "author": "Proxy Node Converter", "license": "MIT", "dependencies": {"js-yaml": "^4.1.0"}, "devDependencies": {"@types/node": "^20.0.0"}}