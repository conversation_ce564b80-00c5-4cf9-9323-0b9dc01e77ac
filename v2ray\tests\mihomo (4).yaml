proxies:
  - cipher: aes-256-gcm
    name: "\U0001F1ED\U0001F1F0香港1 | ⬇️ 8.4MB/s"
    password: abb58966-8d7b-4cdf-bae9-b0d8e184d93d
    port: 16010
    server: *************
    type: ss
    udp: true
  - cipher: aes-128-gcm
    name: "\U0001F300其他1-GR | ⬇️ 8.8MB/s"
    password: b86a48e8-542d-4b90-bb6e-ff0a3e7e4e97
    port: 47317
    server: sz.fanhua.art
    type: ss
    udp: true
  - cipher: aes-256-cfb
    name: "\U0001F1F8\U0001F1EC新加坡1 | ⬇️ 7.9MB/s"
    password: qawszxc123
    port: 443
    server: ************
    type: ss
    udp: true
  - name: "\U0001F1ED\U0001F1F0香港2 | ⬇️ 8.5MB/s"
    password: RlzoEILU
    port: 15624
    server: **************
    skip-cert-verify: true
    type: trojan
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1ED\U0001F1F0香港3 | ⬇️ 8.1MB/s"
    password: b8fa2e30-a210-4d8b-bb1e-bf42ea9c9e7e
    port: 16004
    server: *************
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1ED\U0001F1F0香港4 | ⬇️ 7.9MB/s"
    password: b8fa2e30-a210-4d8b-bb1e-bf42ea9c9e7e
    port: 16014
    server: *************
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1ED\U0001F1F0香港5 | ⬇️ 9.2MB/s"
    password: c86d483c-431f-41df-bb6b-c1dcebfc7401
    port: 16006
    server: *************
    type: ss
    udp: true
  - cipher: chacha20-ietf-poly1305
    name: "\U0001F1F8\U0001F1EC新加坡2 | ⬇️ 8.1MB/s"
    password: ed6c8eea-8503-43e1-9563-0a355b0edcac
    port: 42028
    server: eepl1.dhh114514.christmas
    type: ss
    udp: true
  - down: ''
    fingerprint: ''
    name: "\U0001F1ED\U0001F1F0香港6 | ⬇️ 9.7MB/s"
    obfs: ''
    obfs-password: ''
    password: b751e995-b24c-4f30-8425-05db0b9eac45
    port: 8021
    server: hkhkt.ccwink.cc
    skip-cert-verify: true
    sni: hkhkt.ccwink.cc
    type: hysteria2
    up: ''
  - down: ''
    fingerprint: ''
    name: "\U0001F1ED\U0001F1F0香港7 | ⬇️ 8.1MB/s"
    obfs: ''
    obfs-password: ''
    password: b751e995-b24c-4f30-8425-05db0b9eac45
    port: 8022
    server: hkhkt.ccwink.cc
    skip-cert-verify: true
    sni: hkhkt.ccwink.cc
    type: hysteria2
    up: ''
  - down: ''
    fingerprint: ''
    name: "\U0001F1ED\U0001F1F0香港8 | ⬇️ 8.4MB/s"
    obfs: ''
    obfs-password: ''
    password: b751e995-b24c-4f30-8425-05db0b9eac45
    port: 8023
    server: hkt01.ccwink.cc
    skip-cert-verify: true
    sni: hkt01.ccwink.cc
    type: hysteria2
    up: ''
  - alpn:
      - ''
    alterId: '0'
    cipher: auto
    name: "\U0001F300其他2-未识别 | ⬇️ 12.0MB/s"
    network: ws
    port: 80
    server: 0989a4f1-856a-4d0b-530b-aed9bf289ad8.castlepeakhospital.moe
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 21c90669-193d-49d3-8e37-f5c1462eb134
    ws-opts:
      headers:
        Host: tms.dingtalk.com
      path: /
    xudp: true
  - down: ''
    fingerprint: ''
    name: "\U0001F1FA\U0001F1F8美国1 | ⬇️ 9.4MB/s"
    obfs: ''
    obfs-password: ''
    password: b8bd42a9-551f-419d-b70d-4aefdd2cb074
    port: 443
    server: **************
    skip-cert-verify: true
    sni: www.bing.com
    type: hysteria2
    up: ''
  - down: ''
    fingerprint: ''
    name: "\U0001F1F7\U0001F1FA俄罗斯1 | ⬇️ 9.9MB/s"
    obfs: ''
    obfs-password: ''
    password: dongtaiwang.com
    port: 30033
    server: hy2.694463.xyz
    skip-cert-verify: true
    sni: www.bing.com
    type: hysteria2
    up: ''
  - down: ''
    fingerprint: ''
    name: "\U0001F1FA\U0001F1F8美国2 | ⬇️ 8.1MB/s"
    obfs: ''
    obfs-password: ''
    password: 16955d72-1794-11f0-a035-f23c95b6f51d
    port: 1443
    server: 9b614a41-swb8g0-sxs14h-dnss.la.shifen.uk
    skip-cert-verify: false
    sni: 9b614a41-swb8g0-sxs14h-dnss.la.shifen.uk
    type: hysteria2
    up: ''
  - down: ''
    fingerprint: ''
    name: "\U0001F1FA\U0001F1F8美国3 | ⬇️ 10.4MB/s"
    obfs: ''
    obfs-password: ''
    password: b8bd42a9-551f-419d-b70d-4aefdd2cb074
    port: 443
    server: *************
    skip-cert-verify: true
    sni: www.bing.com
    type: hysteria2
    up: ''
  - name: "\U0001F1E9\U0001F1EA德国1 | ⬇️ 12.5MB/s"
    network: ws
    port: 8880
    server: **************
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
        User-Agent: >-
          Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like
          Gecko) Chrome/40.0.2214.115 Safari/537.36
      path: /?ed=2560
    xudp: true
  - cipher: aes-128-gcm
    name: "\U0001F300其他3-CY | ⬇️ 9.9MB/s"
    password: b86a48e8-542d-4b90-bb6e-ff0a3e7e4e97
    port: 15423
    server: sz.fanhua.art
    type: ss
    udp: true
  - client-fingerprint: chrome
    flow: xtls-rprx-vision
    name: "\U0001F1FA\U0001F1F8美国4 | ⬇️ 10.0MB/s"
    network: tcp
    port: 8443
    reality-opts:
      public-key: 4Qekb9y1dqO8hvRzVSGeSRNyhko_gqpeWD94zrLCvjs
      short-id: 5488b0e7
    server: sj-arm.nfsn666.gq
    tls: true
    type: vless
    udp: true
    uuid: 06121b89-607b-44c9-9c01-cc2fc6a7321d
    xudp: true
    servername: www.yahoo.com
  - cipher: aes-256-cfb
    name: "\U0001F1F0\U0001F1F7韩国1 | ⬇️ 13.7MB/s"
    password: qwerREWQ@@
    port: 11389
    server: p231.panda004.net
    type: ss
    udp: true
  - name: "\U0001F1FA\U0001F1F8美国5 | ⬇️ 8.4MB/s"
    password: 2b1ed981-6547-4094-998b-06a3323d6f6c
    port: 21603
    server: xd-js.timiwc.com
    skip-cert-verify: true
    sni: xd-js.timiwc.com
    type: trojan
    udp: true
  - name: "\U0001F300其他4-MY | ⬇️ 8.4MB/s"
    password: 2b1ed981-6547-4094-998b-06a3323d6f6c
    port: 21079
    server: **************
    skip-cert-verify: true
    type: trojan
    udp: true
  - name: "\U0001F300其他5-MY | ⬇️ 11.2MB/s"
    password: 2b1ed981-6547-4094-998b-06a3323d6f6c
    port: 21181
    server: **************
    skip-cert-verify: true
    type: trojan
    udp: true
  - name: "\U0001F1EF\U0001F1F5日本1 | ⬇️ 10.7MB/s"
    password: 7669a04d-a459-4c96-bc0e-fde51cb984f3
    port: 12032
    server: cn2.cdn.xfltd-cdn.top
    skip-cert-verify: true
    sni: cn2.cdn.xfltd-cdn.top
    type: trojan
    udp: true
  - name: "\U0001F1FA\U0001F1F8美国6 | ⬇️ 9.4MB/s"
    password: 2b1ed981-6547-4094-998b-06a3323d6f6c
    port: 59599
    server: xd-js.timiwc.com
    skip-cert-verify: true
    sni: xd-js.timiwc.com
    type: trojan
    udp: true
  - name: "\U0001F1F0\U0001F1F7韩国2 | ⬇️ 10.4MB/s"
    password: 7669a04d-a459-4c96-bc0e-fde51cb984f3
    port: 12041
    server: cn2.cdn.xfltd-cdn.top
    skip-cert-verify: true
    sni: cn2.cdn.xfltd-cdn.top
    type: trojan
    udp: true
  - name: "\U0001F1EF\U0001F1F5日本2 | ⬇️ 9.5MB/s"
    password: 7669a04d-a459-4c96-bc0e-fde51cb984f3
    port: 12035
    server: cn2.cdn.xfltd-cdn.top
    skip-cert-verify: true
    sni: cn2.cdn.xfltd-cdn.top
    type: trojan
    udp: true
  - name: "\U0001F1F0\U0001F1F7韩国3 | ⬇️ 10.1MB/s"
    password: RlzoEILU
    port: 47655
    server: *************
    skip-cert-verify: true
    type: trojan
    udp: true
  - cipher: chacha20-ietf-poly1305
    name: "\U0001F1FA\U0001F1F8美国7 | ⬇️ 9.0MB/s"
    password: ed6c8eea-8503-43e1-9563-0a355b0edcac
    port: 42023
    server: eepl1.dhh114514.christmas
    type: ss
    udp: true
  - name: "\U0001F1ED\U0001F1F0香港9 | ⬇️ 10.2MB/s"
    password: RlzoEILU
    port: 28296
    server: *************
    skip-cert-verify: true
    type: trojan
    udp: true
  - name: "\U0001F1ED\U0001F1F0香港10 | ⬇️ 8.2MB/s"
    password: RlzoEILU
    port: 46861
    server: *************
    skip-cert-verify: true
    type: trojan
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1EF\U0001F1F5日本3 | ⬇️ 8.1MB/s"
    password: c86d483c-431f-41df-bb6b-c1dcebfc7401
    port: 19010
    server: ************
    type: ss
    udp: true
  - name: "\U0001F1F8\U0001F1EC新加坡3 | ⬇️ 10.2MB/s"
    password: 7669a04d-a459-4c96-bc0e-fde51cb984f3
    port: 12025
    server: cn2.cdn.xfltd-cdn.top
    skip-cert-verify: true
    sni: cn2.cdn.xfltd-cdn.top
    type: trojan
    udp: true
  - alterId: 0
    cipher: auto
    name: "\U0001F1EF\U0001F1F5日本4 | ⬇️ 8.9MB/s"
    port: 17233
    server: 4a89bc7a-swtr40-sxzp1j-1irfn.cm5.p5pv.com
    skip-cert-verify: true
    tls: false
    type: vmess
    udp: true
    uuid: a33115b6-75fa-11ed-8826-f23c9164ca5d
  - name: "\U0001F1EF\U0001F1F5日本5 | ⬇️ 10.2MB/s"
    password: 7669a04d-a459-4c96-bc0e-fde51cb984f3
    port: 12033
    server: cn2.cdn.xfltd-cdn.top
    skip-cert-verify: true
    sni: cn2.cdn.xfltd-cdn.top
    type: trojan
    udp: true
  - name: "\U0001F1FA\U0001F1F8美国8 | ⬇️ 10.4MB/s"
    password: 7669a04d-a459-4c96-bc0e-fde51cb984f3
    port: 12053
    server: cn2.cdn.xfltd-cdn.top
    skip-cert-verify: true
    sni: cn2.cdn.xfltd-cdn.top
    type: trojan
    udp: true
  - name: "\U0001F1F8\U0001F1EC新加坡4 | ⬇️ 9.9MB/s"
    password: 7669a04d-a459-4c96-bc0e-fde51cb984f3
    port: 12024
    server: cn2.cdn.xfltd-cdn.top
    skip-cert-verify: true
    sni: cn2.cdn.xfltd-cdn.top
    type: trojan
    udp: true
  - name: "\U0001F1F8\U0001F1EC新加坡5 | ⬇️ 11.6MB/s"
    password: 7669a04d-a459-4c96-bc0e-fde51cb984f3
    port: 12022
    server: cn2.cdn.xfltd-cdn.top
    skip-cert-verify: true
    sni: cn2.cdn.xfltd-cdn.top
    type: trojan
    udp: true
  - name: "\U0001F1F3\U0001F1F1荷兰1 | ⬇️ 9.1MB/s"
    password: RlzoEILU
    port: 33097
    server: *************
    skip-cert-verify: true
    type: trojan
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F300其他6-VN | ⬇️ 10.3MB/s"
    password: abb58966-8d7b-4cdf-bae9-b0d8e184d93d
    port: 29001
    server: ************
    type: ss
    udp: true
  - cipher: aes-256-cfb
    name: "\U0001F1EF\U0001F1F5日本6 | ⬇️ 9.2MB/s"
    password: awsps0501
    port: 443
    server: **************
    type: ss
    udp: true
  - cipher: aes-256-cfb
    name: "\U0001F1F0\U0001F1F7韩国4 | ⬇️ 8.5MB/s"
    password: yijian0503
    port: 443
    server: ************
    type: ss
    udp: true
  - cipher: aes-256-cfb
    name: "\U0001F1EF\U0001F1F5日本7 | ⬇️ 9.7MB/s"
    password: awsps0501
    port: 443
    server: **************
    type: ss
    udp: true
  - cipher: aes-256-cfb
    name: "\U0001F1EF\U0001F1F5日本8 | ⬇️ 12.0MB/s"
    password: awsps0501
    port: 443
    server: ************
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1EE\U0001F1F9意大利1 | ⬇️ 8.4MB/s"
    password: ed6c8eea-8503-43e1-9563-0a355b0edcac
    port: 42003
    server: eepl1.dhh114514.christmas
    type: ss
    udp: true
  - cipher: chacha20-ietf-poly1305
    name: "\U0001F1FA\U0001F1F8美国9 | ⬇️ 10.9MB/s"
    password: ed6c8eea-8503-43e1-9563-0a355b0edcac
    port: 42022
    server: eepl1.dhh114514.christmas
    type: ss
    udp: true
  - cipher: chacha20-ietf-poly1305
    name: "\U0001F1F0\U0001F1F7韩国5 | ⬇️ 13.9MB/s"
    password: ed6c8eea-8503-43e1-9563-0a355b0edcac
    port: 42010
    server: eepl2.d-h-h.de
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1ED\U0001F1F0香港11 | ⬇️ 10.1MB/s"
    password: ed6c8eea-8503-43e1-9563-0a355b0edcac
    port: 42097
    server: eepl2.d-h-h.de
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1ED\U0001F1F0香港12 | ⬇️ 9.2MB/s"
    password: abb58966-8d7b-4cdf-bae9-b0d8e184d93d
    port: 16014
    server: *************
    type: ss
    udp: true
  - name: "\U0001F1F8\U0001F1EC新加坡6 | ⬇️ 9.3MB/s"
    password: 7669a04d-a459-4c96-bc0e-fde51cb984f3
    port: 12021
    server: cn2.cdn.xfltd-cdn.top
    skip-cert-verify: true
    sni: cn2.cdn.xfltd-cdn.top
    type: trojan
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1ED\U0001F1F0香港13 | ⬇️ 8.4MB/s"
    password: c86d483c-431f-41df-bb6b-c1dcebfc7401
    port: 16002
    server: ************
    type: ss
    udp: true
  - name: "\U0001F1FA\U0001F1F8美国10 | ⬇️ 9.7MB/s"
    password: 2b1ed981-6547-4094-998b-06a3323d6f6c
    port: 21332
    server: xd-js.timiwc.com
    skip-cert-verify: true
    sni: xd-js.timiwc.com
    type: trojan
    udp: true
  - name: "\U0001F300其他7-TR | ⬇️ 9.2MB/s"
    password: 7669a04d-a459-4c96-bc0e-fde51cb984f3
    port: 12064
    server: cn2.cdn.xfltd-cdn.top
    skip-cert-verify: true
    sni: cn2.cdn.xfltd-cdn.top
    type: trojan
    udp: true
  - name: "\U0001F300其他8-TH | ⬇️ 7.9MB/s"
    password: 7669a04d-a459-4c96-bc0e-fde51cb984f3
    port: 12076
    server: cn2.cdn.xfltd-cdn.top
    skip-cert-verify: true
    sni: cn2.cdn.xfltd-cdn.top
    type: trojan
    udp: true
  - cipher: chacha20-ietf-poly1305
    name: "\U0001F300其他9-SE | ⬇️ 10.9MB/s"
    password: ed6c8eea-8503-43e1-9563-0a355b0edcac
    port: 42015
    server: eepl2.d-h-h.de
    type: ss
    udp: true
  - cipher: chacha20-ietf-poly1305
    name: "\U0001F1ED\U0001F1F0香港14 | ⬇️ 15.5MB/s"
    password: ed6c8eea-8503-43e1-9563-0a355b0edcac
    port: 42099
    server: eepl1.dhh114514.christmas
    type: ss
    udp: true
  - cipher: chacha20-ietf-poly1305
    name: "\U0001F1EF\U0001F1F5日本9 | ⬇️ 15.5MB/s"
    password: f87772ed-cef9-444a-a8e8-bcf299c850ec
    port: 23335
    server: link.karleynetwork.xyz
    type: ss
    udp: true
  - name: "\U0001F1E9\U0001F1EA德国2 | ⬇️ 13.3MB/s"
    network: ws
    port: 8880
    server: **************
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
        User-Agent: >-
          Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like
          Gecko) Chrome/35.0.1916.153 Safari/537.36
      path: /?ed=2560
    xudp: true
  - name: "\U0001F1FA\U0001F1F8美国11 | ⬇️ 15.1MB/s"
    network: ws
    port: 8880
    server: ***********
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
        User-Agent: >-
          Mozilla/5.0 (Windows NT 6.1) AppleWebKit/537.36 (KHTML, like Gecko)
          Chrome/55.0.2883.87 Safari/537.36
      path: /?ed=2560
    xudp: true
  - cipher: aes-128-gcm
    name: "\U0001F1EF\U0001F1F5日本10 | ⬇️ 10.1MB/s"
    password: b86a48e8-542d-4b90-bb6e-ff0a3e7e4e97
    port: 36166
    server: sz.fanhua.art
    type: ss
    udp: true
  - cipher: aes-128-gcm
    name: "\U0001F1F9\U0001F1FC台湾1 | ⬇️ 10.4MB/s"
    password: b86a48e8-542d-4b90-bb6e-ff0a3e7e4e97
    port: 17154
    server: sz.fanhua.art
    type: ss
    udp: true
  - name: "\U0001F1FA\U0001F1F8美国12 | ⬇️ 13.5MB/s"
    network: ws
    port: 8880
    server: ***********
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
        User-Agent: >-
          Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like
          Gecko) Chrome/47.0.2526.106 Safari/537.36
      path: /?ed=2560
    xudp: true
    servername: reedfree8mahsang2.redorg.ir
  - name: "\U0001F1E9\U0001F1EA德国3 | ⬇️ 13.8MB/s"
    network: ws
    port: 80
    server: ************
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
        User-Agent: >-
          Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like
          Gecko) Chrome/49.0.2623.112 Safari/537.36
      path: '/Telegram: @vpnAndroid2/?ed=2560'
    xudp: true
  - name: "\U0001F1F8\U0001F1EC新加坡7 | ⬇️ 8.6MB/s"
    network: ws
    port: 8880
    server: **************
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
        User-Agent: >-
          Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML,
          like Gecko) Chrome/65.0.3325.181 Safari/537.36
      path: /?ed=2560
    xudp: true
  - name: "\U0001F300其他10-未识别 | ⬇️ 14.2MB/s"
    network: ws
    port: 80
    server: partner.zoom.us
    type: vless
    udp: true
    uuid: 438f9559-1671-45cf-9d2c-338fe6766acf
    ws-opts:
      headers:
        Host: 14.sahanwickramasinghe.shop
        User-Agent: >-
          Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML,
          like Gecko) Chrome/61.0.3163.100 Safari/537.36
      path: /
    xudp: true
    servername: 14.sahanwickramasinghe.shop
  - client-fingerprint: firefox
    name: "\U0001F1FA\U0001F1F8美国13 | ⬇️ 8.5MB/s"
    network: tcp
    port: 10001
    reality-opts:
      public-key: A8YlN1VE64tBOBhnaHy5lwBXRU79kELWmtNqlRsjvkE
      short-id: 1a24af87
    server: *************
    tls: true
    type: vless
    udp: true
    uuid: 748292a0-ac0f-4d55-a460-40bea786f17f
    xudp: true
    servername: yahoo.com
  - client-fingerprint: chrome
    flow: xtls-rprx-vision
    name: "\U0001F1EF\U0001F1F5日本11 | ⬇️ 9.9MB/s"
    network: tcp
    port: 20230
    reality-opts:
      public-key: lbOfuIKCBPcQH4AEnwnPw1LNxWrl-Bul6KU99H240Fc
      short-id: abae4722
    server: jp004.421421.xyz
    tls: true
    type: vless
    udp: true
    uuid: 2ba7e4d0-f2f9-456e-a43e-f5a724d7e367
    xudp: true
    servername: www.nvidia.com
  - cipher: aes-128-gcm
    name: "\U0001F1FA\U0001F1E6乌克兰1 | ⬇️ 8.9MB/s"
    password: b86a48e8-542d-4b90-bb6e-ff0a3e7e4e97
    port: 60028
    server: sz.fanhua.art
    type: ss
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1ED\U0001F1F0香港15 | ⬇️ 14.3MB/s"
    password: 2c605663-b89a-5734-a9d6-97d4743d72cf
    port: 8313
    server: dozo01.flztjc.top
    skip-cert-verify: false
    sni: hk-13-568.flztjc.net
    type: trojan
    udp: true
  - cipher: aes-128-gcm
    name: "\U0001F1ED\U0001F1F0香港16 | ⬇️ 11.3MB/s"
    password: b86a48e8-542d-4b90-bb6e-ff0a3e7e4e97
    port: 37001
    server: sz.fanhua.art
    type: ss
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1F0\U0001F1F7韩国6 | ⬇️ 11.6MB/s"
    password: RlzoEILU
    port: 28548
    server: *************
    skip-cert-verify: true
    sni: cdn.egvra.cn
    type: trojan
    udp: true
  - cipher: aes-128-gcm
    name: "\U0001F1ED\U0001F1F0香港17 | ⬇️ 12.1MB/s"
    password: b86a48e8-542d-4b90-bb6e-ff0a3e7e4e97
    port: 63447
    server: sz.fanhua.art
    type: ss
    udp: true
  - alterId: '0'
    cipher: auto
    name: "\U0001F1F3\U0001F1F1荷兰2 | ⬇️ 10.3MB/s"
    network: ws
    port: 80
    server: tiamo1.tiamocloud.us.kg
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 14c3dea6-4594-4d13-b251-737c3a5d79a3
    ws-opts:
      headers:
        Host: tiamo1.tiamocloud.us.kg
      path: /
    xudp: true
  - down: ''
    fingerprint: ''
    name: "\U0001F1FA\U0001F1F8美国14 | ⬇️ 8.6MB/s"
    obfs: ''
    obfs-password: ''
    password: 48c1e014-28d6-11ec-a0fc-f23c913c8d2b
    port: 8443
    server: a4e3ef78-swin40-sxlsd4-3z3v.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: a4e3ef78-swin40-sxlsd4-3z3v.hy2.gotochinatown.net
    type: hysteria2
    up: ''
  - cipher: aes-128-gcm
    name: "\U0001F1F8\U0001F1EC新加坡8 | ⬇️ 12.1MB/s"
    password: b86a48e8-542d-4b90-bb6e-ff0a3e7e4e97
    port: 21420
    server: sz.fanhua.art
    type: ss
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1ED\U0001F1F0香港18 | ⬇️ 15.8MB/s"
    password: 2c605663-b89a-5734-a9d6-97d4743d72cf
    port: 8313
    server: *************
    skip-cert-verify: true
    sni: hk-13-568.flztjc.net
    type: trojan
    udp: true
  - down: ''
    fingerprint: ''
    name: "\U0001F1FA\U0001F1F8美国15 | ⬇️ 9.0MB/s"
    obfs: ''
    obfs-password: ''
    password: 0da8651e-e1f6-11ec-bd7c-f23c913c8d2b
    port: 8443
    server: e06eb389-swq1s0-tcinla-hrtf.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: e06eb389-swq1s0-tcinla-hrtf.hy2.gotochinatown.net
    type: hysteria2
    up: ''
  - name: "\U0001F1F8\U0001F1EC新加坡9 | ⬇️ 18.3MB/s"
    network: ws
    port: 8880
    server: ***********
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
        User-Agent: >-
          Mozilla/5.0 (Linux; Android 7.0; Moto G (5) Build/NPPS25.137-93-14)
          AppleWebKit/537.36 (KHTML, like Gecko) Chrome/69.0.3497.100 Mobile
          Safari/537.36
      path: /?ed=2560
    xudp: true
  - client-fingerprint: chrome
    name: "\U0001F1FA\U0001F1F8美国16 | ⬇️ 8.5MB/s"
    password: 398e0d38-8649-11ef-959c-f23c9164ca5d
    port: 15229
    server: 699bbddc-swq1s0-sxzls8-1gxvd.cu.plebai.net
    skip-cert-verify: false
    type: trojan
    udp: true
    sni: 699bbddc-swq1s0-sxzls8-1gxvd.cu.plebai.net
  - cipher: aes-128-gcm
    name: "\U0001F1F0\U0001F1F7韩国7 | ⬇️ 10.6MB/s"
    password: b86a48e8-542d-4b90-bb6e-ff0a3e7e4e97
    port: 24733
    server: sz.fanhua.art
    type: ss
    udp: true
  - name: "\U0001F1E9\U0001F1EA德国4 | ⬇️ 15.9MB/s"
    network: ws
    port: 80
    server: *************
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
        User-Agent: >-
          Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML,
          like Gecko) Chrome/59.0.3071.115 Safari/537.36
      path: '/Telegram:'
    xudp: true
  - cipher: aes-256-gcm
    name: "\U0001F1F8\U0001F1EC新加坡10 | ⬇️ 10.1MB/s"
    password: c86d483c-431f-41df-bb6b-c1dcebfc7401
    port: 18002
    server: *************
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1FA\U0001F1F8美国17 | ⬇️ 9.4MB/s"
    password: c86d483c-431f-41df-bb6b-c1dcebfc7401
    port: 20010
    server: *************
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1FA\U0001F1F8美国18 | ⬇️ 8.8MB/s"
    password: c86d483c-431f-41df-bb6b-c1dcebfc7401
    port: 20007
    server: ************
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1F8\U0001F1EC新加坡11 | ⬇️ 12.0MB/s"
    password: b8fa2e30-a210-4d8b-bb1e-bf42ea9c9e7e
    port: 18005
    server: *************
    type: ss
    udp: true
  - cipher: aes-256-cfb
    name: "\U0001F1F8\U0001F1EC新加坡12 | ⬇️ 11.5MB/s"
    password: qawszxc123
    port: 443
    server: ************
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F300其他11-MY | ⬇️ 10.3MB/s"
    password: c86d483c-431f-41df-bb6b-c1dcebfc7401
    port: 29010
    server: *************
    type: ss
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F300其他12-MY | ⬇️ 11.0MB/s"
    password: 2b1ed981-6547-4094-998b-06a3323d6f6c
    port: 21031
    server: **************
    skip-cert-verify: true
    type: trojan
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1FA\U0001F1F8美国19 | ⬇️ 9.2MB/s"
    password: 200c650a-4d7c-4180-b0ce-20093784902e
    port: 20005
    server: *************
    type: ss
    udp: true
  - cipher: aes-128-gcm
    name: "\U0001F1F8\U0001F1EC新加坡13 | ⬇️ 11.6MB/s"
    password: 763bf612-4c66-4fd4-b54b-5349bdea6bca
    port: 570
    server: aisalayer-b.upperlay.xyz
    type: ss
    udp: true
  - cipher: aes-128-gcm
    name: "\U0001F1F8\U0001F1EC新加坡14 | ⬇️ 10.9MB/s"
    password: 763bf612-4c66-4fd4-b54b-5349bdea6bca
    port: 568
    server: aisalayer-a.upperlay.xyz
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1F8\U0001F1EC新加坡15 | ⬇️ 8.1MB/s"
    password: abb58966-8d7b-4cdf-bae9-b0d8e184d93d
    port: 18004
    server: *************
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1F8\U0001F1EC新加坡16 | ⬇️ 9.8MB/s"
    password: abb58966-8d7b-4cdf-bae9-b0d8e184d93d
    port: 18002
    server: ************
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1EF\U0001F1F5日本12 | ⬇️ 9.0MB/s"
    password: abb58966-8d7b-4cdf-bae9-b0d8e184d93d
    port: 19002
    server: *************
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1EF\U0001F1F5日本13 | ⬇️ 11.6MB/s"
    password: abb58966-8d7b-4cdf-bae9-b0d8e184d93d
    port: 19007
    server: *************
    type: ss
    udp: true
  - down: ''
    fingerprint: ''
    name: "\U0001F1FA\U0001F1F8美国20 | ⬇️ 8.1MB/s"
    obfs: ''
    obfs-password: ''
    password: nfsn666
    port: 8888
    server: sj-arm.nfsn666.gq
    skip-cert-verify: true
    sni: sj-arm.nfsn666.gq
    type: hysteria2
    up: ''
  - cipher: aes-256-gcm
    name: "\U0001F1F9\U0001F1FC台湾2 | ⬇️ 10.7MB/s"
    password: abb58966-8d7b-4cdf-bae9-b0d8e184d93d
    port: 17010
    server: *************
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1F9\U0001F1FC台湾3 | ⬇️ 8.7MB/s"
    password: abb58966-8d7b-4cdf-bae9-b0d8e184d93d
    port: 17001
    server: ************
    type: ss
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1FA\U0001F1F8美国21 | ⬇️ 10.2MB/s"
    password: 2b1ed981-6547-4094-998b-06a3323d6f6c
    port: 21118
    server: **************
    skip-cert-verify: true
    sni: k17.tudou211.com
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1ED\U0001F1F0香港19 | ⬇️ 10.1MB/s"
    password: 7669a04d-a459-4c96-bc0e-fde51cb984f3
    port: 12003
    server: cn1.cdn.xfltd-cdn.top
    skip-cert-verify: true
    sni: cdn.alibaba.com
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1ED\U0001F1F0香港20 | ⬇️ 12.9MB/s"
    password: 7669a04d-a459-4c96-bc0e-fde51cb984f3
    port: 12007
    server: cn1.cdn.xfltd-cdn.top
    skip-cert-verify: true
    sni: cdn.alibaba.com
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1ED\U0001F1F0香港21 | ⬇️ 10.0MB/s"
    password: 7669a04d-a459-4c96-bc0e-fde51cb984f3
    port: 12006
    server: cn1.cdn.xfltd-cdn.top
    skip-cert-verify: true
    sni: cdn.alibaba.com
    type: trojan
    udp: true
  - alpn:
      - ''
    alterId: '0'
    cipher: auto
    name: "\U0001F1ED\U0001F1F0香港22 | ⬇️ 10.2MB/s"
    network: ws
    port: 80
    server: 9351cca4-e594-cbfa-a6de-3c1ee2315694.castlepeakhospital.moe
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 14a8a0db-e922-453a-af8d-843af8734b3b
    ws-opts:
      headers:
        Host: tms.dingtalk.com
      path: /
    xudp: true
  - alpn:
      - ''
    alterId: '0'
    cipher: auto
    name: "\U0001F1ED\U0001F1F0香港23 | ⬇️ 9.0MB/s"
    network: ws
    port: 80
    server: 0b17b6cf-2c23-f38e-114f-8f1f3bcd06a3.castlepeakhospital.moe
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 14a8a0db-e922-453a-af8d-843af8734b3b
    ws-opts:
      headers:
        Host: tms.dingtalk.com
      path: /
    xudp: true
  - client-fingerprint: chrome
    name: "\U0001F1FA\U0001F1F8美国22 | ⬇️ 10.2MB/s"
    password: 7669a04d-a459-4c96-bc0e-fde51cb984f3
    port: 12004
    server: cn1.cdn.xfltd-cdn.top
    skip-cert-verify: true
    sni: cdn.alibaba.com
    type: trojan
    udp: true
  - alpn:
      - ''
    alterId: '0'
    cipher: auto
    name: "\U0001F1ED\U0001F1F0香港24 | ⬇️ 15.2MB/s"
    network: ws
    port: 80
    server: 445522e0-a147-66c0-6b59-becb865f30a5.castlepeakhospital.moe
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 14a8a0db-e922-453a-af8d-843af8734b3b
    ws-opts:
      headers:
        Host: tms.dingtalk.com
      path: /
    xudp: true
  - client-fingerprint: chrome
    name: "\U0001F1F8\U0001F1EC新加坡17 | ⬇️ 11.3MB/s"
    password: 7669a04d-a459-4c96-bc0e-fde51cb984f3
    port: 12023
    server: cn2.cdn.xfltd-cdn.top
    skip-cert-verify: true
    sni: cdn.alibaba.com
    type: trojan
    udp: true
  - alpn:
      - ''
    alterId: '0'
    cipher: auto
    name: "\U0001F1F8\U0001F1EC新加坡18 | ⬇️ 11.2MB/s"
    network: ws
    port: 80
    server: 0989a4f1-856a-4d0b-530b-aed9bf289ad8.castlepeakhospital.moe
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 14a8a0db-e922-453a-af8d-843af8734b3b
    ws-opts:
      headers:
        Host: tms.dingtalk.com
      path: /
    xudp: true
  - alpn:
      - ''
    alterId: '0'
    cipher: auto
    name: "\U0001F1F8\U0001F1EC新加坡19 | ⬇️ 8.4MB/s"
    network: ws
    port: 80
    server: 06809388-302a-29e7-ec15-006f5353d530.castlepeakhospital.moe
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 14a8a0db-e922-453a-af8d-843af8734b3b
    ws-opts:
      headers:
        Host: tms.dingtalk.com
      path: /
    xudp: true
  - client-fingerprint: chrome
    flow: xtls-rprx-vision
    http-opts:
      headers: {}
      path:
        - /
    name: "\U0001F1EF\U0001F1F5日本14 | ⬇️ 12.8MB/s"
    network: tcp
    port: 20230
    reality-opts:
      public-key: opaO8sUF9JU5hP2wRoUgS6aWxFLfen83in6ZWJMonG4
      short-id: b92b2a09
    server: jp001.421421.xyz
    tls: true
    type: vless
    udp: true
    uuid: 0bfa2050-b165-4156-859c-70f36d300dce
    xudp: true
    servername: www.nvidia.com
  - alpn:
      - ''
    alterId: '0'
    cipher: auto
    name: "\U0001F1FA\U0001F1F8美国23 | ⬇️ 13.8MB/s"
    network: ws
    port: 20012
    server: 2fea6447-81c4-2d7d-d592-a4f729209777.7head.icu
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 14a8a0db-e922-453a-af8d-843af8734b3b
    ws-opts:
      headers:
        Host: tms.dingtalk.com
      path: /
    xudp: true
  - client-fingerprint: chrome
    name: "\U0001F1FA\U0001F1F8美国24 | ⬇️ 10.7MB/s"
    password: 7669a04d-a459-4c96-bc0e-fde51cb984f3
    port: 12052
    server: cn2.cdn.xfltd-cdn.top
    skip-cert-verify: true
    sni: cdn.alibaba.com
    type: trojan
    udp: true
  - cipher: chacha20-ietf-poly1305
    name: "\U0001F1F9\U0001F1FC台湾4 | ⬇️ 11.0MB/s"
    password: ed6c8eea-8503-43e1-9563-0a355b0edcac
    port: 42029
    server: eepl2.d-h-h.de
    type: ss
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1FA\U0001F1F8美国25 | ⬇️ 8.3MB/s"
    password: 7669a04d-a459-4c96-bc0e-fde51cb984f3
    port: 12054
    server: cn2.cdn.xfltd-cdn.top
    skip-cert-verify: true
    sni: cdn.alibaba.com
    type: trojan
    udp: true
  - alpn:
      - ''
    alterId: '0'
    cipher: auto
    name: "\U0001F1F9\U0001F1FC台湾5 | ⬇️ 12.3MB/s"
    network: ws
    port: 80
    server: 205928ec-61f8-1f90-8438-1b912b849b80.castlepeakhospital.moe
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 14a8a0db-e922-453a-af8d-843af8734b3b
    ws-opts:
      headers:
        Host: tms.dingtalk.com
      path: /
    xudp: true
  - alpn:
      - ''
    alterId: '0'
    cipher: auto
    name: "\U0001F1F9\U0001F1FC台湾6 | ⬇️ 10.0MB/s"
    network: ws
    port: 80
    server: 85ef02dd-b03e-94d5-61f4-bb5a8d5f491f.castlepeakhospital.moe
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 14a8a0db-e922-453a-af8d-843af8734b3b
    ws-opts:
      headers:
        Host: tms.dingtalk.com
      path: /
    xudp: true
  - cipher: chacha20-ietf-poly1305
    name: "\U0001F1F9\U0001F1FC台湾7 | ⬇️ 9.4MB/s"
    password: ed6c8eea-8503-43e1-9563-0a355b0edcac
    port: 42029
    server: eepl1.dhh114514.christmas
    type: ss
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1F9\U0001F1FC台湾8 | ⬇️ 9.6MB/s"
    password: 7669a04d-a459-4c96-bc0e-fde51cb984f3
    port: 12011
    server: cn1.cdn.xfltd-cdn.top
    skip-cert-verify: true
    sni: cdn.alibaba.com
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1EE\U0001F1F3印度1 | ⬇️ 10.1MB/s"
    password: 7669a04d-a459-4c96-bc0e-fde51cb984f3
    port: 12072
    server: cn2.cdn.xfltd-cdn.top
    skip-cert-verify: true
    sni: cdn.alibaba.com
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1E6\U0001F1FA澳大利亚1 | ⬇️ 11.1MB/s"
    password: 7669a04d-a459-4c96-bc0e-fde51cb984f3
    port: 12068
    server: cn2.cdn.xfltd-cdn.top
    skip-cert-verify: true
    sni: cdn.alibaba.com
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F300其他13-MY | ⬇️ 10.4MB/s"
    password: ed6c8eea-8503-43e1-9563-0a355b0edcac
    port: 41012
    server: cm1.d-h-h.in
    skip-cert-verify: true
    sni: v1-my1.776688.best
    type: trojan
    udp: true
  - cipher: chacha20-ietf-poly1305
    name: "\U0001F1F7\U0001F1FA俄罗斯2 | ⬇️ 10.1MB/s"
    password: ed6c8eea-8503-43e1-9563-0a355b0edcac
    port: 41001
    server: eepl2.d-h-h.de
    type: ss
    udp: true
  - cipher: chacha20-ietf-poly1305
    name: "\U0001F1F7\U0001F1FA俄罗斯3 | ⬇️ 8.2MB/s"
    password: ed6c8eea-8503-43e1-9563-0a355b0edcac
    port: 41001
    server: eepl1.dhh114514.christmas
    type: ss
    udp: true
  - alpn:
      - ''
    alterId: '0'
    cipher: auto
    name: "\U0001F300其他14-ID | ⬇️ 8.7MB/s"
    network: tcp
    port: 17234
    server: 4a89bc7a-swtr40-sxzp1j-1irfn.cm5.p5pv.com
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: a33115b6-75fa-11ed-8826-f23c9164ca5d
    xudp: true
  - client-fingerprint: chrome
    name: "\U0001F1E8\U0001F1ED瑞士1 | ⬇️ 10.7MB/s"
    password: 7669a04d-a459-4c96-bc0e-fde51cb984f3
    port: 12062
    server: cn2.cdn.xfltd-cdn.top
    skip-cert-verify: true
    sni: cdn.alibaba.com
    type: trojan
    udp: true
  - down: ''
    fingerprint: ''
    name: "\U0001F1FA\U0001F1F8美国26 | ⬇️ 8.4MB/s"
    obfs: ''
    obfs-password: ''
    password: 93fb69fc-77cf-11ee-85ee-f23c91369f2d
    port: 8443
    server: a132e977-sw5og0-t12cnj-1ol97.hy2.gotochinatown.net
    skip-cert-verify: true
    sni: a132e977-sw5og0-t12cnj-1ol97.hy2.gotochinatown.net
    type: hysteria2
    up: ''
  - name: "\U0001F1E9\U0001F1EA德国5 | ⬇️ 11.9MB/s"
    network: ws
    port: 8880
    server: ***********
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
        User-Agent: >-
          Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML,
          like Gecko) Chrome/60.0.3112.113 Safari/537.36
      path: "/Telegram\U0001F1E8\U0001F1F3 @pgkj666 /?ed=2560"
    xudp: true
  - client-fingerprint: chrome
    name: "\U0001F1ED\U0001F1F0香港25 | ⬇️ 10.8MB/s"
    password: RlzoEILU
    port: 39689
    server: *************
    skip-cert-verify: true
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1F0\U0001F1F7韩国8 | ⬇️ 11.0MB/s"
    password: RlzoEILU
    port: 28910
    server: *************
    skip-cert-verify: true
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1E6\U0001F1FA澳大利亚2 | ⬇️ 9.6MB/s"
    password: RlzoEILU
    port: 11641
    server: *************
    skip-cert-verify: true
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1ED\U0001F1F0香港26 | ⬇️ 10.9MB/s"
    password: RlzoEILU
    port: 3754
    server: *************
    skip-cert-verify: true
    type: trojan
    udp: true
  - name: "\U0001F1E9\U0001F1EA德国6 | ⬇️ 18.5MB/s"
    network: ws
    port: 8880
    server: ************
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
        User-Agent: >-
          Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML,
          like Gecko) Chrome/64.0.3282.186 Safari/537.36
      path: "/Telegram\U0001F1E8\U0001F1F3 @pgkj666 /?ed=2560"
    xudp: true
  - client-fingerprint: chrome
    name: "\U0001F1F3\U0001F1F1荷兰3 | ⬇️ 9.2MB/s"
    password: RlzoEILU
    port: 8565
    server: *************
    skip-cert-verify: true
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1E6\U0001F1FA澳大利亚3 | ⬇️ 10.5MB/s"
    password: RlzoEILU
    port: 40252
    server: **************
    skip-cert-verify: true
    type: trojan
    udp: true
  - alpn:
      - ''
    alterId: '2'
    cipher: auto
    name: "\U0001F1ED\U0001F1F0香港27 | ⬇️ 8.7MB/s"
    network: ws
    port: 80
    server: 04b47978-swb8g0-swdpud-duku.hk3.p5pv.com
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 8e0f8ef2-5fe3-11ec-a8bf-f23c91cfbbc9
    ws-opts:
      headers:
        Host: broadcastlv.chat.bilibili.com
      path: /
    xudp: true
  - down: ''
    fingerprint: ''
    name: "\U0001F1FA\U0001F1F8美国27 | ⬇️ 7.8MB/s"
    obfs: ''
    obfs-password: ''
    password: b79d79ae-8bce-11ef-a2b8-f23c9164ca5d
    port: 8443
    server: 341d6ff9-swb8g0-t67sv3-1snfs.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 341d6ff9-swb8g0-t67sv3-1snfs.hy2.gotochinatown.net
    type: hysteria2
    up: ''
  - client-fingerprint: chrome
    flow: xtls-rprx-vision
    name: "\U0001F1F8\U0001F1EC新加坡20 | ⬇️ 11.4MB/s"
    network: tcp
    port: 20230
    reality-opts:
      public-key: hBXQa1tSoP0jjIezHxeyKhf1YKxe8CoBHoxvIxEJuAc
      short-id: 2c5ecaae
    server: sg003.421421.xyz
    tls: true
    type: vless
    udp: true
    uuid: fdd1d613-f234-40f6-b63d-97517e0fc4b3
    xudp: true
    servername: www.nvidia.com
  - down: ''
    fingerprint: ''
    name: "\U0001F1FA\U0001F1F8美国28 | ⬇️ 8.3MB/s"
    obfs: ''
    obfs-password: ''
    password: 4a7f880c-72f4-11ed-b0b5-f23c9164ca5d
    port: 8443
    server: 0e53d104-svwf40-t6ouc9-13xtu.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 0e53d104-svwf40-t6ouc9-13xtu.hy2.gotochinatown.net
    type: hysteria2
    up: ''
  - alterId: '2'
    cipher: auto
    name: "\U0001F1ED\U0001F1F0香港28 | ⬇️ 8.0MB/s"
    network: ws
    port: 80
    server: 11a88ecb-swkhs0-tf70jh-vm13.hk.p5pv.com
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 3fa7157d-efe8-832e-9b3c-582d78efbc52
    ws-opts:
      headers:
        Host: broadcastlv.chat.bilibili.com
      path: /
    xudp: true
  - alterId: '2'
    cipher: auto
    name: "\U0001F1ED\U0001F1F0香港29 | ⬇️ 9.8MB/s"
    network: ws
    port: 80
    server: 373dbf60-swkhs0-t8kd6j-1c9em.hk.p5pv.com
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 336501b6-51d2-11ee-a993-f23c9164ca5d
    ws-opts:
      headers:
        Host: broadcastlv.chat.bilibili.com
      path: /
    xudp: true
  - cipher: 2022-blake3-aes-128-gcm
    name: "\U0001F1FA\U0001F1F8美国29 | ⬇️ 8.1MB/s"
    password: 'M2JmZjZkMWQyOGE0Yjg2NQ==:Yjc1MWU5OTUtYjI0Yy00Zg=='
    port: 2012
    server: guang.ccwink.cc
    type: ss
    udp: true
  - cipher: 2022-blake3-aes-128-gcm
    name: "\U0001F1F8\U0001F1EC新加坡21 | ⬇️ 9.2MB/s"
    password: 'M2JmZjZkMWQyOGE0Yjg2NQ==:Yjc1MWU5OTUtYjI0Yy00Zg=='
    port: 2015
    server: guang.ccwink.cc
    type: ss
    udp: true
  - cipher: 2022-blake3-aes-128-gcm
    name: "\U0001F1F8\U0001F1EC新加坡22 | ⬇️ 9.7MB/s"
    password: 'M2JmZjZkMWQyOGE0Yjg2NQ==:Yjc1MWU5OTUtYjI0Yy00Zg=='
    port: 2014
    server: guang.ccwink.cc
    type: ss
    udp: true
  - alterId: 64
    cipher: auto
    name: "\U0001F1EF\U0001F1F5日本15 | ⬇️ 9.6MB/s"
    network: ws
    port: 1443
    server: sslvpn.51job.com
    skip-cert-verify: true
    tls: true
    type: vmess
    uuid: a6a0d901-67e9-460a-90b5-634c5c4f9782
    ws-opts:
      headers:
        Host: centos7
      path: /634c5c4f9782
    servername: centos7
port: 7890
socks-port: 7891
redir-port: 7892
mixed-port: 7893
tproxy-port: 7894
ipv6: false
allow-lan: true
unified-delay: true
tcp-concurrent: true
geodata-mode: false
geodata-loader: standard
geo-auto-update: true
geo-update-interval: 48
geox-url:
  geoip: 'https://testingcf.jsdelivr.net/gh/MetaCubeX/meta-rules-dat@release/geoip.dat'
  geosite: >-
    https://testingcf.jsdelivr.net/gh/MetaCubeX/meta-rules-dat@release/geosite.dat
  mmdb: >-
    https://testingcf.jsdelivr.net/gh/MetaCubeX/meta-rules-dat@release/country.mmdb
  asn: >-
    https://github.com/xishang0128/geoip/releases/download/latest/GeoLite2-ASN.mmdb
profile:
  store-selected: true
  store-fake-ip: true
sniffer:
  enable: true
  sniff:
    HTTP:
      ports:
        - 80
        - 8080-8880
      override-destination: true
    TLS:
      ports:
        - 443
        - 8443
    QUIC:
      ports:
        - 443
        - 8443
  force-domain:
    - +.v2ex.com
  skip-domain:
    - Mijia Cloud
    - dlg.io.mi.com
    - +.push.apple.com
    - +.apple.com
dns:
  enable: true
  listen: '0.0.0.0:1053'
  ipv6: false
  respect-rules: true
  enhanced-mode: fake-ip
  fake-ip-range: ********/8
  fake-ip-filter-mode: blacklist
  fake-ip-filter:
    - +.lan
    - +.local
    - 'geosite:private'
    - 'geosite:cn'
  default-nameserver:
    - *********
    - ************
  proxy-server-nameserver:
    - *********
    - ************
  nameserver:
    - *********
    - ************
  nameserver-policy:
    'rule-set:private_domain,cn_domain':
      - *********
      - ************
    'rule-set:geolocation-!cn':
      - 'https://dns.cloudflare.com/dns-query'
      - 'https://dns.google/dns-query'
pr:
  type: select
  proxies:
    - "\U0001F680 节点选择"
    - "\U0001F1ED\U0001F1F0 香港负载均衡"
    - "\U0001F1EF\U0001F1F5 日本负载均衡"
    - "\U0001F1F0\U0001F1F7 韩国负载均衡"
    - "\U0001F1F8\U0001F1EC 新加坡负载均衡"
    - "\U0001F1FA\U0001F1F8 美国负载均衡"
    - "\U0001F1ED\U0001F1F0 香港自动"
    - "\U0001F1EF\U0001F1F5 日本自动"
    - "\U0001F1F0\U0001F1F7 韩国自动"
    - "\U0001F1F8\U0001F1EC 新加坡自动"
    - "\U0001F1FA\U0001F1F8 美国自动"
    - ♻️ 自动选择
    - "\U0001F1ED\U0001F1F0 香港节点"
    - "\U0001F1EF\U0001F1F5 日本节点"
    - "\U0001F1F0\U0001F1F7 韩国节点"
    - "\U0001F1F8\U0001F1EC 新加坡节点"
    - "\U0001F1FA\U0001F1F8 美国节点"
    - "\U0001F310 全部节点"
proxy-groups:
  - name: "\U0001F310 全部节点"
    type: select
    include-all: true
  - name: "\U0001F680 节点选择"
    type: select
    proxies:
      - "\U0001F1ED\U0001F1F0 香港负载均衡"
      - "\U0001F1EF\U0001F1F5 日本负载均衡"
      - "\U0001F1F0\U0001F1F7 韩国负载均衡"
      - "\U0001F1F8\U0001F1EC 新加坡负载均衡"
      - "\U0001F1FA\U0001F1F8 美国负载均衡"
      - "\U0001F1ED\U0001F1F0 香港自动"
      - "\U0001F1EF\U0001F1F5 日本自动"
      - "\U0001F1F0\U0001F1F7 韩国自动"
      - "\U0001F1F8\U0001F1EC 新加坡自动"
      - "\U0001F1FA\U0001F1F8 美国自动"
      - ♻️ 自动选择
      - "\U0001F1ED\U0001F1F0 香港节点"
      - "\U0001F1EF\U0001F1F5 日本节点"
      - "\U0001F1F0\U0001F1F7 韩国节点"
      - "\U0001F1F8\U0001F1EC 新加坡节点"
      - "\U0001F1FA\U0001F1F8 美国节点"
      - "\U0001F310 全部节点"
  - name: "\U0001F4AC ChatGPT"
    type: select
    proxies:
      - "\U0001F680 节点选择"
      - "\U0001F1ED\U0001F1F0 香港负载均衡"
      - "\U0001F1EF\U0001F1F5 日本负载均衡"
      - "\U0001F1F0\U0001F1F7 韩国负载均衡"
      - "\U0001F1F8\U0001F1EC 新加坡负载均衡"
      - "\U0001F1FA\U0001F1F8 美国负载均衡"
      - "\U0001F1ED\U0001F1F0 香港自动"
      - "\U0001F1EF\U0001F1F5 日本自动"
      - "\U0001F1F0\U0001F1F7 韩国自动"
      - "\U0001F1F8\U0001F1EC 新加坡自动"
      - "\U0001F1FA\U0001F1F8 美国自动"
      - ♻️ 自动选择
      - "\U0001F1ED\U0001F1F0 香港节点"
      - "\U0001F1EF\U0001F1F5 日本节点"
      - "\U0001F1F0\U0001F1F7 韩国节点"
      - "\U0001F1F8\U0001F1EC 新加坡节点"
      - "\U0001F1FA\U0001F1F8 美国节点"
      - "\U0001F310 全部节点"
  - name: "\U0001F4FA YouTube"
    type: select
    proxies:
      - "\U0001F680 节点选择"
      - "\U0001F1ED\U0001F1F0 香港负载均衡"
      - "\U0001F1EF\U0001F1F5 日本负载均衡"
      - "\U0001F1F0\U0001F1F7 韩国负载均衡"
      - "\U0001F1F8\U0001F1EC 新加坡负载均衡"
      - "\U0001F1FA\U0001F1F8 美国负载均衡"
      - "\U0001F1ED\U0001F1F0 香港自动"
      - "\U0001F1EF\U0001F1F5 日本自动"
      - "\U0001F1F0\U0001F1F7 韩国自动"
      - "\U0001F1F8\U0001F1EC 新加坡自动"
      - "\U0001F1FA\U0001F1F8 美国自动"
      - ♻️ 自动选择
      - "\U0001F1ED\U0001F1F0 香港节点"
      - "\U0001F1EF\U0001F1F5 日本节点"
      - "\U0001F1F0\U0001F1F7 韩国节点"
      - "\U0001F1F8\U0001F1EC 新加坡节点"
      - "\U0001F1FA\U0001F1F8 美国节点"
      - "\U0001F310 全部节点"
  - name: "\U0001F3B5 TikTok"
    type: select
    proxies:
      - "\U0001F680 节点选择"
      - "\U0001F1ED\U0001F1F0 香港负载均衡"
      - "\U0001F1EF\U0001F1F5 日本负载均衡"
      - "\U0001F1F0\U0001F1F7 韩国负载均衡"
      - "\U0001F1F8\U0001F1EC 新加坡负载均衡"
      - "\U0001F1FA\U0001F1F8 美国负载均衡"
      - "\U0001F1ED\U0001F1F0 香港自动"
      - "\U0001F1EF\U0001F1F5 日本自动"
      - "\U0001F1F0\U0001F1F7 韩国自动"
      - "\U0001F1F8\U0001F1EC 新加坡自动"
      - "\U0001F1FA\U0001F1F8 美国自动"
      - ♻️ 自动选择
      - "\U0001F1ED\U0001F1F0 香港节点"
      - "\U0001F1EF\U0001F1F5 日本节点"
      - "\U0001F1F0\U0001F1F7 韩国节点"
      - "\U0001F1F8\U0001F1EC 新加坡节点"
      - "\U0001F1FA\U0001F1F8 美国节点"
      - "\U0001F310 全部节点"
  - name: "\U0001F3AC NETFLIX"
    type: select
    proxies:
      - "\U0001F680 节点选择"
      - "\U0001F1ED\U0001F1F0 香港负载均衡"
      - "\U0001F1EF\U0001F1F5 日本负载均衡"
      - "\U0001F1F0\U0001F1F7 韩国负载均衡"
      - "\U0001F1F8\U0001F1EC 新加坡负载均衡"
      - "\U0001F1FA\U0001F1F8 美国负载均衡"
      - "\U0001F1ED\U0001F1F0 香港自动"
      - "\U0001F1EF\U0001F1F5 日本自动"
      - "\U0001F1F0\U0001F1F7 韩国自动"
      - "\U0001F1F8\U0001F1EC 新加坡自动"
      - "\U0001F1FA\U0001F1F8 美国自动"
      - ♻️ 自动选择
      - "\U0001F1ED\U0001F1F0 香港节点"
      - "\U0001F1EF\U0001F1F5 日本节点"
      - "\U0001F1F0\U0001F1F7 韩国节点"
      - "\U0001F1F8\U0001F1EC 新加坡节点"
      - "\U0001F1FA\U0001F1F8 美国节点"
      - "\U0001F310 全部节点"
  - name: "\U0001F1ED\U0001F1F0 香港节点"
    type: select
    include-all: true
    filter: (?i)港|hk|hongkong|hong kong
  - name: "\U0001F1EF\U0001F1F5 日本节点"
    type: select
    include-all: true
    filter: (?i)日|jp|japan
  - name: "\U0001F1F0\U0001F1F7 韩国节点"
    type: select
    include-all: true
    filter: (?i)韩|kr|korea
  - name: "\U0001F1F8\U0001F1EC 新加坡节点"
    type: select
    include-all: true
    filter: (?i)新|狮|sg|singapore|新加坡
  - name: "\U0001F1FA\U0001F1F8 美国节点"
    type: select
    include-all: true
    filter: (?i)美|us|unitedstates|united states
  - name: "\U0001F1ED\U0001F1F0 香港自动"
    type: url-test
    include-all: true
    tolerance: 20
    interval: 300
    filter: (?=.*(港|HK|(?i)Hong))^((?!(台|日|韩|新|深|美)).)*$
  - name: "\U0001F1EF\U0001F1F5 日本自动"
    type: url-test
    include-all: true
    tolerance: 20
    interval: 300
    filter: (?=.*(日|JP|(?i)Japan))^((?!(港|台|韩|新|美)).)*$
  - name: "\U0001F1F0\U0001F1F7 韩国自动"
    type: url-test
    include-all: true
    tolerance: 20
    interval: 300
    filter: (?=.*(韩|KR|(?i)Korea))^((?!(港|台|日|新|美)).)*$
  - name: "\U0001F1F8\U0001F1EC 新加坡自动"
    type: url-test
    include-all: true
    tolerance: 20
    interval: 300
    filter: (?=.*(新|狮|SG|(?i)Singapore))^((?!(港|台|日|韩|美)).)*$
  - name: "\U0001F1FA\U0001F1F8 美国自动"
    type: url-test
    include-all: true
    tolerance: 20
    interval: 300
    filter: (?=.*(美|US|(?i)States|America))^((?!(港|台|日|韩|新)).)*$
  - name: ♻️ 自动选择
    type: url-test
    include-all: true
    tolerance: 20
    interval: 300
    filter: ^((?!(直连)).)*$
  - name: "\U0001F1ED\U0001F1F0 香港负载均衡"
    type: load-balance
    include-all: true
    interval: 60
    lazy: true
    url: 'http://www.google.com/blank.html'
    disable-udp: false
    strategy: round-robin
    timeout: 2000
    max-failed-times: 3
    filter: (?=.*(港|HK|(?i)Hong))^((?!(台|日|韩|新|深|美)).)*$
  - name: "\U0001F1EF\U0001F1F5 日本负载均衡"
    type: load-balance
    include-all: true
    interval: 60
    lazy: true
    url: 'http://www.google.com/blank.html'
    disable-udp: false
    strategy: round-robin
    timeout: 2000
    max-failed-times: 3
    filter: (?=.*(日|JP|(?i)Japan))^((?!(港|台|韩|新|美)).)*$
  - name: "\U0001F1F0\U0001F1F7 韩国负载均衡"
    type: load-balance
    include-all: true
    interval: 60
    lazy: true
    url: 'http://www.google.com/blank.html'
    disable-udp: false
    strategy: round-robin
    timeout: 2000
    max-failed-times: 3
    filter: (?=.*(韩|KR|(?i)Korea))^((?!(港|台|日|新|美)).)*$
  - name: "\U0001F1F8\U0001F1EC 新加坡负载均衡"
    type: load-balance
    include-all: true
    interval: 60
    lazy: true
    url: 'http://www.google.com/blank.html'
    disable-udp: false
    strategy: round-robin
    timeout: 2000
    max-failed-times: 3
    filter: (?=.*(新|狮|SG|(?i)Singapore))^((?!(港|台|日|韩|美)).)*$
  - name: "\U0001F1FA\U0001F1F8 美国负载均衡"
    type: load-balance
    include-all: true
    interval: 60
    lazy: true
    url: 'http://www.google.com/blank.html'
    disable-udp: false
    strategy: round-robin
    timeout: 2000
    max-failed-times: 3
    filter: (?=.*(美|US|(?i)States|America))^((?!(港|台|日|韩|新)).)*$
  - name: "\U0001F3AF 全球直连"
    type: select
    proxies:
      - DIRECT
      - "\U0001F680 节点选择"
  - name: "\U0001F420 漏网之鱼"
    type: select
    proxies:
      - "\U0001F680 节点选择"
      - "\U0001F1ED\U0001F1F0 香港负载均衡"
      - "\U0001F1EF\U0001F1F5 日本负载均衡"
      - "\U0001F1F0\U0001F1F7 韩国负载均衡"
      - "\U0001F1F8\U0001F1EC 新加坡负载均衡"
      - "\U0001F1FA\U0001F1F8 美国负载均衡"
      - "\U0001F1ED\U0001F1F0 香港自动"
      - "\U0001F1EF\U0001F1F5 日本自动"
      - "\U0001F1F0\U0001F1F7 韩国自动"
      - "\U0001F1F8\U0001F1EC 新加坡自动"
      - "\U0001F1FA\U0001F1F8 美国自动"
      - ♻️ 自动选择
      - "\U0001F1ED\U0001F1F0 香港节点"
      - "\U0001F1EF\U0001F1F5 日本节点"
      - "\U0001F1F0\U0001F1F7 韩国节点"
      - "\U0001F1F8\U0001F1EC 新加坡节点"
      - "\U0001F1FA\U0001F1F8 美国节点"
      - "\U0001F310 全部节点"
rules:
  - 'RULE-SET,BanAD,REJECT'
  - 'RULE-SET,BanProgramAD,REJECT'
  - 'RULE-SET,adobe,REJECT'
  - "RULE-SET,youtube_domain,\U0001F4FA YouTube"
  - 'RULE-SET,tencent,DIRECT'
  - 'RULE-SET,private_domain,DIRECT'
  - 'RULE-SET,TencentVideo,DIRECT'
  - "RULE-SET,apple_domain,\U0001F3AF 全球直连"
  - "RULE-SET,ai,\U0001F4AC ChatGPT"
  - "RULE-SET,Spotify,\U0001F680 节点选择"
  - "RULE-SET,github_domain,\U0001F680 节点选择"
  - "RULE-SET,google_domain,\U0001F680 节点选择"
  - "RULE-SET,onedrive_domain,\U0001F3AF 全球直连"
  - "RULE-SET,microsoft_domain,\U0001F3AF 全球直连"
  - "RULE-SET,tiktok_domain,\U0001F3B5 TikTok"
  - "RULE-SET,speedtest_domain,\U0001F680 节点选择"
  - "RULE-SET,telegram_domain,\U0001F680 节点选择"
  - "RULE-SET,netflix_domain,\U0001F3AC NETFLIX"
  - "RULE-SET,Netflix,\U0001F3AC NETFLIX"
  - "RULE-SET,paypal_domain,\U0001F680 节点选择"
  - "RULE-SET,geolocation-!cn,\U0001F680 节点选择"
  - "RULE-SET,cn_domain,\U0001F3AF 全球直连"
  - "RULE-SET,google_ip,\U0001F680 节点选择,no-resolve"
  - "RULE-SET,netflix_ip,\U0001F3AC NETFLIX,no-resolve"
  - "RULE-SET,telegram_ip,\U0001F680 节点选择,no-resolve"
  - "RULE-SET,cn_ip,\U0001F3AF 全球直连"
  - "RULE-SET,proxylite,\U0001F680 节点选择"
  - "MATCH,\U0001F420 漏网之鱼"
rule-anchor:
  ip:
    type: http
    interval: 86400
    behavior: ipcidr
    format: mrs
  domain:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
  qcy:
    type: http
    interval: 86400
    behavior: domain
    format: text
  class:
    type: http
    interval: 86400
    behavior: classical
    format: text
rule-providers:
  BanAD:
    type: http
    interval: 86400
    behavior: classical
    format: text
    url: 'https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/Clash/BanAD.list'
  tencent:
    type: http
    interval: 86400
    behavior: classical
    format: text
    url: >-
      https://cdn.jsdelivr.net/gh/blackmatrix7/ios_rule_script@master/rule/Shadowrocket/Tencent/Tencent.list
  TencentVideo:
    type: http
    interval: 86400
    behavior: classical
    format: text
    url: >-
      https://cdn.jsdelivr.net/gh/blackmatrix7/ios_rule_script@master/rule/Shadowrocket/TencentVideo/TencentVideo.list
  BanProgramAD:
    type: http
    interval: 86400
    behavior: classical
    format: text
    url: >-
      https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/Clash/BanProgramAD.list
  adobe:
    type: http
    interval: 86400
    behavior: classical
    format: text
    url: 'https://raw.githubusercontent.com/cmliu/ACL4SSR/main/Clash/adobe.list'
  private_domain:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/private.mrs
  ai:
    type: http
    interval: 86400
    behavior: classical
    format: text
    url: >-
      https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/a98edee7cd64d64cb58ce26d63d2dbd2475575ad/rule/Clash/OpenAI/OpenAI.list
  Spotify:
    type: http
    interval: 86400
    behavior: classical
    format: text
    url: >-
      https://cdn.jsdelivr.net/gh/blackmatrix7/ios_rule_script@release/rule/Shadowrocket/Spotify/Spotify.list
  Netflix:
    type: http
    interval: 86400
    behavior: classical
    format: text
    url: >-
      https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/a98edee7cd64d64cb58ce26d63d2dbd2475575ad/rule/Clash/Netflix/Netflix.list
  youtube_domain:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/youtube.mrs
  google_domain:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/google.mrs
  github_domain:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/github.mrs
  telegram_domain:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/telegram.mrs
  netflix_domain:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/netflix.mrs
  paypal_domain:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/paypal.mrs
  onedrive_domain:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/onedrive.mrs
  microsoft_domain:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/microsoft.mrs
  apple_domain:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/apple-cn.mrs
  speedtest_domain:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/ookla-speedtest.mrs
  tiktok_domain:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/tiktok.mrs
  gfw_domain:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/gfw.mrs
  geolocation-!cn:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/geolocation-!cn.mrs
  cn_domain:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/cn.mrs
  proxylite:
    type: http
    interval: 86400
    behavior: classical
    format: text
    url: >-
      https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/refs/heads/master/rule/Clash/ProxyLite/ProxyLite.list
  cn_ip:
    type: http
    interval: 86400
    behavior: ipcidr
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geoip/cn.mrs
  google_ip:
    type: http
    interval: 86400
    behavior: ipcidr
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geoip/google.mrs
  telegram_ip:
    type: http
    interval: 86400
    behavior: ipcidr
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geoip/telegram.mrs
  netflix_ip:
    type: http
    interval: 86400
    behavior: ipcidr
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geoip/netflix.mrs
