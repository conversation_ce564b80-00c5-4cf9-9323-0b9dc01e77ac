/**
 * VLESS 协议解析器
 */

import { ProxyTypes } from '../types.js';

/**
 * 解析 VLESS URL
 * 支持格式: vless://uuid@server:port?params#name
 * @param {string} url - VLESS URL
 * @returns {Object|null} 解析后的节点信息
 */
export function parseVLESSUrl(url) {
  try {
    if (!url.startsWith('vless://')) {
      return null;
    }

    const urlObj = new URL(url);
    const uuid = urlObj.username;
    const server = urlObj.hostname;
    const port = parseInt(urlObj.port);
    const name = decodeURIComponent(urlObj.hash.slice(1)) || `${server}:${port}`;

    const params = new URLSearchParams(urlObj.search);

    return {
      type: ProxyTypes.VLESS,
      name: name,
      server: server,
      port: port,
      uuid: uuid,
      flow: params.get('flow') || '',
      encryption: params.get('encryption') || 'none',
      network: params.get('type') || 'tcp',
      tls: {
        enabled: params.get('security') === 'tls' || params.get('security') === 'reality',
        serverName: params.get('sni') || '',
        alpn: params.get('alpn') ? params.get('alpn').split(',') : [],
        fingerprint: params.get('fp') || ''
      },
      transport: parseTransportParams(params),
      reality: parseRealityParams(params)
    };
  } catch (error) {
    console.error('解析 VLESS URL 失败:', error);
    return null;
  }
}

/**
 * 生成 VLESS URL
 * @param {Object} node - 节点信息
 * @returns {string} VLESS URL
 */
export function generateVLESSUrl(node) {
  try {
    const url = new URL(`vless://${node.uuid}@${node.server}:${node.port}`);
    
    const params = new URLSearchParams();
    
    if (node.flow) params.set('flow', node.flow);
    if (node.encryption && node.encryption !== 'none') params.set('encryption', node.encryption);
    if (node.network && node.network !== 'tcp') params.set('type', node.network);
    
    // TLS 配置
    if (node.tls?.enabled) {
      if (node.reality?.enabled) {
        params.set('security', 'reality');
        if (node.reality.publicKey) params.set('pbk', node.reality.publicKey);
        if (node.reality.shortId) params.set('sid', node.reality.shortId);
        if (node.reality.spiderX) params.set('spx', node.reality.spiderX);
      } else {
        params.set('security', 'tls');
      }
      
      if (node.tls.serverName) params.set('sni', node.tls.serverName);
      if (node.tls.alpn?.length) params.set('alpn', node.tls.alpn.join(','));
      if (node.tls.fingerprint) params.set('fp', node.tls.fingerprint);
    }

    // 传输层配置
    addTransportParams(params, node);

    url.search = params.toString();
    url.hash = encodeURIComponent(node.name);
    
    return url.toString();
  } catch (error) {
    console.error('生成 VLESS URL 失败:', error);
    return null;
  }
}

/**
 * 转换为 Clash 格式
 * @param {Object} node - 节点信息
 * @returns {Object} Clash 格式节点
 */
export function toClashFormat(node) {
  const clashNode = {
    name: node.name,
    type: 'vless',
    server: node.server,
    port: node.port,
    uuid: node.uuid,
    network: node.network,
    flow: node.flow || ''
  };

  // TLS 配置
  if (node.tls?.enabled) {
    clashNode.tls = true;
    if (node.tls.serverName) {
      clashNode.servername = node.tls.serverName;
    }
    if (node.tls.alpn?.length) {
      clashNode.alpn = node.tls.alpn;
    }
  }

  // Reality 配置
  if (node.reality?.enabled) {
    clashNode.reality = {
      enabled: true,
      'public-key': node.reality.publicKey,
      'short-id': node.reality.shortId
    };
  }

  // 传输层配置
  addClashTransportConfig(clashNode, node);

  return clashNode;
}

/**
 * 从 Clash 格式解析
 * @param {Object} clashNode - Clash 格式节点
 * @returns {Object} 标准节点格式
 */
export function fromClashFormat(clashNode) {
  const node = {
    type: ProxyTypes.VLESS,
    name: clashNode.name,
    server: clashNode.server,
    port: clashNode.port,
    uuid: clashNode.uuid,
    flow: clashNode.flow || '',
    encryption: 'none',
    network: clashNode.network || 'tcp',
    tls: {
      enabled: !!clashNode.tls,
      serverName: clashNode.servername || '',
      alpn: clashNode.alpn || []
    },
    transport: {},
    reality: {
      enabled: !!clashNode.reality?.enabled,
      publicKey: clashNode.reality?.['public-key'] || '',
      shortId: clashNode.reality?.['short-id'] || ''
    }
  };

  // 解析传输层配置
  parseClashTransportConfig(node, clashNode);

  return node;
}

/**
 * 解析传输层参数
 * @param {URLSearchParams} params - URL参数
 * @returns {Object} 传输层配置
 */
function parseTransportParams(params) {
  const transport = {};
  const network = params.get('type') || 'tcp';

  switch (network) {
    case 'ws':
      transport.path = params.get('path') || '/';
      transport.host = params.get('host') || '';
      break;
    case 'h2':
      transport.path = params.get('path') || '/';
      transport.host = params.get('host') || '';
      break;
    case 'grpc':
      transport.serviceName = params.get('serviceName') || '';
      transport.mode = params.get('mode') || 'gun';
      break;
    case 'tcp':
      if (params.get('headerType') === 'http') {
        transport.headerType = 'http';
        transport.host = params.get('host') || '';
        transport.path = params.get('path') || '/';
      }
      break;
  }

  return transport;
}

/**
 * 解析 Reality 参数
 * @param {URLSearchParams} params - URL参数
 * @returns {Object} Reality 配置
 */
function parseRealityParams(params) {
  return {
    enabled: params.get('security') === 'reality',
    publicKey: params.get('pbk') || '',
    shortId: params.get('sid') || '',
    spiderX: params.get('spx') || ''
  };
}

/**
 * 添加传输层参数到URL
 * @param {URLSearchParams} params - URL参数
 * @param {Object} node - 节点信息
 */
function addTransportParams(params, node) {
  if (!node.transport) return;

  switch (node.network) {
    case 'ws':
      if (node.transport.path) params.set('path', node.transport.path);
      if (node.transport.host) params.set('host', node.transport.host);
      break;
    case 'h2':
      if (node.transport.path) params.set('path', node.transport.path);
      if (node.transport.host) params.set('host', node.transport.host);
      break;
    case 'grpc':
      if (node.transport.serviceName) params.set('serviceName', node.transport.serviceName);
      if (node.transport.mode) params.set('mode', node.transport.mode);
      break;
    case 'tcp':
      if (node.transport.headerType === 'http') {
        params.set('headerType', 'http');
        if (node.transport.host) params.set('host', node.transport.host);
        if (node.transport.path) params.set('path', node.transport.path);
      }
      break;
  }
}

/**
 * 添加 Clash 传输层配置
 * @param {Object} clashNode - Clash 节点
 * @param {Object} node - 标准节点
 */
function addClashTransportConfig(clashNode, node) {
  if (!node.transport) return;

  switch (node.network) {
    case 'ws':
      clashNode['ws-opts'] = {
        path: node.transport.path || '/',
        headers: node.transport.host ? { Host: node.transport.host } : {}
      };
      break;
    case 'h2':
      clashNode['h2-opts'] = {
        host: node.transport.host ? [node.transport.host] : [],
        path: node.transport.path || '/'
      };
      break;
    case 'grpc':
      clashNode['grpc-opts'] = {
        'grpc-service-name': node.transport.serviceName || ''
      };
      break;
  }
}

/**
 * 解析 Clash 传输层配置
 * @param {Object} node - 标准节点
 * @param {Object} clashNode - Clash 节点
 */
function parseClashTransportConfig(node, clashNode) {
  switch (node.network) {
    case 'ws':
      if (clashNode['ws-opts']) {
        node.transport = {
          path: clashNode['ws-opts'].path || '/',
          host: clashNode['ws-opts'].headers?.Host || ''
        };
      }
      break;
    case 'h2':
      if (clashNode['h2-opts']) {
        node.transport = {
          host: clashNode['h2-opts'].host?.[0] || '',
          path: clashNode['h2-opts'].path || '/'
        };
      }
      break;
    case 'grpc':
      if (clashNode['grpc-opts']) {
        node.transport = {
          serviceName: clashNode['grpc-opts']['grpc-service-name'] || ''
        };
      }
      break;
  }
}

/**
 * 验证节点配置
 * @param {Object} node - 节点信息
 * @returns {boolean} 是否有效
 */
export function validateNode(node) {
  return !!(
    node.server &&
    node.port &&
    node.uuid &&
    node.port > 0 &&
    node.port < 65536 &&
    /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(node.uuid)
  );
}
