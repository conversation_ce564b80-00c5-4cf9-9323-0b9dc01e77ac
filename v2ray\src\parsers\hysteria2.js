/**
 * Hysteria2 协议解析器
 */

import { ProxyTypes } from '../types.js';

/**
 * 解析 Hysteria2 URL
 * 支持格式: hysteria2://password@server:port?params#name
 * @param {string} url - Hysteria2 URL
 * @returns {Object|null} 解析后的节点信息
 */
export function parseHysteria2Url(url) {
  try {
    if (!url.startsWith('hysteria2://') && !url.startsWith('hy2://')) {
      return null;
    }

    const urlObj = new URL(url);
    const password = urlObj.username;
    const server = urlObj.hostname;
    const port = parseInt(urlObj.port);
    const name = decodeURIComponent(urlObj.hash.slice(1)) || `${server}:${port}`;

    const params = new URLSearchParams(urlObj.search);

    return {
      type: ProxyTypes.HYSTERIA2,
      name: name,
      server: server,
      port: port,
      password: password,
      auth: password, // Hysteria2 使用 auth 字段
      obfs: {
        type: params.get('obfs') || '',
        password: params.get('obfs-password') || ''
      },
      tls: {
        enabled: true, // Hysteria2 默认使用 TLS
        serverName: params.get('sni') || params.get('peer') || server,
        alpn: params.get('alpn') ? params.get('alpn').split(',') : ['h3'],
        skipCertVerify: params.get('insecure') === '1'
      },
      bandwidth: {
        up: params.get('up') || '',
        down: params.get('down') || ''
      },
      congestion: params.get('congestion') || 'bbr',
      fastOpen: params.get('fastopen') === '1',
      lazy: params.get('lazy') === '1'
    };
  } catch (error) {
    console.error('解析 Hysteria2 URL 失败:', error);
    return null;
  }
}

/**
 * 生成 Hysteria2 URL
 * @param {Object} node - 节点信息
 * @returns {string} Hysteria2 URL
 */
export function generateHysteria2Url(node) {
  try {
    const url = new URL(`hysteria2://${encodeURIComponent(node.password || node.auth)}@${node.server}:${node.port}`);
    
    const params = new URLSearchParams();
    
    // 混淆配置
    if (node.obfs?.type) {
      params.set('obfs', node.obfs.type);
      if (node.obfs.password) {
        params.set('obfs-password', node.obfs.password);
      }
    }
    
    // TLS 配置
    if (node.tls) {
      if (node.tls.serverName && node.tls.serverName !== node.server) {
        params.set('sni', node.tls.serverName);
      }
      if (node.tls.alpn?.length && node.tls.alpn.join(',') !== 'h3') {
        params.set('alpn', node.tls.alpn.join(','));
      }
      if (node.tls.skipCertVerify) {
        params.set('insecure', '1');
      }
    }
    
    // 带宽配置
    if (node.bandwidth?.up) params.set('up', node.bandwidth.up);
    if (node.bandwidth?.down) params.set('down', node.bandwidth.down);
    
    // 其他配置
    if (node.congestion && node.congestion !== 'bbr') {
      params.set('congestion', node.congestion);
    }
    if (node.fastOpen) params.set('fastopen', '1');
    if (node.lazy) params.set('lazy', '1');

    if (params.toString()) {
      url.search = params.toString();
    }
    url.hash = encodeURIComponent(node.name);
    
    return url.toString();
  } catch (error) {
    console.error('生成 Hysteria2 URL 失败:', error);
    return null;
  }
}

/**
 * 转换为 Clash 格式
 * @param {Object} node - 节点信息
 * @returns {Object} Clash 格式节点
 */
export function toClashFormat(node) {
  const clashNode = {
    name: node.name,
    type: 'hysteria2',
    server: node.server,
    port: node.port,
    password: node.password || node.auth
  };

  // 混淆配置
  if (node.obfs?.type) {
    clashNode.obfs = node.obfs.type;
    if (node.obfs.password) {
      clashNode['obfs-password'] = node.obfs.password;
    }
  }

  // TLS 配置
  if (node.tls) {
    if (node.tls.serverName) {
      clashNode.sni = node.tls.serverName;
    }
    if (node.tls.alpn?.length) {
      clashNode.alpn = node.tls.alpn;
    }
    if (node.tls.skipCertVerify) {
      clashNode['skip-cert-verify'] = true;
    }
  }

  // 带宽配置
  if (node.bandwidth?.up) clashNode.up = node.bandwidth.up;
  if (node.bandwidth?.down) clashNode.down = node.bandwidth.down;

  // 其他配置
  if (node.congestion) clashNode.congestion = node.congestion;
  if (node.fastOpen) clashNode['fast-open'] = true;
  if (node.lazy) clashNode.lazy = true;

  return clashNode;
}

/**
 * 从 Clash 格式解析
 * @param {Object} clashNode - Clash 格式节点
 * @returns {Object} 标准节点格式
 */
export function fromClashFormat(clashNode) {
  return {
    type: ProxyTypes.HYSTERIA2,
    name: clashNode.name,
    server: clashNode.server,
    port: clashNode.port,
    password: clashNode.password,
    auth: clashNode.password,
    obfs: {
      type: clashNode.obfs || '',
      password: clashNode['obfs-password'] || ''
    },
    tls: {
      enabled: true,
      serverName: clashNode.sni || clashNode.server,
      alpn: clashNode.alpn || ['h3'],
      skipCertVerify: !!clashNode['skip-cert-verify']
    },
    bandwidth: {
      up: clashNode.up || '',
      down: clashNode.down || ''
    },
    congestion: clashNode.congestion || 'bbr',
    fastOpen: !!clashNode['fast-open'],
    lazy: !!clashNode.lazy
  };
}

/**
 * 验证节点配置
 * @param {Object} node - 节点信息
 * @returns {boolean} 是否有效
 */
export function validateNode(node) {
  return !!(
    node.server &&
    node.port &&
    (node.password || node.auth) &&
    node.port > 0 &&
    node.port < 65536
  );
}
