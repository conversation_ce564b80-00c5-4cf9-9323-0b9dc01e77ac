export default {
  meta: {
    name: '基础夜间',
    author: 'DesnLee',
    label: 'dark',
    extend: '',
  },
  colors: {
    // 全局高亮色
    'primary-color': '#478EF2',
    'primary-color-end': '#496AF2',
    'second-color': '#FA6419',
    'third-color': '#0ED57D',

    'danger-color': '#E56459',
    'succeed-color': '#49bb88',

    // icon色
    'icon-nav-bar-right': '#606266',
    'unimportant-icon-color': '#FFFFFF34',

    // 组件色
    'status-bar-background-color': '#121212',
    'background-color': '#121212',
    'nav-bar-color': '#12121299',
    'tab-bar-color': '#121212AA',
    'popup-color': '#121212',
    'divider-color': '#FFFFFF08',
    'card-color': '#202020',
    'dialog-color': '#202020',
    'switch-close-background-color': '#FFFFFF14',
    'switch-active-background-color': '#478EF2',
    'compare-item-background-color': '#191919',
    'picker-mask-near-color': '#12121248',
    'picker-mask-far-color': '#121212',

    // 文字色
    'primary-text-color': '#FFFFFFEE',
    'second-text-color': '#FFFFFFBB',
    'comment-text-color': '#FFFFFF88',
    'lowest-text-color': '#FFFFFF36',

    // 其他
    'img-brightness': '100',
    'nav-bar-blur': '16px',
    'tab-bar-blur': '16px',
    'sticky-title-blur': '16px',

    'compare-tag-text-color': '#FFFFFF88',
    'compare-tag-background-color': '#FFFFFF22',
  },
};
//--second-text-color
//--comment-text-color 覆盖
