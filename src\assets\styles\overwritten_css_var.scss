// switch
.nut-switch {
  background: var(--switch-active-background-color);
}

// tab 切换面板
.tab-panel-wrapper {
  background-color: var(--popup-color);
  color: var(--primary-text-color);

  .auto-tab-panel {
    .nut-tabs__titles {
      border-bottom: 1px solid var(--divider-color);
    }

    .nut-tabpane {
      background-color: inherit;
      color: inherit;
    }
  }
}

// sticky title 顶部粘性定位标题
.sticky-title-wrapper {
  margin-top: var(--safe-area-side);
  // backdrop-filter: blur(var(--sticky-title-blur));
  // -webkit-backdrop-filter: blur(var(--sticky-title-blur));
  color: var(--comment-text-color);
  // background-color: var(--nav-bar-color);
}

// picker
.nut-popup {
  -webkit-user-select: none;
  user-select: none;
  .nut-picker-roller {
    @media screen and (min-width: 560px) {
      transition: transform 300ms 0s !important;
    }
  }

  .nut-cascader__bar {
    color: var(--primary-text-color);
    background-color: var(--popup-color);
  }

  .nut-cascader.nut-tabs {
    background-color: var(--popup-color);
    .nut-tabs__titles {
      background: var(--popup-color);
    }
    .nut-tabs__content {
      background: var(--popup-color);

      .nut-tabpane {
        background-color: var(--popup-color);
        margin-bottom: 60px;
      }
    }
  }

  .nut-picker__bar,
  .nut-picker__column {
    background-color: var(--popup-color);
  }
  .nut-picker__column {
    cursor: grab;
  }

  .nut-picker-roller-mask {
    background-image: linear-gradient(
        180deg,
        var(--picker-mask-far-color),
        var(--picker-mask-near-color)
      ),
      linear-gradient(
        0deg,
        var(--picker-mask-far-color),
        var(--picker-mask-near-color)
      );
  }
}

// img
img.auto-reverse {
  filter: brightness(var(--img-brightness));
}

// dialog
.auto-dialog {
  background-color: var(--dialog-color) !important;

  > .nut-dialog {
    > .nut-dialog__content {
      max-height: unset;
    }

    > .nut-dialog__header {
      color: var(--primary-text-color);
      font-weight: bold;
    }
  }
}
// nut-popup round popup-bottom nut-cascader__popup
// form
.nut-cascader__popup,
.nut-cascader-pane,
.nut-cell-group__warp {
  .nut-form-item,
  .nut-textarea {
    background: var(--card-color);

    .nut-form-item__label {
      color: var(--comment-text-color);
    }

    &::after {
      border-color: var(--divider-color);
    }
  }
}

// input & textarea
.input-text,
.nut-input-readonly,
.nut-input,
.nut-input-text {
  color: var(--second-text-color) !important;
}
:-moz-placeholder {
  color: var(--comment-text-color);
}
::-moz-placeholder {
  color: var(--comment-text-color);
}
input:-ms-input-placeholder,
textarea:-ms-input-placeholder {
  color: var(--comment-text-color);
}
input::-webkit-input-placeholder,
textarea::-webkit-input-placeholder {
  color: var(--comment-text-color) !important;
}

.Toastify__close-button {
  position: relative;
  left: -8px;
  bottom: -8px;
}

.Toastify__toast-container, .Toastify__toast-container--top-center  {
  --toastify-toast-min-height: 48px;
}

.nut-radio, .nut-radio--round, .nut-checkbox, .nut-checkbox--round  {
  cursor: pointer;
}
.fa-circle-question, .fa-location-arrow {
  cursor: pointer;
}