proxies:
  - alterId: '2'
    cipher: auto
    name: "\U0001F1ED\U0001F1F0香港1 | ⬇️ 9.7MB/s"
    network: ws
    port: 459
    server: aa080c80-swo740-sy4opd-bhc9.hkt.east.wctype.com
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 60a3d204-c7f8-11ef-adbd-f23c93141fad
    ws-opts:
      headers:
        Host: a605477178.m.ctrip.com
      path: /
    xudp: true
  - client-fingerprint: chrome
    name: "\U0001F1ED\U0001F1F0香港2 | ⬇️ 8.3MB/s"
    password: RlzoEILU
    port: 28296
    server: *************
    skip-cert-verify: true
    sni: cdn.egvra.cn
    type: trojan
    udp: true
  - name: "\U0001F1ED\U0001F1F0香港3 | ⬇️ 8.6MB/s"
    password: RlzoEILU
    port: 48959
    server: **************
    skip-cert-verify: true
    type: trojan
    udp: true
  - cipher: aes-256-cfb
    name: "\U0001F1EF\U0001F1F5日本1 | ⬇️ 10.3MB/s"
    password: awsps0501
    port: 443
    server: **************
    type: ss
    udp: true
  - cipher: chacha20-ietf-poly1305
    name: "\U0001F1F0\U0001F1F7韩国1 | ⬇️ 9.2MB/s"
    password: ed6c8eea-8503-43e1-9563-0a355b0edcac
    port: 42010
    server: eepl2.d-h-h.de
    type: ss
    udp: true
  - cipher: chacha20-ietf-poly1305
    name: "\U0001F1FA\U0001F1F8美国1 | ⬇️ 10.1MB/s"
    password: ed6c8eea-8503-43e1-9563-0a355b0edcac
    port: 42022
    server: eepl1.dhh114514.christmas
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1ED\U0001F1F0香港4 | ⬇️ 11.0MB/s"
    password: ed6c8eea-8503-43e1-9563-0a355b0edcac
    port: 42097
    server: eepl2.d-h-h.de
    type: ss
    udp: true
  - cipher: chacha20-ietf-poly1305
    name: "\U0001F300其他1-SE | ⬇️ 8.0MB/s"
    password: ed6c8eea-8503-43e1-9563-0a355b0edcac
    port: 42015
    server: eepl2.d-h-h.de
    type: ss
    udp: true
  - cipher: chacha20-ietf-poly1305
    name: "\U0001F1ED\U0001F1F0香港5 | ⬇️ 9.0MB/s"
    password: ed6c8eea-8503-43e1-9563-0a355b0edcac
    port: 42099
    server: eepl1.dhh114514.christmas
    type: ss
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1F0\U0001F1F7韩国2 | ⬇️ 10.1MB/s"
    password: RlzoEILU
    port: 28548
    server: *************
    skip-cert-verify: true
    sni: cdn.egvra.cn
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1ED\U0001F1F0香港6 | ⬇️ 8.6MB/s"
    password: RlzoEILU
    port: 50723
    server: **************
    skip-cert-verify: true
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1F0\U0001F1F7韩国3 | ⬇️ 9.6MB/s"
    password: RlzoEILU
    port: 28910
    server: *************
    skip-cert-verify: true
    sni: cdn.egvra.cn
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1F8\U0001F1EC新加坡1 | ⬇️ 10.3MB/s"
    password: RlzoEILU
    port: 17166
    server: **************
    skip-cert-verify: true
    sni: cdn.egvra.cn
    type: trojan
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F300其他2-未识别 | ⬇️ 10.8MB/s"
    password: c38ab133-f18f-4537-8dc8-e2e2c2c24e18
    port: 16007
    server: ************
    type: ss
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1FA\U0001F1F8美国2 | ⬇️ 11.2MB/s"
    password: RlzoEILU
    port: 3330
    server: *************
    skip-cert-verify: true
    sni: cdn.egvra.cn
    type: trojan
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1F8\U0001F1EC新加坡2 | ⬇️ 9.0MB/s"
    password: c86d483c-431f-41df-bb6b-c1dcebfc7401
    port: 18002
    server: *************
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1EF\U0001F1F5日本2 | ⬇️ 7.9MB/s"
    password: abb58966-8d7b-4cdf-bae9-b0d8e184d93d
    port: 19007
    server: *************
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1EF\U0001F1F5日本3 | ⬇️ 8.9MB/s"
    password: abb58966-8d7b-4cdf-bae9-b0d8e184d93d
    port: 19002
    server: *************
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1F9\U0001F1FC台湾1 | ⬇️ 9.9MB/s"
    password: abb58966-8d7b-4cdf-bae9-b0d8e184d93d
    port: 17010
    server: *************
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1F9\U0001F1FC台湾2 | ⬇️ 8.3MB/s"
    password: abb58966-8d7b-4cdf-bae9-b0d8e184d93d
    port: 17002
    server: *************
    type: ss
    udp: true
  - cipher: chacha20-ietf-poly1305
    name: "\U0001F1F8\U0001F1EC新加坡3 | ⬇️ 9.0MB/s"
    password: ed6c8eea-8503-43e1-9563-0a355b0edcac
    port: 42028
    server: eepl1.dhh114514.christmas
    type: ss
    udp: true
  - cipher: chacha20-ietf-poly1305
    name: "\U0001F1F9\U0001F1FC台湾3 | ⬇️ 11.4MB/s"
    password: ed6c8eea-8503-43e1-9563-0a355b0edcac
    port: 42029
    server: eepl2.d-h-h.de
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1ED\U0001F1F0香港7 | ⬇️ 9.2MB/s"
    password: 3030b609-f36f-4fd1-a4ef-a6e653780536
    port: 16008
    server: *************
    type: ss
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F300其他3-MY | ⬇️ 9.4MB/s"
    password: ed6c8eea-8503-43e1-9563-0a355b0edcac
    port: 41012
    server: cm1.d-h-h.in
    skip-cert-verify: true
    sni: v1-my1.776688.best
    type: trojan
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1ED\U0001F1F0香港8 | ⬇️ 11.2MB/s"
    password: 3030b609-f36f-4fd1-a4ef-a6e653780536
    port: 16004
    server: ************
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1F9\U0001F1FC台湾4 | ⬇️ 10.2MB/s"
    password: 3030b609-f36f-4fd1-a4ef-a6e653780536
    port: 17004
    server: *************
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1F9\U0001F1FC台湾5 | ⬇️ 11.9MB/s"
    password: 3030b609-f36f-4fd1-a4ef-a6e653780536
    port: 17005
    server: ************
    type: ss
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1ED\U0001F1F0香港9 | ⬇️ 10.7MB/s"
    password: RlzoEILU
    port: 39689
    server: *************
    skip-cert-verify: true
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1E6\U0001F1FA澳大利亚1 | ⬇️ 9.8MB/s"
    password: RlzoEILU
    port: 11641
    server: *************
    skip-cert-verify: true
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1E6\U0001F1FA澳大利亚2 | ⬇️ 10.4MB/s"
    password: RlzoEILU
    port: 40252
    server: **************
    skip-cert-verify: true
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1F3\U0001F1F1荷兰1 | ⬇️ 8.4MB/s"
    password: RlzoEILU
    port: 8565
    server: *************
    skip-cert-verify: true
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1ED\U0001F1F0香港10 | ⬇️ 12.0MB/s"
    password: RlzoEILU
    port: 34041
    server: **************
    skip-cert-verify: true
    sni: cdn.egvra.cn
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1F8\U0001F1EC新加坡4 | ⬇️ 10.9MB/s"
    password: RlzoEILU
    port: 29220
    server: *************
    skip-cert-verify: true
    sni: cdn.egvra.cn
    type: trojan
    udp: true
  - down: ''
    fingerprint: ''
    name: "\U0001F1ED\U0001F1F0香港11 | ⬇️ 9.6MB/s"
    obfs: ''
    obfs-password: ''
    password: b751e995-b24c-4f30-8425-05db0b9eac45
    port: 8021
    server: hkhkt.ccwink.cc
    skip-cert-verify: true
    sni: hkhkt.ccwink.cc
    type: hysteria2
    up: ''
  - down: ''
    fingerprint: ''
    name: "\U0001F1ED\U0001F1F0香港12 | ⬇️ 12.5MB/s"
    obfs: ''
    obfs-password: ''
    password: b751e995-b24c-4f30-8425-05db0b9eac45
    port: 8023
    server: hkt01.ccwink.cc
    skip-cert-verify: true
    sni: hkt01.ccwink.cc
    type: hysteria2
    up: ''
  - down: ''
    fingerprint: ''
    name: "\U0001F1ED\U0001F1F0香港13 | ⬇️ 9.3MB/s"
    obfs: ''
    obfs-password: ''
    password: b751e995-b24c-4f30-8425-05db0b9eac45
    port: 8022
    server: hkhkt.ccwink.cc
    skip-cert-verify: true
    sni: hkhkt.ccwink.cc
    type: hysteria2
    up: ''
  - down: ''
    fingerprint: ''
    name: "\U0001F1FA\U0001F1F8美国3 | ⬇️ 7.9MB/s"
    obfs: ''
    obfs-password: ''
    password: b8bd42a9-551f-419d-b70d-4aefdd2cb074
    port: 443
    server: *************
    skip-cert-verify: true
    sni: www.bing.com
    type: hysteria2
    up: ''
  - cipher: chacha20-ietf-poly1305
    name: "\U0001F1EF\U0001F1F5日本4 | ⬇️ 15.0MB/s"
    password: f87772ed-cef9-444a-a8e8-bcf299c850ec
    port: 23335
    server: link.karleynetwork.xyz
    type: ss
    udp: true
  - alterId: '0'
    cipher: auto
    name: "\U0001F1F3\U0001F1F1荷兰2 | ⬇️ 9.4MB/s"
    network: ws
    port: 80
    server: tiamo1.tiamocloud.us.kg
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: f0ebc016-1dba-480e-9b53-8a4bc541ab71
    ws-opts:
      headers:
        Host: tiamo1.tiamocloud.us.kg
      path: /
    xudp: true
  - alterId: '0'
    cipher: auto
    name: "\U0001F1F3\U0001F1F1荷兰3 | ⬇️ 8.1MB/s"
    network: ws
    port: 80
    server: tiamo1.tiamocloud.us.kg
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 4b9ca003-9642-45a1-892f-41d11cc64b0d
    ws-opts:
      headers:
        Host: tiamo1.tiamocloud.us.kg
      path: /
    xudp: true
  - client-fingerprint: chrome
    flow: xtls-rprx-vision
    name: "\U0001F1FA\U0001F1F8美国4 | ⬇️ 9.6MB/s"
    network: tcp
    port: 8443
    reality-opts:
      public-key: 4Qekb9y1dqO8hvRzVSGeSRNyhko_gqpeWD94zrLCvjs
      short-id: 5488b0e7
    server: sj-arm.nfsn666.gq
    tls: true
    type: vless
    udp: true
    uuid: 06121b89-607b-44c9-9c01-cc2fc6a7321d
    xudp: true
    servername: www.yahoo.com
  - client-fingerprint: chrome
    name: "\U0001F1ED\U0001F1F0香港14 | ⬇️ 10.8MB/s"
    password: RlzoEILU
    port: 5800
    server: **************
    skip-cert-verify: true
    sni: cdn.egvra.cn
    type: trojan
    udp: true
  - name: "\U0001F1FA\U0001F1F8美国5 | ⬇️ 14.4MB/s"
    network: ws
    port: 8880
    server: *************
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
        User-Agent: >-
          Mozilla/5.0 (Windows NT 10.0) AppleWebKit/537.36 (KHTML, like Gecko)
          Chrome/70.0.3538.67 Safari/537.36
      path: /?ed=2560
    xudp: true
  - name: "\U0001F1FA\U0001F1F8美国6 | ⬇️ 10.1MB/s"
    password: 2b1ed981-6547-4094-998b-06a3323d6f6c
    port: 59599
    server: xd-js.timiwc.com
    skip-cert-verify: true
    sni: xd-js.timiwc.com
    type: trojan
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1EF\U0001F1F5日本5 | ⬇️ 11.1MB/s"
    password: c86d483c-431f-41df-bb6b-c1dcebfc7401
    port: 19010
    server: ************
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1EF\U0001F1F5日本6 | ⬇️ 8.7MB/s"
    password: abb58966-8d7b-4cdf-bae9-b0d8e184d93d
    port: 19009
    server: ************
    type: ss
    udp: true
  - cipher: aes-256-cfb
    name: "\U0001F1F0\U0001F1F7韩国4 | ⬇️ 10.1MB/s"
    password: yijian0503
    port: 443
    server: **********
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1ED\U0001F1F0香港15 | ⬇️ 10.5MB/s"
    password: abb58966-8d7b-4cdf-bae9-b0d8e184d93d
    port: 16005
    server: *************
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1ED\U0001F1F0香港16 | ⬇️ 9.6MB/s"
    password: abb58966-8d7b-4cdf-bae9-b0d8e184d93d
    port: 16014
    server: *************
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1ED\U0001F1F0香港17 | ⬇️ 10.0MB/s"
    password: abb58966-8d7b-4cdf-bae9-b0d8e184d93d
    port: 16010
    server: *************
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1ED\U0001F1F0香港18 | ⬇️ 10.7MB/s"
    password: abb58966-8d7b-4cdf-bae9-b0d8e184d93d
    port: 16010
    server: ************
    type: ss
    udp: true
  - name: "\U0001F1FA\U0001F1F8美国7 | ⬇️ 10.1MB/s"
    password: 2b1ed981-6547-4094-998b-06a3323d6f6c
    port: 21332
    server: xd-js.timiwc.com
    skip-cert-verify: true
    sni: xd-js.timiwc.com
    type: trojan
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1ED\U0001F1F0香港19 | ⬇️ 8.2MB/s"
    password: GXUTEVGNLHEJ8GQN
    port: 15009
    server: 8tv68qhq.slashdevslashnetslashtun.net
    type: ss
    udp: true
  - alterId: '2'
    cipher: auto
    name: "\U0001F1ED\U0001F1F0香港20 | ⬇️ 14.1MB/s"
    network: ws
    port: 459
    server: da3e1424-swq1s0-sy4opd-bhc9.hkt.east.wctype.com
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 60a3d204-c7f8-11ef-adbd-f23c93141fad
    ws-opts:
      headers:
        Host: a605477178.m.ctrip.com
      path: /
    xudp: true
  - client-fingerprint: chrome
    name: "\U0001F1FA\U0001F1F8美国8 | ⬇️ 10.3MB/s"
    password: QIdQdCbReF
    port: 58250
    server: ************
    skip-cert-verify: true
    sni: ************
    type: trojan
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F300其他4-未识别 | ⬇️ 10.1MB/s"
    password: c86d483c-431f-41df-bb6b-c1dcebfc7401
    port: 20010
    server: *************
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1EF\U0001F1F5日本7 | ⬇️ 10.6MB/s"
    password: b8fa2e30-a210-4d8b-bb1e-bf42ea9c9e7e
    port: 19001
    server: *************
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F300其他5-未识别 | ⬇️ 10.5MB/s"
    password: b8fa2e30-a210-4d8b-bb1e-bf42ea9c9e7e
    port: 29001
    server: *************
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1ED\U0001F1F0香港21 | ⬇️ 11.2MB/s"
    password: 200c650a-4d7c-4180-b0ce-20093784902e
    port: 16007
    server: ************
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F300其他6-未识别 | ⬇️ 9.9MB/s"
    password: c38ab133-f18f-4537-8dc8-e2e2c2c24e18
    port: 16004
    server: ************
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1ED\U0001F1F0香港22 | ⬇️ 9.6MB/s"
    password: 200c650a-4d7c-4180-b0ce-20093784902e
    port: 16001
    server: *************
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F300其他7-未识别 | ⬇️ 11.7MB/s"
    password: b8fa2e30-a210-4d8b-bb1e-bf42ea9c9e7e
    port: 16003
    server: *************
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F300其他8-未识别 | ⬇️ 10.7MB/s"
    password: c86d483c-431f-41df-bb6b-c1dcebfc7401
    port: 29010
    server: *************
    type: ss
    udp: true
  - cipher: aes-256-cfb
    name: "\U0001F1F8\U0001F1EC新加坡5 | ⬇️ 13.2MB/s"
    password: qawszxc123
    port: 443
    server: *************
    type: ss
    udp: true
  - cipher: aes-256-cfb
    name: "\U0001F1F8\U0001F1EC新加坡6 | ⬇️ 8.4MB/s"
    password: qawszxc123
    port: 443
    server: *************
    type: ss
    udp: true
  - cipher: aes-256-cfb
    name: "\U0001F1F8\U0001F1EC新加坡7 | ⬇️ 10.9MB/s"
    password: qawszxc123
    port: 443
    server: ************
    type: ss
    udp: true
  - alpn:
      - ''
    alterId: '0'
    cipher: auto
    name: "\U0001F1ED\U0001F1F0香港23 | ⬇️ 14.7MB/s"
    network: tcp
    port: 26010
    server: ppy-hkv1.02ijp4uos1.download
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: ffc323ce-efe2-3b60-a1d1-f4f5497bf2d7
    xudp: true
  - alpn:
      - ''
    alterId: '0'
    cipher: auto
    name: "\U0001F300其他9-未识别 | ⬇️ 13.2MB/s"
    network: ws
    port: 80
    server: 9351cca4-e594-cbfa-a6de-3c1ee2315694.castlepeakhospital.moe
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 14a8a0db-e922-453a-af8d-843af8734b3b
    ws-opts:
      headers:
        Host: tms.dingtalk.com
      path: /
    xudp: true
  - alpn:
      - ''
    alterId: '0'
    cipher: auto
    name: "\U0001F300其他10-未识别 | ⬇️ 14.9MB/s"
    network: ws
    port: 80
    server: 0b17b6cf-2c23-f38e-114f-8f1f3bcd06a3.castlepeakhospital.moe
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 14a8a0db-e922-453a-af8d-843af8734b3b
    ws-opts:
      headers:
        Host: tms.dingtalk.com
      path: /
    xudp: true
  - alpn:
      - ''
    alterId: '0'
    cipher: auto
    name: "\U0001F300其他11-未识别 | ⬇️ 12.6MB/s"
    network: ws
    port: 80
    server: 445522e0-a147-66c0-6b59-becb865f30a5.castlepeakhospital.moe
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 14a8a0db-e922-453a-af8d-843af8734b3b
    ws-opts:
      headers:
        Host: tms.dingtalk.com
      path: /
    xudp: true
  - down: ''
    fingerprint: ''
    name: "\U0001F1EF\U0001F1F5日本8 | ⬇️ 8.1MB/s"
    obfs: ''
    obfs-password: ''
    password: f6552825-6e1b-4fd2-9a2b-1d2b363d9d36
    port: 21077
    server: *************
    skip-cert-verify: true
    sni: bestcast.pw
    type: hysteria2
    up: ''
  - client-fingerprint: chrome
    name: "\U0001F1FA\U0001F1F8美国9 | ⬇️ 9.9MB/s"
    password: c983a532-d8ee-4074-991c-c4a721178fdc
    port: 43744
    server: 32d48363ca5fffa0f9c8fb50d981e589.us.in.node-is.green
    skip-cert-verify: true
    sni: local.bilibili.com
    type: trojan
    udp: true
  - alpn:
      - ''
    alterId: '0'
    cipher: auto
    name: "\U0001F1FA\U0001F1F8美国10 | ⬇️ 11.6MB/s"
    network: ws
    port: 20012
    server: 2fea6447-81c4-2d7d-d592-a4f729209777.7head.icu
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 14a8a0db-e922-453a-af8d-843af8734b3b
    ws-opts:
      headers:
        Host: tms.dingtalk.com
      path: /
    xudp: true
  - alpn:
      - ''
    alterId: '0'
    cipher: auto
    name: "\U0001F1FA\U0001F1F8美国11 | ⬇️ 12.3MB/s"
    network: ws
    port: 20014
    server: 2fea6447-81c4-2d7d-d592-a4f729209777.7head.icu
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 14a8a0db-e922-453a-af8d-843af8734b3b
    ws-opts:
      headers:
        Host: tms.dingtalk.com
      path: /
    xudp: true
  - alpn:
      - ''
    alterId: '0'
    cipher: auto
    name: "\U0001F1F0\U0001F1F7韩国5 | ⬇️ 14.7MB/s"
    network: ws
    port: 20000
    server: 2fea6447-81c4-2d7d-d592-a4f729209777.7head.icu
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 14a8a0db-e922-453a-af8d-843af8734b3b
    ws-opts:
      headers:
        Host: tms.dingtalk.com
      path: /
    xudp: true
  - alpn:
      - ''
    alterId: '0'
    cipher: auto
    name: "\U0001F1F0\U0001F1F7韩国6 | ⬇️ 14.6MB/s"
    network: ws
    port: 20000
    server: 2fea6447-81c4-2d7d-d592-a4f729209777.7head.icu
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 21c90669-193d-49d3-8e37-f5c1462eb134
    ws-opts:
      headers:
        Host: tms.dingtalk.com
      path: /
    xudp: true
  - client-fingerprint: chrome
    flow: xtls-rprx-vision
    name: "\U0001F1F8\U0001F1EC新加坡8 | ⬇️ 10.4MB/s"
    network: tcp
    port: 20230
    reality-opts:
      public-key: Bkxd25PPovqeYw6AevM-L266KM2kLDJomgmorzvGFmM
      short-id: 2c846487
    server: sg004.421421.xyz
    tls: true
    type: vless
    udp: true
    uuid: a124e41a-0fb8-4543-a0ab-a127c7b515a9
    xudp: true
    servername: www.nvidia.com
  - client-fingerprint: chrome
    flow: xtls-rprx-vision
    name: "\U0001F1F8\U0001F1EC新加坡9 | ⬇️ 8.8MB/s"
    network: tcp
    port: 20230
    reality-opts:
      public-key: 1cs7mxEcoVKwcYepAnKgqHAFhRxPv6aO3tv7lNhwLDQ
      short-id: 46bf6ea0
    server: sg002.421421.xyz
    tls: true
    type: vless
    udp: true
    uuid: f08d6a6b-a0b3-410e-a0ba-eae71b521904
    xudp: true
    servername: www.nvidia.com
  - client-fingerprint: chrome
    flow: xtls-rprx-vision
    name: "\U0001F1EF\U0001F1F5日本9 | ⬇️ 10.4MB/s"
    network: tcp
    port: 20230
    reality-opts:
      public-key: lbOfuIKCBPcQH4AEnwnPw1LNxWrl-Bul6KU99H240Fc
      short-id: abae4722
    server: jp004.421421.xyz
    tls: true
    type: vless
    udp: true
    uuid: a124e41a-0fb8-4543-a0ab-a127c7b515a9
    xudp: true
    servername: www.nvidia.com
  - alterId: 64
    cipher: auto
    name: "\U0001F1EF\U0001F1F5日本10 | ⬇️ 9.0MB/s"
    network: ws
    port: 1443
    server: sslvpn.51job.com
    skip-cert-verify: true
    tls: true
    type: vmess
    uuid: a6a0d901-67e9-460a-90b5-634c5c4f9782
    ws-opts:
      headers:
        Host: centos7
      path: /634c5c4f9782
    servername: centos7
  - alterId: 2
    cipher: auto
    name: "\U0001F1FA\U0001F1F8美国12 | ⬇️ 8.3MB/s"
    network: ws
    port: 22643
    server: tk.hzlt.tkddns.xyz
    skip-cert-verify: true
    tls: true
    type: vmess
    uuid: 98e96c9f-4bb3-39d4-9a2c-fac04257f7c7
    ws-opts:
      headers:
        Host: tk.hzlt.tkddns.xyz
      path: /
    servername: tk.hzlt.tkddns.xyz
rule-providers:
  private:
    url: >-
      https://testingcf.jsdelivr.net/gh/MetaCubeX/meta-rules-dat@meta/geo/geosite/private.yaml
    path: ./ruleset/private.yaml
    behavior: domain
    interval: 86400
    format: yaml
    type: http
  cn_domain:
    url: >-
      https://testingcf.jsdelivr.net/gh/MetaCubeX/meta-rules-dat@meta/geo/geosite/cn.yaml
    path: ./ruleset/cn_domain.yaml
    behavior: domain
    interval: 86400
    format: yaml
    type: http
  telegram_domain:
    url: >-
      https://testingcf.jsdelivr.net/gh/MetaCubeX/meta-rules-dat@meta/geo/geosite/telegram.yaml
    path: ./ruleset/telegram_domain.yaml
    behavior: domain
    interval: 86400
    format: yaml
    type: http
  google_domain:
    url: >-
      https://testingcf.jsdelivr.net/gh/MetaCubeX/meta-rules-dat@meta/geo/geosite/google.yaml
    path: ./ruleset/google_domain.yaml
    behavior: domain
    interval: 86400
    format: yaml
    type: http
  geolocation-!cn:
    url: >-
      https://testingcf.jsdelivr.net/gh/MetaCubeX/meta-rules-dat@meta/geo/geosite/geolocation-!cn.yaml
    path: ./ruleset/geolocation-!cn.yaml
    behavior: domain
    interval: 86400
    format: yaml
    type: http
  cn_ip:
    url: >-
      https://testingcf.jsdelivr.net/gh/MetaCubeX/meta-rules-dat@meta/geo/geoip/cn.yaml
    path: ./ruleset/cn_ip.yaml
    behavior: ipcidr
    interval: 86400
    format: yaml
    type: http
  telegram_ip:
    url: >-
      https://testingcf.jsdelivr.net/gh/MetaCubeX/meta-rules-dat@meta/geo/geoip/telegram.yaml
    path: ./ruleset/telegram_ip.yaml
    behavior: ipcidr
    interval: 86400
    format: yaml
    type: http
  google_ip:
    url: >-
      https://testingcf.jsdelivr.net/gh/MetaCubeX/meta-rules-dat@meta/geo/geoip/google.yaml
    path: ./ruleset/google_ip.yaml
    behavior: ipcidr
    interval: 86400
    format: yaml
    type: http
  bing:
    url: >-
      https://testingcf.jsdelivr.net/gh/blackmatrix7/ios_rule_script@master/rule/Clash/Bing/Bing.yaml
    path: ./ruleset/bing.yaml
    behavior: classical
    interval: 86400
    format: yaml
    type: http
  copilot:
    url: >-
      https://testingcf.jsdelivr.net/gh/blackmatrix7/ios_rule_script@master/rule/Clash/Copilot/Copilot.yaml
    path: ./ruleset/copilot.yaml
    behavior: classical
    interval: 86400
    format: yaml
    type: http
  claude:
    url: >-
      https://testingcf.jsdelivr.net/gh/blackmatrix7/ios_rule_script@master/rule/Clash/Claude/Claude.yaml
    path: ./ruleset/claude.yaml
    behavior: classical
    interval: 86400
    format: yaml
    type: http
  bard:
    url: >-
      https://testingcf.jsdelivr.net/gh/blackmatrix7/ios_rule_script@master/rule/Clash/BardAI/BardAI.yaml
    path: ./ruleset/bard.yaml
    behavior: classical
    interval: 86400
    format: yaml
    type: http
  openai:
    url: >-
      https://testingcf.jsdelivr.net/gh/blackmatrix7/ios_rule_script@master/rule/Clash/OpenAI/OpenAI.yaml
    path: ./ruleset/openai.yaml
    behavior: classical
    interval: 86400
    format: yaml
    type: http
  steam:
    url: >-
      https://testingcf.jsdelivr.net/gh/blackmatrix7/ios_rule_script@master/rule/Clash/Steam/Steam.yaml
    path: ./ruleset/steam.yaml
    behavior: classical
    interval: 86400
    format: yaml
    type: http
proxy-groups:
  - icon: 'https://testingcf.jsdelivr.net/gh/Orz-3/mini@master/Color/Static.png'
    include-all: true
    exclude-filter: (?i)GB|Traffic|Expire|Premium|频道|订阅|ISP|流量|到期|重置
    name: PROXY
    type: select
    proxies:
      - AUTO
      - HK AUTO
      - SG AUTO
      - JP AUTO
      - US AUTO
  - icon: 'https://testingcf.jsdelivr.net/gh/Orz-3/mini@master/Color/Urltest.png'
    include-all: true
    exclude-filter: (?i)GB|Traffic|Expire|Premium|频道|订阅|ISP|流量|到期|重置
    name: AUTO
    type: url-test
    interval: 300
  - icon: 'https://testingcf.jsdelivr.net/gh/Orz-3/mini@master/Color/OpenAI.png'
    name: AIGC
    type: select
    proxies:
      - SG AUTO
      - JP AUTO
      - US AUTO
  - icon: 'https://testingcf.jsdelivr.net/gh/Orz-3/mini@master/Color/Telegram.png'
    name: Telegram
    type: select
    proxies:
      - AUTO
      - HK AUTO
      - SG AUTO
      - JP AUTO
      - US AUTO
  - icon: 'https://testingcf.jsdelivr.net/gh/Orz-3/mini@master/Color/Google.png'
    name: Google
    type: select
    proxies:
      - AUTO
      - HK AUTO
      - SG AUTO
      - JP AUTO
      - US AUTO
  - icon: 'https://testingcf.jsdelivr.net/gh/Orz-3/mini@master/Color/HK.png'
    include-all: true
    exclude-filter: (?i)GB|Traffic|Expire|Premium|频道|订阅|ISP|流量|到期|重置
    filter: "(?i)香港|Hong Kong|HK|\U0001F1ED\U0001F1F0"
    name: HK AUTO
    type: url-test
    interval: 300
  - icon: 'https://testingcf.jsdelivr.net/gh/Orz-3/mini@master/Color/SG.png'
    include-all: true
    exclude-filter: (?i)GB|Traffic|Expire|Premium|频道|订阅|ISP|流量|到期|重置
    filter: "(?i)新加坡|Singapore|\U0001F1F8\U0001F1EC"
    name: SG AUTO
    type: url-test
    interval: 300
  - icon: 'https://testingcf.jsdelivr.net/gh/Orz-3/mini@master/Color/JP.png'
    include-all: true
    exclude-filter: (?i)GB|Traffic|Expire|Premium|频道|订阅|ISP|流量|到期|重置
    filter: "(?i)日本|Japan|\U0001F1EF\U0001F1F5"
    name: JP AUTO
    type: url-test
    interval: 300
  - icon: 'https://testingcf.jsdelivr.net/gh/Orz-3/mini@master/Color/US.png'
    include-all: true
    exclude-filter: (?i)GB|Traffic|Expire|Premium|频道|订阅|ISP|流量|到期|重置
    filter: "(?i)美国|USA|\U0001F1FA\U0001F1F8"
    name: US AUTO
    type: url-test
    interval: 300
  - icon: 'https://testingcf.jsdelivr.net/gh/Orz-3/mini@master/Color/Global.png'
    include-all: true
    exclude-filter: (?i)GB|Traffic|Expire|Premium|频道|订阅|ISP|流量|到期|重置
    proxies:
      - PROXY
      - AUTO
      - AIGC
      - Telegram
      - Google
      - HK AUTO
      - SG AUTO
      - JP AUTO
      - US AUTO
    name: GLOBAL
    type: select
rules:
  - 'PROCESS-NAME,subs-check.exe,DIRECT'
  - 'PROCESS-NAME,subs-check,DIRECT'
  - 'RULE-SET,private,DIRECT'
  - 'RULE-SET,bing,AIGC'
  - 'RULE-SET,copilot,AIGC'
  - 'RULE-SET,bard,AIGC'
  - 'RULE-SET,openai,AIGC'
  - 'RULE-SET,claude,AIGC'
  - 'RULE-SET,steam,PROXY'
  - 'RULE-SET,telegram_domain,Telegram'
  - 'RULE-SET,telegram_ip,Telegram'
  - 'RULE-SET,google_domain,Google'
  - 'RULE-SET,google_ip,Google'
  - 'RULE-SET,geolocation-!cn,PROXY'
  - 'RULE-SET,cn_domain,DIRECT'
  - 'RULE-SET,cn_ip,DIRECT'
  - 'MATCH,PROXY'
