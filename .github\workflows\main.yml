name: build
on:
  push:
    branches:
      - master
    paths:
      - "package.json"
  pull_request:
    branches:
      - master
    paths:
      - "package.json"

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          ref: "master"
      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: "20"
      - name: Install dependencies
        run: |
          npm install -g pnpm
          pnpm config set registry https://registry.npmmirror.com
          pnpm i --no-frozen-lockfile
      - name: build
        run: |
          pnpm build
      - uses: amondnet/vercel-action@v25 #deploy
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }} # Required
          # github-token: ${{ secrets.GITHUB_TOKEN }} # Optional
          vercel-args: "--prod" # Optional
          vercel-org-id: ${{ secrets.ORG_ID}} # Required
          vercel-project-id: ${{ secrets.PROJECT_ID}} # Required
          working-directory: dist
      - name: zip
        run: |
          zip -r dist.zip dist
      - id: tag
        name: Generate release tag
        run: |
          SUB_STORE_RELEASE=`node --eval="process.stdout.write(require('./package.json').version)"`
          echo "release_tag=$SUB_STORE_RELEASE" >> $GITHUB_OUTPUT
      - name: Prepare release
        run: |
          pnpm i -D conventional-changelog-cli
          pnpm run changelog
      - name: Release
        uses: softprops/action-gh-release@v1
        if: ${{ success() }}
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          body_path: ./CHANGELOG.md
          tag_name: ${{ steps.tag.outputs.release_tag }}
          # generate_release_notes: true
          files: |
            dist.zip
