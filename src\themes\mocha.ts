export default {
  meta: {
    name: '摩卡',
    author: 'Peng-YM',
    label: 'light',
    extend: 'light',
  },
  colors: {
    // 全局高亮色
    'primary-color': '#75abcd',
    'primary-color-end': '#75abcd',
    'second-color': '#a2483b',
    'third-color': '#83b9b2',

    'danger-color': '#b53a29',
    'succeed-color': '#0ED57D',

    // 组件色
    'status-bar-background-color': '#F4F4F4BB',
    'background-color': '#f5f3ee',
    'nav-bar-color': '#f4f4f4bb',
    'tab-bar-color': '#f4f4f4bb',
    'popup-color': '#ece7df',
    'divider-color': '#e0d7c8',
    'card-color': '#ece7df',
    'dialog-color': '#ece7df',
    'switch-close-background-color': '#00000012',
    'switch-active-background-color': '#478EF2',
    'compare-item-background-color': '#EEEEEE',

    // 文字色
    'primary-text-color': '#443623',
    'second-text-color': '#606266',
    'comment-text-color': '#909399',
    'lowest-text-color': '#c0c4cc',

    // 其他
    'nav-bar-blur': '100px',
    'sticky-title-blur': '100px',

    'compare-tag-text-color': '#707277',
    'compare-tag-background-color': '#c0c4cc',
  },
};
