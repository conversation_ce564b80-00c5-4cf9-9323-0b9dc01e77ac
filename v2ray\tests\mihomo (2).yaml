proxies:
  - down: ''
    fingerprint: ''
    name: "\U0001F1FA\U0001F1F8美国1 | ⬇️ 12.5MB/s"
    obfs: ''
    obfs-password: ''
    password: b8bd42a9-551f-419d-b70d-4aefdd2cb074
    port: 443
    server: **************
    skip-cert-verify: true
    sni: www.bing.com
    type: hysteria2
    up: ''
  - down: ''
    fingerprint: ''
    name: "\U0001F1F7\U0001F1FA俄罗斯1 | ⬇️ 7.9MB/s"
    obfs: ''
    obfs-password: ''
    password: dongtaiwang.com
    port: 30033
    server: hy2.694463.xyz
    skip-cert-verify: true
    sni: www.bing.com
    type: hysteria2
    up: ''
  - down: ''
    fingerprint: ''
    name: "\U0001F1FA\U0001F1F8美国2 | ⬇️ 8.2MB/s"
    obfs: ''
    obfs-password: ''
    password: 16955d72-1794-11f0-a035-f23c95b6f51d
    port: 1443
    server: 2ef15a2a-svi800-sw77c8-dnss.la.shifen.uk
    skip-cert-verify: false
    sni: 2ef15a2a-svi800-sw77c8-dnss.la.shifen.uk
    type: hysteria2
    up: ''
  - down: ''
    fingerprint: ''
    name: "\U0001F1FA\U0001F1F8美国3 | ⬇️ 9.2MB/s"
    obfs: ''
    obfs-password: ''
    password: b8bd42a9-551f-419d-b70d-4aefdd2cb074
    port: 443
    server: **************
    skip-cert-verify: true
    sni: www.bing.com
    type: hysteria2
    up: ''
  - down: ''
    fingerprint: ''
    name: "\U0001F1FA\U0001F1F8美国4 | ⬇️ 8.7MB/s"
    obfs: ''
    obfs-password: ''
    password: 16955d72-1794-11f0-a035-f23c95b6f51d
    port: 1443
    server: 9b614a41-swb8g0-sxs14h-dnss.la.shifen.uk
    skip-cert-verify: false
    sni: 9b614a41-swb8g0-sxs14h-dnss.la.shifen.uk
    type: hysteria2
    up: ''
  - down: ''
    fingerprint: ''
    name: "\U0001F1FA\U0001F1F8美国5 | ⬇️ 8.9MB/s"
    obfs: ''
    obfs-password: ''
    password: 92b2a3d4-f353-11ef-b714-f23c93136cb3
    port: 1443
    server: 9e4d0b01-swd340-sxscwg-63bp.la.shifen.uk
    skip-cert-verify: false
    sni: 9e4d0b01-swd340-sxscwg-63bp.la.shifen.uk
    type: hysteria2
    up: ''
  - client-fingerprint: chrome
    name: "\U0001F1EF\U0001F1F5日本1 | ⬇️ 12.2MB/s"
    password: 9ca6c56f-ce76-45c2-9855-e3d8a724dcc0
    port: 13002
    server: ver-006.7e4ea15f-92ab-4aa7-b35b-38c3b1d0e495.inbound.ykkcloud.link
    skip-cert-verify: true
    sni: upos-sz-mirroraliov.bilivideo.com
    type: trojan
    udp: true
  - alterId: '2'
    cipher: auto
    name: "\U0001F1ED\U0001F1F0香港1 | ⬇️ 15.0MB/s"
    network: ws
    port: 459
    server: aa080c80-swo740-sy4opd-bhc9.hkt.east.wctype.com
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 60a3d204-c7f8-11ef-adbd-f23c93141fad
    ws-opts:
      headers:
        Host: a605477178.m.ctrip.com
      path: /
    xudp: true
  - client-fingerprint: chrome
    name: "\U0001F1FA\U0001F1F8美国6 | ⬇️ 10.4MB/s"
    password: RlzoEILU
    port: 13542
    server: *************
    skip-cert-verify: true
    sni: cdn.egvra.cn
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1FA\U0001F1F8美国7 | ⬇️ 11.1MB/s"
    password: 9ca6c56f-ce76-45c2-9855-e3d8a724dcc0
    port: 15005
    server: ver-006.7e4ea15f-92ab-4aa7-b35b-38c3b1d0e495.inbound.ykkcloud.link
    skip-cert-verify: true
    sni: upos-sz-mirroraliov.bilivideo.com
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1ED\U0001F1F0香港2 | ⬇️ 11.9MB/s"
    password: RlzoEILU
    port: 28296
    server: *************
    skip-cert-verify: true
    sni: cdn.egvra.cn
    type: trojan
    udp: true
  - cipher: xchacha20-ietf-poly1305
    name: "\U0001F1ED\U0001F1F0香港3 | ⬇️ 15.3MB/s"
    password: '@CfftfYVgp4gkMHMirH6@_C'
    port: 49758
    server: *************
    type: ss
    udp: true
  - cipher: aes-128-gcm
    name: "\U0001F1ED\U0001F1F0香港4 | ⬇️ 10.8MB/s"
    password: sadujij!@diQojd1254
    port: 49759
    server: *************
    type: ss
    udp: true
  - name: "\U0001F1FA\U0001F1F8美国8 | ⬇️ 10.2MB/s"
    password: 2b1ed981-6547-4094-998b-06a3323d6f6c
    port: 21603
    server: xd-js.timiwc.com
    skip-cert-verify: true
    sni: xd-js.timiwc.com
    type: trojan
    udp: true
  - cipher: aes-128-gcm
    name: "\U0001F1F8\U0001F1EC新加坡1 | ⬇️ 11.6MB/s"
    password: f16163ec-3c35-4719-a19b-68c864cdc626
    port: 13038
    server: *************
    type: ss
    udp: true
  - name: "\U0001F300其他1-MY | ⬇️ 8.5MB/s"
    password: 2b1ed981-6547-4094-998b-06a3323d6f6c
    port: 21181
    server: **************
    skip-cert-verify: true
    type: trojan
    udp: true
  - cipher: chacha20-ietf-poly1305
    name: "\U0001F1FA\U0001F1F8美国9 | ⬇️ 14.5MB/s"
    password: 92e0a2cd-f842-42b6-84ef-dd2da5c711ac
    port: 44223
    server: 03.kill704.win
    type: ss
    udp: true
  - name: "\U0001F1ED\U0001F1F0香港5 | ⬇️ 11.9MB/s"
    password: RlzoEILU
    port: 48959
    server: **************
    skip-cert-verify: true
    type: trojan
    udp: true
  - cipher: chacha20-ietf-poly1305
    name: "\U0001F1FA\U0001F1F8美国10 | ⬇️ 11.1MB/s"
    password: ed6c8eea-8503-43e1-9563-0a355b0edcac
    port: 42023
    server: eepl1.dhh114514.christmas
    type: ss
    udp: true
  - name: "\U0001F1F0\U0001F1F7韩国1 | ⬇️ 11.1MB/s"
    password: RlzoEILU
    port: 47655
    server: *************
    skip-cert-verify: true
    type: trojan
    udp: true
  - name: "\U0001F1F0\U0001F1F7韩国2 | ⬇️ 8.0MB/s"
    password: RlzoEILU
    port: 44907
    server: *************
    skip-cert-verify: true
    type: trojan
    udp: true
  - name: "\U0001F1ED\U0001F1F0香港6 | ⬇️ 8.4MB/s"
    password: RlzoEILU
    port: 46861
    server: *************
    skip-cert-verify: true
    type: trojan
    udp: true
  - name: "\U0001F1F3\U0001F1F1荷兰1 | ⬇️ 9.4MB/s"
    password: RlzoEILU
    port: 15407
    server: *************
    skip-cert-verify: true
    type: trojan
    udp: true
  - name: "\U0001F1F3\U0001F1F1荷兰2 | ⬇️ 8.7MB/s"
    password: RlzoEILU
    port: 33097
    server: *************
    skip-cert-verify: true
    type: trojan
    udp: true
  - cipher: aes-128-gcm
    name: "\U0001F1F8\U0001F1EC新加坡2 | ⬇️ 8.4MB/s"
    password: 8e9e3c27-6531-45f3-96aa-770e9e7f5f46
    port: 41530
    server: 7sshgqnew.bigmeyear.org
    type: ss
    udp: true
  - cipher: aes-128-gcm
    name: "\U0001F1F8\U0001F1EC新加坡3 | ⬇️ 11.0MB/s"
    password: 8e9e3c27-6531-45f3-96aa-770e9e7f5f46
    port: 42090
    server: premium02.icfjlk.xyz
    type: ss
    udp: true
  - cipher: aes-256-cfb
    name: "\U0001F1EF\U0001F1F5日本2 | ⬇️ 12.3MB/s"
    password: awsps0501
    port: 443
    server: **************
    type: ss
    udp: true
  - cipher: aes-256-cfb
    name: "\U0001F1EF\U0001F1F5日本3 | ⬇️ 8.5MB/s"
    password: awsps0501
    port: 443
    server: **************
    type: ss
    udp: true
  - cipher: aes-256-cfb
    name: "\U0001F1EF\U0001F1F5日本4 | ⬇️ 12.0MB/s"
    password: awsps0501
    port: 443
    server: **************
    type: ss
    udp: true
  - cipher: aes-256-cfb
    name: "\U0001F1F0\U0001F1F7韩国3 | ⬇️ 8.5MB/s"
    password: yijian0503
    port: 443
    server: ************
    type: ss
    udp: true
  - cipher: aes-128-gcm
    name: "\U0001F1ED\U0001F1F0香港7 | ⬇️ 12.2MB/s"
    password: 8e9e3c27-6531-45f3-96aa-770e9e7f5f46
    port: 42089
    server: premium01.icfjlk.xyz
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1EE\U0001F1F9意大利1 | ⬇️ 8.6MB/s"
    password: ed6c8eea-8503-43e1-9563-0a355b0edcac
    port: 42003
    server: eepl1.dhh114514.christmas
    type: ss
    udp: true
  - name: "\U0001F1FA\U0001F1F8美国11 | ⬇️ 8.5MB/s"
    password: 0a335fd6-be0b-11ec-8dfa-f23c91cfbbc9
    port: 15229
    server: ab697313-swmcg0-sxkd63-17z95.cu.plebai.net
    skip-cert-verify: true
    sni: ab697313-swmcg0-sxkd63-17z95.cu.plebai.net
    type: trojan
    udp: true
  - cipher: chacha20-ietf-poly1305
    name: "\U0001F1FA\U0001F1F8美国12 | ⬇️ 12.3MB/s"
    password: ed6c8eea-8503-43e1-9563-0a355b0edcac
    port: 42022
    server: eepl1.dhh114514.christmas
    type: ss
    udp: true
  - cipher: chacha20-ietf-poly1305
    name: "\U0001F1F0\U0001F1F7韩国4 | ⬇️ 14.0MB/s"
    password: ed6c8eea-8503-43e1-9563-0a355b0edcac
    port: 42010
    server: eepl2.d-h-h.de
    type: ss
    udp: true
  - cipher: chacha20-ietf-poly1305
    name: "\U0001F1F0\U0001F1F7韩国5 | ⬇️ 15.4MB/s"
    password: ed6c8eea-8503-43e1-9563-0a355b0edcac
    port: 42010
    server: eepl1.dhh114514.christmas
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1ED\U0001F1F0香港8 | ⬇️ 10.6MB/s"
    password: ed6c8eea-8503-43e1-9563-0a355b0edcac
    port: 42097
    server: eepl2.d-h-h.de
    type: ss
    udp: true
  - cipher: aes-128-gcm
    name: "\U0001F1E9\U0001F1EA德国1 | ⬇️ 9.0MB/s"
    password: 8e9e3c27-6531-45f3-96aa-770e9e7f5f46
    port: 41727
    server: rare01new.bigmeyear.org
    type: ss
    udp: true
  - cipher: aes-128-gcm
    name: "\U0001F1FA\U0001F1F8美国13 | ⬇️ 9.0MB/s"
    password: 8e9e3c27-6531-45f3-96aa-770e9e7f5f46
    port: 43552
    server: us01new.bigmeyear.org
    type: ss
    udp: true
  - cipher: aes-128-gcm
    name: "\U0001F1FA\U0001F1F8美国14 | ⬇️ 9.7MB/s"
    password: 8e9e3c27-6531-45f3-96aa-770e9e7f5f46
    port: 46089
    server: us02new.bigmeyear.org
    type: ss
    udp: true
  - name: "\U0001F1ED\U0001F1F0香港9 | ⬇️ 10.5MB/s"
    password: RlzoEILU
    port: 15624
    server: **************
    skip-cert-verify: true
    type: trojan
    udp: true
  - cipher: aes-128-gcm
    name: "\U0001F300其他2-MO | ⬇️ 9.4MB/s"
    password: 8e9e3c27-6531-45f3-96aa-770e9e7f5f46
    port: 43892
    server: mo01new.bigmeyear.org
    type: ss
    udp: true
  - cipher: aes-128-gcm
    name: "\U0001F1ED\U0001F1F0香港10 | ⬇️ 8.8MB/s"
    password: 8e9e3c27-6531-45f3-96aa-770e9e7f5f46
    port: 42088
    server: e4uvap8.icfjlk.xyz
    type: ss
    udp: true
  - cipher: aes-128-gcm
    name: "\U0001F1EF\U0001F1F5日本5 | ⬇️ 8.8MB/s"
    password: 8e9e3c27-6531-45f3-96aa-770e9e7f5f46
    port: 44951
    server: jp02new.bigmeyear.org
    type: ss
    udp: true
  - cipher: aes-128-gcm
    name: "\U0001F1F0\U0001F1F7韩国6 | ⬇️ 9.2MB/s"
    password: 8e9e3c27-6531-45f3-96aa-770e9e7f5f46
    port: 42814
    server: 81xh0xbpnew.bigmeyear.org
    type: ss
    udp: true
  - cipher: aes-128-gcm
    name: "\U0001F1F9\U0001F1FC台湾1 | ⬇️ 10.1MB/s"
    password: 8e9e3c27-6531-45f3-96aa-770e9e7f5f46
    port: 43383
    server: z01tytnew.bigmeyear.org
    type: ss
    udp: true
  - cipher: aes-128-gcm
    name: "\U0001F1FA\U0001F1F8美国15 | ⬇️ 8.6MB/s"
    password: 8e9e3c27-6531-45f3-96aa-770e9e7f5f46
    port: 40083
    server: us03new.bigmeyear.org
    type: ss
    udp: true
  - cipher: chacha20-ietf-poly1305
    name: "\U0001F300其他3-SE | ⬇️ 11.7MB/s"
    password: ed6c8eea-8503-43e1-9563-0a355b0edcac
    port: 42015
    server: eepl2.d-h-h.de
    type: ss
    udp: true
  - cipher: chacha20-ietf-poly1305
    name: "\U0001F1ED\U0001F1F0香港11 | ⬇️ 14.0MB/s"
    password: ed6c8eea-8503-43e1-9563-0a355b0edcac
    port: 42099
    server: eepl1.dhh114514.christmas
    type: ss
    udp: true
  - cipher: aes-128-gcm
    name: "\U0001F1F8\U0001F1EC新加坡4 | ⬇️ 12.1MB/s"
    password: 8e9e3c27-6531-45f3-96aa-770e9e7f5f46
    port: 46151
    server: goh8x3new.bigmeyear.org
    type: ss
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1F0\U0001F1F7韩国7 | ⬇️ 11.9MB/s"
    password: RlzoEILU
    port: 28548
    server: *************
    skip-cert-verify: true
    sni: cdn.egvra.cn
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1ED\U0001F1F0香港12 | ⬇️ 12.2MB/s"
    password: RlzoEILU
    port: 50723
    server: **************
    skip-cert-verify: true
    type: trojan
    udp: true
  - name: "\U0001F1E9\U0001F1EA德国2 | ⬇️ 9.2MB/s"
    network: ws
    port: 80
    server: ***************
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
        User-Agent: >-
          Mozilla/5.0 (Windows NT 6.1) AppleWebKit/537.36 (KHTML, like Gecko)
          Chrome/70.0.3538.110 Safari/537.36
      path: '/Telegram: @vpnAndroid2/?ed=2560'
    xudp: true
  - client-fingerprint: chrome
    name: "\U0001F1F0\U0001F1F7韩国8 | ⬇️ 12.1MB/s"
    password: RlzoEILU
    port: 28910
    server: *************
    skip-cert-verify: true
    sni: cdn.egvra.cn
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1FA\U0001F1F8美国16 | ⬇️ 10.8MB/s"
    password: 9ca6c56f-ce76-45c2-9855-e3d8a724dcc0
    port: 15004
    server: ver-006.7e4ea15f-92ab-4aa7-b35b-38c3b1d0e495.inbound.ykkcloud.link
    skip-cert-verify: true
    sni: upos-sz-mirroraliov.bilivideo.com
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1F8\U0001F1EC新加坡5 | ⬇️ 12.4MB/s"
    password: 9ca6c56f-ce76-45c2-9855-e3d8a724dcc0
    port: 16001
    server: ver-006.7e4ea15f-92ab-4aa7-b35b-38c3b1d0e495.inbound.ykkcloud.link
    skip-cert-verify: true
    sni: upos-sz-mirroraliov.bilivideo.com
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1FA\U0001F1F8美国17 | ⬇️ 10.9MB/s"
    password: 9ca6c56f-ce76-45c2-9855-e3d8a724dcc0
    port: 15003
    server: ver-006.7e4ea15f-92ab-4aa7-b35b-38c3b1d0e495.inbound.ykkcloud.link
    skip-cert-verify: true
    sni: upos-sz-mirroraliov.bilivideo.com
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1F3\U0001F1F1荷兰3 | ⬇️ 8.5MB/s"
    password: RlzoEILU
    port: 16641
    server: *************
    skip-cert-verify: true
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1F9\U0001F1FC台湾2 | ⬇️ 12.1MB/s"
    password: 9ca6c56f-ce76-45c2-9855-e3d8a724dcc0
    port: 12002
    server: ver-006.7e4ea15f-92ab-4aa7-b35b-38c3b1d0e495.inbound.ykkcloud.link
    skip-cert-verify: true
    sni: upos-sz-mirroraliov.bilivideo.com
    type: trojan
    udp: true
  - name: "\U0001F1EB\U0001F1F7法国1 | ⬇️ 11.9MB/s"
    network: ws
    port: 8080
    server: ************
    type: vless
    udp: true
    uuid: 51fda508-20aa-419e-b4c3-3a6202411412
    ws-opts:
      headers:
        Host: 18.6na7PY43IT.zuLaiR.Org.
        User-Agent: >-
          Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML,
          like Gecko) Chrome/66.0.3359.117 Safari/537.36
      path: /?ed=2048
    xudp: true
  - client-fingerprint: chrome
    name: "\U0001F1F8\U0001F1EC新加坡6 | ⬇️ 11.2MB/s"
    password: 9ca6c56f-ce76-45c2-9855-e3d8a724dcc0
    port: 16003
    server: ver-006.7e4ea15f-92ab-4aa7-b35b-38c3b1d0e495.inbound.ykkcloud.link
    skip-cert-verify: true
    sni: upos-sz-mirroraliov.bilivideo.com
    type: trojan
    udp: true
  - cipher: chacha20-ietf-poly1305
    name: "\U0001F1ED\U0001F1F0香港13 | ⬇️ 13.2MB/s"
    password: f87772ed-cef9-444a-a8e8-bcf299c850ec
    port: 23331
    server: link.karleynetwork.xyz
    type: ss
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1F8\U0001F1EC新加坡7 | ⬇️ 12.0MB/s"
    password: RlzoEILU
    port: 17166
    server: **************
    skip-cert-verify: true
    sni: cdn.egvra.cn
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1ED\U0001F1F0香港14 | ⬇️ 11.7MB/s"
    password: 9ca6c56f-ce76-45c2-9855-e3d8a724dcc0
    port: 11001
    server: ver-006.7e4ea15f-92ab-4aa7-b35b-38c3b1d0e495.inbound.ykkcloud.link
    skip-cert-verify: true
    sni: upos-sz-mirroraliov.bilivideo.com
    type: trojan
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1E9\U0001F1EA德国3 | ⬇️ 8.7MB/s"
    password: RHESC53MSXLUHT9M
    port: 20030
    server: **************
    type: ss
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1ED\U0001F1F0香港15 | ⬇️ 12.1MB/s"
    password: 9ca6c56f-ce76-45c2-9855-e3d8a724dcc0
    port: 11001
    server: *************
    skip-cert-verify: true
    sni: upos-sz-mirroraliov.bilivideo.com
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1EF\U0001F1F5日本6 | ⬇️ 11.2MB/s"
    password: 9ca6c56f-ce76-45c2-9855-e3d8a724dcc0
    port: 13005
    server: ver-006.7e4ea15f-92ab-4aa7-b35b-38c3b1d0e495.inbound.ykkcloud.link
    skip-cert-verify: true
    sni: upos-sz-mirroraliov.bilivideo.com
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1F8\U0001F1EC新加坡8 | ⬇️ 11.3MB/s"
    password: 9ca6c56f-ce76-45c2-9855-e3d8a724dcc0
    port: 16002
    server: ver-006.7e4ea15f-92ab-4aa7-b35b-38c3b1d0e495.inbound.ykkcloud.link
    skip-cert-verify: true
    sni: upos-sz-mirroraliov.bilivideo.com
    type: trojan
    udp: true
  - alterId: '0'
    cipher: auto
    name: "\U0001F300其他4-未识别 | ⬇️ 9.9MB/s"
    network: ws
    port: 80
    server: tiamo1.tiamocloud.us.kg
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: d804d781-b628-452b-acc6-248a669737f3
    ws-opts:
      headers:
        Host: tiamo1.tiamocloud.us.kg
      path: /
    xudp: true
  - client-fingerprint: chrome
    name: "\U0001F1EF\U0001F1F5日本7 | ⬇️ 11.0MB/s"
    password: 9ca6c56f-ce76-45c2-9855-e3d8a724dcc0
    port: 13003
    server: ver-006.7e4ea15f-92ab-4aa7-b35b-38c3b1d0e495.inbound.ykkcloud.link
    skip-cert-verify: true
    sni: upos-sz-mirroraliov.bilivideo.com
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1ED\U0001F1F0香港16 | ⬇️ 12.0MB/s"
    password: RlzoEILU
    port: 45569
    server: **************
    skip-cert-verify: true
    sni: cdn.egvra.cn
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1FA\U0001F1F8美国18 | ⬇️ 10.7MB/s"
    password: 9ca6c56f-ce76-45c2-9855-e3d8a724dcc0
    port: 15002
    server: ver-006.7e4ea15f-92ab-4aa7-b35b-38c3b1d0e495.inbound.ykkcloud.link
    skip-cert-verify: true
    sni: upos-sz-mirroraliov.bilivideo.com
    type: trojan
    udp: true
  - down: ''
    fingerprint: ''
    name: "\U0001F1FA\U0001F1F8美国19 | ⬇️ 8.0MB/s"
    obfs: ''
    obfs-password: ''
    password: a2c52961
    port: 64921
    server: ***************
    skip-cert-verify: true
    sni: www.bing.com
    type: hysteria2
    up: ''
  - name: "\U0001F1E9\U0001F1EA德国4 | ⬇️ 12.4MB/s"
    network: ws
    port: 8880
    server: *************
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
        User-Agent: >-
          Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like
          Gecko) Chrome/36.0.1985.143 Safari/537.36
      path: /?ed=2560
    xudp: true
  - name: "\U0001F1F0\U0001F1F7韩国9 | ⬇️ 14.5MB/s"
    network: ws
    port: 80
    server: *************
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
        User-Agent: >-
          Mozilla/5.0 (Windows NT 5.1) AppleWebKit/537.36 (KHTML, like Gecko)
          Chrome/49.0.2623.112 Safari/537.36
      path: '/Telegram:@vpnAndroid2/?ed=2560'
    xudp: true
    servername: reedfree8mahsang2.redorg.ir
  - down: ''
    fingerprint: ''
    name: "\U0001F1FA\U0001F1F8美国20 | ⬇️ 8.1MB/s"
    obfs: ''
    obfs-password: ''
    password: 869a0163-456f-4c06-bd4a-2376e4563eae
    port: 30003
    server: qyxjp2.qy1357.top
    skip-cert-verify: true
    sni: qyxjp2.qy1357.top
    type: hysteria2
    up: ''
  - client-fingerprint: chrome
    name: "\U0001F1ED\U0001F1F0香港17 | ⬇️ 12.2MB/s"
    password: 9ca6c56f-ce76-45c2-9855-e3d8a724dcc0
    port: 11002
    server: ver-006.7e4ea15f-92ab-4aa7-b35b-38c3b1d0e495.inbound.ykkcloud.link
    skip-cert-verify: true
    sni: upos-sz-mirroraliov.bilivideo.com
    type: trojan
    udp: true
  - alterId: '0'
    cipher: auto
    name: "\U0001F1F3\U0001F1F1荷兰4 | ⬇️ 9.7MB/s"
    network: ws
    port: 80
    server: tiamo1.tiamocloud.us.kg
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 97592bb9-be6d-4648-9d38-e2c15f528b90
    ws-opts:
      headers:
        Host: tiamo1.tiamocloud.us.kg
      path: /
    xudp: true
  - alterId: '0'
    cipher: auto
    name: "\U0001F1F3\U0001F1F1荷兰5 | ⬇️ 8.3MB/s"
    network: ws
    port: 80
    server: tiamo1.tiamocloud.us.kg
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 730ef443-ce0f-46e7-aace-4ab2da7c92e4
    ws-opts:
      headers:
        Host: tiamo1.tiamocloud.us.kg
      path: /
    xudp: true
  - client-fingerprint: chrome
    name: "\U0001F1F0\U0001F1F7韩国10 | ⬇️ 12.0MB/s"
    password: 9ca6c56f-ce76-45c2-9855-e3d8a724dcc0
    port: 14001
    server: ver-006.7e4ea15f-92ab-4aa7-b35b-38c3b1d0e495.inbound.ykkcloud.link
    skip-cert-verify: true
    sni: upos-sz-mirroraliov.bilivideo.com
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1ED\U0001F1F0香港18 | ⬇️ 11.6MB/s"
    password: 9ca6c56f-ce76-45c2-9855-e3d8a724dcc0
    port: 11004
    server: ver-006.7e4ea15f-92ab-4aa7-b35b-38c3b1d0e495.inbound.ykkcloud.link
    skip-cert-verify: true
    sni: upos-sz-mirroraliov.bilivideo.com
    type: trojan
    udp: true
  - alterId: '0'
    cipher: auto
    name: "\U0001F1F3\U0001F1F1荷兰6 | ⬇️ 11.0MB/s"
    network: ws
    port: 80
    server: tiamo1.tiamocloud.us.kg
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 14e3c26a-4b9c-4a7c-b267-9c538cd47283
    ws-opts:
      headers:
        Host: tiamo1.tiamocloud.us.kg
      path: /
    xudp: true
  - alterId: '0'
    cipher: auto
    name: "\U0001F1ED\U0001F1F0香港19 | ⬇️ 8.5MB/s"
    network: ws
    port: 8080
    server: e0b97381-swo740-sy0k9b-yjq0.hgc1.tcpbbr.net
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 25b2699a-e18e-11ec-8e69-f23c91cfbbc9
    ws-opts:
      headers:
        Host: broadcastlv.chat.bilibili.com
      path: /
    xudp: true
  - name: "\U0001F1E9\U0001F1EA德国5 | ⬇️ 9.4MB/s"
    network: ws
    port: 8880
    server: *************
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
        User-Agent: >-
          Mozilla/5.0 (Linux; Android 7.0; Moto C Build/NRD90M.059)
          AppleWebKit/537.36 (KHTML, like Gecko) Chrome/69.0.3497.100 Mobile
          Safari/537.36
      path: /?ed=2560
    xudp: true
  - client-fingerprint: chrome
    name: "\U0001F1FA\U0001F1F8美国21 | ⬇️ 10.3MB/s"
    password: RlzoEILU
    port: 3330
    server: *************
    skip-cert-verify: true
    sni: cdn.egvra.cn
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1F9\U0001F1FC台湾3 | ⬇️ 12.0MB/s"
    password: 9ca6c56f-ce76-45c2-9855-e3d8a724dcc0
    port: 12004
    server: ver-006.7e4ea15f-92ab-4aa7-b35b-38c3b1d0e495.inbound.ykkcloud.link
    skip-cert-verify: true
    sni: upos-sz-mirroraliov.bilivideo.com
    type: trojan
    udp: true
  - client-fingerprint: chrome
    flow: xtls-rprx-vision
    name: "\U0001F1F7\U0001F1FA俄罗斯2 | ⬇️ 8.0MB/s"
    network: tcp
    port: 42632
    reality-opts:
      public-key: qtqcKrPLXaN-jpo7Zs1_80QDmA2uvCZKFGanJLJFFXA
      short-id: 1f8b5201
    server: **************
    tls: true
    type: vless
    udp: true
    uuid: f3ba3679-f2f4-465f-92a5-de320b775a88
    xudp: true
    servername: www.yahoo.com
  - client-fingerprint: chrome
    name: "\U0001F1EF\U0001F1F5日本8 | ⬇️ 12.2MB/s"
    password: 9ca6c56f-ce76-45c2-9855-e3d8a724dcc0
    port: 13001
    server: ver-006.7e4ea15f-92ab-4aa7-b35b-38c3b1d0e495.inbound.ykkcloud.link
    skip-cert-verify: true
    sni: upos-sz-mirroraliov.bilivideo.com
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1ED\U0001F1F0香港20 | ⬇️ 11.3MB/s"
    password: 9ca6c56f-ce76-45c2-9855-e3d8a724dcc0
    port: 11003
    server: ver-006.7e4ea15f-92ab-4aa7-b35b-38c3b1d0e495.inbound.ykkcloud.link
    skip-cert-verify: true
    sni: upos-sz-mirroraliov.bilivideo.com
    type: trojan
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F300其他5-未识别 | ⬇️ 13.3MB/s"
    password: 3e0c78da-8bfb-4b1f-b1ad-de8b073f693e
    port: 16003
    server: *************
    type: ss
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1ED\U0001F1F0香港21 | ⬇️ 11.1MB/s"
    password: 9ca6c56f-ce76-45c2-9855-e3d8a724dcc0
    port: 11005
    server: ver-006.7e4ea15f-92ab-4aa7-b35b-38c3b1d0e495.inbound.ykkcloud.link
    skip-cert-verify: true
    sni: upos-sz-mirroraliov.bilivideo.com
    type: trojan
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F300其他6-未识别 | ⬇️ 11.5MB/s"
    password: c86d483c-431f-41df-bb6b-c1dcebfc7401
    port: 18002
    server: *************
    type: ss
    udp: true
  - cipher: aes-256-cfb
    name: "\U0001F1F0\U0001F1F7韩国11 | ⬇️ 12.6MB/s"
    password: yijian0503
    port: 443
    server: *************
    type: ss
    udp: true
  - cipher: aes-256-cfb
    name: "\U0001F1F0\U0001F1F7韩国12 | ⬇️ 8.4MB/s"
    password: yijian0503
    port: 443
    server: ************
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F300其他7-未识别 | ⬇️ 8.1MB/s"
    password: 3e0c78da-8bfb-4b1f-b1ad-de8b073f693e
    port: 19001
    server: *************
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1F9\U0001F1FC台湾4 | ⬇️ 9.2MB/s"
    password: c86d483c-431f-41df-bb6b-c1dcebfc7401
    port: 17007
    server: *************
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F300其他8-未识别 | ⬇️ 9.4MB/s"
    password: b8fa2e30-a210-4d8b-bb1e-bf42ea9c9e7e
    port: 16014
    server: *************
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F300其他9-TH | ⬇️ 9.4MB/s"
    password: c86d483c-431f-41df-bb6b-c1dcebfc7401
    port: 29015
    server: *************
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F300其他10-未识别 | ⬇️ 9.0MB/s"
    password: 3e0c78da-8bfb-4b1f-b1ad-de8b073f693e
    port: 16005
    server: *************
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F300其他11-未识别 | ⬇️ 9.3MB/s"
    password: 324970cf-e758-44d2-982d-32983fad93bb
    port: 17005
    server: ************
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1ED\U0001F1F0香港22 | ⬇️ 10.3MB/s"
    password: c38ab133-f18f-4537-8dc8-e2e2c2c24e18
    port: 16007
    server: ************
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1F8\U0001F1EC新加坡9 | ⬇️ 9.3MB/s"
    password: 49cdeea8-97dd-402a-bf8f-961cb59123a7
    port: 18001
    server: *************
    type: ss
    udp: true
  - cipher: aes-128-gcm
    name: "\U0001F1F8\U0001F1EC新加坡10 | ⬇️ 12.1MB/s"
    password: 763bf612-4c66-4fd4-b54b-5349bdea6bca
    port: 570
    server: aisalayer-b.upperlay.xyz
    type: ss
    udp: true
  - cipher: aes-128-gcm
    name: "\U0001F1F8\U0001F1EC新加坡11 | ⬇️ 12.3MB/s"
    password: 763bf612-4c66-4fd4-b54b-5349bdea6bca
    port: 568
    server: aisalayer-a.upperlay.xyz
    type: ss
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1EF\U0001F1F5日本9 | ⬇️ 12.0MB/s"
    password: nktaqlk-1O8bEfVXgIhUvYc_
    port: 443
    server: starlink-tko6.2513142.xyz
    skip-cert-verify: true
    sni: www.cloudflare.com
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1EF\U0001F1F5日本10 | ⬇️ 10.6MB/s"
    password: sH-dNCXpq8RiI_PeL6Mr4lMT
    port: 443
    server: starlink-tko5.2513142.xyz
    skip-cert-verify: true
    sni: www.cloudflare.com
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F300其他12-未识别 | ⬇️ 11.7MB/s"
    password: sH-dNCXpq8RiI_PeL6Mr4lMT
    port: 443
    server: starlink-tko4.2513142.xyz
    skip-cert-verify: true
    sni: www.cloudflare.com
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F300其他13-未识别 | ⬇️ 10.9MB/s"
    password: sH-dNCXpq8RiI_PeL6Mr4lMT
    port: 443
    server: starlink-tko6.2513142.xyz
    skip-cert-verify: true
    sni: www.cloudflare.com
    type: trojan
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1F8\U0001F1EC新加坡12 | ⬇️ 11.8MB/s"
    password: abb58966-8d7b-4cdf-bae9-b0d8e184d93d
    port: 18009
    server: *************
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1F8\U0001F1EC新加坡13 | ⬇️ 10.4MB/s"
    password: abb58966-8d7b-4cdf-bae9-b0d8e184d93d
    port: 18004
    server: *************
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1F8\U0001F1EC新加坡14 | ⬇️ 11.6MB/s"
    password: abb58966-8d7b-4cdf-bae9-b0d8e184d93d
    port: 18002
    server: ************
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1EF\U0001F1F5日本11 | ⬇️ 10.3MB/s"
    password: abb58966-8d7b-4cdf-bae9-b0d8e184d93d
    port: 19002
    server: *************
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1EF\U0001F1F5日本12 | ⬇️ 7.9MB/s"
    password: abb58966-8d7b-4cdf-bae9-b0d8e184d93d
    port: 19007
    server: *************
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1F9\U0001F1FC台湾5 | ⬇️ 11.0MB/s"
    password: abb58966-8d7b-4cdf-bae9-b0d8e184d93d
    port: 17010
    server: *************
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1FA\U0001F1F8美国22 | ⬇️ 9.2MB/s"
    password: abb58966-8d7b-4cdf-bae9-b0d8e184d93d
    port: 20009
    server: *************
    type: ss
    udp: true
  - cipher: aes-128-gcm
    name: "\U0001F1FA\U0001F1F8美国23 | ⬇️ 12.1MB/s"
    password: abb58966-8d7b-4cdf-bae9-b0d8e184d93d
    port: 29020
    server: *************
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1F9\U0001F1FC台湾6 | ⬇️ 8.8MB/s"
    password: abb58966-8d7b-4cdf-bae9-b0d8e184d93d
    port: 17002
    server: *************
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1F9\U0001F1FC台湾7 | ⬇️ 9.6MB/s"
    password: abb58966-8d7b-4cdf-bae9-b0d8e184d93d
    port: 17001
    server: ************
    type: ss
    udp: true
  - client-fingerprint: chrome
    grpc-opts:
      grpc-service-name: >-
        CANALTELEGRAM--jet_config--TELEGRAM--jet_config--CANALTELEGRAM--jet_config--TELEGRAM--jet_config--CANALTELEGRAM--jet_config
    name: "\U0001F1EC\U0001F1E7英国1 | ⬇️ 8.0MB/s"
    network: grpc
    port: 2030
    reality-opts:
      public-key: YWfCdTnr4FAOMYTY2dLrMtQUokyxOGpPhYEEszPj20E
      short-id: 7fe29733
    server: *************
    tls: true
    type: vless
    udp: true
    uuid: ad5e479a-d0f6-4809-902c-e74f5404336c
    xudp: true
    servername: refersion.com
  - cipher: aes-256-gcm
    name: "\U0001F300其他14-MY | ⬇️ 10.6MB/s"
    password: abb58966-8d7b-4cdf-bae9-b0d8e184d93d
    port: 29010
    server: *************
    type: ss
    udp: true
  - cipher: aes-128-gcm
    name: "\U0001F1ED\U0001F1F0香港23 | ⬇️ 11.4MB/s"
    password: ed6c8eea-8503-43e1-9563-0a355b0edcac
    port: 51001
    server: cm1.d-h-h.in
    type: ss
    udp: true
  - cipher: chacha20-ietf-poly1305
    name: "\U0001F1F8\U0001F1EC新加坡15 | ⬇️ 15.9MB/s"
    password: ed6c8eea-8503-43e1-9563-0a355b0edcac
    port: 42028
    server: eepl1.dhh114514.christmas
    type: ss
    udp: true
  - cipher: chacha20-ietf-poly1305
    name: "\U0001F1F8\U0001F1EC新加坡16 | ⬇️ 14.0MB/s"
    password: ed6c8eea-8503-43e1-9563-0a355b0edcac
    port: 42028
    server: eepl2.d-h-h.de
    type: ss
    udp: true
  - cipher: chacha20-ietf-poly1305
    name: "\U0001F1EF\U0001F1F5日本13 | ⬇️ 15.1MB/s"
    password: ed6c8eea-8503-43e1-9563-0a355b0edcac
    port: 42031
    server: eepl1.dhh114514.christmas
    type: ss
    udp: true
  - alpn:
      - ''
    alterId: '0'
    cipher: auto
    name: "\U0001F1F8\U0001F1EC新加坡17 | ⬇️ 9.9MB/s"
    network: ws
    port: 49102
    server: free-relay.themars.top
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 781807c4-37df-4da3-9942-c6e82032399a
    ws-opts:
      headers:
        Host: www.cctv.com
      path: /cctv1.m3u8
    xudp: true
  - cipher: chacha20-ietf-poly1305
    name: "\U0001F1EF\U0001F1F5日本14 | ⬇️ 13.9MB/s"
    password: ed6c8eea-8503-43e1-9563-0a355b0edcac
    port: 42031
    server: eepl2.d-h-h.de
    type: ss
    udp: true
  - down: ''
    fingerprint: ''
    name: "\U0001F1EF\U0001F1F5日本15 | ⬇️ 8.3MB/s"
    obfs: ''
    obfs-password: ''
    password: f6552825-6e1b-4fd2-9a2b-1d2b363d9d36
    port: 21077
    server: **************
    skip-cert-verify: true
    sni: bestcast.pw
    type: hysteria2
    up: ''
  - down: ''
    fingerprint: ''
    name: "\U0001F1EF\U0001F1F5日本16 | ⬇️ 8.9MB/s"
    obfs: ''
    obfs-password: ''
    password: f6552825-6e1b-4fd2-9a2b-1d2b363d9d36
    port: 21077
    server: **************
    skip-cert-verify: true
    sni: bestcast.pw
    type: hysteria2
    up: ''
  - down: ''
    fingerprint: ''
    name: "\U0001F1EF\U0001F1F5日本17 | ⬇️ 7.9MB/s"
    obfs: ''
    obfs-password: ''
    password: f6552825-6e1b-4fd2-9a2b-1d2b363d9d36
    port: 21077
    server: *************
    skip-cert-verify: true
    sni: bestcast.pw
    type: hysteria2
    up: ''
  - down: ''
    fingerprint: ''
    name: "\U0001F1EF\U0001F1F5日本18 | ⬇️ 8.2MB/s"
    obfs: ''
    obfs-password: ''
    password: f6552825-6e1b-4fd2-9a2b-1d2b363d9d36
    port: 21077
    server: **************
    skip-cert-verify: true
    sni: bestcast.pw
    type: hysteria2
    up: ''
  - down: ''
    fingerprint: ''
    name: "\U0001F1EF\U0001F1F5日本19 | ⬇️ 8.2MB/s"
    obfs: ''
    obfs-password: ''
    password: f6552825-6e1b-4fd2-9a2b-1d2b363d9d36
    port: 21077
    server: **************
    skip-cert-verify: true
    sni: bestcast.pw
    type: hysteria2
    up: ''
  - cipher: chacha20-ietf-poly1305
    name: "\U0001F1F9\U0001F1FC台湾8 | ⬇️ 16.0MB/s"
    password: ed6c8eea-8503-43e1-9563-0a355b0edcac
    port: 42029
    server: eepl2.d-h-h.de
    type: ss
    udp: true
  - cipher: aes-128-gcm
    name: "\U0001F1F9\U0001F1FC台湾9 | ⬇️ 12.1MB/s"
    password: 8e9e3c27-6531-45f3-96aa-770e9e7f5f46
    port: 45658
    server: pvmatwnew.bigmeyear.org
    type: ss
    udp: true
  - cipher: chacha20-ietf-poly1305
    name: "\U0001F1F9\U0001F1FC台湾10 | ⬇️ 14.8MB/s"
    password: ed6c8eea-8503-43e1-9563-0a355b0edcac
    port: 42029
    server: eepl1.dhh114514.christmas
    type: ss
    udp: true
  - cipher: aes-128-gcm
    name: "\U0001F1F9\U0001F1FC台湾11 | ⬇️ 10.7MB/s"
    password: 8e9e3c27-6531-45f3-96aa-770e9e7f5f46
    port: 46653
    server: npunapnew.bigmeyear.org
    type: ss
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F300其他15-MY | ⬇️ 11.9MB/s"
    password: ed6c8eea-8503-43e1-9563-0a355b0edcac
    port: 41012
    server: cm1.d-h-h.in
    skip-cert-verify: true
    sni: v1-my1.776688.best
    type: trojan
    udp: true
  - cipher: chacha20-ietf-poly1305
    name: "\U0001F300其他16-SE | ⬇️ 11.9MB/s"
    password: ed6c8eea-8503-43e1-9563-0a355b0edcac
    port: 42015
    server: eepl1.dhh114514.christmas
    type: ss
    udp: true
  - alpn:
      - ''
    alterId: '0'
    cipher: auto
    name: "\U0001F300其他17-未识别 | ⬇️ 14.3MB/s"
    network: ws
    port: 20006
    server: 2fea6447-81c4-2d7d-d592-a4f729209777.7head.icu
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 14a8a0db-e922-453a-af8d-843af8734b3b
    ws-opts:
      headers:
        Host: tms.dingtalk.com
      path: /
    xudp: true
  - cipher: aes-256-gcm
    name: "\U0001F1ED\U0001F1F0香港24 | ⬇️ 11.4MB/s"
    password: 3030b609-f36f-4fd1-a4ef-a6e653780536
    port: 16008
    server: *************
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1ED\U0001F1F0香港25 | ⬇️ 10.1MB/s"
    password: 3030b609-f36f-4fd1-a4ef-a6e653780536
    port: 16007
    server: ************
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1ED\U0001F1F0香港26 | ⬇️ 11.1MB/s"
    password: 3030b609-f36f-4fd1-a4ef-a6e653780536
    port: 16004
    server: ************
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1ED\U0001F1F0香港27 | ⬇️ 11.4MB/s"
    password: 3030b609-f36f-4fd1-a4ef-a6e653780536
    port: 16002
    server: *************
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1F8\U0001F1EC新加坡18 | ⬇️ 11.1MB/s"
    password: 3030b609-f36f-4fd1-a4ef-a6e653780536
    port: 18004
    server: *************
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1F8\U0001F1EC新加坡19 | ⬇️ 11.0MB/s"
    password: 3030b609-f36f-4fd1-a4ef-a6e653780536
    port: 18005
    server: ************
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1EF\U0001F1F5日本20 | ⬇️ 11.3MB/s"
    password: 3030b609-f36f-4fd1-a4ef-a6e653780536
    port: 19002
    server: ************
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1FA\U0001F1F8美国24 | ⬇️ 9.8MB/s"
    password: 3030b609-f36f-4fd1-a4ef-a6e653780536
    port: 20004
    server: *************
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1F9\U0001F1FC台湾12 | ⬇️ 10.8MB/s"
    password: 3030b609-f36f-4fd1-a4ef-a6e653780536
    port: 17004
    server: *************
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1F9\U0001F1FC台湾13 | ⬇️ 10.6MB/s"
    password: 3030b609-f36f-4fd1-a4ef-a6e653780536
    port: 17005
    server: ************
    type: ss
    udp: true
  - client-fingerprint: firefox
    flow: xtls-rprx-vision
    name: "\U0001F300其他18-AT | ⬇️ 10.1MB/s"
    network: tcp
    port: 56701
    reality-opts:
      public-key: sW8BfHeovVzmbFuAnr9nH8oJaKYze6shKoKMdek5ai8
      short-id: 792147b8
    server: **************
    tls: true
    type: vless
    udp: true
    uuid: a21bd0f9-e11a-4c69-aa66-31fb9d31c1cb
    xudp: true
    servername: addons.mozilla.org
  - alpn:
      - ''
    alterId: '0'
    cipher: auto
    name: "\U0001F1ED\U0001F1F0香港28 | ⬇️ 10.2MB/s"
    network: ws
    port: 8080
    server: dedeef0e-swd340-syfwbx-1k0yf.hgc1.tcpbbr.net
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 9cc3c03e-21fd-11ee-a642-f23c91369f2d
    ws-opts:
      headers:
        Host: broadcastlv.chat.bilibili.com
      path: /
    xudp: true
  - alpn:
      - ''
    alterId: '0'
    cipher: auto
    name: "\U0001F1ED\U0001F1F0香港29 | ⬇️ 13.8MB/s"
    network: ws
    port: 8080
    server: ba0a1f6c-swd340-sxicvr-1swry.hgc1.tcpbbr.net
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 8a5d58a0-c50e-11ef-a46e-f23c913c8d2b
    ws-opts:
      headers:
        Host: broadcastlv.chat.bilibili.com
      path: /
    xudp: true
  - name: "\U0001F1FA\U0001F1F8美国25 | ⬇️ 9.3MB/s"
    network: ws
    port: 8880
    server: **************
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
        User-Agent: >-
          Mozilla/5.0 (Linux; Android 7.0; TRT-LX3 Build/HUAWEITRT-LX3)
          AppleWebKit/537.36 (KHTML, like Gecko) Chrome/69.0.3497.100 Mobile
          Safari/537.36
      path: "/Telegram\U0001F1E8\U0001F1F3 @pgkj666 /?ed=2560"
    xudp: true
  - name: "\U0001F1FA\U0001F1F8美国26 | ⬇️ 12.3MB/s"
    network: ws
    port: 8880
    server: ***********
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
        User-Agent: >-
          Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like
          Gecko) Chrome/53.0.2785.143 Safari/537.36
      path: "/Telegram\U0001F1E8\U0001F1F3 @pgkj666 /?ed=2560"
    xudp: true
  - name: "\U0001F1F0\U0001F1F7韩国13 | ⬇️ 10.0MB/s"
    network: ws
    port: 8880
    server: ************
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
        User-Agent: >-
          Mozilla/5.0 (Linux; Android 7.0; TRT-LX3 Build/HUAWEITRT-LX3)
          AppleWebKit/537.36 (KHTML, like Gecko) Chrome/69.0.3497.100 Mobile
          Safari/537.36
      path: "/Telegram\U0001F1E8\U0001F1F3 @pgkj666 /?ed=2560"
    xudp: true
  - client-fingerprint: chrome
    name: "\U0001F1ED\U0001F1F0香港30 | ⬇️ 10.7MB/s"
    password: RlzoEILU
    port: 39689
    server: *************
    skip-cert-verify: true
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1E6\U0001F1FA澳大利亚1 | ⬇️ 10.5MB/s"
    password: RlzoEILU
    port: 11641
    server: *************
    skip-cert-verify: true
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1ED\U0001F1F0香港31 | ⬇️ 12.2MB/s"
    password: RlzoEILU
    port: 3754
    server: *************
    skip-cert-verify: true
    type: trojan
    udp: true
  - name: "\U0001F1E9\U0001F1EA德国6 | ⬇️ 12.7MB/s"
    network: ws
    port: 8880
    server: ***********
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
        User-Agent: >-
          Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML,
          like Gecko) Chrome/57.0.2987.133 Safari/537.36
      path: "/Telegram\U0001F1E8\U0001F1F3 @pgkj666 /?ed=2560"
    xudp: true
  - client-fingerprint: chrome
    name: "\U0001F1F3\U0001F1F1荷兰7 | ⬇️ 9.3MB/s"
    password: RlzoEILU
    port: 8565
    server: *************
    skip-cert-verify: true
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1E6\U0001F1FA澳大利亚2 | ⬇️ 11.2MB/s"
    password: RlzoEILU
    port: 40252
    server: **************
    skip-cert-verify: true
    type: trojan
    udp: true
  - alpn:
      - ''
    alterId: '0'
    cipher: auto
    name: "\U0001F1ED\U0001F1F0香港32 | ⬇️ 8.0MB/s"
    network: ws
    port: 8080
    server: 39345447-sw7j40-swdpud-duku.hgc1.tcpbbr.net
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 8e0f8ef2-5fe3-11ec-a8bf-f23c91cfbbc9
    ws-opts:
      headers:
        Host: broadcastlv.chat.bilibili.com
      path: /
    xudp: true
  - alpn:
      - ''
    alterId: '0'
    cipher: auto
    name: "\U0001F1ED\U0001F1F0香港33 | ⬇️ 10.2MB/s"
    network: ws
    port: 8080
    server: cd26efe7-sw7j40-sznzxg-1jfvb.hgc1.tcpbbr.net
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 06b2ac52-fcc6-11ec-bb74-f23c9164ca5d
    ws-opts:
      headers:
        Host: broadcastlv.chat.bilibili.com
      path: /
    xudp: true
  - alpn:
      - ''
    alterId: '0'
    cipher: auto
    name: "\U0001F1ED\U0001F1F0香港34 | ⬇️ 10.9MB/s"
    network: ws
    port: 8080
    server: 85a1c6cd-sw5og0-sx4b2t-tng5.hgc1.tcpbbr.net
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 2ec58858-976b-11ea-82ef-f23c9164ca5d
    ws-opts:
      headers:
        Host: broadcastlv.chat.bilibili.com
      path: /
    xudp: true
  - alpn:
      - ''
    alterId: '0'
    cipher: auto
    name: "\U0001F1ED\U0001F1F0香港35 | ⬇️ 9.9MB/s"
    network: ws
    port: 8080
    server: 12d99852-swb8g0-sww7b0-1qwp5.hgc1.tcpbbr.net
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 6e9cdc20-b92a-11ef-973a-f23c913c8d2b
    ws-opts:
      headers:
        Host: broadcastlv.chat.bilibili.com
      path: /
    xudp: true
  - alpn:
      - ''
    alterId: '0'
    cipher: auto
    name: "\U0001F1ED\U0001F1F0香港36 | ⬇️ 8.9MB/s"
    network: ws
    port: 8080
    server: 0862d860-swb8g0-swdpud-duku.hgc1.tcpbbr.net
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 8e0f8ef2-5fe3-11ec-a8bf-f23c91cfbbc9
    ws-opts:
      headers:
        Host: broadcastlv.chat.bilibili.com
      path: /
    xudp: true
  - alpn:
      - ''
    alterId: '0'
    cipher: auto
    name: "\U0001F1ED\U0001F1F0香港37 | ⬇️ 11.1MB/s"
    network: ws
    port: 8080
    server: e6eb04ed-swb8g0-sxhurg-1th8j.hgc1.tcpbbr.net
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 7d7c111e-fbc1-11ef-ab5f-f23c913c8d2b
    ws-opts:
      headers:
        Host: broadcastlv.chat.bilibili.com
      path: /
    xudp: true
  - alpn:
      - ''
    alterId: '0'
    cipher: auto
    name: "\U0001F1ED\U0001F1F0香港38 | ⬇️ 13.4MB/s"
    network: ws
    port: 8080
    server: 56481893-swb8g0-t6ouc9-13xtu.hgc1.tcpbbr.net
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 4a7f880c-72f4-11ed-b0b5-f23c9164ca5d
    ws-opts:
      headers:
        Host: broadcastlv.chat.bilibili.com
      path: /
    xudp: true
  - alpn:
      - ''
    alterId: '0'
    cipher: auto
    name: "\U0001F1ED\U0001F1F0香港39 | ⬇️ 13.4MB/s"
    network: ws
    port: 8080
    server: 4e1958e7-swb8g0-tf70jh-vm13.hgc1.tcpbbr.net
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 3fa7157d-efe8-832e-9b3c-582d78efbc52
    ws-opts:
      headers:
        Host: broadcastlv.chat.bilibili.com
      path: /
    xudp: true
  - alpn:
      - ''
    alterId: '0'
    cipher: auto
    name: "\U0001F1ED\U0001F1F0香港40 | ⬇️ 15.4MB/s"
    network: ws
    port: 8080
    server: 56884bc4-svukg0-syhw90-1rsuw.hgc1.tcpbbr.net
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 0de37cdc-abff-11ef-b7c6-f23c913c8d2b
    ws-opts:
      headers:
        Host: broadcastlv.chat.bilibili.com
      path: /
    xudp: true
  - alpn:
      - ''
    alterId: '0'
    cipher: auto
    name: "\U0001F1ED\U0001F1F0香港41 | ⬇️ 10.8MB/s"
    network: ws
    port: 8080
    server: 8ad4f3f8-svwf40-sx4b2t-tng5.hgc1.tcpbbr.net
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 2ec58858-976b-11ea-82ef-f23c9164ca5d
    ws-opts:
      headers:
        Host: broadcastlv.chat.bilibili.com
      path: /
    xudp: true
  - name: "\U0001F1EB\U0001F1F7法国2 | ⬇️ 13.1MB/s"
    network: ws
    port: 8080
    server: *************
    type: vless
    udp: true
    uuid: 4ea841c1-0dc1-4563-9f47-deba8407cb4e
    ws-opts:
      headers:
        Host: J9.oDOtZrHUoO.ZuLAIR.ORg.
        User-Agent: >-
          Mozilla/5.0 (X11; Datanyze; Linux x86_64) AppleWebKit/537.36 (KHTML,
          like Gecko) Chrome/65.0.3325.181 Safari/537.36
      path: /?ed=2048
    xudp: true
  - alterId: '0'
    cipher: auto
    name: "\U0001F1ED\U0001F1F0香港42 | ⬇️ 8.9MB/s"
    network: ws
    port: 8080
    server: c25102a2-swkhs0-sxzls8-1gxvd.hgc1.tcpbbr.net
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 398e0d38-8649-11ef-959c-f23c9164ca5d
    ws-opts:
      headers:
        Host: broadcastlv.chat.bilibili.com
      path: /
    xudp: true
  - alterId: '0'
    cipher: auto
    name: "\U0001F1ED\U0001F1F0香港43 | ⬇️ 11.6MB/s"
    network: ws
    port: 8080
    server: e6212e56-swkhs0-sx0fe4-1j6h0.hgc1.tcpbbr.net
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: dcccacba-fa44-11ef-8400-f23c9164ca5d
    ws-opts:
      headers:
        Host: broadcastlv.chat.bilibili.com
      path: /
    xudp: true
  - client-fingerprint: chrome
    name: "\U0001F1FA\U0001F1F8美国27 | ⬇️ 11.0MB/s"
    password: 9ca6c56f-ce76-45c2-9855-e3d8a724dcc0
    port: 15001
    server: ver-006.7e4ea15f-92ab-4aa7-b35b-38c3b1d0e495.inbound.ykkcloud.link
    skip-cert-verify: true
    sni: upos-sz-mirroraliov.bilivideo.com
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1F9\U0001F1FC台湾14 | ⬇️ 12.2MB/s"
    password: 9ca6c56f-ce76-45c2-9855-e3d8a724dcc0
    port: 12001
    server: ver-006.7e4ea15f-92ab-4aa7-b35b-38c3b1d0e495.inbound.ykkcloud.link
    skip-cert-verify: true
    sni: upos-sz-mirroraliov.bilivideo.com
    type: trojan
    udp: true
  - alterId: '0'
    cipher: auto
    name: "\U0001F1ED\U0001F1F0香港44 | ⬇️ 11.4MB/s"
    network: ws
    port: 8080
    server: 622c7865-swo740-tfeciy-1iddy.hgc1.tcpbbr.net
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: e168c43e-c6d9-11ed-a8bf-f23c91cfbbc9
    ws-opts:
      headers:
        Host: broadcastlv.chat.bilibili.com
      path: /
    xudp: true
  - cipher: aes-256-gcm
    name: "\U0001F1EF\U0001F1F5日本21 | ⬇️ 8.7MB/s"
    password: 5UM6JOZY1S7KAQRS
    port: 18007
    server: ti3hyra4.slashdevslashnetslashtun.net
    type: ss
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1ED\U0001F1F0香港45 | ⬇️ 11.8MB/s"
    password: RlzoEILU
    port: 34041
    server: **************
    skip-cert-verify: true
    sni: cdn.egvra.cn
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1F9\U0001F1FC台湾15 | ⬇️ 12.4MB/s"
    password: 9ca6c56f-ce76-45c2-9855-e3d8a724dcc0
    port: 12005
    server: ver-006.7e4ea15f-92ab-4aa7-b35b-38c3b1d0e495.inbound.ykkcloud.link
    skip-cert-verify: true
    sni: upos-sz-mirroraliov.bilivideo.com
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1F8\U0001F1EC新加坡20 | ⬇️ 12.2MB/s"
    password: 9ca6c56f-ce76-45c2-9855-e3d8a724dcc0
    port: 16004
    server: ver-006.7e4ea15f-92ab-4aa7-b35b-38c3b1d0e495.inbound.ykkcloud.link
    skip-cert-verify: true
    sni: upos-sz-mirroraliov.bilivideo.com
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1F9\U0001F1FC台湾16 | ⬇️ 11.2MB/s"
    password: 9ca6c56f-ce76-45c2-9855-e3d8a724dcc0
    port: 12003
    server: ver-006.7e4ea15f-92ab-4aa7-b35b-38c3b1d0e495.inbound.ykkcloud.link
    skip-cert-verify: true
    sni: upos-sz-mirroraliov.bilivideo.com
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1EF\U0001F1F5日本22 | ⬇️ 12.2MB/s"
    password: 9ca6c56f-ce76-45c2-9855-e3d8a724dcc0
    port: 13004
    server: ver-006.7e4ea15f-92ab-4aa7-b35b-38c3b1d0e495.inbound.ykkcloud.link
    skip-cert-verify: true
    sni: upos-sz-mirroraliov.bilivideo.com
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1F8\U0001F1EC新加坡21 | ⬇️ 11.5MB/s"
    password: RlzoEILU
    port: 29220
    server: *************
    skip-cert-verify: true
    sni: cdn.egvra.cn
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1F8\U0001F1EC新加坡22 | ⬇️ 11.9MB/s"
    password: 9ca6c56f-ce76-45c2-9855-e3d8a724dcc0
    port: 16005
    server: ver-006.7e4ea15f-92ab-4aa7-b35b-38c3b1d0e495.inbound.ykkcloud.link
    skip-cert-verify: true
    sni: upos-sz-mirroraliov.bilivideo.com
    type: trojan
    udp: true
rule-providers:
  private:
    url: >-
      https://testingcf.jsdelivr.net/gh/MetaCubeX/meta-rules-dat@meta/geo/geosite/private.yaml
    path: ./ruleset/private.yaml
    behavior: domain
    interval: 86400
    format: yaml
    type: http
  cn_domain:
    url: >-
      https://testingcf.jsdelivr.net/gh/MetaCubeX/meta-rules-dat@meta/geo/geosite/cn.yaml
    path: ./ruleset/cn_domain.yaml
    behavior: domain
    interval: 86400
    format: yaml
    type: http
  telegram_domain:
    url: >-
      https://testingcf.jsdelivr.net/gh/MetaCubeX/meta-rules-dat@meta/geo/geosite/telegram.yaml
    path: ./ruleset/telegram_domain.yaml
    behavior: domain
    interval: 86400
    format: yaml
    type: http
  google_domain:
    url: >-
      https://testingcf.jsdelivr.net/gh/MetaCubeX/meta-rules-dat@meta/geo/geosite/google.yaml
    path: ./ruleset/google_domain.yaml
    behavior: domain
    interval: 86400
    format: yaml
    type: http
  geolocation-!cn:
    url: >-
      https://testingcf.jsdelivr.net/gh/MetaCubeX/meta-rules-dat@meta/geo/geosite/geolocation-!cn.yaml
    path: ./ruleset/geolocation-!cn.yaml
    behavior: domain
    interval: 86400
    format: yaml
    type: http
  cn_ip:
    url: >-
      https://testingcf.jsdelivr.net/gh/MetaCubeX/meta-rules-dat@meta/geo/geoip/cn.yaml
    path: ./ruleset/cn_ip.yaml
    behavior: ipcidr
    interval: 86400
    format: yaml
    type: http
  telegram_ip:
    url: >-
      https://testingcf.jsdelivr.net/gh/MetaCubeX/meta-rules-dat@meta/geo/geoip/telegram.yaml
    path: ./ruleset/telegram_ip.yaml
    behavior: ipcidr
    interval: 86400
    format: yaml
    type: http
  google_ip:
    url: >-
      https://testingcf.jsdelivr.net/gh/MetaCubeX/meta-rules-dat@meta/geo/geoip/google.yaml
    path: ./ruleset/google_ip.yaml
    behavior: ipcidr
    interval: 86400
    format: yaml
    type: http
  bing:
    url: >-
      https://testingcf.jsdelivr.net/gh/blackmatrix7/ios_rule_script@master/rule/Clash/Bing/Bing.yaml
    path: ./ruleset/bing.yaml
    behavior: classical
    interval: 86400
    format: yaml
    type: http
  copilot:
    url: >-
      https://testingcf.jsdelivr.net/gh/blackmatrix7/ios_rule_script@master/rule/Clash/Copilot/Copilot.yaml
    path: ./ruleset/copilot.yaml
    behavior: classical
    interval: 86400
    format: yaml
    type: http
  claude:
    url: >-
      https://testingcf.jsdelivr.net/gh/blackmatrix7/ios_rule_script@master/rule/Clash/Claude/Claude.yaml
    path: ./ruleset/claude.yaml
    behavior: classical
    interval: 86400
    format: yaml
    type: http
  bard:
    url: >-
      https://testingcf.jsdelivr.net/gh/blackmatrix7/ios_rule_script@master/rule/Clash/BardAI/BardAI.yaml
    path: ./ruleset/bard.yaml
    behavior: classical
    interval: 86400
    format: yaml
    type: http
  openai:
    url: >-
      https://testingcf.jsdelivr.net/gh/blackmatrix7/ios_rule_script@master/rule/Clash/OpenAI/OpenAI.yaml
    path: ./ruleset/openai.yaml
    behavior: classical
    interval: 86400
    format: yaml
    type: http
  steam:
    url: >-
      https://testingcf.jsdelivr.net/gh/blackmatrix7/ios_rule_script@master/rule/Clash/Steam/Steam.yaml
    path: ./ruleset/steam.yaml
    behavior: classical
    interval: 86400
    format: yaml
    type: http
proxy-groups:
  - icon: 'https://testingcf.jsdelivr.net/gh/Orz-3/mini@master/Color/Static.png'
    include-all: true
    exclude-filter: (?i)GB|Traffic|Expire|Premium|频道|订阅|ISP|流量|到期|重置
    name: PROXY
    type: select
    proxies:
      - AUTO
      - HK AUTO
      - SG AUTO
      - JP AUTO
      - US AUTO
  - icon: 'https://testingcf.jsdelivr.net/gh/Orz-3/mini@master/Color/Urltest.png'
    include-all: true
    exclude-filter: (?i)GB|Traffic|Expire|Premium|频道|订阅|ISP|流量|到期|重置
    name: AUTO
    type: url-test
    interval: 300
  - icon: 'https://testingcf.jsdelivr.net/gh/Orz-3/mini@master/Color/OpenAI.png'
    name: AIGC
    type: select
    proxies:
      - SG AUTO
      - JP AUTO
      - US AUTO
  - icon: 'https://testingcf.jsdelivr.net/gh/Orz-3/mini@master/Color/Telegram.png'
    name: Telegram
    type: select
    proxies:
      - AUTO
      - HK AUTO
      - SG AUTO
      - JP AUTO
      - US AUTO
  - icon: 'https://testingcf.jsdelivr.net/gh/Orz-3/mini@master/Color/Google.png'
    name: Google
    type: select
    proxies:
      - AUTO
      - HK AUTO
      - SG AUTO
      - JP AUTO
      - US AUTO
  - icon: 'https://testingcf.jsdelivr.net/gh/Orz-3/mini@master/Color/HK.png'
    include-all: true
    exclude-filter: (?i)GB|Traffic|Expire|Premium|频道|订阅|ISP|流量|到期|重置
    filter: "(?i)香港|Hong Kong|HK|\U0001F1ED\U0001F1F0"
    name: HK AUTO
    type: url-test
    interval: 300
  - icon: 'https://testingcf.jsdelivr.net/gh/Orz-3/mini@master/Color/SG.png'
    include-all: true
    exclude-filter: (?i)GB|Traffic|Expire|Premium|频道|订阅|ISP|流量|到期|重置
    filter: "(?i)新加坡|Singapore|\U0001F1F8\U0001F1EC"
    name: SG AUTO
    type: url-test
    interval: 300
  - icon: 'https://testingcf.jsdelivr.net/gh/Orz-3/mini@master/Color/JP.png'
    include-all: true
    exclude-filter: (?i)GB|Traffic|Expire|Premium|频道|订阅|ISP|流量|到期|重置
    filter: "(?i)日本|Japan|\U0001F1EF\U0001F1F5"
    name: JP AUTO
    type: url-test
    interval: 300
  - icon: 'https://testingcf.jsdelivr.net/gh/Orz-3/mini@master/Color/US.png'
    include-all: true
    exclude-filter: (?i)GB|Traffic|Expire|Premium|频道|订阅|ISP|流量|到期|重置
    filter: "(?i)美国|USA|\U0001F1FA\U0001F1F8"
    name: US AUTO
    type: url-test
    interval: 300
  - icon: 'https://testingcf.jsdelivr.net/gh/Orz-3/mini@master/Color/Global.png'
    include-all: true
    exclude-filter: (?i)GB|Traffic|Expire|Premium|频道|订阅|ISP|流量|到期|重置
    proxies:
      - PROXY
      - AUTO
      - AIGC
      - Telegram
      - Google
      - HK AUTO
      - SG AUTO
      - JP AUTO
      - US AUTO
    name: GLOBAL
    type: select
rules:
  - 'PROCESS-NAME,subs-check.exe,DIRECT'
  - 'PROCESS-NAME,subs-check,DIRECT'
  - 'RULE-SET,private,DIRECT'
  - 'RULE-SET,bing,AIGC'
  - 'RULE-SET,copilot,AIGC'
  - 'RULE-SET,bard,AIGC'
  - 'RULE-SET,openai,AIGC'
  - 'RULE-SET,claude,AIGC'
  - 'RULE-SET,steam,PROXY'
  - 'RULE-SET,telegram_domain,Telegram'
  - 'RULE-SET,telegram_ip,Telegram'
  - 'RULE-SET,google_domain,Google'
  - 'RULE-SET,google_ip,Google'
  - 'RULE-SET,geolocation-!cn,PROXY'
  - 'RULE-SET,cn_domain,DIRECT'
  - 'RULE-SET,cn_ip,DIRECT'
  - 'MATCH,PROXY'
