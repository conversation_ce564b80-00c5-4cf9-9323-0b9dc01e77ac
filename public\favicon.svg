<?xml version="1.0" encoding="UTF-8"?>
<svg width="108px" height="108px" viewBox="0 0 108 108" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>logo</title>
    <defs>
        <linearGradient x1="100%" y1="50%" x2="0%" y2="50%" id="linearGradient-1">
            <stop stop-color="#93EEFE" offset="0%"></stop>
            <stop stop-color="#A1FCC6" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="100%" y1="50%" x2="0%" y2="50%" id="linearGradient-2">
            <stop stop-color="#93EEFE" offset="0%"></stop>
            <stop stop-color="#A1FCC6" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="logo" transform="translate(-0.000000, 0.000000)" fill-rule="nonzero">
            <polygon id="路径" fill="#121212" transform="translate(54.000000, 54.000000) scale(-1, 1) rotate(-180.000000) translate(-54.000000, -54.000000) " points="7.10542736e-15 54 7.10542736e-15 7.10542736e-15 54 7.10542736e-15 108 7.10542736e-15 108 54 108 108 54 108 7.10542736e-15 108"></polygon>
            <path d="M57.7183124,39.2119247 C61.1183124,28.9119247 51.2183124,18.6119247 40.9183124,21.7119247 C38.3183124,22.5119247 34.5183124,25.5119247 29.1183124,30.9119247 L21.2183124,38.9119247 L23.4183124,41.1119247 L25.6183124,43.3119247 L33.4183124,35.6119247 C39.8183124,29.2119247 41.7183124,27.9119247 44.4183124,27.9119247 C51.6183124,27.9119247 54.1183124,36.1119247 48.6183124,41.4119247 L45.6183124,44.3119247 L47.8183124,46.6119247 L50.0183124,48.9119247 L53.3183124,45.7119247 C55.1183124,44.0119247 57.1183124,41.0119247 57.7183124,39.2119247 Z" id="路径" fill="url(#linearGradient-1)" transform="translate(39.809156, 35.031598) scale(-1, 1) rotate(-360.000000) translate(-39.809156, -35.031598) "></path>
            <path d="M49.4,60.392072 L43.6,54.5 L46.5,51.5 L49.4,48.5 L47.2,46.3 L45,44 L39.8,49.3 L34.5,54.5 L39.5,59.5 C42.2,62.2 44.7,64.5 45.1,64.5 C45.3,64.5 46.7333333,63.1306907 49.4,60.392072 Z" id="路径" fill-opacity="0.96" fill="#FFFFFF" transform="translate(41.950000, 54.250000) scale(-1, 1) rotate(-180.000000) translate(-41.950000, -54.250000) "></path>
            <path d="M73.3,60.392072 L67.5,54.5 L70.4,51.5 L73.3,48.5 L71.1,46.3 L68.9,44 L63.7,49.3 L58.4,54.5 L63.4,59.5 C66.1,62.2 68.6,64.5 69,64.5 C69.2,64.5 70.6333333,63.1306907 73.3,60.392072 Z" id="路径" fill-opacity="0.96" fill="#FFFFFF" transform="translate(65.850000, 54.250000) rotate(-180.000000) translate(-65.850000, -54.250000) "></path>
            <path d="M31,79.0608814 C27.5,75.0608814 27.1,70.6608814 30,67.7608814 C33.1,64.6608814 38,65.1608814 41.5,68.7608814 L44.4,71.7608814 L46.7,69.5608814 L49,67.3608814 L45.8,64.0608814 C39.6,57.6608814 31.5,57.2608814 25.6,63.2608814 C19.6,69.1608814 19.9,76.8608814 26.3,83.4608814 L29.4,86.7608814 L31.7,84.5608814 L33.9,82.4608814 L31,79.0608814 Z" id="路径" fill="url(#linearGradient-2)" transform="translate(35.146221, 72.880441) scale(-1, 1) rotate(-180.000000) translate(-35.146221, -72.880441) "></path>
            <path d="M68.7075588,41.2119247 C65.2075588,37.2119247 64.8075588,32.8119247 67.7075588,29.9119247 C70.8075588,26.8119247 75.7075588,27.3119247 79.2075588,30.9119247 L82.1075588,33.9119247 L84.4075588,31.7119247 L86.7075588,29.5119247 L83.5075588,26.2119247 C77.3075588,19.8119247 69.2075588,19.4119247 63.3075588,25.4119247 C57.3075588,31.3119247 57.6075588,39.0119247 64.0075588,45.6119247 L67.1075588,48.9119247 L69.4075588,46.7119247 L71.6075588,44.6119247 L68.7075588,41.2119247 Z" id="路径" fill="url(#linearGradient-2)" transform="translate(72.853779, 35.031484) scale(-1, 1) rotate(-360.000000) translate(-72.853779, -35.031484) "></path>
            <path d="M86.1,77.0606524 C89.5,66.7606524 79.6,56.4606524 69.3,59.5606524 C66.7,60.3606524 62.9,63.3606524 57.5,68.7606524 L49.6,76.7606524 L51.8,78.9606524 L54,81.1606524 L61.8,73.4606524 C68.2,67.0606524 70.1,65.7606524 72.8,65.7606524 C80,65.7606524 82.5,73.9606524 77,79.2606524 L74,82.1606524 L76.2,84.4606524 L78.4,86.7606524 L81.7,83.5606524 C83.5,81.8606524 85.5,78.8606524 86.1,77.0606524 Z" id="路径" fill="url(#linearGradient-1)" transform="translate(68.190844, 72.880326) scale(-1, 1) rotate(-180.000000) translate(-68.190844, -72.880326) "></path>
        </g>
    </g>
</svg>