[{"type": "ss", "name": "🇯🇵 日本 001", "server": "54.95.11.147", "port": 443, "password": "awsps0501", "method": "aes-256-cfb", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "JP", "originalName": "🇯🇵 日本 001"}, {"type": "hysteria2", "name": "🇯🇵 日本 002", "server": "jiangzhixjp.54264944.xyz", "port": 43999, "password": "6c510073-4ca8-423b-87a5-a6d73c0ca557", "auth": "6c510073-4ca8-423b-87a5-a6d73c0ca557", "obfs": {"type": "", "password": ""}, "tls": {"enabled": true, "serverName": "jiangzhixjp.54264944.xyz", "alpn": ["h3"], "skipCertVerify": true}, "bandwidth": {"up": "", "down": ""}, "congestion": "bbr", "fastOpen": false, "lazy": false, "_sourceFormat": "base64", "detectedRegion": "JP", "originalName": "🇯🇵 日本 002"}, {"type": "ss", "name": "🇯🇵 日本 003", "server": "************", "port": 19010, "password": "c86d483c-431f-41df-bb6b-c1dcebfc7401", "method": "aes-256-gcm", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "JP", "originalName": "🇯🇵 日本 003"}, {"type": "ss", "name": "🇯🇵 日本 004", "server": "*************", "port": 19008, "password": "c86d483c-431f-41df-bb6b-c1dcebfc7401", "method": "aes-256-gcm", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "JP", "originalName": "🇯🇵 日本 004"}, {"type": "ss", "name": "🇯🇵 日本 005", "server": "************", "port": 19002, "password": "3030b609-f36f-4fd1-a4ef-a6e653780536", "method": "aes-256-gcm", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "JP", "originalName": "🇯🇵 日本 005"}, {"type": "hysteria2", "name": "🇯🇵 日本 006", "server": "jp5.dexlos.com", "port": 9517, "password": "0A1DDFE6-A16A-43FA-A6E3-70EDE4EA61C3", "auth": "0A1DDFE6-A16A-43FA-A6E3-70EDE4EA61C3", "obfs": {"type": "", "password": ""}, "tls": {"enabled": true, "serverName": "jp5.dexlos.com", "alpn": ["h3"], "skipCertVerify": false}, "bandwidth": {"up": "", "down": ""}, "congestion": "bbr", "fastOpen": false, "lazy": false, "_sourceFormat": "base64", "detectedRegion": "JP", "originalName": "🇯🇵 日本 006"}, {"type": "hysteria2", "name": "🇯🇵 日本 007", "server": "jp1.dexlos.com", "port": 1717, "password": "0A1DDFE6-A16A-43FA-A6E3-70EDE4EA61C3", "auth": "0A1DDFE6-A16A-43FA-A6E3-70EDE4EA61C3", "obfs": {"type": "", "password": ""}, "tls": {"enabled": true, "serverName": "jp1.dexlos.com", "alpn": ["h3"], "skipCertVerify": false}, "bandwidth": {"up": "", "down": ""}, "congestion": "bbr", "fastOpen": false, "lazy": false, "_sourceFormat": "base64", "detectedRegion": "JP", "originalName": "🇯🇵 日本 007"}, {"type": "hysteria2", "name": "🇯🇵 日本 008", "server": "jp4.dexlos.com", "port": 7270, "password": "0A1DDFE6-A16A-43FA-A6E3-70EDE4EA61C3", "auth": "0A1DDFE6-A16A-43FA-A6E3-70EDE4EA61C3", "obfs": {"type": "", "password": ""}, "tls": {"enabled": true, "serverName": "jp4.dexlos.com", "alpn": ["h3"], "skipCertVerify": false}, "bandwidth": {"up": "", "down": ""}, "congestion": "bbr", "fastOpen": false, "lazy": false, "_sourceFormat": "base64", "detectedRegion": "JP", "originalName": "🇯🇵 日本 008"}, {"type": "trojan", "name": "🇯🇵 日本 009", "server": "f10021.ylxblkyndjj.sbs", "port": 34664, "password": "74b001ba-7020-443d-b33b-5a403a156b39", "network": "tcp", "tls": {"enabled": true, "serverName": "jp04.ckcloud.info", "alpn": [], "fingerprint": "", "skipCertVerify": true}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "JP", "originalName": "🇯🇵 日本 009"}, {"type": "trojan", "name": "🇯🇵 日本 010", "server": "cn2.cdn.xfltd-cdn.top", "port": 12033, "password": "7669a04d-a459-4c96-bc0e-fde51cb984f3", "network": "tcp", "tls": {"enabled": true, "serverName": "cn2.cdn.xfltd-cdn.top", "alpn": [], "fingerprint": "chrome", "skipCertVerify": true}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "JP", "originalName": "🇯🇵 日本 010"}, {"type": "trojan", "name": "🇯🇵 日本 011", "server": "***********", "port": 443, "password": "CXCu72eya8wFeRySSFpDz3CN6jBYac5OIl3q0gSl8xZOADY3EK94pxZanDA3RT", "network": "tcp", "tls": {"enabled": true, "serverName": "broker.superpokemon.com", "alpn": [], "fingerprint": "", "skipCertVerify": true}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "JP", "originalName": "🇯🇵 日本 011"}, {"type": "trojan", "name": "🇯🇵 日本 012", "server": "50369d05cf0fd12df03d8a9dbaaf53ec.v1.cac.node-is.green", "port": 42982, "password": "e2241e38-d5d4-4f60-afcd-bf7d6bfb6668", "network": "tcp", "tls": {"enabled": true, "serverName": "jp1.bilibili.com", "alpn": [], "fingerprint": "chrome", "skipCertVerify": true}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "JP", "originalName": "🇯🇵 日本 012"}, {"type": "trojan", "name": "🇯🇵 日本 013", "server": "cn2.cdn.xfltd-cdn.top", "port": 12032, "password": "7669a04d-a459-4c96-bc0e-fde51cb984f3", "network": "tcp", "tls": {"enabled": true, "serverName": "cn2.cdn.xfltd-cdn.top", "alpn": [], "fingerprint": "chrome", "skipCertVerify": true}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "JP", "originalName": "🇯🇵 日本 013"}, {"type": "trojan", "name": "🇯🇵 日本 014", "server": "cn2.cdn.xfltd-cdn.top", "port": 12035, "password": "7669a04d-a459-4c96-bc0e-fde51cb984f3", "network": "tcp", "tls": {"enabled": true, "serverName": "cn2.cdn.xfltd-cdn.top", "alpn": [], "fingerprint": "chrome", "skipCertVerify": true}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "JP", "originalName": "🇯🇵 日本 014"}, {"type": "hysteria2", "name": "🇯🇵 日本 015", "server": "jp01.poke-mon.xyz", "port": 20000, "password": "82762002-40f5-4775-a9ad-3da97a2772f7", "auth": "82762002-40f5-4775-a9ad-3da97a2772f7", "obfs": {"type": "", "password": ""}, "tls": {"enabled": true, "serverName": "www.bing.com", "alpn": ["h3"], "skipCertVerify": true}, "bandwidth": {"up": "", "down": ""}, "congestion": "bbr", "fastOpen": false, "lazy": false, "_sourceFormat": "base64", "detectedRegion": "JP", "originalName": "🇯🇵 日本 015"}, {"type": "ss", "name": "🇯🇵 日本 016", "server": "**************", "port": 443, "password": "awsps0501", "method": "aes-256-cfb", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "JP", "originalName": "🇯🇵 日本 016"}, {"type": "ss", "name": "🇯🇵 日本 017", "server": "*************", "port": 19007, "password": "abb58966-8d7b-4cdf-bae9-b0d8e184d93d", "method": "aes-256-gcm", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "JP", "originalName": "🇯🇵 日本 017"}, {"type": "ss", "name": "🇯🇵 日本 018", "server": "*************", "port": 19002, "password": "abb58966-8d7b-4cdf-bae9-b0d8e184d93d", "method": "aes-256-gcm", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "JP", "originalName": "🇯🇵 日本 018"}, {"type": "ss", "name": "🇯🇵 日本 019", "server": "************", "port": 19009, "password": "abb58966-8d7b-4cdf-bae9-b0d8e184d93d", "method": "aes-256-gcm", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "JP", "originalName": "🇯🇵 日本 019"}, {"type": "ss", "name": "🇯🇵 日本 020", "server": "*************", "port": 19001, "password": "b8fa2e30-a210-4d8b-bb1e-bf42ea9c9e7e", "method": "aes-256-gcm", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "JP", "originalName": "🇯🇵 日本 020"}, {"type": "hysteria2", "name": "🇯🇵 日本 021", "server": "*************", "port": 21077, "password": "f6552825-6e1b-4fd2-9a2b-1d2b363d9d36", "auth": "f6552825-6e1b-4fd2-9a2b-1d2b363d9d36", "obfs": {"type": "", "password": ""}, "tls": {"enabled": true, "serverName": "bestcast.pw", "alpn": ["h3"], "skipCertVerify": true}, "bandwidth": {"up": "", "down": ""}, "congestion": "bbr", "fastOpen": false, "lazy": false, "_sourceFormat": "base64", "detectedRegion": "JP", "originalName": "🇯🇵 日本 021"}, {"type": "vless", "name": "🇯🇵 日本 022", "server": "jp004.421421.xyz", "port": 20230, "uuid": "a124e41a-0fb8-4543-a0ab-a127c7b515a9", "flow": "xtls-rprx-vision", "encryption": "none", "network": "tcp", "tls": {"enabled": true, "serverName": "www.nvidia.com", "alpn": [], "fingerprint": "chrome"}, "transport": {}, "reality": {"enabled": true, "publicKey": "lbOfuIKCBPcQH4AEnwnPw1LNxWrl-Bul6KU99H240Fc", "shortId": "abae4722", "spiderX": ""}, "_sourceFormat": "base64", "detectedRegion": "JP", "originalName": "🇯🇵 日本 022"}, {"type": "ss", "name": "🇯🇵 日本 023", "server": "**************", "port": 443, "password": "awsps0501", "method": "aes-256-cfb", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "JP", "originalName": "🇯🇵 日本 023"}, {"type": "ss", "name": "🇯🇵 日本 024", "server": "sz.fanhua.art", "port": 36166, "password": "b86a48e8-542d-4b90-bb6e-ff0a3e7e4e97", "method": "aes-128-gcm", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "JP", "originalName": "🇯🇵 日本 024"}, {"type": "vless", "name": "🇯🇵 日本 025", "server": "jp001.421421.xyz", "port": 20230, "uuid": "0bfa2050-b165-4156-859c-70f36d300dce", "flow": "xtls-rprx-vision", "encryption": "none", "network": "tcp", "tls": {"enabled": true, "serverName": "www.nvidia.com", "alpn": [], "fingerprint": "chrome"}, "transport": {}, "reality": {"enabled": true, "publicKey": "opaO8sUF9JU5hP2wRoUgS6aWxFLfen83in6ZWJMonG4", "shortId": "b92b2a09", "spiderX": ""}, "_sourceFormat": "base64", "detectedRegion": "JP", "originalName": "🇯🇵 日本 025"}, {"type": "trojan", "name": "🇯🇵 日本 026", "server": "cn2.cdn.xfltd-cdn.top", "port": 12034, "password": "7669a04d-a459-4c96-bc0e-fde51cb984f3", "network": "tcp", "tls": {"enabled": true, "serverName": "cn2.cdn.xfltd-cdn.top", "alpn": [], "fingerprint": "", "skipCertVerify": true}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "JP", "originalName": "🇯🇵 日本 026"}, {"type": "trojan", "name": "🇯🇵 日本 027", "server": "cn2.cdn.xfltd-cdn.top", "port": 12031, "password": "7669a04d-a459-4c96-bc0e-fde51cb984f3", "network": "tcp", "tls": {"enabled": true, "serverName": "cn2.cdn.xfltd-cdn.top", "alpn": [], "fingerprint": "", "skipCertVerify": true}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "JP", "originalName": "🇯🇵 日本 027"}, {"type": "ss", "name": "🇯🇵 日本 028", "server": "eepl1.dhh114514.christmas", "port": 42031, "password": "ed6c8eea-8503-43e1-9563-0a355b0edcac", "method": "chacha20-ietf-poly1305", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "JP", "originalName": "🇯🇵 日本 028"}, {"type": "ss", "name": "🇯🇵 日本 029", "server": "eepl2.d-h-h.de", "port": 42031, "password": "ed6c8eea-8503-43e1-9563-0a355b0edcac", "method": "chacha20-ietf-poly1305", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "JP", "originalName": "🇯🇵 日本 029"}, {"type": "ss", "name": "🇯🇵 日本 030", "server": "ti3hyra4.slashdevslashnetslashtun.net", "port": 18010, "password": "9FUHILBF7J8FJOUP", "method": "aes-256-gcm", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "JP", "originalName": "🇯🇵 日本 030"}, {"type": "ss", "name": "🇯🇵 日本 031", "server": "ti3hyra4.slashdevslashnetslashtun.net", "port": 18003, "password": "IFZ122XFFWS7SFVN", "method": "aes-256-gcm", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "JP", "originalName": "🇯🇵 日本 031"}, {"type": "ss", "name": "🇯🇵 日本 032", "server": "8tv68qhq.slashdevslashnetslashtun.net", "port": 18002, "password": "W6QW1HQ85JDNZS33", "method": "aes-256-gcm", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "JP", "originalName": "🇯🇵 日本 032"}, {"type": "ss", "name": "🇯🇵 日本 033", "server": "54.168.106.133", "port": 443, "password": "awsps0501", "method": "aes-256-cfb", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "JP", "originalName": "🇯🇵 日本 033"}, {"type": "ss", "name": "🇯🇵 日本 034", "server": "*************", "port": 19002, "password": "c86d483c-431f-41df-bb6b-c1dcebfc7401", "method": "aes-256-gcm", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "JP", "originalName": "🇯🇵 日本 034"}, {"type": "hysteria2", "name": "🇯🇵 日本 035", "server": "qyxjp2.qy1357.top", "port": 30003, "password": "796ec552-f8e4-43c7-ac2f-5c2e668074de", "auth": "796ec552-f8e4-43c7-ac2f-5c2e668074de", "obfs": {"type": "", "password": ""}, "tls": {"enabled": true, "serverName": "qyxjp2.qy1357.top", "alpn": ["h3"], "skipCertVerify": true}, "bandwidth": {"up": "", "down": ""}, "congestion": "bbr", "fastOpen": false, "lazy": false, "_sourceFormat": "base64", "detectedRegion": "JP", "originalName": "🇯🇵 日本 035"}, {"type": "hysteria2", "name": "🇯🇵 日本 036", "server": "jp3.dexlos.com", "port": 5347, "password": "48173CB0-68B5-4CAB-9587-FB7CCDE206A7", "auth": "48173CB0-68B5-4CAB-9587-FB7CCDE206A7", "obfs": {"type": "", "password": ""}, "tls": {"enabled": true, "serverName": "jp3.dexlos.com", "alpn": ["h3"], "skipCertVerify": false}, "bandwidth": {"up": "", "down": ""}, "congestion": "bbr", "fastOpen": false, "lazy": false, "_sourceFormat": "base64", "detectedRegion": "JP", "originalName": "🇯🇵 日本 036"}, {"type": "hysteria2", "name": "🇯🇵 日本 037", "server": "jp5.dexlos.com", "port": 9197, "password": "48173CB0-68B5-4CAB-9587-FB7CCDE206A7", "auth": "48173CB0-68B5-4CAB-9587-FB7CCDE206A7", "obfs": {"type": "", "password": ""}, "tls": {"enabled": true, "serverName": "jp5.dexlos.com", "alpn": ["h3"], "skipCertVerify": false}, "bandwidth": {"up": "", "down": ""}, "congestion": "bbr", "fastOpen": false, "lazy": false, "_sourceFormat": "base64", "detectedRegion": "JP", "originalName": "🇯🇵 日本 037"}, {"type": "hysteria2", "name": "🇯🇵 日本 038", "server": "jp3.844300.xyz", "port": 30000, "password": "67d9d454-0d38-4eea-830d-6f9362d988c6", "auth": "67d9d454-0d38-4eea-830d-6f9362d988c6", "obfs": {"type": "", "password": ""}, "tls": {"enabled": true, "serverName": "jp3.844300.xyz", "alpn": ["h3"], "skipCertVerify": false}, "bandwidth": {"up": "", "down": ""}, "congestion": "bbr", "fastOpen": false, "lazy": false, "_sourceFormat": "base64", "detectedRegion": "JP", "originalName": "🇯🇵 日本 038"}, {"type": "hysteria2", "name": "🇯🇵 日本 039", "server": "jp5.844300.xyz", "port": 30000, "password": "67d9d454-0d38-4eea-830d-6f9362d988c6", "auth": "67d9d454-0d38-4eea-830d-6f9362d988c6", "obfs": {"type": "", "password": ""}, "tls": {"enabled": true, "serverName": "jp5.844300.xyz", "alpn": ["h3"], "skipCertVerify": false}, "bandwidth": {"up": "", "down": ""}, "congestion": "bbr", "fastOpen": false, "lazy": false, "_sourceFormat": "base64", "detectedRegion": "JP", "originalName": "🇯🇵 日本 039"}, {"type": "hysteria2", "name": "🇯🇵 日本 040", "server": "jp2.844300.xyz", "port": 30000, "password": "67d9d454-0d38-4eea-830d-6f9362d988c6", "auth": "67d9d454-0d38-4eea-830d-6f9362d988c6", "obfs": {"type": "", "password": ""}, "tls": {"enabled": true, "serverName": "jp2.844300.xyz", "alpn": ["h3"], "skipCertVerify": false}, "bandwidth": {"up": "", "down": ""}, "congestion": "bbr", "fastOpen": false, "lazy": false, "_sourceFormat": "base64", "detectedRegion": "JP", "originalName": "🇯🇵 日本 040"}, {"type": "vless", "name": "🇯🇵 日本 041", "server": "jp002.421421.xyz", "port": 20230, "uuid": "a124e41a-0fb8-4543-a0ab-a127c7b515a9", "flow": "xtls-rprx-vision", "encryption": "none", "network": "tcp", "tls": {"enabled": true, "serverName": "www.nvidia.com", "alpn": [], "fingerprint": "chrome"}, "transport": {}, "reality": {"enabled": true, "publicKey": "yNOemxnmT-HQ2HiR9fdHSN2B8HpHsRyMIsir_iAlaHE", "shortId": "5c3c9f11", "spiderX": ""}, "_sourceFormat": "base64", "detectedRegion": "JP", "originalName": "🇯🇵 日本 041"}, {"type": "trojan", "name": "🇺🇸 美国 001", "server": "cn1.cdn.xfltd-cdn.top", "port": 12004, "password": "7669a04d-a459-4c96-bc0e-fde51cb984f3", "network": "tcp", "tls": {"enabled": true, "serverName": "cdn.alibaba.com", "alpn": [], "fingerprint": "chrome", "skipCertVerify": true}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "US", "originalName": "🇺🇸 美国 001"}, {"type": "hysteria2", "name": "🇺🇸 美国 002", "server": "qyhg.qy1357.top", "port": 30003, "password": "d010926e-0311-4924-a013-b84fbae430f9", "auth": "d010926e-0311-4924-a013-b84fbae430f9", "obfs": {"type": "", "password": ""}, "tls": {"enabled": true, "serverName": "qyhg.qy1357.top", "alpn": ["h3"], "skipCertVerify": true}, "bandwidth": {"up": "", "down": ""}, "congestion": "bbr", "fastOpen": false, "lazy": false, "_sourceFormat": "base64", "detectedRegion": "US", "originalName": "🇺🇸 美国 002"}, {"type": "hysteria2", "name": "🇺🇸 美国 003", "server": "*************", "port": 57773, "password": "dongtaiwang.com", "auth": "dongtaiwang.com", "obfs": {"type": "", "password": ""}, "tls": {"enabled": true, "serverName": "apple.com", "alpn": ["h3"], "skipCertVerify": true}, "bandwidth": {"up": "", "down": ""}, "congestion": "bbr", "fastOpen": false, "lazy": false, "_sourceFormat": "base64", "detectedRegion": "US", "originalName": "🇺🇸 美国 003"}, {"type": "ss", "name": "🇺🇸 美国 004", "server": "*************", "port": 20002, "password": "3030b609-f36f-4fd1-a4ef-a6e653780536", "method": "aes-256-gcm", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "US", "originalName": "🇺🇸 美国 004"}, {"type": "trojan", "name": "🇺🇸 美国 005", "server": "xd-js.timiwc.com", "port": 21603, "password": "2b1ed981-6547-4094-998b-06a3323d6f6c", "network": "tcp", "tls": {"enabled": true, "serverName": "k61.tudou211.com", "alpn": [], "fingerprint": "chrome", "skipCertVerify": true}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "US", "originalName": "🇺🇸 美国 005"}, {"type": "hysteria2", "name": "🇺🇸 美国 006", "server": "**************", "port": 3234, "password": "65a43b6e-19c5-4fab-b960-d110a07d66a4", "auth": "65a43b6e-19c5-4fab-b960-d110a07d66a4", "obfs": {"type": "", "password": ""}, "tls": {"enabled": true, "serverName": "**************", "alpn": ["h3"], "skipCertVerify": true}, "bandwidth": {"up": "", "down": ""}, "congestion": "bbr", "fastOpen": false, "lazy": false, "_sourceFormat": "base64", "detectedRegion": "US", "originalName": "🇺🇸 美国 006"}, {"type": "ss", "name": "🇺🇸 美国 007", "server": "*************", "port": 20004, "password": "3030b609-f36f-4fd1-a4ef-a6e653780536", "method": "aes-256-gcm", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "US", "originalName": "🇺🇸 美国 007"}, {"type": "vless", "name": "🇺🇸 美国 008", "server": "dmit.jhyl.bid", "port": 443, "uuid": "0cc14bae-0703-4c2d-e9de-ed4672eadd30", "flow": "", "encryption": "none", "network": "ws", "tls": {"enabled": true, "serverName": "dmit.jhyl.bid", "alpn": [], "fingerprint": "chrome"}, "transport": {"path": "/download", "host": "dmit.jhyl.bid"}, "reality": {"enabled": false, "publicKey": "", "shortId": "", "spiderX": ""}, "_sourceFormat": "base64", "detectedRegion": "US", "originalName": "🇺🇸 美国 008"}, {"type": "trojan", "name": "🇺🇸 美国 009", "server": "xd-js.timiwc.com", "port": 59599, "password": "2b1ed981-6547-4094-998b-06a3323d6f6c", "network": "tcp", "tls": {"enabled": true, "serverName": "k62.tudou211.com", "alpn": [], "fingerprint": "chrome", "skipCertVerify": true}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "US", "originalName": "🇺🇸 美国 009"}, {"type": "trojan", "name": "🇺🇸 美国 010", "server": "xd-js.timiwc.com", "port": 21332, "password": "2b1ed981-6547-4094-998b-06a3323d6f6c", "network": "tcp", "tls": {"enabled": true, "serverName": "xd-js.timiwc.com", "alpn": [], "fingerprint": "chrome", "skipCertVerify": true}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "US", "originalName": "🇺🇸 美国 010"}, {"type": "vless", "name": "🇺🇸 美国 011", "server": "**************", "port": 8880, "uuid": "53fa8faf-ba4b-4322-9c69-a3e5b1555049", "flow": "", "encryption": "none", "network": "ws", "tls": {"enabled": false, "serverName": "", "alpn": [], "fingerprint": ""}, "transport": {"path": "/Telegram🇨🇳 @pgkj666 /?ed=2560", "host": "reedfree8mahsang2.redorg.ir"}, "reality": {"enabled": false, "publicKey": "", "shortId": "", "spiderX": ""}, "_sourceFormat": "base64", "detectedRegion": "US", "originalName": "🇺🇸 美国 011"}, {"type": "vless", "name": "🇺🇸 美国 012", "server": "***********", "port": 8880, "uuid": "53fa8faf-ba4b-4322-9c69-a3e5b1555049", "flow": "", "encryption": "none", "network": "ws", "tls": {"enabled": false, "serverName": "", "alpn": [], "fingerprint": ""}, "transport": {"path": "/Telegram🇨🇳 @pgkj666 /?ed=2560", "host": "reedfree8mahsang2.redorg.ir"}, "reality": {"enabled": false, "publicKey": "", "shortId": "", "spiderX": ""}, "_sourceFormat": "base64", "detectedRegion": "US", "originalName": "🇺🇸 美国 012"}, {"type": "hysteria2", "name": "🇺🇸 美国 013", "server": "************", "port": 444, "password": "sysadmin.sysadmin", "auth": "sysadmin.sysadmin", "obfs": {"type": "", "password": ""}, "tls": {"enabled": true, "serverName": "mg1.wtn.wang", "alpn": ["h3"], "skipCertVerify": true}, "bandwidth": {"up": "", "down": ""}, "congestion": "bbr", "fastOpen": false, "lazy": false, "_sourceFormat": "base64", "detectedRegion": "US", "originalName": "🇺🇸 美国 013"}, {"type": "vless", "name": "🇺🇸 美国 014", "server": "*************", "port": 8880, "uuid": "53fa8faf-ba4b-4322-9c69-a3e5b1555049", "flow": "", "encryption": "none", "network": "ws", "tls": {"enabled": false, "serverName": "", "alpn": [], "fingerprint": ""}, "transport": {"path": "/Telegram🇨🇳 @pgkj666 /?ed=2560", "host": "reedfree8mahsang2.redorg.ir"}, "reality": {"enabled": false, "publicKey": "", "shortId": "", "spiderX": ""}, "_sourceFormat": "base64", "detectedRegion": "US", "originalName": "🇺🇸 美国 014"}, {"type": "vless", "name": "🇺🇸 美国 015", "server": "*************", "port": 8880, "uuid": "53fa8faf-ba4b-4322-9c69-a3e5b1555049", "flow": "", "encryption": "none", "network": "ws", "tls": {"enabled": false, "serverName": "", "alpn": [], "fingerprint": ""}, "transport": {"path": "/Telegram🇨🇳 @pgkj666 /?ed=2560", "host": "reedfree8mahsang2.redorg.ir"}, "reality": {"enabled": false, "publicKey": "", "shortId": "", "spiderX": ""}, "_sourceFormat": "base64", "detectedRegion": "US", "originalName": "🇺🇸 美国 015"}, {"type": "hysteria2", "name": "🇺🇸 美国 016", "server": "**************", "port": 443, "password": "b8bd42a9-551f-419d-b70d-4aefdd2cb074", "auth": "b8bd42a9-551f-419d-b70d-4aefdd2cb074", "obfs": {"type": "", "password": ""}, "tls": {"enabled": true, "serverName": "www.bing.com", "alpn": ["h3"], "skipCertVerify": true}, "bandwidth": {"up": "", "down": ""}, "congestion": "bbr", "fastOpen": false, "lazy": false, "_sourceFormat": "base64", "detectedRegion": "US", "originalName": "🇺🇸 美国 016"}, {"type": "hysteria2", "name": "🇺🇸 美国 017", "server": "**************", "port": 443, "password": "b8bd42a9-551f-419d-b70d-4aefdd2cb074", "auth": "b8bd42a9-551f-419d-b70d-4aefdd2cb074", "obfs": {"type": "", "password": ""}, "tls": {"enabled": true, "serverName": "www.bing.com", "alpn": ["h3"], "skipCertVerify": true}, "bandwidth": {"up": "", "down": ""}, "congestion": "bbr", "fastOpen": false, "lazy": false, "_sourceFormat": "base64", "detectedRegion": "US", "originalName": "🇺🇸 美国 017"}, {"type": "hysteria2", "name": "🇺🇸 美国 018", "server": "*************", "port": 443, "password": "b8bd42a9-551f-419d-b70d-4aefdd2cb074", "auth": "b8bd42a9-551f-419d-b70d-4aefdd2cb074", "obfs": {"type": "", "password": ""}, "tls": {"enabled": true, "serverName": "www.bing.com", "alpn": ["h3"], "skipCertVerify": true}, "bandwidth": {"up": "", "down": ""}, "congestion": "bbr", "fastOpen": false, "lazy": false, "_sourceFormat": "base64", "detectedRegion": "US", "originalName": "🇺🇸 美国 018"}, {"type": "hysteria2", "name": "🇺🇸 美国 019", "server": "**************", "port": 47262, "password": "b72ba5d5-2d5e-45b7-93b5-236d343baa7c", "auth": "b72ba5d5-2d5e-45b7-93b5-236d343baa7c", "obfs": {"type": "", "password": ""}, "tls": {"enabled": true, "serverName": "www.bing.com", "alpn": ["h3"], "skipCertVerify": true}, "bandwidth": {"up": "", "down": ""}, "congestion": "bbr", "fastOpen": false, "lazy": false, "_sourceFormat": "base64", "detectedRegion": "US", "originalName": "🇺🇸 美国 019"}, {"type": "ss", "name": "🇺🇸 美国 020", "server": "03.kill704.win", "port": 44223, "password": "92e0a2cd-f842-42b6-84ef-dd2da5c711ac", "method": "chacha20-ietf-poly1305", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "US", "originalName": "🇺🇸 美国 020"}, {"type": "trojan", "name": "🇺🇸 美国 021", "server": "a6b099d1-swrwg0-swzxpi-44k4.cu.plebai.net", "port": 15229, "password": "c5eb2c2c-bc1d-11ed-a8bf-f23c91cfbbc9", "network": "tcp", "tls": {"enabled": true, "serverName": "a6b099d1-swrwg0-swzxpi-44k4.cu.plebai.net", "alpn": [], "fingerprint": "", "skipCertVerify": true}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "US", "originalName": "🇺🇸 美国 021"}, {"type": "vless", "name": "🇺🇸 美国 022", "server": "************", "port": 8880, "uuid": "53fa8faf-ba4b-4322-9c69-a3e5b1555049", "flow": "", "encryption": "none", "network": "ws", "tls": {"enabled": false, "serverName": "", "alpn": [], "fingerprint": ""}, "transport": {"path": "/Telegram🇨🇳 @pgkj666 /?ed=2560", "host": "reedfree8mahsang2.redorg.ir"}, "reality": {"enabled": false, "publicKey": "", "shortId": "", "spiderX": ""}, "_sourceFormat": "base64", "detectedRegion": "US", "originalName": "🇺🇸 美国 022"}, {"type": "trojan", "name": "🇺🇸 美国 023", "server": "f10011.ylxblkyndjj.sbs", "port": 51345, "password": "74b001ba-7020-443d-b33b-5a403a156b39", "network": "tcp", "tls": {"enabled": true, "serverName": "us03.ckcloud.info", "alpn": [], "fingerprint": "", "skipCertVerify": true}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "US", "originalName": "🇺🇸 美国 023"}, {"type": "trojan", "name": "🇺🇸 美国 024", "server": "f10021.ylxblkyndjj.sbs", "port": 51345, "password": "74b001ba-7020-443d-b33b-5a403a156b39", "network": "tcp", "tls": {"enabled": true, "serverName": "us03.ckcloud.info", "alpn": [], "fingerprint": "", "skipCertVerify": true}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "US", "originalName": "🇺🇸 美国 024"}, {"type": "vless", "name": "🇺🇸 美国 025", "server": "us002.421421.xyz", "port": 20230, "uuid": "5a81db08-7af3-49a4-b089-b7a5b2c24c38", "flow": "", "encryption": "none", "network": "grpc", "tls": {"enabled": true, "serverName": "www.nvidia.com", "alpn": [], "fingerprint": "chrome"}, "transport": {"serviceName": "tj", "mode": "gun"}, "reality": {"enabled": true, "publicKey": "Bq_RQyPwxp7zTYywC37jLxmcjV9npNN6H6_1d_R6GT0", "shortId": "c9916730", "spiderX": ""}, "_sourceFormat": "base64", "detectedRegion": "US", "originalName": "🇺🇸 美国 025"}, {"type": "hysteria2", "name": "🇺🇸 美国 026", "server": "mg.yuanbaojc.site", "port": 30367, "password": "646d2022-4f69-4a6e-b142-d00e475b36e6", "auth": "646d2022-4f69-4a6e-b142-d00e475b36e6", "obfs": {"type": "", "password": ""}, "tls": {"enabled": true, "serverName": "mg.yuanbaojc.site", "alpn": ["h3"], "skipCertVerify": false}, "bandwidth": {"up": "", "down": ""}, "congestion": "bbr", "fastOpen": false, "lazy": false, "_sourceFormat": "base64", "detectedRegion": "US", "originalName": "🇺🇸 美国 026"}, {"type": "vless", "name": "🇺🇸 美国 027", "server": "************", "port": 8880, "uuid": "53fa8faf-ba4b-4322-9c69-a3e5b1555049", "flow": "", "encryption": "none", "network": "ws", "tls": {"enabled": false, "serverName": "", "alpn": [], "fingerprint": ""}, "transport": {"path": "/Telegram🇨🇳 @MxlShare @WangCai2 /?ed=2560", "host": "reedfree8mahsang2.redorg.ir"}, "reality": {"enabled": false, "publicKey": "", "shortId": "", "spiderX": ""}, "_sourceFormat": "base64", "detectedRegion": "US", "originalName": "🇺🇸 美国 027"}, {"type": "vless", "name": "🇺🇸 美国 028", "server": "***********", "port": 8880, "uuid": "53fa8faf-ba4b-4322-9c69-a3e5b1555049", "flow": "", "encryption": "none", "network": "ws", "tls": {"enabled": false, "serverName": "", "alpn": [], "fingerprint": ""}, "transport": {"path": "/Telegram🇨🇳 @MxlShare @WangCai2 /?ed=2560", "host": "reedfree8mahsang2.redorg.ir"}, "reality": {"enabled": false, "publicKey": "", "shortId": "", "spiderX": ""}, "_sourceFormat": "base64", "detectedRegion": "US", "originalName": "🇺🇸 美国 028"}, {"type": "hysteria2", "name": "🇺🇸 美国 029", "server": "5f374286-svtc00-tdex4g-6qyv.la.shifen.uk", "port": 1443, "password": "a346c669-6374-11ef-bc6a-f23c9313b177", "auth": "a346c669-6374-11ef-bc6a-f23c9313b177", "obfs": {"type": "", "password": ""}, "tls": {"enabled": true, "serverName": "5f374286-svtc00-tdex4g-6qyv.la.shifen.uk", "alpn": ["h3"], "skipCertVerify": true}, "bandwidth": {"up": "", "down": ""}, "congestion": "bbr", "fastOpen": false, "lazy": false, "_sourceFormat": "base64", "detectedRegion": "US", "originalName": "🇺🇸 美国 029"}, {"type": "hysteria2", "name": "🇺🇸 美国 030", "server": "sj-arm.nfsn666.gq", "port": 8888, "password": "nfsn666", "auth": "nfsn666", "obfs": {"type": "", "password": ""}, "tls": {"enabled": true, "serverName": "sj-arm.nfsn666.gq", "alpn": ["h3"], "skipCertVerify": true}, "bandwidth": {"up": "", "down": ""}, "congestion": "bbr", "fastOpen": false, "lazy": false, "_sourceFormat": "base64", "detectedRegion": "US", "originalName": "🇺🇸 美国 030"}, {"type": "hysteria2", "name": "🇺🇸 美国 031", "server": "************", "port": 8888, "password": "nfsn666", "auth": "nfsn666", "obfs": {"type": "", "password": ""}, "tls": {"enabled": true, "serverName": "sj-arm.nfsn666.gq", "alpn": ["h3"], "skipCertVerify": true}, "bandwidth": {"up": "", "down": ""}, "congestion": "bbr", "fastOpen": false, "lazy": false, "_sourceFormat": "base64", "detectedRegion": "US", "originalName": "🇺🇸 美国 031"}, {"type": "vless", "name": "🇺🇸 美国 032", "server": "sj-arm.nfsn666.gq", "port": 8443, "uuid": "06121b89-607b-44c9-9c01-cc2fc6a7321d", "flow": "xtls-rprx-vision", "encryption": "none", "network": "tcp", "tls": {"enabled": true, "serverName": "www.yahoo.com", "alpn": [], "fingerprint": "chrome"}, "transport": {}, "reality": {"enabled": true, "publicKey": "4Qekb9y1dqO8hvRzVSGeSRNyhko_gqpeWD94zrLCvjs", "shortId": "5488b0e7", "spiderX": ""}, "_sourceFormat": "base64", "detectedRegion": "US", "originalName": "🇺🇸 美国 032"}, {"type": "vless", "name": "🇺🇸 美国 033", "server": "************", "port": 443, "uuid": "e3820a55-ff58-4e42-ac82-df9a9376e699", "flow": "", "encryption": "none", "network": "ws", "tls": {"enabled": true, "serverName": "madcity2.777999.lol", "alpn": ["h3", "h2", "http/1.1"], "fingerprint": "chrome"}, "transport": {"path": "/?ed=2560", "host": "madcity2.777999.lol"}, "reality": {"enabled": false, "publicKey": "", "shortId": "", "spiderX": ""}, "_sourceFormat": "base64", "detectedRegion": "US", "originalName": "🇺🇸 美国 033"}, {"type": "ss", "name": "🇺🇸 美国 034", "server": "*************", "port": 20010, "password": "c86d483c-431f-41df-bb6b-c1dcebfc7401", "method": "aes-256-gcm", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "US", "originalName": "🇺🇸 美国 034"}, {"type": "trojan", "name": "🇺🇸 美国 035", "server": "a28054aa-swtr40-sy9vy9-1rfon.cu.plebai.net", "port": 15229, "password": "aed1cc24-351d-11ef-ba52-f23c9164ca5d", "network": "tcp", "tls": {"enabled": true, "serverName": "a28054aa-swtr40-sy9vy9-1rfon.cu.plebai.net", "alpn": [], "fingerprint": "", "skipCertVerify": false}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "US", "originalName": "🇺🇸 美国 035"}, {"type": "trojan", "name": "🇺🇸 美国 036", "server": "863f62ce-swtr40-szcku7-153sp.cu.plebai.net", "port": 15229, "password": "31ede2d8-3f22-11ef-b023-f23c9164ca5d", "network": "tcp", "tls": {"enabled": true, "serverName": "863f62ce-swtr40-szcku7-153sp.cu.plebai.net", "alpn": [], "fingerprint": "", "skipCertVerify": true}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "US", "originalName": "🇺🇸 美国 036"}, {"type": "trojan", "name": "🇺🇸 美国 037", "server": "e068bf84-swxgg0-sww7b0-1qwp5.cu.plebai.net", "port": 15229, "password": "6e9cdc20-b92a-11ef-973a-f23c913c8d2b", "network": "tcp", "tls": {"enabled": true, "serverName": "e068bf84-swxgg0-sww7b0-1qwp5.cu.plebai.net", "alpn": [], "fingerprint": "", "skipCertVerify": false}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "US", "originalName": "🇺🇸 美国 037"}, {"type": "trojan", "name": "🇺🇸 美国 038", "server": "eddfd42e-swtr40-sy3zfb-ezjz.cu.plebai.net", "port": 15229, "password": "591ac730-bd6f-11ed-a8bf-f23c91cfbbc9", "network": "tcp", "tls": {"enabled": true, "serverName": "eddfd42e-swtr40-sy3zfb-ezjz.cu.plebai.net", "alpn": [], "fingerprint": "", "skipCertVerify": false}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "US", "originalName": "🇺🇸 美国 038"}, {"type": "trojan", "name": "🇺🇸 美国 039", "server": "f22a91be-swvls0-thdi1k-19yro.cu.plebai.net", "port": 15229, "password": "3d7182ce-8dcc-11ef-a3f6-f23c9164ca5d", "network": "tcp", "tls": {"enabled": true, "serverName": "f22a91be-swvls0-thdi1k-19yro.cu.plebai.net", "alpn": [], "fingerprint": "", "skipCertVerify": false}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "US", "originalName": "🇺🇸 美国 039"}, {"type": "ss", "name": "🇺🇸 美国 040", "server": "***************", "port": 23340, "password": "0fbed6c9-0fb4-4ec3-8582-f569f8d773d5", "method": "chacha20-ietf-poly1305", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "US", "originalName": "🇺🇸 美国 040"}, {"type": "trojan", "name": "🇺🇸 美国 041", "server": "684f1982-swvls0-sx9mz9-m0b9.cu.plebai.net", "port": 15229, "password": "bb85e074-b0c2-11ea-ad28-f23c913c8d2b", "network": "tcp", "tls": {"enabled": true, "serverName": "684f1982-swvls0-sx9mz9-m0b9.cu.plebai.net", "alpn": [], "fingerprint": "", "skipCertVerify": false}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "US", "originalName": "🇺🇸 美国 041"}, {"type": "vless", "name": "🇺🇸 美国 042", "server": "************", "port": 8880, "uuid": "53fa8faf-ba4b-4322-9c69-a3e5b1555049", "flow": "", "encryption": "none", "network": "ws", "tls": {"enabled": false, "serverName": "", "alpn": [], "fingerprint": ""}, "transport": {"path": "/?ed=2560", "host": "reedfree8mahsang2.redorg.ir"}, "reality": {"enabled": false, "publicKey": "", "shortId": "", "spiderX": ""}, "_sourceFormat": "base64", "detectedRegion": "US", "originalName": "🇺🇸 美国 042"}, {"type": "vless", "name": "🇺🇸 美国 043", "server": "************", "port": 8880, "uuid": "53fa8faf-ba4b-4322-9c69-a3e5b1555049", "flow": "", "encryption": "none", "network": "ws", "tls": {"enabled": false, "serverName": "", "alpn": [], "fingerprint": ""}, "transport": {"path": "/Telegram🇨🇳 @MxlShare @WangCai2 /?ed=2560", "host": "reedfree8mahsang2.redorg.ir"}, "reality": {"enabled": false, "publicKey": "", "shortId": "", "spiderX": ""}, "_sourceFormat": "base64", "detectedRegion": "US", "originalName": "🇺🇸 美国 043"}, {"type": "trojan", "name": "🇺🇸 美国 044", "server": "3aadf07e-swvls0-ta5nd4-e06r.cu.plebai.net", "port": 15229, "password": "861726bb-c61a-b0e0-99fa-5ddfdcf21902", "network": "tcp", "tls": {"enabled": true, "serverName": "3aadf07e-swvls0-ta5nd4-e06r.cu.plebai.net", "alpn": [], "fingerprint": "", "skipCertVerify": false}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "US", "originalName": "🇺🇸 美国 044"}, {"type": "trojan", "name": "🇺🇸 美国 045", "server": "19727a29-swtr40-tcinla-hrtf.cu.plebai.net", "port": 15229, "password": "0da8651e-e1f6-11ec-bd7c-f23c913c8d2b", "network": "tcp", "tls": {"enabled": true, "serverName": "19727a29-swtr40-tcinla-hrtf.cu.plebai.net", "alpn": [], "fingerprint": "", "skipCertVerify": false}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "US", "originalName": "🇺🇸 美国 045"}, {"type": "vless", "name": "🇺🇸 美国 046", "server": "************", "port": 8880, "uuid": "53fa8faf-ba4b-4322-9c69-a3e5b1555049", "flow": "", "encryption": "none", "network": "ws", "tls": {"enabled": false, "serverName": "", "alpn": [], "fingerprint": ""}, "transport": {"path": "/Telegram🇨🇳 @MxlShare @WangCai2 /?ed=2560", "host": "reedfree8mahsang2.redorg.ir"}, "reality": {"enabled": false, "publicKey": "", "shortId": "", "spiderX": ""}, "_sourceFormat": "base64", "detectedRegion": "US", "originalName": "🇺🇸 美国 046"}, {"type": "vless", "name": "🇺🇸 美国 047", "server": "**************", "port": 8880, "uuid": "53fa8faf-ba4b-4322-9c69-a3e5b1555049", "flow": "", "encryption": "none", "network": "ws", "tls": {"enabled": false, "serverName": "", "alpn": [], "fingerprint": ""}, "transport": {"path": "/Telegram🇨🇳 @pgkj666 /?ed=2560", "host": "reedfree8mahsang2.redorg.ir"}, "reality": {"enabled": false, "publicKey": "", "shortId": "", "spiderX": ""}, "_sourceFormat": "base64", "detectedRegion": "US", "originalName": "🇺🇸 美国 047"}, {"type": "hysteria2", "name": "🇺🇸 美国 048", "server": "mg.yuanbaojc.site", "port": 30396, "password": "5cc61971-19b5-454a-a933-abb628ce7b80", "auth": "5cc61971-19b5-454a-a933-abb628ce7b80", "obfs": {"type": "", "password": ""}, "tls": {"enabled": true, "serverName": "mg.yuanbaojc.site", "alpn": ["h3"], "skipCertVerify": false}, "bandwidth": {"up": "", "down": ""}, "congestion": "bbr", "fastOpen": false, "lazy": false, "_sourceFormat": "base64", "detectedRegion": "US", "originalName": "🇺🇸 美国 048"}, {"type": "trojan", "name": "🇺🇸 美国 049", "server": "77a2be97-swvls0-t3o6u7-1osdm.cu.plebai.net", "port": 15229, "password": "91365a7a-46d7-11ee-a8b9-f23c9164ca5d", "network": "tcp", "tls": {"enabled": true, "serverName": "77a2be97-swvls0-t3o6u7-1osdm.cu.plebai.net", "alpn": [], "fingerprint": "", "skipCertVerify": false}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "US", "originalName": "🇺🇸 美国 049"}, {"type": "hysteria2", "name": "🇺🇸 美国 050", "server": "d465b594-swvls0-sx0fe4-1j6h0.hy2.gotochinatown.net", "port": 8443, "password": "dcccacba-fa44-11ef-8400-f23c9164ca5d", "auth": "dcccacba-fa44-11ef-8400-f23c9164ca5d", "obfs": {"type": "", "password": ""}, "tls": {"enabled": true, "serverName": "d465b594-swvls0-sx0fe4-1j6h0.hy2.gotochinatown.net", "alpn": ["h3"], "skipCertVerify": false}, "bandwidth": {"up": "", "down": ""}, "congestion": "bbr", "fastOpen": false, "lazy": false, "_sourceFormat": "base64", "detectedRegion": "US", "originalName": "🇺🇸 美国 050"}, {"type": "trojan", "name": "🇺🇸 美国 051", "server": "036c81df-swtr40-sx3h07-1g8k0.cu.plebai.net", "port": 15229, "password": "d4018e28-e328-11ed-98a7-f23c913c8d2b", "network": "tcp", "tls": {"enabled": true, "serverName": "036c81df-swtr40-sx3h07-1g8k0.cu.plebai.net", "alpn": [], "fingerprint": "", "skipCertVerify": false}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "US", "originalName": "🇺🇸 美国 051"}, {"type": "trojan", "name": "🇺🇸 美国 052", "server": "ccab8269-swxgg0-szdere-155d9.cu.plebai.net", "port": 15229, "password": "34f0cf42-0f8a-11ec-a8bf-f23c91cfbbc9", "network": "tcp", "tls": {"enabled": true, "serverName": "ccab8269-swxgg0-szdere-155d9.cu.plebai.net", "alpn": [], "fingerprint": "", "skipCertVerify": false}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "US", "originalName": "🇺🇸 美国 052"}, {"type": "trojan", "name": "🇺🇸 美国 053", "server": "0e1a59af-swxgg0-t8kd6j-1c9em.cu.plebai.net", "port": 15229, "password": "336501b6-51d2-11ee-a993-f23c9164ca5d", "network": "tcp", "tls": {"enabled": true, "serverName": "0e1a59af-swxgg0-t8kd6j-1c9em.cu.plebai.net", "alpn": [], "fingerprint": "", "skipCertVerify": false}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "US", "originalName": "🇺🇸 美国 053"}, {"type": "trojan", "name": "🇺🇸 美国 054", "server": "3238ec22-swxgg0-tfnoge-1luqs.cu.plebai.net", "port": 15229, "password": "788328ee-d49f-11ef-bd97-f23c9164ca5d", "network": "tcp", "tls": {"enabled": true, "serverName": "3238ec22-swxgg0-tfnoge-1luqs.cu.plebai.net", "alpn": [], "fingerprint": "", "skipCertVerify": false}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "US", "originalName": "🇺🇸 美国 054"}, {"type": "hysteria2", "name": "🇺🇸 美国 055", "server": "qydg.qy1357.top", "port": 33003, "password": "869a0163-456f-4c06-bd4a-2376e4563eae", "auth": "869a0163-456f-4c06-bd4a-2376e4563eae", "obfs": {"type": "", "password": ""}, "tls": {"enabled": true, "serverName": "qydg.qy1357.top", "alpn": ["h3"], "skipCertVerify": true}, "bandwidth": {"up": "", "down": ""}, "congestion": "bbr", "fastOpen": false, "lazy": false, "_sourceFormat": "base64", "detectedRegion": "US", "originalName": "🇺🇸 美国 055"}, {"type": "hysteria2", "name": "🇺🇸 美国 056", "server": "4f88735f-swxgg0-t8kd6j-1c9em.hy2.gotochinatown.net", "port": 8443, "password": "336501b6-51d2-11ee-a993-f23c9164ca5d", "auth": "336501b6-51d2-11ee-a993-f23c9164ca5d", "obfs": {"type": "", "password": ""}, "tls": {"enabled": true, "serverName": "4f88735f-swxgg0-t8kd6j-1c9em.hy2.gotochinatown.net", "alpn": ["h3"], "skipCertVerify": false}, "bandwidth": {"up": "", "down": ""}, "congestion": "bbr", "fastOpen": false, "lazy": false, "_sourceFormat": "base64", "detectedRegion": "US", "originalName": "🇺🇸 美国 056"}, {"type": "ss", "name": "🇺🇸 美国 057", "server": "eepl1.dhh114514.christmas", "port": 42022, "password": "ed6c8eea-8503-43e1-9563-0a355b0edcac", "method": "chacha20-ietf-poly1305", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "US", "originalName": "🇺🇸 美国 057"}, {"type": "trojan", "name": "🇺🇸 美国 058", "server": "*************", "port": 3330, "password": "RlzoEILU", "network": "tcp", "tls": {"enabled": true, "serverName": "cdn.egvra.cn", "alpn": [], "fingerprint": "chrome", "skipCertVerify": true}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "US", "originalName": "🇺🇸 美国 058"}, {"type": "vmess", "name": "🇺🇸 美国 059", "server": "tiamo1.tiamocloud.us.kg", "port": 80, "uuid": "f0ebc016-1dba-480e-9b53-8a4bc541ab71", "alterId": 0, "cipher": "auto", "network": "ws", "tls": {"enabled": false, "serverName": "tiamo1.tiamocloud.us.kg"}, "transport": {"path": "/", "headers": {"Host": "tiamo1.tiamocloud.us.kg"}}, "_sourceFormat": "base64", "detectedRegion": "US", "originalName": "ðºð¸ ç¾å½ 059"}, {"type": "vless", "name": "🇺🇸 美国 060", "server": "*************", "port": 8880, "uuid": "53fa8faf-ba4b-4322-9c69-a3e5b1555049", "flow": "", "encryption": "none", "network": "ws", "tls": {"enabled": false, "serverName": "", "alpn": [], "fingerprint": ""}, "transport": {"path": "/?ed=2560", "host": "reedfree8mahsang2.redorg.ir"}, "reality": {"enabled": false, "publicKey": "", "shortId": "", "spiderX": ""}, "_sourceFormat": "base64", "detectedRegion": "US", "originalName": "🇺🇸 美国 060"}, {"type": "trojan", "name": "🇺🇸 美国 061", "server": "************", "port": 58250, "password": "QIdQdCbReF", "network": "tcp", "tls": {"enabled": true, "serverName": "************", "alpn": [], "fingerprint": "chrome", "skipCertVerify": true}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "US", "originalName": "🇺🇸 美国 061"}, {"type": "trojan", "name": "🇺🇸 美国 062", "server": "32d48363ca5fffa0f9c8fb50d981e589.us.in.node-is.green", "port": 43744, "password": "c983a532-d8ee-4074-991c-c4a721178fdc", "network": "tcp", "tls": {"enabled": true, "serverName": "local.bilibili.com", "alpn": [], "fingerprint": "chrome", "skipCertVerify": true}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "US", "originalName": "🇺🇸 美国 062"}, {"type": "hysteria2", "name": "🇺🇸 美国 063", "server": "9b614a41-swb8g0-sxs14h-dnss.la.shifen.uk", "port": 1443, "password": "16955d72-1794-11f0-a035-f23c95b6f51d", "auth": "16955d72-1794-11f0-a035-f23c95b6f51d", "obfs": {"type": "", "password": ""}, "tls": {"enabled": true, "serverName": "9b614a41-swb8g0-sxs14h-dnss.la.shifen.uk", "alpn": ["h3"], "skipCertVerify": false}, "bandwidth": {"up": "", "down": ""}, "congestion": "bbr", "fastOpen": false, "lazy": false, "_sourceFormat": "base64", "detectedRegion": "US", "originalName": "🇺🇸 美国 063"}, {"type": "ss", "name": "🇺🇸 美国 064", "server": "eepl1.dhh114514.christmas", "port": 42023, "password": "ed6c8eea-8503-43e1-9563-0a355b0edcac", "method": "chacha20-ietf-poly1305", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "US", "originalName": "🇺🇸 美国 064"}, {"type": "trojan", "name": "🇺🇸 美国 065", "server": "cn2.cdn.xfltd-cdn.top", "port": 12053, "password": "7669a04d-a459-4c96-bc0e-fde51cb984f3", "network": "tcp", "tls": {"enabled": true, "serverName": "cn2.cdn.xfltd-cdn.top", "alpn": [], "fingerprint": "", "skipCertVerify": true}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "US", "originalName": "🇺🇸 美国 065"}, {"type": "vless", "name": "🇺🇸 美国 066", "server": "***********", "port": 8880, "uuid": "53fa8faf-ba4b-4322-9c69-a3e5b1555049", "flow": "", "encryption": "none", "network": "ws", "tls": {"enabled": false, "serverName": "", "alpn": [], "fingerprint": ""}, "transport": {"path": "/?ed=2560", "host": "reedfree8mahsang2.redorg.ir"}, "reality": {"enabled": false, "publicKey": "", "shortId": "", "spiderX": ""}, "_sourceFormat": "base64", "detectedRegion": "US", "originalName": "🇺🇸 美国 066"}, {"type": "vless", "name": "🇺🇸 美国 067", "server": "*************", "port": 10001, "uuid": "748292a0-ac0f-4d55-a460-40bea786f17f", "flow": "", "encryption": "none", "network": "tcp", "tls": {"enabled": true, "serverName": "yahoo.com", "alpn": [], "fingerprint": "firefox"}, "transport": {}, "reality": {"enabled": true, "publicKey": "A8YlN1VE64tBOBhnaHy5lwBXRU79kELWmtNqlRsjvkE", "shortId": "1a24af87", "spiderX": ""}, "_sourceFormat": "base64", "detectedRegion": "US", "originalName": "🇺🇸 美国 067"}, {"type": "hysteria2", "name": "🇺🇸 美国 068", "server": "a4e3ef78-swin40-sxlsd4-3z3v.hy2.gotochinatown.net", "port": 8443, "password": "48c1e014-28d6-11ec-a0fc-f23c913c8d2b", "auth": "48c1e014-28d6-11ec-a0fc-f23c913c8d2b", "obfs": {"type": "", "password": ""}, "tls": {"enabled": true, "serverName": "a4e3ef78-swin40-sxlsd4-3z3v.hy2.gotochinatown.net", "alpn": ["h3"], "skipCertVerify": false}, "bandwidth": {"up": "", "down": ""}, "congestion": "bbr", "fastOpen": false, "lazy": false, "_sourceFormat": "base64", "detectedRegion": "US", "originalName": "🇺🇸 美国 068"}, {"type": "hysteria2", "name": "🇺🇸 美国 069", "server": "e06eb389-swq1s0-tcinla-hrtf.hy2.gotochinatown.net", "port": 8443, "password": "0da8651e-e1f6-11ec-bd7c-f23c913c8d2b", "auth": "0da8651e-e1f6-11ec-bd7c-f23c913c8d2b", "obfs": {"type": "", "password": ""}, "tls": {"enabled": true, "serverName": "e06eb389-swq1s0-tcinla-hrtf.hy2.gotochinatown.net", "alpn": ["h3"], "skipCertVerify": false}, "bandwidth": {"up": "", "down": ""}, "congestion": "bbr", "fastOpen": false, "lazy": false, "_sourceFormat": "base64", "detectedRegion": "US", "originalName": "🇺🇸 美国 069"}, {"type": "trojan", "name": "🇺🇸 美国 070", "server": "699bbddc-swq1s0-sxzls8-1gxvd.cu.plebai.net", "port": 15229, "password": "398e0d38-8649-11ef-959c-f23c9164ca5d", "network": "tcp", "tls": {"enabled": true, "serverName": "699bbddc-swq1s0-sxzls8-1gxvd.cu.plebai.net", "alpn": [], "fingerprint": "chrome", "skipCertVerify": false}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "US", "originalName": "🇺🇸 美国 070"}, {"type": "ss", "name": "🇺🇸 美国 071", "server": "************", "port": 20007, "password": "c86d483c-431f-41df-bb6b-c1dcebfc7401", "method": "aes-256-gcm", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "US", "originalName": "🇺🇸 美国 071"}, {"type": "ss", "name": "🇺🇸 美国 072", "server": "*************", "port": 20005, "password": "200c650a-4d7c-4180-b0ce-20093784902e", "method": "aes-256-gcm", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "US", "originalName": "🇺🇸 美国 072"}, {"type": "trojan", "name": "🇺🇸 美国 073", "server": "**************", "port": 21118, "password": "2b1ed981-6547-4094-998b-06a3323d6f6c", "network": "tcp", "tls": {"enabled": true, "serverName": "k17.tudou211.com", "alpn": [], "fingerprint": "chrome", "skipCertVerify": true}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "US", "originalName": "🇺🇸 美国 073"}, {"type": "trojan", "name": "🇺🇸 美国 074", "server": "cn2.cdn.xfltd-cdn.top", "port": 12052, "password": "7669a04d-a459-4c96-bc0e-fde51cb984f3", "network": "tcp", "tls": {"enabled": true, "serverName": "cdn.alibaba.com", "alpn": [], "fingerprint": "chrome", "skipCertVerify": true}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "US", "originalName": "🇺🇸 美国 074"}, {"type": "trojan", "name": "🇺🇸 美国 075", "server": "cn2.cdn.xfltd-cdn.top", "port": 12054, "password": "7669a04d-a459-4c96-bc0e-fde51cb984f3", "network": "tcp", "tls": {"enabled": true, "serverName": "cdn.alibaba.com", "alpn": [], "fingerprint": "chrome", "skipCertVerify": true}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "US", "originalName": "🇺🇸 美国 075"}, {"type": "hysteria2", "name": "🇺🇸 美国 076", "server": "a132e977-sw5og0-t12cnj-1ol97.hy2.gotochinatown.net", "port": 8443, "password": "93fb69fc-77cf-11ee-85ee-f23c91369f2d", "auth": "93fb69fc-77cf-11ee-85ee-f23c91369f2d", "obfs": {"type": "", "password": ""}, "tls": {"enabled": true, "serverName": "a132e977-sw5og0-t12cnj-1ol97.hy2.gotochinatown.net", "alpn": ["h3"], "skipCertVerify": true}, "bandwidth": {"up": "", "down": ""}, "congestion": "bbr", "fastOpen": false, "lazy": false, "_sourceFormat": "base64", "detectedRegion": "US", "originalName": "🇺🇸 美国 076"}, {"type": "hysteria2", "name": "🇺🇸 美国 077", "server": "341d6ff9-swb8g0-t67sv3-1snfs.hy2.gotochinatown.net", "port": 8443, "password": "b79d79ae-8bce-11ef-a2b8-f23c9164ca5d", "auth": "b79d79ae-8bce-11ef-a2b8-f23c9164ca5d", "obfs": {"type": "", "password": ""}, "tls": {"enabled": true, "serverName": "341d6ff9-swb8g0-t67sv3-1snfs.hy2.gotochinatown.net", "alpn": ["h3"], "skipCertVerify": false}, "bandwidth": {"up": "", "down": ""}, "congestion": "bbr", "fastOpen": false, "lazy": false, "_sourceFormat": "base64", "detectedRegion": "US", "originalName": "🇺🇸 美国 077"}, {"type": "hysteria2", "name": "🇺🇸 美国 078", "server": "0e53d104-svwf40-t6ouc9-13xtu.hy2.gotochinatown.net", "port": 8443, "password": "4a7f880c-72f4-11ed-b0b5-f23c9164ca5d", "auth": "4a7f880c-72f4-11ed-b0b5-f23c9164ca5d", "obfs": {"type": "", "password": ""}, "tls": {"enabled": true, "serverName": "0e53d104-svwf40-t6ouc9-13xtu.hy2.gotochinatown.net", "alpn": ["h3"], "skipCertVerify": false}, "bandwidth": {"up": "", "down": ""}, "congestion": "bbr", "fastOpen": false, "lazy": false, "_sourceFormat": "base64", "detectedRegion": "US", "originalName": "🇺🇸 美国 078"}, {"type": "ss", "name": "🇺🇸 美国 079", "server": "guang.ccwink.cc", "port": 2012, "password": "M2JmZjZkMWQyOGE0Yjg2NQ%3D%3D%3AYjc1MWU5OTUtYjI0Yy00Zg%3D%3D", "method": "2022-blake3-aes-128-gcm", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "US", "originalName": "🇺🇸 美国 079"}, {"type": "hysteria2", "name": "🇺🇸 美国 080", "server": "50528787-swb8g0-sxscwg-63bp.la.shifen.uk", "port": 1443, "password": "92b2a3d4-f353-11ef-b714-f23c93136cb3", "auth": "92b2a3d4-f353-11ef-b714-f23c93136cb3", "obfs": {"type": "", "password": ""}, "tls": {"enabled": true, "serverName": "50528787-swb8g0-sxscwg-63bp.la.shifen.uk", "alpn": ["h3"], "skipCertVerify": false}, "bandwidth": {"up": "", "down": ""}, "congestion": "bbr", "fastOpen": false, "lazy": false, "_sourceFormat": "base64", "detectedRegion": "US", "originalName": "🇺🇸 美国 080"}, {"type": "hysteria2", "name": "🇺🇸 美国 081", "server": "2ef15a2a-svi800-sw77c8-dnss.la.shifen.uk", "port": 1443, "password": "16955d72-1794-11f0-a035-f23c95b6f51d", "auth": "16955d72-1794-11f0-a035-f23c95b6f51d", "obfs": {"type": "", "password": ""}, "tls": {"enabled": true, "serverName": "2ef15a2a-svi800-sw77c8-dnss.la.shifen.uk", "alpn": ["h3"], "skipCertVerify": false}, "bandwidth": {"up": "", "down": ""}, "congestion": "bbr", "fastOpen": false, "lazy": false, "_sourceFormat": "base64", "detectedRegion": "US", "originalName": "🇺🇸 美国 081"}, {"type": "ss", "name": "🇺🇸 美国 082", "server": "**************", "port": 601, "password": "52d2c388-5458-470d-b253-f0e0f5833283", "method": "aes-256-gcm", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "US", "originalName": "🇺🇸 美国 082"}, {"type": "trojan", "name": "🇺🇸 美国 083", "server": "cn2.cdn.xfltd-cdn.top", "port": 12051, "password": "7669a04d-a459-4c96-bc0e-fde51cb984f3", "network": "tcp", "tls": {"enabled": true, "serverName": "cdn.alibaba.com", "alpn": [], "fingerprint": "chrome", "skipCertVerify": true}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "US", "originalName": "🇺🇸 美国 083"}, {"type": "vless", "name": "🇺🇸 美国 084", "server": "**************", "port": 8880, "uuid": "53fa8faf-ba4b-4322-9c69-a3e5b1555049", "flow": "", "encryption": "none", "network": "ws", "tls": {"enabled": false, "serverName": "", "alpn": [], "fingerprint": ""}, "transport": {"path": "/Telegram🇨🇳 @pgkj666 /?ed=2560", "host": "reedfree8mahsang2.redorg.ir"}, "reality": {"enabled": false, "publicKey": "", "shortId": "", "spiderX": ""}, "_sourceFormat": "base64", "detectedRegion": "US", "originalName": "🇺🇸 美国 084"}, {"type": "hysteria2", "name": "🇺🇸 美国 085", "server": "**************", "port": 443, "password": "b8bd42a9-551f-419d-b70d-4aefdd2cb074", "auth": "b8bd42a9-551f-419d-b70d-4aefdd2cb074", "obfs": {"type": "", "password": ""}, "tls": {"enabled": true, "serverName": "www.bing.com", "alpn": ["h3"], "skipCertVerify": true}, "bandwidth": {"up": "", "down": ""}, "congestion": "bbr", "fastOpen": false, "lazy": false, "_sourceFormat": "base64", "detectedRegion": "US", "originalName": "🇺🇸 美国 085"}, {"type": "ss", "name": "🇺🇸 美国 086", "server": "************", "port": 50631, "password": "P1lrnsJwO4", "method": "aes-256-gcm", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "US", "originalName": "🇺🇸 美国 086"}, {"type": "hysteria2", "name": "🇺🇸 美国 087", "server": "e8798f42-sw9ds0-t0kcfe-5ywi.la.shifen.uk", "port": 1443, "password": "b6b95866-4bf9-11ee-95b5-f23c93136cb3", "auth": "b6b95866-4bf9-11ee-95b5-f23c93136cb3", "obfs": {"type": "", "password": ""}, "tls": {"enabled": true, "serverName": "e8798f42-sw9ds0-t0kcfe-5ywi.la.shifen.uk", "alpn": ["h3"], "skipCertVerify": false}, "bandwidth": {"up": "", "down": ""}, "congestion": "bbr", "fastOpen": false, "lazy": false, "_sourceFormat": "base64", "detectedRegion": "US", "originalName": "🇺🇸 美国 087"}, {"type": "hysteria2", "name": "🇺🇸 美国 088", "server": "7da41e11-sw9ds0-szcxa1-dmd8.la.shifen.uk", "port": 1443, "password": "2058bdd6-1505-11f0-a035-f23c95b6f51d", "auth": "2058bdd6-1505-11f0-a035-f23c95b6f51d", "obfs": {"type": "", "password": ""}, "tls": {"enabled": true, "serverName": "7da41e11-sw9ds0-szcxa1-dmd8.la.shifen.uk", "alpn": ["h3"], "skipCertVerify": false}, "bandwidth": {"up": "", "down": ""}, "congestion": "bbr", "fastOpen": false, "lazy": false, "_sourceFormat": "base64", "detectedRegion": "US", "originalName": "🇺🇸 美国 088"}, {"type": "hysteria2", "name": "🇺🇸 美国 089", "server": "a2fff907-sw9ds0-sxkcyq-b4pt.la.shifen.uk", "port": 1443, "password": "be8cc8f6-0c6a-11f0-a5a3-f23c93141fad", "auth": "be8cc8f6-0c6a-11f0-a5a3-f23c93141fad", "obfs": {"type": "", "password": ""}, "tls": {"enabled": true, "serverName": "a2fff907-sw9ds0-sxkcyq-b4pt.la.shifen.uk", "alpn": ["h3"], "skipCertVerify": false}, "bandwidth": {"up": "", "down": ""}, "congestion": "bbr", "fastOpen": false, "lazy": false, "_sourceFormat": "base64", "detectedRegion": "US", "originalName": "🇺🇸 美国 089"}, {"type": "hysteria2", "name": "🇺🇸 美国 090", "server": "34a15372-svlxc0-sw77c8-dnss.la.shifen.uk", "port": 1443, "password": "16955d72-1794-11f0-a035-f23c95b6f51d", "auth": "16955d72-1794-11f0-a035-f23c95b6f51d", "obfs": {"type": "", "password": ""}, "tls": {"enabled": true, "serverName": "34a15372-svlxc0-sw77c8-dnss.la.shifen.uk", "alpn": ["h3"], "skipCertVerify": true}, "bandwidth": {"up": "", "down": ""}, "congestion": "bbr", "fastOpen": false, "lazy": false, "_sourceFormat": "base64", "detectedRegion": "US", "originalName": "🇺🇸 美国 090"}, {"type": "hysteria2", "name": "🇺🇸 美国 091", "server": "2e54cb70-swd340-sxs14h-dnss.la.shifen.uk", "port": 1443, "password": "16955d72-1794-11f0-a035-f23c95b6f51d", "auth": "16955d72-1794-11f0-a035-f23c95b6f51d", "obfs": {"type": "", "password": ""}, "tls": {"enabled": true, "serverName": "2e54cb70-swd340-sxs14h-dnss.la.shifen.uk", "alpn": ["h3"], "skipCertVerify": false}, "bandwidth": {"up": "", "down": ""}, "congestion": "bbr", "fastOpen": false, "lazy": false, "_sourceFormat": "base64", "detectedRegion": "US", "originalName": "🇺🇸 美国 091"}, {"type": "hysteria2", "name": "🇺🇸 美国 092", "server": "b9cd5106-sw9ds0-sxihu3-an44.la.shifen.uk", "port": 1443, "password": "bafc1f54-7a2d-11ef-9f82-f23c93136cb3", "auth": "bafc1f54-7a2d-11ef-9f82-f23c93136cb3", "obfs": {"type": "", "password": ""}, "tls": {"enabled": true, "serverName": "b9cd5106-sw9ds0-sxihu3-an44.la.shifen.uk", "alpn": ["h3"], "skipCertVerify": false}, "bandwidth": {"up": "", "down": ""}, "congestion": "bbr", "fastOpen": false, "lazy": false, "_sourceFormat": "base64", "detectedRegion": "US", "originalName": "🇺🇸 美国 092"}, {"type": "hysteria2", "name": "🇺🇸 美国 093", "server": "9e4d0b01-swd340-sxscwg-63bp.la.shifen.uk", "port": 1443, "password": "92b2a3d4-f353-11ef-b714-f23c93136cb3", "auth": "92b2a3d4-f353-11ef-b714-f23c93136cb3", "obfs": {"type": "", "password": ""}, "tls": {"enabled": true, "serverName": "9e4d0b01-swd340-sxscwg-63bp.la.shifen.uk", "alpn": ["h3"], "skipCertVerify": false}, "bandwidth": {"up": "", "down": ""}, "congestion": "bbr", "fastOpen": false, "lazy": false, "_sourceFormat": "base64", "detectedRegion": "US", "originalName": "🇺🇸 美国 093"}, {"type": "vless", "name": "🇺🇸 美国 094", "server": "*************", "port": 443, "uuid": "ad32ab40-a469-4ce6-a1da-63c6cae6c546", "flow": "", "encryption": "none", "network": "ws", "tls": {"enabled": true, "serverName": "qifei.876566.xyz", "alpn": [], "fingerprint": "random"}, "transport": {"path": "/?ed=2560", "host": "qifei.876566.xyz"}, "reality": {"enabled": false, "publicKey": "", "shortId": "", "spiderX": ""}, "_sourceFormat": "base64", "detectedRegion": "US", "originalName": "🇺🇸 美国 094"}, {"type": "hysteria2", "name": "🇺🇸 美国 095", "server": "1fec14d5-swrwg0-sww7b0-1qwp5.hy2.gotochinatown.net", "port": 8443, "password": "6e9cdc20-b92a-11ef-973a-f23c913c8d2b", "auth": "6e9cdc20-b92a-11ef-973a-f23c913c8d2b", "obfs": {"type": "", "password": ""}, "tls": {"enabled": true, "serverName": "1fec14d5-swrwg0-sww7b0-1qwp5.hy2.gotochinatown.net", "alpn": ["h3"], "skipCertVerify": false}, "bandwidth": {"up": "", "down": ""}, "congestion": "bbr", "fastOpen": false, "lazy": false, "_sourceFormat": "base64", "detectedRegion": "US", "originalName": "🇺🇸 美国 095"}, {"type": "hysteria2", "name": "🇺🇸 美国 096", "server": "jiangzhidb.54264944.xyz", "port": 43999, "password": "6c510073-4ca8-423b-87a5-a6d73c0ca557", "auth": "6c510073-4ca8-423b-87a5-a6d73c0ca557", "obfs": {"type": "", "password": ""}, "tls": {"enabled": true, "serverName": "jiangzhidb.54264944.xyz", "alpn": ["h3"], "skipCertVerify": true}, "bandwidth": {"up": "", "down": ""}, "congestion": "bbr", "fastOpen": false, "lazy": false, "_sourceFormat": "base64", "detectedRegion": "US", "originalName": "🇺🇸 美国 096"}, {"type": "hysteria2", "name": "🇺🇸 美国 097", "server": "us3.dexlos.com", "port": 5790, "password": "48173CB0-68B5-4CAB-9587-FB7CCDE206A7", "auth": "48173CB0-68B5-4CAB-9587-FB7CCDE206A7", "obfs": {"type": "", "password": ""}, "tls": {"enabled": true, "serverName": "us3.dexlos.com", "alpn": ["h3"], "skipCertVerify": false}, "bandwidth": {"up": "", "down": ""}, "congestion": "bbr", "fastOpen": false, "lazy": false, "_sourceFormat": "base64", "detectedRegion": "US", "originalName": "🇺🇸 美国 097"}, {"type": "trojan", "name": "🇺🇸 美国 098", "server": "************", "port": 443, "password": "3lSlAcxDSNpeKOyZ3pIlyYRC7328C3X93Aa8SFOgDX4D7An0zYSRDRuaRE8lCpCASE6Bu", "network": "tcp", "tls": {"enabled": true, "serverName": "theory.wireshop.net", "alpn": [], "fingerprint": "chrome", "skipCertVerify": false}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "US", "originalName": "🇺🇸 美国 098"}, {"type": "hysteria2", "name": "🇺🇸 美国 099", "server": "us4.dexlos.com", "port": 7605, "password": "48173CB0-68B5-4CAB-9587-FB7CCDE206A7", "auth": "48173CB0-68B5-4CAB-9587-FB7CCDE206A7", "obfs": {"type": "", "password": ""}, "tls": {"enabled": true, "serverName": "us4.dexlos.com", "alpn": ["h3"], "skipCertVerify": false}, "bandwidth": {"up": "", "down": ""}, "congestion": "bbr", "fastOpen": false, "lazy": false, "_sourceFormat": "base64", "detectedRegion": "US", "originalName": "🇺🇸 美国 099"}, {"type": "hysteria2", "name": "🇺🇸 美国 100", "server": "us3.dexlos.com", "port": 5192, "password": "48173CB0-68B5-4CAB-9587-FB7CCDE206A7", "auth": "48173CB0-68B5-4CAB-9587-FB7CCDE206A7", "obfs": {"type": "", "password": ""}, "tls": {"enabled": true, "serverName": "us3.dexlos.com", "alpn": ["h3"], "skipCertVerify": false}, "bandwidth": {"up": "", "down": ""}, "congestion": "bbr", "fastOpen": false, "lazy": false, "_sourceFormat": "base64", "detectedRegion": "US", "originalName": "🇺🇸 美国 100"}, {"type": "hysteria2", "name": "🇺🇸 美国 101", "server": "us5.dexlos.com", "port": 9166, "password": "48173CB0-68B5-4CAB-9587-FB7CCDE206A7", "auth": "48173CB0-68B5-4CAB-9587-FB7CCDE206A7", "obfs": {"type": "", "password": ""}, "tls": {"enabled": true, "serverName": "us5.dexlos.com", "alpn": ["h3"], "skipCertVerify": false}, "bandwidth": {"up": "", "down": ""}, "congestion": "bbr", "fastOpen": false, "lazy": false, "_sourceFormat": "base64", "detectedRegion": "US", "originalName": "🇺🇸 美国 101"}, {"type": "hysteria2", "name": "🇺🇸 美国 102", "server": "qymg.qy1357.top", "port": 30003, "password": "d010926e-0311-4924-a013-b84fbae430f9", "auth": "d010926e-0311-4924-a013-b84fbae430f9", "obfs": {"type": "", "password": ""}, "tls": {"enabled": true, "serverName": "qymg.qy1357.top", "alpn": ["h3"], "skipCertVerify": true}, "bandwidth": {"up": "", "down": ""}, "congestion": "bbr", "fastOpen": false, "lazy": false, "_sourceFormat": "base64", "detectedRegion": "US", "originalName": "🇺🇸 美国 102"}, {"type": "hysteria2", "name": "🇺🇸 美国 103", "server": "479aad99-swd340-tcinla-hrtf.hy2.gotochinatown.net", "port": 8443, "password": "0da8651e-e1f6-11ec-bd7c-f23c913c8d2b", "auth": "0da8651e-e1f6-11ec-bd7c-f23c913c8d2b", "obfs": {"type": "", "password": ""}, "tls": {"enabled": true, "serverName": "479aad99-swd340-tcinla-hrtf.hy2.gotochinatown.net", "alpn": ["h3"], "skipCertVerify": false}, "bandwidth": {"up": "", "down": ""}, "congestion": "bbr", "fastOpen": false, "lazy": false, "_sourceFormat": "base64", "detectedRegion": "US", "originalName": "🇺🇸 美国 103"}, {"type": "vless", "name": "🇺🇸 美国 104", "server": "************", "port": 8443, "uuid": "06121b89-607b-44c9-9c01-cc2fc6a7321d", "flow": "xtls-rprx-vision", "encryption": "none", "network": "tcp", "tls": {"enabled": true, "serverName": "www.yahoo.com", "alpn": [], "fingerprint": "chrome"}, "transport": {}, "reality": {"enabled": true, "publicKey": "4Qekb9y1dqO8hvRzVSGeSRNyhko_gqpeWD94zrLCvjs", "shortId": "5488b0e7", "spiderX": ""}, "_sourceFormat": "base64", "detectedRegion": "US", "originalName": "🇺🇸 美国 104"}, {"type": "hysteria2", "name": "🇺🇸 美国 105", "server": "*************", "port": 60000, "password": "41a11f83", "auth": "41a11f83", "obfs": {"type": "", "password": ""}, "tls": {"enabled": true, "serverName": "www.bing.com", "alpn": ["h3"], "skipCertVerify": true}, "bandwidth": {"up": "", "down": ""}, "congestion": "bbr", "fastOpen": false, "lazy": false, "_sourceFormat": "base64", "detectedRegion": "US", "originalName": "🇺🇸 美国 105"}, {"type": "hysteria2", "name": "🇺🇸 美国 106", "server": "us1.844300.xyz", "port": 30000, "password": "67d9d454-0d38-4eea-830d-6f9362d988c6", "auth": "67d9d454-0d38-4eea-830d-6f9362d988c6", "obfs": {"type": "", "password": ""}, "tls": {"enabled": true, "serverName": "us1.844300.xyz", "alpn": ["h3"], "skipCertVerify": false}, "bandwidth": {"up": "", "down": ""}, "congestion": "bbr", "fastOpen": false, "lazy": false, "_sourceFormat": "base64", "detectedRegion": "US", "originalName": "🇺🇸 美国 106"}, {"type": "ss", "name": "🇭🇰 香港 001", "server": "*************", "port": 16008, "password": "3030b609-f36f-4fd1-a4ef-a6e653780536", "method": "aes-256-gcm", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "HK", "originalName": "🇭🇰 香港 001"}, {"type": "ss", "name": "🇭🇰 香港 002", "server": "************", "port": 16007, "password": "3030b609-f36f-4fd1-a4ef-a6e653780536", "method": "aes-256-gcm", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "HK", "originalName": "🇭🇰 香港 002"}, {"type": "ss", "name": "🇭🇰 香港 003", "server": "*************", "port": 16002, "password": "3030b609-f36f-4fd1-a4ef-a6e653780536", "method": "aes-256-gcm", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "HK", "originalName": "🇭🇰 香港 003"}, {"type": "ss", "name": "🇭🇰 香港 004", "server": "*************", "port": 49758, "password": "@CfftfYVgp4gkMHMirH6@_C", "method": "xchacha20-ietf-poly1305", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "HK", "originalName": "🇭🇰 香港 004"}, {"type": "trojan", "name": "🇭🇰 香港 005", "server": "cn1.cdn.xfltd-cdn.top", "port": 12007, "password": "7669a04d-a459-4c96-bc0e-fde51cb984f3", "network": "tcp", "tls": {"enabled": true, "serverName": "cdn.alibaba.com", "alpn": [], "fingerprint": "chrome", "skipCertVerify": true}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "HK", "originalName": "🇭🇰 香港 005"}, {"type": "trojan", "name": "🇭🇰 香港 006", "server": "cn1.cdn.xfltd-cdn.top", "port": 12002, "password": "7669a04d-a459-4c96-bc0e-fde51cb984f3", "network": "tcp", "tls": {"enabled": true, "serverName": "cdn.alibaba.com", "alpn": [], "fingerprint": "chrome", "skipCertVerify": true}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "HK", "originalName": "🇭🇰 香港 006"}, {"type": "trojan", "name": "🇭🇰 香港 007", "server": "cn1.cdn.xfltd-cdn.top", "port": 12005, "password": "7669a04d-a459-4c96-bc0e-fde51cb984f3", "network": "tcp", "tls": {"enabled": true, "serverName": "cdn.alibaba.com", "alpn": [], "fingerprint": "chrome", "skipCertVerify": true}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "HK", "originalName": "🇭🇰 香港 007"}, {"type": "ss", "name": "🇭🇰 香港 008", "server": "************", "port": 16002, "password": "c86d483c-431f-41df-bb6b-c1dcebfc7401", "method": "aes-256-gcm", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "HK", "originalName": "🇭🇰 香港 008"}, {"type": "ss", "name": "🇭🇰 香港 009", "server": "*************", "port": 16005, "password": "c86d483c-431f-41df-bb6b-c1dcebfc7401", "method": "aes-256-gcm", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "HK", "originalName": "🇭🇰 香港 009"}, {"type": "ss", "name": "🇭🇰 香港 010", "server": "************", "port": 16004, "password": "3030b609-f36f-4fd1-a4ef-a6e653780536", "method": "aes-256-gcm", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "HK", "originalName": "🇭🇰 香港 010"}, {"type": "ss", "name": "🇭🇰 香港 011", "server": "*************", "port": 16006, "password": "c86d483c-431f-41df-bb6b-c1dcebfc7401", "method": "aes-256-gcm", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "HK", "originalName": "🇭🇰 香港 011"}, {"type": "ss", "name": "🇭🇰 香港 012", "server": "*************", "port": 49759, "password": "sadujij!@diQojd1254", "method": "aes-128-gcm", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "HK", "originalName": "🇭🇰 香港 012"}, {"type": "trojan", "name": "🇭🇰 香港 013", "server": "e5cf8bf8bd48d330b0509e4aaf1b57f5.v1.cac.node-is.green", "port": 42204, "password": "e2241e38-d5d4-4f60-afcd-bf7d6bfb6668", "network": "tcp", "tls": {"enabled": true, "serverName": "hk14.bilibili.com", "alpn": [], "fingerprint": "chrome", "skipCertVerify": true}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "HK", "originalName": "🇭🇰 香港 013"}, {"type": "trojan", "name": "🇭🇰 香港 014", "server": "**************", "port": 8313, "password": "2c605663-b89a-5734-a9d6-97d4743d72cf", "network": "tcp", "tls": {"enabled": true, "serverName": "**************", "alpn": [], "fingerprint": "chrome", "skipCertVerify": true}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "HK", "originalName": "🇭🇰 香港 014"}, {"type": "trojan", "name": "🇭🇰 香港 015", "server": "4c48724e1f11cf0912ab5b04e0a2391b.v1.cac.node-is.green", "port": 44446, "password": "e2241e38-d5d4-4f60-afcd-bf7d6bfb6668", "network": "tcp", "tls": {"enabled": true, "serverName": "hk13.bilibili.com", "alpn": [], "fingerprint": "chrome", "skipCertVerify": true}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "HK", "originalName": "🇭🇰 香港 015"}, {"type": "trojan", "name": "🇭🇰 香港 016", "server": "f10021.ylxblkyndjj.sbs", "port": 16706, "password": "74b001ba-7020-443d-b33b-5a403a156b39", "network": "tcp", "tls": {"enabled": true, "serverName": "hk04.ckcloud.info", "alpn": [], "fingerprint": "", "skipCertVerify": true}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "HK", "originalName": "🇭🇰 香港 016"}, {"type": "vmess", "name": "🇭🇰 香港 017", "server": "f77b7ed3-swtr40-syb15h-8caj.hkt.east.wctype.com", "port": 459, "uuid": "a67a6c18-3f6d-11ef-ab9c-f23c9313b177", "alterId": 2, "cipher": "auto", "network": "ws", "tls": {"enabled": false, "serverName": "a605477178.m.ctrip.com"}, "transport": {"path": "/", "headers": {"Host": "a605477178.m.ctrip.com"}}, "_sourceFormat": "base64", "detectedRegion": "HK", "originalName": "ð­ð° é¦æ¸¯ 017"}, {"type": "vmess", "name": "🇭🇰 香港 018", "server": "9d6c2f7e-swvls0-syb15h-8caj.hkt.east.wctype.com", "port": 459, "uuid": "a67a6c18-3f6d-11ef-ab9c-f23c9313b177", "alterId": 2, "cipher": "auto", "network": "ws", "tls": {"enabled": false, "serverName": "a605477178.m.ctrip.com"}, "transport": {"path": "/", "headers": {"Host": "a605477178.m.ctrip.com"}}, "_sourceFormat": "base64", "detectedRegion": "HK", "originalName": "ð­ð° é¦æ¸¯ 018"}, {"type": "trojan", "name": "🇭🇰 香港 019", "server": "cn1.cdn.xfltd-cdn.top", "port": 12003, "password": "7669a04d-a459-4c96-bc0e-fde51cb984f3", "network": "tcp", "tls": {"enabled": true, "serverName": "cdn.alibaba.com", "alpn": [], "fingerprint": "chrome", "skipCertVerify": true}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "HK", "originalName": "🇭🇰 香港 019"}, {"type": "trojan", "name": "🇭🇰 香港 020", "server": "cn1.cdn.xfltd-cdn.top", "port": 12006, "password": "7669a04d-a459-4c96-bc0e-fde51cb984f3", "network": "tcp", "tls": {"enabled": true, "serverName": "cdn.alibaba.com", "alpn": [], "fingerprint": "chrome", "skipCertVerify": true}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "HK", "originalName": "🇭🇰 香港 020"}, {"type": "trojan", "name": "🇭🇰 香港 021", "server": "dozo01.flztjc.top", "port": 8313, "password": "2c605663-b89a-5734-a9d6-97d4743d72cf", "network": "tcp", "tls": {"enabled": true, "serverName": "hk-13-568.flztjc.net", "alpn": [], "fingerprint": "chrome", "skipCertVerify": false}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "HK", "originalName": "🇭🇰 香港 021"}, {"type": "ss", "name": "🇭🇰 香港 022", "server": "************", "port": 16014, "password": "c86d483c-431f-41df-bb6b-c1dcebfc7401", "method": "aes-256-gcm", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "HK", "originalName": "🇭🇰 香港 022"}, {"type": "trojan", "name": "🇭🇰 香港 023", "server": "**************", "port": 45569, "password": "RlzoEILU", "network": "tcp", "tls": {"enabled": true, "serverName": "cdn.egvra.cn", "alpn": [], "fingerprint": "chrome", "skipCertVerify": true}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "HK", "originalName": "🇭🇰 香港 023"}, {"type": "vless", "name": "🇭🇰 香港 024", "server": "************", "port": 8880, "uuid": "53fa8faf-ba4b-4322-9c69-a3e5b1555049", "flow": "", "encryption": "none", "network": "ws", "tls": {"enabled": false, "serverName": "", "alpn": [], "fingerprint": ""}, "transport": {"path": "/Telegram🇨🇳 @MxlShare @WangCai2 /?ed=2560", "host": "reedfree8mahsang2.redorg.ir"}, "reality": {"enabled": false, "publicKey": "", "shortId": "", "spiderX": ""}, "_sourceFormat": "base64", "detectedRegion": "HK", "originalName": "🇭🇰 香港 024"}, {"type": "vmess", "name": "🇭🇰 香港 025", "server": "688f6e4a-swxgg0-syb15h-8caj.hkt.east.wctype.com", "port": 459, "uuid": "a67a6c18-3f6d-11ef-ab9c-f23c9313b177", "alterId": 2, "cipher": "auto", "network": "ws", "tls": {"enabled": false, "serverName": "a605477178.m.ctrip.com"}, "transport": {"path": "/", "headers": {"Host": "a605477178.m.ctrip.com"}}, "_sourceFormat": "base64", "detectedRegion": "HK", "originalName": "ð­ð° é¦æ¸¯ 025"}, {"type": "vless", "name": "🇭🇰 香港 026", "server": "*************", "port": 8880, "uuid": "53fa8faf-ba4b-4322-9c69-a3e5b1555049", "flow": "", "encryption": "none", "network": "ws", "tls": {"enabled": false, "serverName": "", "alpn": [], "fingerprint": ""}, "transport": {"path": "/?ed=2560", "host": "reedfree8mahsang2.redorg.ir"}, "reality": {"enabled": false, "publicKey": "", "shortId": "", "spiderX": ""}, "_sourceFormat": "base64", "detectedRegion": "HK", "originalName": "🇭🇰 香港 026"}, {"type": "vmess", "name": "🇭🇰 香港 027", "server": "aa080c80-swo740-sy4opd-bhc9.hkt.east.wctype.com", "port": 459, "uuid": "60a3d204-c7f8-11ef-adbd-f23c93141fad", "alterId": 2, "cipher": "auto", "network": "ws", "tls": {"enabled": false, "serverName": "a605477178.m.ctrip.com"}, "transport": {"path": "/", "headers": {"Host": "a605477178.m.ctrip.com"}}, "_sourceFormat": "base64", "detectedRegion": "HK", "originalName": "ð­ð° é¦æ¸¯ 027"}, {"type": "trojan", "name": "🇭🇰 香港 028", "server": "*************", "port": 28296, "password": "RlzoEILU", "network": "tcp", "tls": {"enabled": true, "serverName": "cdn.egvra.cn", "alpn": [], "fingerprint": "chrome", "skipCertVerify": true}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "HK", "originalName": "🇭🇰 香港 028"}, {"type": "trojan", "name": "🇭🇰 香港 029", "server": "**************", "port": 48959, "password": "RlzoEILU", "network": "tcp", "tls": {"enabled": true, "serverName": "**************", "alpn": [], "fingerprint": "", "skipCertVerify": true}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "HK", "originalName": "🇭🇰 香港 029"}, {"type": "ss", "name": "🇭🇰 香港 030", "server": "eepl2.d-h-h.de", "port": 42097, "password": "ed6c8eea-8503-43e1-9563-0a355b0edcac", "method": "aes-256-gcm", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "HK", "originalName": "🇭🇰 香港 030"}, {"type": "ss", "name": "🇭🇰 香港 031", "server": "eepl1.dhh114514.christmas", "port": 42099, "password": "ed6c8eea-8503-43e1-9563-0a355b0edcac", "method": "chacha20-ietf-poly1305", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "HK", "originalName": "🇭🇰 香港 031"}, {"type": "trojan", "name": "🇭🇰 香港 032", "server": "**************", "port": 50723, "password": "RlzoEILU", "network": "tcp", "tls": {"enabled": true, "serverName": "**************", "alpn": [], "fingerprint": "chrome", "skipCertVerify": true}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "HK", "originalName": "🇭🇰 香港 032"}, {"type": "trojan", "name": "🇭🇰 香港 033", "server": "*************", "port": 39689, "password": "RlzoEILU", "network": "tcp", "tls": {"enabled": true, "serverName": "*************", "alpn": [], "fingerprint": "chrome", "skipCertVerify": true}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "HK", "originalName": "🇭🇰 香港 033"}, {"type": "trojan", "name": "🇭🇰 香港 034", "server": "**************", "port": 34041, "password": "RlzoEILU", "network": "tcp", "tls": {"enabled": true, "serverName": "cdn.egvra.cn", "alpn": [], "fingerprint": "chrome", "skipCertVerify": true}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "HK", "originalName": "🇭🇰 香港 034"}, {"type": "hysteria2", "name": "🇭🇰 香港 035", "server": "hkhkt.ccwink.cc", "port": 8021, "password": "b751e995-b24c-4f30-8425-05db0b9eac45", "auth": "b751e995-b24c-4f30-8425-05db0b9eac45", "obfs": {"type": "", "password": ""}, "tls": {"enabled": true, "serverName": "hkhkt.ccwink.cc", "alpn": ["h3"], "skipCertVerify": true}, "bandwidth": {"up": "", "down": ""}, "congestion": "bbr", "fastOpen": false, "lazy": false, "_sourceFormat": "base64", "detectedRegion": "HK", "originalName": "🇭🇰 香港 035"}, {"type": "hysteria2", "name": "🇭🇰 香港 036", "server": "hkt01.ccwink.cc", "port": 8023, "password": "b751e995-b24c-4f30-8425-05db0b9eac45", "auth": "b751e995-b24c-4f30-8425-05db0b9eac45", "obfs": {"type": "", "password": ""}, "tls": {"enabled": true, "serverName": "hkt01.ccwink.cc", "alpn": ["h3"], "skipCertVerify": true}, "bandwidth": {"up": "", "down": ""}, "congestion": "bbr", "fastOpen": false, "lazy": false, "_sourceFormat": "base64", "detectedRegion": "HK", "originalName": "🇭🇰 香港 036"}, {"type": "hysteria2", "name": "🇭🇰 香港 037", "server": "hkhkt.ccwink.cc", "port": 8022, "password": "b751e995-b24c-4f30-8425-05db0b9eac45", "auth": "b751e995-b24c-4f30-8425-05db0b9eac45", "obfs": {"type": "", "password": ""}, "tls": {"enabled": true, "serverName": "hkhkt.ccwink.cc", "alpn": ["h3"], "skipCertVerify": true}, "bandwidth": {"up": "", "down": ""}, "congestion": "bbr", "fastOpen": false, "lazy": false, "_sourceFormat": "base64", "detectedRegion": "HK", "originalName": "🇭🇰 香港 037"}, {"type": "trojan", "name": "🇭🇰 香港 038", "server": "**************", "port": 5800, "password": "RlzoEILU", "network": "tcp", "tls": {"enabled": true, "serverName": "cdn.egvra.cn", "alpn": [], "fingerprint": "chrome", "skipCertVerify": true}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "HK", "originalName": "🇭🇰 香港 038"}, {"type": "ss", "name": "🇭🇰 香港 039", "server": "*************", "port": 16005, "password": "abb58966-8d7b-4cdf-bae9-b0d8e184d93d", "method": "aes-256-gcm", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "HK", "originalName": "🇭🇰 香港 039"}, {"type": "ss", "name": "🇭🇰 香港 040", "server": "*************", "port": 16014, "password": "abb58966-8d7b-4cdf-bae9-b0d8e184d93d", "method": "aes-256-gcm", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "HK", "originalName": "🇭🇰 香港 040"}, {"type": "ss", "name": "🇭🇰 香港 041", "server": "*************", "port": 16010, "password": "abb58966-8d7b-4cdf-bae9-b0d8e184d93d", "method": "aes-256-gcm", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "HK", "originalName": "🇭🇰 香港 041"}, {"type": "ss", "name": "🇭🇰 香港 042", "server": "************", "port": 16010, "password": "abb58966-8d7b-4cdf-bae9-b0d8e184d93d", "method": "aes-256-gcm", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "HK", "originalName": "🇭🇰 香港 042"}, {"type": "ss", "name": "🇭🇰 香港 043", "server": "8tv68qhq.slashdevslashnetslashtun.net", "port": 15009, "password": "GXUTEVGNLHEJ8GQN", "method": "aes-256-gcm", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "HK", "originalName": "🇭🇰 香港 043"}, {"type": "vmess", "name": "🇭🇰 香港 044", "server": "da3e1424-swq1s0-sy4opd-bhc9.hkt.east.wctype.com", "port": 459, "uuid": "60a3d204-c7f8-11ef-adbd-f23c93141fad", "alterId": 2, "cipher": "auto", "network": "ws", "tls": {"enabled": false, "serverName": "a605477178.m.ctrip.com"}, "transport": {"path": "/", "headers": {"Host": "a605477178.m.ctrip.com"}}, "_sourceFormat": "base64", "detectedRegion": "HK", "originalName": "ð­ð° é¦æ¸¯ 044"}, {"type": "ss", "name": "🇭🇰 香港 045", "server": "************", "port": 16007, "password": "200c650a-4d7c-4180-b0ce-20093784902e", "method": "aes-256-gcm", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "HK", "originalName": "🇭🇰 香港 045"}, {"type": "ss", "name": "🇭🇰 香港 046", "server": "*************", "port": 16001, "password": "200c650a-4d7c-4180-b0ce-20093784902e", "method": "aes-256-gcm", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "HK", "originalName": "🇭🇰 香港 046"}, {"type": "vmess", "name": "🇭🇰 香港 047", "server": "ppy-hkv1.02ijp4uos1.download", "port": 26010, "uuid": "ffc323ce-efe2-3b60-a1d1-f4f5497bf2d7", "alterId": 0, "cipher": "auto", "network": "tcp", "tls": {"enabled": false, "serverName": ""}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "HK", "originalName": "ð­ð° é¦æ¸¯ 047"}, {"type": "trojan", "name": "🇭🇰 香港 048", "server": "**************", "port": 15624, "password": "RlzoEILU", "network": "tcp", "tls": {"enabled": true, "serverName": "**************", "alpn": [], "fingerprint": "", "skipCertVerify": true}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "HK", "originalName": "🇭🇰 香港 048"}, {"type": "ss", "name": "🇭🇰 香港 049", "server": "*************", "port": 16004, "password": "b8fa2e30-a210-4d8b-bb1e-bf42ea9c9e7e", "method": "aes-256-gcm", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "HK", "originalName": "🇭🇰 香港 049"}, {"type": "ss", "name": "🇭🇰 香港 050", "server": "*************", "port": 16014, "password": "b8fa2e30-a210-4d8b-bb1e-bf42ea9c9e7e", "method": "aes-256-gcm", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "HK", "originalName": "🇭🇰 香港 050"}, {"type": "trojan", "name": "🇭🇰 香港 051", "server": "*************", "port": 46861, "password": "RlzoEILU", "network": "tcp", "tls": {"enabled": true, "serverName": "*************", "alpn": [], "fingerprint": "", "skipCertVerify": true}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "HK", "originalName": "🇭🇰 香港 051"}, {"type": "ss", "name": "🇭🇰 香港 052", "server": "sz.fanhua.art", "port": 37001, "password": "b86a48e8-542d-4b90-bb6e-ff0a3e7e4e97", "method": "aes-128-gcm", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "HK", "originalName": "🇭🇰 香港 052"}, {"type": "ss", "name": "🇭🇰 香港 053", "server": "sz.fanhua.art", "port": 63447, "password": "b86a48e8-542d-4b90-bb6e-ff0a3e7e4e97", "method": "aes-128-gcm", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "HK", "originalName": "🇭🇰 香港 053"}, {"type": "trojan", "name": "🇭🇰 香港 054", "server": "*************", "port": 8313, "password": "2c605663-b89a-5734-a9d6-97d4743d72cf", "network": "tcp", "tls": {"enabled": true, "serverName": "hk-13-568.flztjc.net", "alpn": [], "fingerprint": "chrome", "skipCertVerify": true}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "HK", "originalName": "🇭🇰 香港 054"}, {"type": "trojan", "name": "🇭🇰 香港 055", "server": "*************", "port": 3754, "password": "RlzoEILU", "network": "tcp", "tls": {"enabled": true, "serverName": "*************", "alpn": [], "fingerprint": "chrome", "skipCertVerify": true}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "HK", "originalName": "🇭🇰 香港 055"}, {"type": "vmess", "name": "🇭🇰 香港 056", "server": "04b47978-swb8g0-swdpud-duku.hk3.p5pv.com", "port": 80, "uuid": "8e0f8ef2-5fe3-11ec-a8bf-f23c91cfbbc9", "alterId": 2, "cipher": "auto", "network": "ws", "tls": {"enabled": false, "serverName": "broadcastlv.chat.bilibili.com"}, "transport": {"path": "/", "headers": {"Host": "broadcastlv.chat.bilibili.com"}}, "_sourceFormat": "base64", "detectedRegion": "HK", "originalName": "ð­ð° é¦æ¸¯ 056"}, {"type": "vmess", "name": "🇭🇰 香港 057", "server": "11a88ecb-swkhs0-tf70jh-vm13.hk.p5pv.com", "port": 80, "uuid": "3fa7157d-efe8-832e-9b3c-582d78efbc52", "alterId": 2, "cipher": "auto", "network": "ws", "tls": {"enabled": false, "serverName": "broadcastlv.chat.bilibili.com"}, "transport": {"path": "/", "headers": {"Host": "broadcastlv.chat.bilibili.com"}}, "_sourceFormat": "base64", "detectedRegion": "HK", "originalName": "ð­ð° é¦æ¸¯ 057"}, {"type": "vmess", "name": "🇭🇰 香港 058", "server": "373dbf60-swkhs0-t8kd6j-1c9em.hk.p5pv.com", "port": 80, "uuid": "336501b6-51d2-11ee-a993-f23c9164ca5d", "alterId": 2, "cipher": "auto", "network": "ws", "tls": {"enabled": false, "serverName": "broadcastlv.chat.bilibili.com"}, "transport": {"path": "/", "headers": {"Host": "broadcastlv.chat.bilibili.com"}}, "_sourceFormat": "base64", "detectedRegion": "HK", "originalName": "ð­ð° é¦æ¸¯ 058"}, {"type": "ss", "name": "🇭🇰 香港 059", "server": "**************", "port": 2019, "password": "e04ae67d4e4cd165", "method": "aes-256-gcm", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "HK", "originalName": "🇭🇰 香港 059"}, {"type": "ss", "name": "🇭🇰 香港 060", "server": "*************", "port": 16009, "password": "b8fa2e30-a210-4d8b-bb1e-bf42ea9c9e7e", "method": "aes-256-gcm", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "HK", "originalName": "🇭🇰 香港 060"}, {"type": "ss", "name": "🇭🇰 香港 061", "server": "************", "port": 16002, "password": "49cdeea8-97dd-402a-bf8f-961cb59123a7", "method": "aes-256-gcm", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "HK", "originalName": "🇭🇰 香港 061"}, {"type": "ss", "name": "🇭🇰 香港 062", "server": "cm1.d-h-h.in", "port": 51001, "password": "ed6c8eea-8503-43e1-9563-0a355b0edcac", "method": "aes-128-gcm", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "HK", "originalName": "🇭🇰 香港 062"}, {"type": "ss", "name": "🇭🇰 香港 063", "server": "8tv68qhq.slashdevslashnetslashtun.net", "port": 15013, "password": "R2F7VQ5ABAAJQIBH", "method": "aes-256-gcm", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "HK", "originalName": "🇭🇰 香港 063"}, {"type": "ss", "name": "🇭🇰 香港 064", "server": "ti3hyra4.slashdevslashnetslashtun.net", "port": 15007, "password": "9MUA3J02RL6G9NB6", "method": "aes-256-gcm", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "HK", "originalName": "🇭🇰 香港 064"}, {"type": "ss", "name": "🇭🇰 香港 065", "server": "8tv68qhq.slashdevslashnetslashtun.net", "port": 15014, "password": "2HZGM3TWD6433RJ2", "method": "aes-256-gcm", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "HK", "originalName": "🇭🇰 香港 065"}, {"type": "ss", "name": "🇭🇰 香港 066", "server": "ti3hyra4.slashdevslashnetslashtun.net", "port": 15015, "password": "U3NE5UAS2AO9RWDV", "method": "aes-256-gcm", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "HK", "originalName": "🇭🇰 香港 066"}, {"type": "ss", "name": "🇭🇰 香港 067", "server": "ti3hyra4.slashdevslashnetslashtun.net", "port": 15016, "password": "NANGHYCW70BLYMX7", "method": "aes-256-gcm", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "HK", "originalName": "🇭🇰 香港 067"}, {"type": "ss", "name": "🇭🇰 香港 068", "server": "8tv68qhq.slashdevslashnetslashtun.net", "port": 15012, "password": "GFT5YY53X9S4I4VA", "method": "aes-256-gcm", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "HK", "originalName": "🇭🇰 香港 068"}, {"type": "ss", "name": "🇹🇼 台湾 001", "server": "************", "port": 17005, "password": "3030b609-f36f-4fd1-a4ef-a6e653780536", "method": "aes-256-gcm", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "TW", "originalName": "🇹🇼 台湾 001"}, {"type": "ss", "name": "🇹🇼 台湾 002", "server": "*************", "port": 17004, "password": "3030b609-f36f-4fd1-a4ef-a6e653780536", "method": "aes-256-gcm", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "TW", "originalName": "🇹🇼 台湾 002"}, {"type": "ss", "name": "🇹🇼 台湾 003", "server": "*************", "port": 17007, "password": "c86d483c-431f-41df-bb6b-c1dcebfc7401", "method": "aes-256-gcm", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "TW", "originalName": "🇹🇼 台湾 003"}, {"type": "ss", "name": "🇹🇼 台湾 004", "server": "*************", "port": 17001, "password": "c86d483c-431f-41df-bb6b-c1dcebfc7401", "method": "aes-256-gcm", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "TW", "originalName": "🇹🇼 台湾 004"}, {"type": "ss", "name": "🇹🇼 台湾 005", "server": "************", "port": 17003, "password": "c86d483c-431f-41df-bb6b-c1dcebfc7401", "method": "aes-256-gcm", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "TW", "originalName": "🇹🇼 台湾 005"}, {"type": "trojan", "name": "🇹🇼 台湾 006", "server": "cn1.cdn.xfltd-cdn.top", "port": 12011, "password": "7669a04d-a459-4c96-bc0e-fde51cb984f3", "network": "tcp", "tls": {"enabled": true, "serverName": "cdn.alibaba.com", "alpn": [], "fingerprint": "chrome", "skipCertVerify": true}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "TW", "originalName": "🇹🇼 台湾 006"}, {"type": "trojan", "name": "🇹🇼 台湾 007", "server": "d059223076607593acf0528944466a85.v1.cac.node-is.green", "port": 45285, "password": "e2241e38-d5d4-4f60-afcd-bf7d6bfb6668", "network": "tcp", "tls": {"enabled": true, "serverName": "tw1.bilibili.com", "alpn": [], "fingerprint": "chrome", "skipCertVerify": true}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "TW", "originalName": "🇹🇼 台湾 007"}, {"type": "ss", "name": "🇹🇼 台湾 008", "server": "************", "port": 17005, "password": "c86d483c-431f-41df-bb6b-c1dcebfc7401", "method": "aes-256-gcm", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "TW", "originalName": "🇹🇼 台湾 008"}, {"type": "trojan", "name": "🇹🇼 台湾 009", "server": "f10021.ylxblkyndjj.sbs", "port": 49148, "password": "74b001ba-7020-443d-b33b-5a403a156b39", "network": "tcp", "tls": {"enabled": true, "serverName": "tw02.ckcloud.info", "alpn": [], "fingerprint": "", "skipCertVerify": true}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "TW", "originalName": "🇹🇼 台湾 009"}, {"type": "ss", "name": "🇹🇼 台湾 010", "server": "*************", "port": 17010, "password": "abb58966-8d7b-4cdf-bae9-b0d8e184d93d", "method": "aes-256-gcm", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "TW", "originalName": "🇹🇼 台湾 010"}, {"type": "ss", "name": "🇹🇼 台湾 011", "server": "*************", "port": 17002, "password": "abb58966-8d7b-4cdf-bae9-b0d8e184d93d", "method": "aes-256-gcm", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "TW", "originalName": "🇹🇼 台湾 011"}, {"type": "ss", "name": "🇹🇼 台湾 012", "server": "eepl2.d-h-h.de", "port": 42029, "password": "ed6c8eea-8503-43e1-9563-0a355b0edcac", "method": "chacha20-ietf-poly1305", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "TW", "originalName": "🇹🇼 台湾 012"}, {"type": "ss", "name": "🇹🇼 台湾 013", "server": "link.karleynetwork.xyz", "port": 23335, "password": "f87772ed-cef9-444a-a8e8-bcf299c850ec", "method": "chacha20-ietf-poly1305", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "TW", "originalName": "🇹🇼 台湾 013"}, {"type": "ss", "name": "🇹🇼 台湾 014", "server": "sz.fanhua.art", "port": 17154, "password": "b86a48e8-542d-4b90-bb6e-ff0a3e7e4e97", "method": "aes-128-gcm", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "TW", "originalName": "🇹🇼 台湾 014"}, {"type": "ss", "name": "🇹🇼 台湾 015", "server": "************", "port": 17001, "password": "abb58966-8d7b-4cdf-bae9-b0d8e184d93d", "method": "aes-256-gcm", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "TW", "originalName": "🇹🇼 台湾 015"}, {"type": "ss", "name": "🇹🇼 台湾 016", "server": "eepl1.dhh114514.christmas", "port": 42029, "password": "ed6c8eea-8503-43e1-9563-0a355b0edcac", "method": "chacha20-ietf-poly1305", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "TW", "originalName": "🇹🇼 台湾 016"}, {"type": "ss", "name": "🇹🇼 台湾 017", "server": "*************", "port": 17010, "password": "b8fa2e30-a210-4d8b-bb1e-bf42ea9c9e7e", "method": "aes-256-gcm", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "TW", "originalName": "🇹🇼 台湾 017"}, {"type": "ss", "name": "🇸🇬 新加坡 001", "server": "*************", "port": 18004, "password": "3030b609-f36f-4fd1-a4ef-a6e653780536", "method": "aes-256-gcm", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "SG", "originalName": "🇸🇬 新加坡 001"}, {"type": "ss", "name": "🇸🇬 新加坡 002", "server": "************", "port": 18005, "password": "3030b609-f36f-4fd1-a4ef-a6e653780536", "method": "aes-256-gcm", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "SG", "originalName": "🇸🇬 新加坡 002"}, {"type": "ss", "name": "🇸🇬 新加坡 003", "server": "52.221.248.58", "port": 443, "password": "qawszxc123", "method": "aes-256-cfb", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "SG", "originalName": "🇸🇬 新加坡 003"}, {"type": "ss", "name": "🇸🇬 新加坡 004", "server": "52.77.246.86", "port": 443, "password": "qawszxc123", "method": "aes-256-cfb", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "SG", "originalName": "🇸🇬 新加坡 004"}, {"type": "ss", "name": "🇸🇬 新加坡 005", "server": "36.156.184.14", "port": 13038, "password": "f16163ec-3c35-4719-a19b-68c864cdc626", "method": "aes-128-gcm", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "SG", "originalName": "🇸🇬 新加坡 005"}, {"type": "vless", "name": "🇸🇬 新加坡 006", "server": "************", "port": 80, "uuid": "438f9559-1671-45cf-9d2c-338fe6766acf", "flow": "", "encryption": "none", "network": "ws", "tls": {"enabled": false, "serverName": "", "alpn": [], "fingerprint": ""}, "transport": {"path": "/", "host": "14.<PERSON><PERSON>wickramasinghe.shop"}, "reality": {"enabled": false, "publicKey": "", "shortId": "", "spiderX": ""}, "_sourceFormat": "base64", "detectedRegion": "SG", "originalName": "🇸🇬 新加坡 006"}, {"type": "trojan", "name": "🇸🇬 新加坡 007", "server": "cn2.cdn.xfltd-cdn.top", "port": 12024, "password": "7669a04d-a459-4c96-bc0e-fde51cb984f3", "network": "tcp", "tls": {"enabled": true, "serverName": "cn2.cdn.xfltd-cdn.top", "alpn": [], "fingerprint": "", "skipCertVerify": true}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "SG", "originalName": "🇸🇬 新加坡 007"}, {"type": "trojan", "name": "🇸🇬 新加坡 008", "server": "f10021.ylxblkyndjj.sbs", "port": 12768, "password": "74b001ba-7020-443d-b33b-5a403a156b39", "network": "tcp", "tls": {"enabled": true, "serverName": "sg03.ckcloud.info", "alpn": [], "fingerprint": "", "skipCertVerify": true}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "SG", "originalName": "🇸🇬 新加坡 008"}, {"type": "vless", "name": "🇸🇬 新加坡 009", "server": "**************", "port": 80, "uuid": "438f9559-1671-45cf-9d2c-338fe6766acf", "flow": "", "encryption": "none", "network": "ws", "tls": {"enabled": false, "serverName": "", "alpn": [], "fingerprint": ""}, "transport": {"path": "/", "host": "14.<PERSON><PERSON>wickramasinghe.shop"}, "reality": {"enabled": false, "publicKey": "", "shortId": "", "spiderX": ""}, "_sourceFormat": "base64", "detectedRegion": "SG", "originalName": "🇸🇬 新加坡 009"}, {"type": "ss", "name": "🇸🇬 新加坡 010", "server": "ti3hyra4.slashdevslashnetslashtun.net", "port": 16013, "password": "H2ORU9S3KCBQ5D4S", "method": "aes-256-gcm", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "SG", "originalName": "🇸🇬 新加坡 010"}, {"type": "hysteria2", "name": "🇸🇬 新加坡 011", "server": "**************", "port": 31667, "password": "hf96oOugMgvkOAlVykIA0EKHk", "auth": "hf96oOugMgvkOAlVykIA0EKHk", "obfs": {"type": "", "password": ""}, "tls": {"enabled": true, "serverName": "bing.com", "alpn": ["h3"], "skipCertVerify": true}, "bandwidth": {"up": "", "down": ""}, "congestion": "bbr", "fastOpen": false, "lazy": false, "_sourceFormat": "base64", "detectedRegion": "SG", "originalName": "🇸🇬 新加坡 011"}, {"type": "vless", "name": "🇸🇬 新加坡 012", "server": "partner.zoom.us", "port": 80, "uuid": "438f9559-1671-45cf-9d2c-338fe6766acf", "flow": "", "encryption": "none", "network": "ws", "tls": {"enabled": false, "serverName": "", "alpn": [], "fingerprint": ""}, "transport": {"path": "/", "host": "14.<PERSON><PERSON>wickramasinghe.shop"}, "reality": {"enabled": false, "publicKey": "", "shortId": "", "spiderX": ""}, "_sourceFormat": "base64", "detectedRegion": "SG", "originalName": "🇸🇬 新加坡 012"}, {"type": "trojan", "name": "🇸🇬 新加坡 013", "server": "cn2.cdn.xfltd-cdn.top", "port": 12025, "password": "7669a04d-a459-4c96-bc0e-fde51cb984f3", "network": "tcp", "tls": {"enabled": true, "serverName": "cn2.cdn.xfltd-cdn.top", "alpn": [], "fingerprint": "chrome", "skipCertVerify": true}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "SG", "originalName": "🇸🇬 新加坡 013"}, {"type": "ss", "name": "🇸🇬 新加坡 014", "server": "*************", "port": 18002, "password": "c86d483c-431f-41df-bb6b-c1dcebfc7401", "method": "aes-256-gcm", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "SG", "originalName": "🇸🇬 新加坡 014"}, {"type": "trojan", "name": "🇸🇬 新加坡 015", "server": "sssg03.521pokemon.com", "port": 55015, "password": "82762002-40f5-4775-a9ad-3da97a2772f7", "network": "tcp", "tls": {"enabled": true, "serverName": "**************", "alpn": [], "fingerprint": "chrome", "skipCertVerify": true}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "SG", "originalName": "🇸🇬 新加坡 015"}, {"type": "ss", "name": "🇸🇬 新加坡 016", "server": "okanc.node-is.green", "port": 21114, "password": "9acfc574-acc3-4c2b-ab3b-491d43a6eb83", "method": "aes-256-gcm", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "SG", "originalName": "🇸🇬 新加坡 016"}, {"type": "ss", "name": "🇸🇬 新加坡 017", "server": "okanc.node-is.green", "port": 21115, "password": "9acfc574-acc3-4c2b-ab3b-491d43a6eb83", "method": "aes-256-gcm", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "SG", "originalName": "🇸🇬 新加坡 017"}, {"type": "trojan", "name": "🇸🇬 新加坡 018", "server": "**************", "port": 17166, "password": "RlzoEILU", "network": "tcp", "tls": {"enabled": true, "serverName": "cdn.egvra.cn", "alpn": [], "fingerprint": "chrome", "skipCertVerify": true}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "SG", "originalName": "🇸🇬 新加坡 018"}, {"type": "ss", "name": "🇸🇬 新加坡 019", "server": "eepl1.dhh114514.christmas", "port": 42028, "password": "ed6c8eea-8503-43e1-9563-0a355b0edcac", "method": "chacha20-ietf-poly1305", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "SG", "originalName": "🇸🇬 新加坡 019"}, {"type": "trojan", "name": "🇸🇬 新加坡 020", "server": "*************", "port": 29220, "password": "RlzoEILU", "network": "tcp", "tls": {"enabled": true, "serverName": "cdn.egvra.cn", "alpn": [], "fingerprint": "chrome", "skipCertVerify": true}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "SG", "originalName": "🇸🇬 新加坡 020"}, {"type": "ss", "name": "🇸🇬 新加坡 021", "server": "*************", "port": 443, "password": "qawszxc123", "method": "aes-256-cfb", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "SG", "originalName": "🇸🇬 新加坡 021"}, {"type": "ss", "name": "🇸🇬 新加坡 022", "server": "************", "port": 443, "password": "qawszxc123", "method": "aes-256-cfb", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "SG", "originalName": "🇸🇬 新加坡 022"}, {"type": "vless", "name": "🇸🇬 新加坡 023", "server": "sg004.421421.xyz", "port": 20230, "uuid": "a124e41a-0fb8-4543-a0ab-a127c7b515a9", "flow": "xtls-rprx-vision", "encryption": "none", "network": "tcp", "tls": {"enabled": true, "serverName": "www.nvidia.com", "alpn": [], "fingerprint": "chrome"}, "transport": {}, "reality": {"enabled": true, "publicKey": "Bkxd25PPovqeYw6AevM-L266KM2kLDJomgmorzvGFmM", "shortId": "2c846487", "spiderX": ""}, "_sourceFormat": "base64", "detectedRegion": "SG", "originalName": "🇸🇬 新加坡 023"}, {"type": "vless", "name": "🇸🇬 新加坡 024", "server": "sg002.421421.xyz", "port": 20230, "uuid": "f08d6a6b-a0b3-410e-a0ba-eae71b521904", "flow": "xtls-rprx-vision", "encryption": "none", "network": "tcp", "tls": {"enabled": true, "serverName": "www.nvidia.com", "alpn": [], "fingerprint": "chrome"}, "transport": {}, "reality": {"enabled": true, "publicKey": "1cs7mxEcoVKwcYepAnKgqHAFhRxPv6aO3tv7lNhwLDQ", "shortId": "46bf6ea0", "spiderX": ""}, "_sourceFormat": "base64", "detectedRegion": "SG", "originalName": "🇸🇬 新加坡 024"}, {"type": "trojan", "name": "🇸🇬 新加坡 025", "server": "cn2.cdn.xfltd-cdn.top", "port": 12022, "password": "7669a04d-a459-4c96-bc0e-fde51cb984f3", "network": "tcp", "tls": {"enabled": true, "serverName": "cn2.cdn.xfltd-cdn.top", "alpn": [], "fingerprint": "", "skipCertVerify": true}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "SG", "originalName": "🇸🇬 新加坡 025"}, {"type": "trojan", "name": "🇸🇬 新加坡 026", "server": "cn2.cdn.xfltd-cdn.top", "port": 12021, "password": "7669a04d-a459-4c96-bc0e-fde51cb984f3", "network": "tcp", "tls": {"enabled": true, "serverName": "cn2.cdn.xfltd-cdn.top", "alpn": [], "fingerprint": "", "skipCertVerify": true}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "SG", "originalName": "🇸🇬 新加坡 026"}, {"type": "vless", "name": "🇸🇬 新加坡 027", "server": "**************", "port": 8880, "uuid": "53fa8faf-ba4b-4322-9c69-a3e5b1555049", "flow": "", "encryption": "none", "network": "ws", "tls": {"enabled": false, "serverName": "", "alpn": [], "fingerprint": ""}, "transport": {"path": "/?ed=2560", "host": "reedfree8mahsang2.redorg.ir"}, "reality": {"enabled": false, "publicKey": "", "shortId": "", "spiderX": ""}, "_sourceFormat": "base64", "detectedRegion": "SG", "originalName": "🇸🇬 新加坡 027"}, {"type": "ss", "name": "🇸🇬 新加坡 028", "server": "sz.fanhua.art", "port": 21420, "password": "b86a48e8-542d-4b90-bb6e-ff0a3e7e4e97", "method": "aes-128-gcm", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "SG", "originalName": "🇸🇬 新加坡 028"}, {"type": "vless", "name": "🇸🇬 新加坡 029", "server": "***********", "port": 8880, "uuid": "53fa8faf-ba4b-4322-9c69-a3e5b1555049", "flow": "", "encryption": "none", "network": "ws", "tls": {"enabled": false, "serverName": "", "alpn": [], "fingerprint": ""}, "transport": {"path": "/?ed=2560", "host": "reedfree8mahsang2.redorg.ir"}, "reality": {"enabled": false, "publicKey": "", "shortId": "", "spiderX": ""}, "_sourceFormat": "base64", "detectedRegion": "SG", "originalName": "🇸🇬 新加坡 029"}, {"type": "ss", "name": "🇸🇬 新加坡 030", "server": "*************", "port": 18005, "password": "b8fa2e30-a210-4d8b-bb1e-bf42ea9c9e7e", "method": "aes-256-gcm", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "SG", "originalName": "🇸🇬 新加坡 030"}, {"type": "ss", "name": "🇸🇬 新加坡 031", "server": "aisalayer-b.upperlay.xyz", "port": 570, "password": "763bf612-4c66-4fd4-b54b-5349bdea6bca", "method": "aes-128-gcm", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "SG", "originalName": "🇸🇬 新加坡 031"}, {"type": "ss", "name": "🇸🇬 新加坡 032", "server": "aisalayer-a.upperlay.xyz", "port": 568, "password": "763bf612-4c66-4fd4-b54b-5349bdea6bca", "method": "aes-128-gcm", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "SG", "originalName": "🇸🇬 新加坡 032"}, {"type": "ss", "name": "🇸🇬 新加坡 033", "server": "*************", "port": 18004, "password": "abb58966-8d7b-4cdf-bae9-b0d8e184d93d", "method": "aes-256-gcm", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "SG", "originalName": "🇸🇬 新加坡 033"}, {"type": "ss", "name": "🇸🇬 新加坡 034", "server": "************", "port": 18002, "password": "abb58966-8d7b-4cdf-bae9-b0d8e184d93d", "method": "aes-256-gcm", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "SG", "originalName": "🇸🇬 新加坡 034"}, {"type": "trojan", "name": "🇸🇬 新加坡 035", "server": "cn2.cdn.xfltd-cdn.top", "port": 12023, "password": "7669a04d-a459-4c96-bc0e-fde51cb984f3", "network": "tcp", "tls": {"enabled": true, "serverName": "cdn.alibaba.com", "alpn": [], "fingerprint": "chrome", "skipCertVerify": true}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "SG", "originalName": "🇸🇬 新加坡 035"}, {"type": "vless", "name": "🇸🇬 新加坡 036", "server": "sg003.421421.xyz", "port": 20230, "uuid": "fdd1d613-f234-40f6-b63d-97517e0fc4b3", "flow": "xtls-rprx-vision", "encryption": "none", "network": "tcp", "tls": {"enabled": true, "serverName": "www.nvidia.com", "alpn": [], "fingerprint": "chrome"}, "transport": {}, "reality": {"enabled": true, "publicKey": "hBXQa1tSoP0jjIezHxeyKhf1YKxe8CoBHoxvIxEJuAc", "shortId": "2c5ecaae", "spiderX": ""}, "_sourceFormat": "base64", "detectedRegion": "SG", "originalName": "🇸🇬 新加坡 036"}, {"type": "ss", "name": "🇸🇬 新加坡 037", "server": "guang.ccwink.cc", "port": 2015, "password": "M2JmZjZkMWQyOGE0Yjg2NQ%3D%3D%3AYjc1MWU5OTUtYjI0Yy00Zg%3D%3D", "method": "2022-blake3-aes-128-gcm", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "SG", "originalName": "🇸🇬 新加坡 037"}, {"type": "ss", "name": "🇸🇬 新加坡 038", "server": "guang.ccwink.cc", "port": 2014, "password": "M2JmZjZkMWQyOGE0Yjg2NQ%3D%3D%3AYjc1MWU5OTUtYjI0Yy00Zg%3D%3D", "method": "2022-blake3-aes-128-gcm", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "SG", "originalName": "🇸🇬 新加坡 038"}, {"type": "ss", "name": "🇸🇬 新加坡 039", "server": "************", "port": 18010, "password": "b8fa2e30-a210-4d8b-bb1e-bf42ea9c9e7e", "method": "aes-256-gcm", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "SG", "originalName": "🇸🇬 新加坡 039"}, {"type": "trojan", "name": "🇸🇬 新加坡 040", "server": "starlink-sgp5.2513142.xyz", "port": 443, "password": "nktaqlk-1O8bEfVXgIhUvYc_", "network": "tcp", "tls": {"enabled": true, "serverName": "www.cloudflare.com", "alpn": [], "fingerprint": "chrome", "skipCertVerify": true}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "SG", "originalName": "🇸🇬 新加坡 040"}, {"type": "trojan", "name": "🇸🇬 新加坡 041", "server": "starlink-sgp6.2513142.xyz", "port": 443, "password": "sH-dNCXpq8RiI_PeL6Mr4lMT", "network": "tcp", "tls": {"enabled": true, "serverName": "www.cloudflare.com", "alpn": [], "fingerprint": "chrome", "skipCertVerify": true}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "SG", "originalName": "🇸🇬 新加坡 041"}, {"type": "ss", "name": "🇸🇬 新加坡 042", "server": "************", "port": 8388, "password": "asd123456", "method": "chacha20-ietf", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "SG", "originalName": "🇸🇬 新加坡 042"}, {"type": "ss", "name": "🇸🇬 新加坡 043", "server": "ti3hyra4.slashdevslashnetslashtun.net", "port": 16003, "password": "CIU43ZEX3BQ3VS9F", "method": "aes-256-gcm", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "SG", "originalName": "🇸🇬 新加坡 043"}, {"type": "ss", "name": "🇸🇬 新加坡 044", "server": "8tv68qhq.slashdevslashnetslashtun.net", "port": 16006, "password": "L0JM8AWELGJYDSUE", "method": "aes-256-gcm", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "SG", "originalName": "🇸🇬 新加坡 044"}, {"type": "ss", "name": "🇸🇬 新加坡 045", "server": "ti3hyra4.slashdevslashnetslashtun.net", "port": 16011, "password": "O8WKA9YNWXIPLZHE", "method": "aes-256-gcm", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "SG", "originalName": "🇸🇬 新加坡 045"}, {"type": "ss", "name": "🇸🇬 新加坡 046", "server": "ti3hyra4.slashdevslashnetslashtun.net", "port": 16004, "password": "A0KMY7WT9NQR3X3F", "method": "aes-256-gcm", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "SG", "originalName": "🇸🇬 新加坡 046"}, {"type": "ss", "name": "🇸🇬 新加坡 047", "server": "ti3hyra4.slashdevslashnetslashtun.net", "port": 16009, "password": "G7DZ7DUSG9VI6CCN", "method": "aes-256-gcm", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "SG", "originalName": "🇸🇬 新加坡 047"}, {"type": "ss", "name": "🇸🇬 新加坡 048", "server": "ti3hyra4.slashdevslashnetslashtun.net", "port": 16015, "password": "LUPCT9VGXN2HELH2", "method": "aes-256-gcm", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "SG", "originalName": "🇸🇬 新加坡 048"}, {"type": "hysteria2", "name": "🇸🇬 新加坡 049", "server": "sg1.844300.xyz", "port": 30000, "password": "67d9d454-0d38-4eea-830d-6f9362d988c6", "auth": "67d9d454-0d38-4eea-830d-6f9362d988c6", "obfs": {"type": "", "password": ""}, "tls": {"enabled": true, "serverName": "sg1.844300.xyz", "alpn": ["h3"], "skipCertVerify": false}, "bandwidth": {"up": "", "down": ""}, "congestion": "bbr", "fastOpen": false, "lazy": false, "_sourceFormat": "base64", "detectedRegion": "SG", "originalName": "🇸🇬 新加坡 049"}, {"type": "hysteria2", "name": "🇸🇬 新加坡 050", "server": "sg4.844300.xyz", "port": 30000, "password": "67d9d454-0d38-4eea-830d-6f9362d988c6", "auth": "67d9d454-0d38-4eea-830d-6f9362d988c6", "obfs": {"type": "", "password": ""}, "tls": {"enabled": true, "serverName": "sg4.844300.xyz", "alpn": ["h3"], "skipCertVerify": false}, "bandwidth": {"up": "", "down": ""}, "congestion": "bbr", "fastOpen": false, "lazy": false, "_sourceFormat": "base64", "detectedRegion": "SG", "originalName": "🇸🇬 新加坡 050"}, {"type": "hysteria2", "name": "🇸🇬 新加坡 051", "server": "sg3.844300.xyz", "port": 30000, "password": "67d9d454-0d38-4eea-830d-6f9362d988c6", "auth": "67d9d454-0d38-4eea-830d-6f9362d988c6", "obfs": {"type": "", "password": ""}, "tls": {"enabled": true, "serverName": "sg3.844300.xyz", "alpn": ["h3"], "skipCertVerify": false}, "bandwidth": {"up": "", "down": ""}, "congestion": "bbr", "fastOpen": false, "lazy": false, "_sourceFormat": "base64", "detectedRegion": "SG", "originalName": "🇸🇬 新加坡 051"}, {"type": "ss", "name": "🇰🇷 韩国 001", "server": "p227.panda004.net", "port": 4857, "password": "qwerREWQ@@", "method": "aes-256-cfb", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "KR", "originalName": "🇰🇷 韩国 001"}, {"type": "hysteria2", "name": "🇰🇷 韩国 002", "server": "**************", "port": 44005, "password": "75e98355-345b-4413-8001-************", "auth": "75e98355-345b-4413-8001-************", "obfs": {"type": "", "password": ""}, "tls": {"enabled": true, "serverName": "www.bing.com", "alpn": ["h3"], "skipCertVerify": true}, "bandwidth": {"up": "", "down": ""}, "congestion": "bbr", "fastOpen": false, "lazy": false, "_sourceFormat": "base64", "detectedRegion": "KR", "originalName": "🇰🇷 韩国 002"}, {"type": "vless", "name": "🇰🇷 韩国 003", "server": "*************", "port": 80, "uuid": "53fa8faf-ba4b-4322-9c69-a3e5b1555049", "flow": "", "encryption": "none", "network": "ws", "tls": {"enabled": false, "serverName": "", "alpn": [], "fingerprint": ""}, "transport": {"path": "/Telegram:", "host": "reedfree8mahsang2.redorg.ir"}, "reality": {"enabled": false, "publicKey": "", "shortId": "", "spiderX": ""}, "_sourceFormat": "base64", "detectedRegion": "KR", "originalName": "🇰🇷 韩国 003"}, {"type": "ss", "name": "🇰🇷 韩国 004", "server": "************", "port": 443, "password": "yijian0503", "method": "aes-256-cfb", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "KR", "originalName": "🇰🇷 韩国 004"}, {"type": "vless", "name": "🇰🇷 韩国 005", "server": "************", "port": 8880, "uuid": "53fa8faf-ba4b-4322-9c69-a3e5b1555049", "flow": "", "encryption": "none", "network": "ws", "tls": {"enabled": false, "serverName": "", "alpn": [], "fingerprint": ""}, "transport": {"path": "/Telegram🇨🇳 @pgkj666 /?ed=2560", "host": "reedfree8mahsang2.redorg.ir"}, "reality": {"enabled": false, "publicKey": "", "shortId": "", "spiderX": ""}, "_sourceFormat": "base64", "detectedRegion": "KR", "originalName": "🇰🇷 韩国 005"}, {"type": "ss", "name": "🇰🇷 韩国 006", "server": "*************", "port": 4857, "password": "qwerREWQ@@", "method": "aes-256-cfb", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "KR", "originalName": "🇰🇷 韩国 006"}, {"type": "vless", "name": "🇰🇷 韩国 007", "server": "*************", "port": 8880, "uuid": "53fa8faf-ba4b-4322-9c69-a3e5b1555049", "flow": "", "encryption": "none", "network": "ws", "tls": {"enabled": false, "serverName": "", "alpn": [], "fingerprint": ""}, "transport": {"path": "/?ed=2560", "host": "reedfree8mahsang2.redorg.ir"}, "reality": {"enabled": false, "publicKey": "", "shortId": "", "spiderX": ""}, "_sourceFormat": "base64", "detectedRegion": "KR", "originalName": "🇰🇷 韩国 007"}, {"type": "vless", "name": "🇰🇷 韩国 008", "server": "************", "port": 80, "uuid": "53fa8faf-ba4b-4322-9c69-a3e5b1555049", "flow": "", "encryption": "none", "network": "ws", "tls": {"enabled": false, "serverName": "", "alpn": [], "fingerprint": ""}, "transport": {"path": "/Telegram: @vpnAndroid2/?ed=2560", "host": "reedfree8mahsang2.redorg.ir"}, "reality": {"enabled": false, "publicKey": "", "shortId": "", "spiderX": ""}, "_sourceFormat": "base64", "detectedRegion": "KR", "originalName": "🇰🇷 韩国 008"}, {"type": "hysteria2", "name": "🇰🇷 韩国 009", "server": "hg.yuanbaojc.site", "port": 30347, "password": "5cc61971-19b5-454a-a933-abb628ce7b80", "auth": "5cc61971-19b5-454a-a933-abb628ce7b80", "obfs": {"type": "", "password": ""}, "tls": {"enabled": true, "serverName": "hg.yuanbaojc.site", "alpn": ["h3"], "skipCertVerify": false}, "bandwidth": {"up": "", "down": ""}, "congestion": "bbr", "fastOpen": false, "lazy": false, "_sourceFormat": "base64", "detectedRegion": "KR", "originalName": "🇰🇷 韩国 009"}, {"type": "hysteria2", "name": "🇰🇷 韩国 010", "server": "hg.yuanbaojc.site", "port": 30349, "password": "5cc61971-19b5-454a-a933-abb628ce7b80", "auth": "5cc61971-19b5-454a-a933-abb628ce7b80", "obfs": {"type": "", "password": ""}, "tls": {"enabled": true, "serverName": "hg.yuanbaojc.site", "alpn": ["h3"], "skipCertVerify": false}, "bandwidth": {"up": "", "down": ""}, "congestion": "bbr", "fastOpen": false, "lazy": false, "_sourceFormat": "base64", "detectedRegion": "KR", "originalName": "🇰🇷 韩国 010"}, {"type": "hysteria2", "name": "🇰🇷 韩国 011", "server": "hg.yuanbaojc.site", "port": 30375, "password": "646d2022-4f69-4a6e-b142-d00e475b36e6", "auth": "646d2022-4f69-4a6e-b142-d00e475b36e6", "obfs": {"type": "", "password": ""}, "tls": {"enabled": true, "serverName": "hg.yuanbaojc.site", "alpn": ["h3"], "skipCertVerify": false}, "bandwidth": {"up": "", "down": ""}, "congestion": "bbr", "fastOpen": false, "lazy": false, "_sourceFormat": "base64", "detectedRegion": "KR", "originalName": "🇰🇷 韩国 011"}, {"type": "vless", "name": "🇰🇷 韩国 012", "server": "**************", "port": 8880, "uuid": "53fa8faf-ba4b-4322-9c69-a3e5b1555049", "flow": "", "encryption": "none", "network": "ws", "tls": {"enabled": false, "serverName": "", "alpn": [], "fingerprint": ""}, "transport": {"path": "/Telegram🇨🇳 @pgkj666 /?ed=2560", "host": "reedfree8mahsang2.redorg.ir"}, "reality": {"enabled": false, "publicKey": "", "shortId": "", "spiderX": ""}, "_sourceFormat": "base64", "detectedRegion": "KR", "originalName": "🇰🇷 韩国 012"}, {"type": "hysteria2", "name": "🇰🇷 韩国 013", "server": "hg.yuanbaojc.site", "port": 30353, "password": "5cc61971-19b5-454a-a933-abb628ce7b80", "auth": "5cc61971-19b5-454a-a933-abb628ce7b80", "obfs": {"type": "", "password": ""}, "tls": {"enabled": true, "serverName": "hg.yuanbaojc.site", "alpn": ["h3"], "skipCertVerify": false}, "bandwidth": {"up": "", "down": ""}, "congestion": "bbr", "fastOpen": false, "lazy": false, "_sourceFormat": "base64", "detectedRegion": "KR", "originalName": "🇰🇷 韩国 013"}, {"type": "ss", "name": "🇰🇷 韩国 014", "server": "p222.panda001.net", "port": 15098, "password": "qwerREWQ@@", "method": "aes-256-cfb", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "KR", "originalName": "🇰🇷 韩国 014"}, {"type": "hysteria2", "name": "🇰🇷 韩国 015", "server": "hg.yuanbaojc.site", "port": 30396, "password": "5cc61971-19b5-454a-a933-abb628ce7b80", "auth": "5cc61971-19b5-454a-a933-abb628ce7b80", "obfs": {"type": "", "password": ""}, "tls": {"enabled": true, "serverName": "hg.yuanbaojc.site", "alpn": ["h3"], "skipCertVerify": false}, "bandwidth": {"up": "", "down": ""}, "congestion": "bbr", "fastOpen": false, "lazy": false, "_sourceFormat": "base64", "detectedRegion": "KR", "originalName": "🇰🇷 韩国 015"}, {"type": "trojan", "name": "🇰🇷 韩国 016", "server": "cn2.cdn.xfltd-cdn.top", "port": 12041, "password": "7669a04d-a459-4c96-bc0e-fde51cb984f3", "network": "tcp", "tls": {"enabled": true, "serverName": "cn2.cdn.xfltd-cdn.top", "alpn": [], "fingerprint": "chrome", "skipCertVerify": true}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "KR", "originalName": "🇰🇷 韩国 016"}, {"type": "hysteria2", "name": "🇰🇷 韩国 017", "server": "hg.yuanbaojc.site", "port": 30308, "password": "5cc61971-19b5-454a-a933-abb628ce7b80", "auth": "5cc61971-19b5-454a-a933-abb628ce7b80", "obfs": {"type": "", "password": ""}, "tls": {"enabled": true, "serverName": "hg.yuanbaojc.site", "alpn": ["h3"], "skipCertVerify": false}, "bandwidth": {"up": "", "down": ""}, "congestion": "bbr", "fastOpen": false, "lazy": false, "_sourceFormat": "base64", "detectedRegion": "KR", "originalName": "🇰🇷 韩国 017"}, {"type": "hysteria2", "name": "🇰🇷 韩国 018", "server": "hg.yuanbaojc.site", "port": 30350, "password": "5cc61971-19b5-454a-a933-abb628ce7b80", "auth": "5cc61971-19b5-454a-a933-abb628ce7b80", "obfs": {"type": "", "password": ""}, "tls": {"enabled": true, "serverName": "hg.yuanbaojc.site", "alpn": ["h3"], "skipCertVerify": false}, "bandwidth": {"up": "", "down": ""}, "congestion": "bbr", "fastOpen": false, "lazy": false, "_sourceFormat": "base64", "detectedRegion": "KR", "originalName": "🇰🇷 韩国 018"}, {"type": "vless", "name": "🇰🇷 韩国 019", "server": "************", "port": 8880, "uuid": "53fa8faf-ba4b-4322-9c69-a3e5b1555049", "flow": "", "encryption": "none", "network": "ws", "tls": {"enabled": false, "serverName": "", "alpn": [], "fingerprint": ""}, "transport": {"path": "/Telegram🇨🇳 @MxlShare @WangCai2 /?ed=2560", "host": "reedfree8mahsang2.redorg.ir"}, "reality": {"enabled": false, "publicKey": "", "shortId": "", "spiderX": ""}, "_sourceFormat": "base64", "detectedRegion": "KR", "originalName": "🇰🇷 韩国 019"}, {"type": "hysteria2", "name": "🇰🇷 韩国 020", "server": "hg.yuanbaojc.site", "port": 30365, "password": "5cc61971-19b5-454a-a933-abb628ce7b80", "auth": "5cc61971-19b5-454a-a933-abb628ce7b80", "obfs": {"type": "", "password": ""}, "tls": {"enabled": true, "serverName": "hg.yuanbaojc.site", "alpn": ["h3"], "skipCertVerify": false}, "bandwidth": {"up": "", "down": ""}, "congestion": "bbr", "fastOpen": false, "lazy": false, "_sourceFormat": "base64", "detectedRegion": "KR", "originalName": "🇰🇷 韩国 020"}, {"type": "hysteria2", "name": "🇰🇷 韩国 021", "server": "hg.yuanbaojc.site", "port": 30306, "password": "5cc61971-19b5-454a-a933-abb628ce7b80", "auth": "5cc61971-19b5-454a-a933-abb628ce7b80", "obfs": {"type": "", "password": ""}, "tls": {"enabled": true, "serverName": "hg.yuanbaojc.site", "alpn": ["h3"], "skipCertVerify": false}, "bandwidth": {"up": "", "down": ""}, "congestion": "bbr", "fastOpen": false, "lazy": false, "_sourceFormat": "base64", "detectedRegion": "KR", "originalName": "🇰🇷 韩国 021"}, {"type": "hysteria2", "name": "🇰🇷 韩国 022", "server": "hg.yuanbaojc.site", "port": 30321, "password": "5cc61971-19b5-454a-a933-abb628ce7b80", "auth": "5cc61971-19b5-454a-a933-abb628ce7b80", "obfs": {"type": "", "password": ""}, "tls": {"enabled": true, "serverName": "hg.yuanbaojc.site", "alpn": ["h3"], "skipCertVerify": false}, "bandwidth": {"up": "", "down": ""}, "congestion": "bbr", "fastOpen": false, "lazy": false, "_sourceFormat": "base64", "detectedRegion": "KR", "originalName": "🇰🇷 韩国 022"}, {"type": "ss", "name": "🇰🇷 韩国 023", "server": "eepl2.d-h-h.de", "port": 42010, "password": "ed6c8eea-8503-43e1-9563-0a355b0edcac", "method": "chacha20-ietf-poly1305", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "KR", "originalName": "🇰🇷 韩国 023"}, {"type": "trojan", "name": "🇰🇷 韩国 024", "server": "*************", "port": 28548, "password": "RlzoEILU", "network": "tcp", "tls": {"enabled": true, "serverName": "cdn.egvra.cn", "alpn": [], "fingerprint": "chrome", "skipCertVerify": true}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "KR", "originalName": "🇰🇷 韩国 024"}, {"type": "trojan", "name": "🇰🇷 韩国 025", "server": "*************", "port": 28910, "password": "RlzoEILU", "network": "tcp", "tls": {"enabled": true, "serverName": "cdn.egvra.cn", "alpn": [], "fingerprint": "chrome", "skipCertVerify": true}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "KR", "originalName": "🇰🇷 韩国 025"}, {"type": "ss", "name": "🇰🇷 韩国 026", "server": "**********", "port": 443, "password": "yijian0503", "method": "aes-256-cfb", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "KR", "originalName": "🇰🇷 韩国 026"}, {"type": "ss", "name": "🇰🇷 韩国 027", "server": "p231.panda004.net", "port": 11389, "password": "qwerREWQ@@", "method": "aes-256-cfb", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "KR", "originalName": "🇰🇷 韩国 027"}, {"type": "trojan", "name": "🇰🇷 韩国 028", "server": "*************", "port": 47655, "password": "RlzoEILU", "network": "tcp", "tls": {"enabled": true, "serverName": "*************", "alpn": [], "fingerprint": "", "skipCertVerify": true}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "KR", "originalName": "🇰🇷 韩国 028"}, {"type": "ss", "name": "🇰🇷 韩国 029", "server": "sz.fanhua.art", "port": 24733, "password": "b86a48e8-542d-4b90-bb6e-ff0a3e7e4e97", "method": "aes-128-gcm", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "KR", "originalName": "🇰🇷 韩国 029"}, {"type": "trojan", "name": "🇰🇷 韩国 030", "server": "*************", "port": 44907, "password": "RlzoEILU", "network": "tcp", "tls": {"enabled": true, "serverName": "*************", "alpn": [], "fingerprint": "", "skipCertVerify": true}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "KR", "originalName": "🇰🇷 韩国 030"}, {"type": "ss", "name": "🇰🇷 韩国 031", "server": "eepl1.dhh114514.christmas", "port": 42010, "password": "ed6c8eea-8503-43e1-9563-0a355b0edcac", "method": "chacha20-ietf-poly1305", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "KR", "originalName": "🇰🇷 韩国 031"}, {"type": "ss", "name": "🇰🇷 韩国 032", "server": "************", "port": 443, "password": "yijian0503", "method": "aes-256-cfb", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "KR", "originalName": "🇰🇷 韩国 032"}, {"type": "hysteria2", "name": "🇰🇷 韩国 033", "server": "kr4.844300.xyz", "port": 30000, "password": "67d9d454-0d38-4eea-830d-6f9362d988c6", "auth": "67d9d454-0d38-4eea-830d-6f9362d988c6", "obfs": {"type": "", "password": ""}, "tls": {"enabled": true, "serverName": "kr4.844300.xyz", "alpn": ["h3"], "skipCertVerify": false}, "bandwidth": {"up": "", "down": ""}, "congestion": "bbr", "fastOpen": false, "lazy": false, "_sourceFormat": "base64", "detectedRegion": "KR", "originalName": "🇰🇷 韩国 033"}, {"type": "hysteria2", "name": "🇫🇷 法国 001", "server": "faguo.959555.xyz", "port": 10000, "password": "5415e20c-dbe6-46ff-9cc1-1c092184d801", "auth": "5415e20c-dbe6-46ff-9cc1-1c092184d801", "obfs": {"type": "", "password": ""}, "tls": {"enabled": true, "serverName": "faguo.959555.xyz", "alpn": ["h3"], "skipCertVerify": true}, "bandwidth": {"up": "", "down": ""}, "congestion": "bbr", "fastOpen": false, "lazy": false, "_sourceFormat": "base64", "detectedRegion": "FR", "originalName": "🇫🇷 法国 001"}, {"type": "vless", "name": "🇫🇷 法国 002", "server": "************", "port": 8080, "uuid": "4ea841c1-0dc1-4563-9f47-deba8407cb4e", "flow": "", "encryption": "none", "network": "ws", "tls": {"enabled": false, "serverName": "", "alpn": [], "fingerprint": ""}, "transport": {"path": "/?ed=2048", "host": "J9.oDOtZrHUoO.ZuLAIR.ORg."}, "reality": {"enabled": false, "publicKey": "", "shortId": "", "spiderX": ""}, "_sourceFormat": "base64", "detectedRegion": "FR", "originalName": "🇫🇷 法国 002"}, {"type": "vmess", "name": "🇫🇷 法国 003", "server": "free-relay.themars.top", "port": 37906, "uuid": "781807c4-37df-4da3-9942-c6e82032399a", "alterId": 0, "cipher": "auto", "network": "ws", "tls": {"enabled": false, "serverName": "www.cctv.com"}, "transport": {"path": "/cctv1.m3u8", "headers": {"Host": "www.cctv.com"}}, "_sourceFormat": "base64", "detectedRegion": "FR", "originalName": "ð«ð· æ³å½ 003"}, {"type": "vmess", "name": "🇫🇷 法国 004", "server": "free-relay.themars.top", "port": 39903, "uuid": "781807c4-37df-4da3-9942-c6e82032399a", "alterId": 0, "cipher": "auto", "network": "ws", "tls": {"enabled": false, "serverName": "www.cctv.com"}, "transport": {"path": "/cctv1.m3u8", "headers": {"Host": "www.cctv.com"}}, "_sourceFormat": "base64", "detectedRegion": "FR", "originalName": "ð«ð· æ³å½ 004"}, {"type": "vless", "name": "🇫🇷 法国 005", "server": "ns8.esfahansiman.com", "port": 1210, "uuid": "cef40844-5b22-472e-b019-a2bdeaaff4c8", "flow": "", "encryption": "none", "network": "grpc", "tls": {"enabled": true, "serverName": "ubuntu.com", "alpn": [], "fingerprint": "chrome"}, "transport": {"serviceName": "CANALTELEGRAM--jet_config--TELEGRAM--jet_config--CANALTELEGRAM--jet_config--TELEGRAM--jet_config--CANALTELEGRAM--jet_config--TELEGRAM--jet_config--CANALTELEGRAM--jet_config--TELEGRAM--jet_config", "mode": "gun"}, "reality": {"enabled": true, "publicKey": "YWfCdTnr4FAOMYTY2dLrMtQUokyxOGpPhYEEszPj20E", "shortId": "7fe29733", "spiderX": ""}, "_sourceFormat": "base64", "detectedRegion": "FR", "originalName": "🇫🇷 法国 005"}, {"type": "vless", "name": "🇫🇷 法国 006", "server": "ns6.esfahansiman.com", "port": 2030, "uuid": "cef40844-5b22-472e-b019-a2bdeaaff4c8", "flow": "", "encryption": "none", "network": "grpc", "tls": {"enabled": true, "serverName": "wordpress.org", "alpn": [], "fingerprint": "chrome"}, "transport": {"serviceName": "CANALTELEGRAM--jet_config--TELEGRAM--jet_config--CANALTELEGRAM--jet_config--TELEGRAM--jet_config--CANALTELEGRAM--jet_config", "mode": "gun"}, "reality": {"enabled": true, "publicKey": "YWfCdTnr4FAOMYTY2dLrMtQUokyxOGpPhYEEszPj20E", "shortId": "7fe29733", "spiderX": ""}, "_sourceFormat": "base64", "detectedRegion": "FR", "originalName": "🇫🇷 法国 006"}, {"type": "vless", "name": "🇫🇷 法国 007", "server": "tgju.org", "port": 8080, "uuid": "56652249-2e67-4429-b86c-3df8b838ad85", "flow": "", "encryption": "none", "network": "ws", "tls": {"enabled": false, "serverName": "", "alpn": [], "fingerprint": ""}, "transport": {"path": "/?ed=2048", "host": "4j.QBAo1g5z6k.ZulaIR.org."}, "reality": {"enabled": false, "publicKey": "", "shortId": "", "spiderX": ""}, "_sourceFormat": "base64", "detectedRegion": "FR", "originalName": "🇫🇷 法国 007"}, {"type": "vmess", "name": "🇫🇷 法国 008", "server": "free-relay.themars.top", "port": 49102, "uuid": "781807c4-37df-4da3-9942-c6e82032399a", "alterId": 0, "cipher": "auto", "network": "ws", "tls": {"enabled": false, "serverName": "www.cctv.com"}, "transport": {"path": "/cctv1.m3u8", "headers": {"Host": "www.cctv.com"}}, "_sourceFormat": "base64", "detectedRegion": "FR", "originalName": "ð«ð· æ³å½ 008"}, {"type": "hysteria2", "name": "🇫🇷 法国 009", "server": "*************", "port": 31180, "password": "dongtaiwang.com", "auth": "dongtaiwang.com", "obfs": {"type": "", "password": ""}, "tls": {"enabled": true, "serverName": "apple.com", "alpn": ["h3"], "skipCertVerify": true}, "bandwidth": {"up": "", "down": ""}, "congestion": "bbr", "fastOpen": false, "lazy": false, "_sourceFormat": "base64", "detectedRegion": "FR", "originalName": "🇫🇷 法国 009"}, {"type": "hysteria2", "name": "🇫🇷 法国 010", "server": "jiangzhifg.54264944.xyz", "port": 43999, "password": "6c510073-4ca8-423b-87a5-a6d73c0ca557", "auth": "6c510073-4ca8-423b-87a5-a6d73c0ca557", "obfs": {"type": "", "password": ""}, "tls": {"enabled": true, "serverName": "jiangzhifr.54264944.xyz", "alpn": ["h3"], "skipCertVerify": true}, "bandwidth": {"up": "", "down": ""}, "congestion": "bbr", "fastOpen": false, "lazy": false, "_sourceFormat": "base64", "detectedRegion": "FR", "originalName": "🇫🇷 法国 010"}, {"type": "hysteria2", "name": "🇩🇪 德国 001", "server": "**************", "port": 44001, "password": "75e98355-345b-4413-8001-************", "auth": "75e98355-345b-4413-8001-************", "obfs": {"type": "", "password": ""}, "tls": {"enabled": true, "serverName": "**************", "alpn": ["h3"], "skipCertVerify": true}, "bandwidth": {"up": "", "down": ""}, "congestion": "bbr", "fastOpen": false, "lazy": false, "_sourceFormat": "base64", "detectedRegion": "DE", "originalName": "🇩🇪 德国 001"}, {"type": "ss", "name": "🇩🇪 德国 002", "server": "************", "port": 28001, "password": "c86d483c-431f-41df-bb6b-c1dcebfc7401", "method": "aes-256-gcm", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "DE", "originalName": "🇩🇪 德国 002"}, {"type": "vmess", "name": "🇩🇪 德国 003", "server": "9351cca4-e594-cbfa-a6de-3c1ee2315694.castlepeakhospital.moe", "port": 80, "uuid": "14a8a0db-e922-453a-af8d-843af8734b3b", "alterId": 0, "cipher": "auto", "network": "ws", "tls": {"enabled": false, "serverName": "tms.dingtalk.com"}, "transport": {"path": "/", "headers": {"Host": "tms.dingtalk.com"}}, "_sourceFormat": "base64", "detectedRegion": "DE", "originalName": "ð©ðª å¾·å½ 003"}, {"type": "vless", "name": "🇩🇪 德国 004", "server": "**************", "port": 8880, "uuid": "53fa8faf-ba4b-4322-9c69-a3e5b1555049", "flow": "", "encryption": "none", "network": "ws", "tls": {"enabled": false, "serverName": "", "alpn": [], "fingerprint": ""}, "transport": {"path": "/?ed=2560", "host": "reedfree8mahsang2.redorg.ir"}, "reality": {"enabled": false, "publicKey": "", "shortId": "", "spiderX": ""}, "_sourceFormat": "base64", "detectedRegion": "DE", "originalName": "🇩🇪 德国 004"}, {"type": "vless", "name": "🇩🇪 德国 005", "server": "***********", "port": 80, "uuid": "53fa8faf-ba4b-4322-9c69-a3e5b1555049", "flow": "", "encryption": "none", "network": "ws", "tls": {"enabled": false, "serverName": "", "alpn": [], "fingerprint": ""}, "transport": {"path": "/Telegram:@vpnAndroid2/?ed=2560", "host": "reedfree8mahsang2.redorg.ir"}, "reality": {"enabled": false, "publicKey": "", "shortId": "", "spiderX": ""}, "_sourceFormat": "base64", "detectedRegion": "DE", "originalName": "🇩🇪 德国 005"}, {"type": "vless", "name": "🇩🇪 德国 006", "server": "***********", "port": 8880, "uuid": "53fa8faf-ba4b-4322-9c69-a3e5b1555049", "flow": "", "encryption": "none", "network": "ws", "tls": {"enabled": false, "serverName": "", "alpn": [], "fingerprint": ""}, "transport": {"path": "/Telegram🇨🇳 @pgkj666 /?ed=2560", "host": "reedfree8mahsang2.redorg.ir"}, "reality": {"enabled": false, "publicKey": "", "shortId": "", "spiderX": ""}, "_sourceFormat": "base64", "detectedRegion": "DE", "originalName": "🇩🇪 德国 006"}, {"type": "vless", "name": "🇩🇪 德国 007", "server": "***********", "port": 8880, "uuid": "53fa8faf-ba4b-4322-9c69-a3e5b1555049", "flow": "", "encryption": "none", "network": "ws", "tls": {"enabled": false, "serverName": "", "alpn": [], "fingerprint": ""}, "transport": {"path": "/?ed=2560", "host": "reedfree8mahsang2.redorg.ir"}, "reality": {"enabled": false, "publicKey": "", "shortId": "", "spiderX": ""}, "_sourceFormat": "base64", "detectedRegion": "DE", "originalName": "🇩🇪 德国 007"}, {"type": "hysteria2", "name": "🇩🇪 德国 008", "server": "dg.yuanbaojc.site", "port": 30399, "password": "77ec4317-0eb2-4403-8baa-5090b0fcc8a1", "auth": "77ec4317-0eb2-4403-8baa-5090b0fcc8a1", "obfs": {"type": "", "password": ""}, "tls": {"enabled": true, "serverName": "dg.yuanbaojc.site", "alpn": ["h3"], "skipCertVerify": false}, "bandwidth": {"up": "", "down": ""}, "congestion": "bbr", "fastOpen": false, "lazy": false, "_sourceFormat": "base64", "detectedRegion": "DE", "originalName": "🇩🇪 德国 008"}, {"type": "trojan", "name": "🇩🇪 德国 009", "server": "f10021.ylxblkyndjj.sbs", "port": 43654, "password": "74b001ba-7020-443d-b33b-5a403a156b39", "network": "tcp", "tls": {"enabled": true, "serverName": "de01.ckcloud.info", "alpn": [], "fingerprint": "", "skipCertVerify": true}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "DE", "originalName": "🇩🇪 德国 009"}, {"type": "hysteria2", "name": "🇩🇪 德国 010", "server": "dg.yuanbaojc.site", "port": 30330, "password": "646d2022-4f69-4a6e-b142-d00e475b36e6", "auth": "646d2022-4f69-4a6e-b142-d00e475b36e6", "obfs": {"type": "", "password": ""}, "tls": {"enabled": true, "serverName": "dg.yuanbaojc.site", "alpn": ["h3"], "skipCertVerify": false}, "bandwidth": {"up": "", "down": ""}, "congestion": "bbr", "fastOpen": false, "lazy": false, "_sourceFormat": "base64", "detectedRegion": "DE", "originalName": "🇩🇪 德国 010"}, {"type": "vless", "name": "🇩🇪 德国 011", "server": "***********0", "port": 8880, "uuid": "53fa8faf-ba4b-4322-9c69-a3e5b1555049", "flow": "", "encryption": "none", "network": "ws", "tls": {"enabled": false, "serverName": "", "alpn": [], "fingerprint": ""}, "transport": {"path": "/Telegram🇨🇳 @pgkj666 /?ed=2560", "host": "reedfree8mahsang2.redorg.ir"}, "reality": {"enabled": false, "publicKey": "", "shortId": "", "spiderX": ""}, "_sourceFormat": "base64", "detectedRegion": "DE", "originalName": "🇩🇪 德国 011"}, {"type": "vless", "name": "🇩🇪 德国 012", "server": "************", "port": 8880, "uuid": "53fa8faf-ba4b-4322-9c69-a3e5b1555049", "flow": "", "encryption": "none", "network": "ws", "tls": {"enabled": false, "serverName": "", "alpn": [], "fingerprint": ""}, "transport": {"path": "/TelegramU0001F1E8U0001F1F3 @MxlShare @WangCai2 /?ed=2560", "host": "reedfree8mahsang2.redorg.ir"}, "reality": {"enabled": false, "publicKey": "", "shortId": "", "spiderX": ""}, "_sourceFormat": "base64", "detectedRegion": "DE", "originalName": "🇩🇪 德国 012"}, {"type": "vless", "name": "🇩🇪 德国 013", "server": "***********", "port": 8880, "uuid": "53fa8faf-ba4b-4322-9c69-a3e5b1555049", "flow": "", "encryption": "none", "network": "ws", "tls": {"enabled": false, "serverName": "", "alpn": [], "fingerprint": ""}, "transport": {"path": "/TelegramU0001F1E8U0001F1F3 @MxlShare @WangCai2 /?ed=2560", "host": "reedfree8mahsang2.redorg.ir"}, "reality": {"enabled": false, "publicKey": "", "shortId": "", "spiderX": ""}, "_sourceFormat": "base64", "detectedRegion": "DE", "originalName": "🇩🇪 德国 013"}, {"type": "vless", "name": "🇩🇪 德国 014", "server": "************", "port": 8880, "uuid": "53fa8faf-ba4b-4322-9c69-a3e5b1555049", "flow": "", "encryption": "none", "network": "ws", "tls": {"enabled": false, "serverName": "", "alpn": [], "fingerprint": ""}, "transport": {"path": "/?ed=2560", "host": "reedfree8mahsang2.redorg.ir"}, "reality": {"enabled": false, "publicKey": "", "shortId": "", "spiderX": ""}, "_sourceFormat": "base64", "detectedRegion": "DE", "originalName": "🇩🇪 德国 014"}, {"type": "vless", "name": "🇩🇪 德国 015", "server": "***********", "port": 8880, "uuid": "53fa8faf-ba4b-4322-9c69-a3e5b1555049", "flow": "", "encryption": "none", "network": "ws", "tls": {"enabled": false, "serverName": "", "alpn": [], "fingerprint": ""}, "transport": {"path": "/Telegram🇨🇳 @MxlShare @WangCai2 /?ed=2560", "host": "reedfree8mahsang2.redorg.ir"}, "reality": {"enabled": false, "publicKey": "", "shortId": "", "spiderX": ""}, "_sourceFormat": "base64", "detectedRegion": "DE", "originalName": "🇩🇪 德国 015"}, {"type": "hysteria2", "name": "🇩🇪 德国 016", "server": "dg.yuanbaojc.site", "port": 30334, "password": "5cc61971-19b5-454a-a933-abb628ce7b80", "auth": "5cc61971-19b5-454a-a933-abb628ce7b80", "obfs": {"type": "", "password": ""}, "tls": {"enabled": true, "serverName": "dg.yuanbaojc.site", "alpn": ["h3"], "skipCertVerify": false}, "bandwidth": {"up": "", "down": ""}, "congestion": "bbr", "fastOpen": false, "lazy": false, "_sourceFormat": "base64", "detectedRegion": "DE", "originalName": "🇩🇪 德国 016"}, {"type": "vmess", "name": "🇩🇪 德国 017", "server": "tls.08.node-for-bigairport.win", "port": 443, "uuid": "c69374da-2208-4cbd-b81e-cdf88b5e7f53", "alterId": 0, "cipher": "auto", "network": "ws", "tls": {"enabled": false, "serverName": "tls.08.node-for-bigairport.win"}, "transport": {"path": "/", "headers": {"Host": "tls.08.node-for-bigairport.win"}}, "_sourceFormat": "base64", "detectedRegion": "DE", "originalName": "ð©ðª å¾·å½ 017"}, {"type": "vless", "name": "🇩🇪 德国 018", "server": "************", "port": 8880, "uuid": "53fa8faf-ba4b-4322-9c69-a3e5b1555049", "flow": "", "encryption": "none", "network": "ws", "tls": {"enabled": false, "serverName": "", "alpn": [], "fingerprint": ""}, "transport": {"path": "/Telegram🇨🇳 @MxlShare @WangCai2 /?ed=2560", "host": "reedfree8mahsang2.redorg.ir"}, "reality": {"enabled": false, "publicKey": "", "shortId": "", "spiderX": ""}, "_sourceFormat": "base64", "detectedRegion": "DE", "originalName": "🇩🇪 德国 018"}, {"type": "vless", "name": "🇩🇪 德国 019", "server": "*************", "port": 8880, "uuid": "53fa8faf-ba4b-4322-9c69-a3e5b1555049", "flow": "", "encryption": "none", "network": "ws", "tls": {"enabled": false, "serverName": "", "alpn": [], "fingerprint": ""}, "transport": {"path": "/Telegram@ShadowProxy66/?ed=2560", "host": "reedfree8mahsang2.redorg.ir"}, "reality": {"enabled": false, "publicKey": "", "shortId": "", "spiderX": ""}, "_sourceFormat": "base64", "detectedRegion": "DE", "originalName": "🇩🇪 德国 019"}, {"type": "vless", "name": "🇩🇪 德国 020", "server": "************", "port": 8880, "uuid": "53fa8faf-ba4b-4322-9c69-a3e5b1555049", "flow": "", "encryption": "none", "network": "ws", "tls": {"enabled": false, "serverName": "", "alpn": [], "fingerprint": ""}, "transport": {"path": "/Telegram🇨🇳 @MxlShare @WangCai2 /?ed=2560", "host": "reedfree8mahsang2.redorg.ir"}, "reality": {"enabled": false, "publicKey": "", "shortId": "", "spiderX": ""}, "_sourceFormat": "base64", "detectedRegion": "DE", "originalName": "🇩🇪 德国 020"}, {"type": "ss", "name": "🇩🇪 德国 021", "server": "eepl2.d-h-h.de", "port": 42015, "password": "ed6c8eea-8503-43e1-9563-0a355b0edcac", "method": "chacha20-ietf-poly1305", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "DE", "originalName": "🇩🇪 德国 021"}, {"type": "vless", "name": "🇩🇪 德国 022", "server": "**************", "port": 8880, "uuid": "53fa8faf-ba4b-4322-9c69-a3e5b1555049", "flow": "", "encryption": "none", "network": "ws", "tls": {"enabled": false, "serverName": "", "alpn": [], "fingerprint": ""}, "transport": {"path": "/?ed=2560", "host": "reedfree8mahsang2.redorg.ir"}, "reality": {"enabled": false, "publicKey": "", "shortId": "", "spiderX": ""}, "_sourceFormat": "base64", "detectedRegion": "DE", "originalName": "🇩🇪 德国 022"}, {"type": "vless", "name": "🇩🇪 德国 023", "server": "**************", "port": 8880, "uuid": "53fa8faf-ba4b-4322-9c69-a3e5b1555049", "flow": "", "encryption": "none", "network": "ws", "tls": {"enabled": false, "serverName": "", "alpn": [], "fingerprint": ""}, "transport": {"path": "/?ed=2560", "host": "reedfree8mahsang2.redorg.ir"}, "reality": {"enabled": false, "publicKey": "", "shortId": "", "spiderX": ""}, "_sourceFormat": "base64", "detectedRegion": "DE", "originalName": "🇩🇪 德国 023"}, {"type": "vless", "name": "🇩🇪 德国 024", "server": "************", "port": 80, "uuid": "53fa8faf-ba4b-4322-9c69-a3e5b1555049", "flow": "", "encryption": "none", "network": "ws", "tls": {"enabled": false, "serverName": "", "alpn": [], "fingerprint": ""}, "transport": {"path": "/Telegram: @vpnAndroid2/?ed=2560", "host": "reedfree8mahsang2.redorg.ir"}, "reality": {"enabled": false, "publicKey": "", "shortId": "", "spiderX": ""}, "_sourceFormat": "base64", "detectedRegion": "DE", "originalName": "🇩🇪 德国 024"}, {"type": "vless", "name": "🇩🇪 德国 025", "server": "*************", "port": 80, "uuid": "53fa8faf-ba4b-4322-9c69-a3e5b1555049", "flow": "", "encryption": "none", "network": "ws", "tls": {"enabled": false, "serverName": "", "alpn": [], "fingerprint": ""}, "transport": {"path": "/Telegram:", "host": "reedfree8mahsang2.redorg.ir"}, "reality": {"enabled": false, "publicKey": "", "shortId": "", "spiderX": ""}, "_sourceFormat": "base64", "detectedRegion": "DE", "originalName": "🇩🇪 德国 025"}, {"type": "ss", "name": "🇩🇪 德国 026", "server": "eepl2.d-h-h.de", "port": 41001, "password": "ed6c8eea-8503-43e1-9563-0a355b0edcac", "method": "chacha20-ietf-poly1305", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "DE", "originalName": "🇩🇪 德国 026"}, {"type": "vless", "name": "🇩🇪 德国 027", "server": "***********", "port": 8880, "uuid": "53fa8faf-ba4b-4322-9c69-a3e5b1555049", "flow": "", "encryption": "none", "network": "ws", "tls": {"enabled": false, "serverName": "", "alpn": [], "fingerprint": ""}, "transport": {"path": "/Telegram🇨🇳 @pgkj666 /?ed=2560", "host": "reedfree8mahsang2.redorg.ir"}, "reality": {"enabled": false, "publicKey": "", "shortId": "", "spiderX": ""}, "_sourceFormat": "base64", "detectedRegion": "DE", "originalName": "🇩🇪 德国 027"}, {"type": "vless", "name": "🇩🇪 德国 028", "server": "************", "port": 8880, "uuid": "53fa8faf-ba4b-4322-9c69-a3e5b1555049", "flow": "", "encryption": "none", "network": "ws", "tls": {"enabled": false, "serverName": "", "alpn": [], "fingerprint": ""}, "transport": {"path": "/Telegram🇨🇳 @pgkj666 /?ed=2560", "host": "reedfree8mahsang2.redorg.ir"}, "reality": {"enabled": false, "publicKey": "", "shortId": "", "spiderX": ""}, "_sourceFormat": "base64", "detectedRegion": "DE", "originalName": "🇩🇪 德国 028"}, {"type": "vless", "name": "🇩🇪 德国 029", "server": "************", "port": 8443, "uuid": "0b193b9c-6ad3-41a2-83c0-d3de9b86b70e", "flow": "", "encryption": "none", "network": "ws", "tls": {"enabled": true, "serverName": "frbn.musics-fa.ru", "alpn": ["h3", "h2", "http/1.1"], "fingerprint": "chrome"}, "transport": {"path": "/telegram----------------v2ryNG01----v2ryNG01----v2ryNG01---v2ryNG01---------------v2ryNG01---v2ryNG01--v2ryNG01----v2ryNG01----v2ryNG01---v2ryNG01---v2ryNG01---v2ryNG01v2ryNG01----v2ryNG01----v2ryNG01---v2ryNG01---v2ryNG01---v2ryNG01?ed=2048", "host": "frbn.musics-fa.ru"}, "reality": {"enabled": false, "publicKey": "", "shortId": "", "spiderX": ""}, "_sourceFormat": "base64", "detectedRegion": "DE", "originalName": "🇩🇪 德国 029"}, {"type": "trojan", "name": "🇩🇪 德国 030", "server": "cm1.d-h-h.in", "port": 41019, "password": "ed6c8eea-8503-43e1-9563-0a355b0edcac", "network": "tcp", "tls": {"enabled": true, "serverName": "v1-de1.776688.best", "alpn": [], "fingerprint": "chrome", "skipCertVerify": true}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "DE", "originalName": "🇩🇪 德国 030"}, {"type": "vmess", "name": "🇩🇪 德国 031", "server": "tls.12.node-for-bigairport.win", "port": 33443, "uuid": "b71f9e84-86c9-49c4-b5f4-b33b35ee7410", "alterId": 0, "cipher": "auto", "network": "ws", "tls": {"enabled": false, "serverName": "tls.12.node-for-bigairport.win"}, "transport": {"path": "/", "headers": {"Host": "tls.12.node-for-bigairport.win"}}, "_sourceFormat": "base64", "detectedRegion": "DE", "originalName": "ð©ðª å¾·å½ 031"}, {"type": "vmess", "name": "🇩🇪 德国 032", "server": "tls.03.node-for-bigairport.win", "port": 12443, "uuid": "c69374da-2208-4cbd-b81e-cdf88b5e7f53", "alterId": 0, "cipher": "auto", "network": "ws", "tls": {"enabled": false, "serverName": "tls.03.node-for-bigairport.win"}, "transport": {"path": "/", "headers": {"Host": "tls.03.node-for-bigairport.win"}}, "_sourceFormat": "base64", "detectedRegion": "DE", "originalName": "ð©ðª å¾·å½ 032"}, {"type": "vless", "name": "🇩🇪 德国 033", "server": "*************", "port": 443, "uuid": "b8bd42a9-551f-419d-b70d-4aefdd2cb074", "flow": "xtls-rprx-vision", "encryption": "none", "network": "tcp", "tls": {"enabled": true, "serverName": "yfnl1.xn--4gq62f52gppi29k.com", "alpn": [], "fingerprint": "chrome"}, "transport": {}, "reality": {"enabled": false, "publicKey": "", "shortId": "", "spiderX": ""}, "_sourceFormat": "base64", "detectedRegion": "DE", "originalName": "🇩🇪 德国 033"}, {"type": "ss", "name": "🇲🇾 马来西亚 001", "server": "*************", "port": 29010, "password": "c86d483c-431f-41df-bb6b-c1dcebfc7401", "method": "aes-256-gcm", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "MY", "originalName": "🇲🇾 马来西亚 001"}, {"type": "trojan", "name": "🇲🇾 马来西亚 002", "server": "**************", "port": 21079, "password": "2b1ed981-6547-4094-998b-06a3323d6f6c", "network": "tcp", "tls": {"enabled": true, "serverName": "**************", "alpn": [], "fingerprint": "", "skipCertVerify": true}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "MY", "originalName": "🇲🇾 马来西亚 002"}, {"type": "trojan", "name": "🇲🇾 马来西亚 003", "server": "**************", "port": 21181, "password": "2b1ed981-6547-4094-998b-06a3323d6f6c", "network": "tcp", "tls": {"enabled": true, "serverName": "**************", "alpn": [], "fingerprint": "", "skipCertVerify": true}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "MY", "originalName": "🇲🇾 马来西亚 003"}, {"type": "trojan", "name": "🇲🇾 马来西亚 004", "server": "**************", "port": 21031, "password": "2b1ed981-6547-4094-998b-06a3323d6f6c", "network": "tcp", "tls": {"enabled": true, "serverName": "**************", "alpn": [], "fingerprint": "chrome", "skipCertVerify": true}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "MY", "originalName": "🇲🇾 马来西亚 004"}, {"type": "ss", "name": "🇲🇾 马来西亚 005", "server": "************", "port": 29010, "password": "b8fa2e30-a210-4d8b-bb1e-bf42ea9c9e7e", "method": "aes-256-gcm", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "MY", "originalName": "🇲🇾 马来西亚 005"}, {"type": "ss", "name": "🇹🇭 泰国 001", "server": "*************", "port": 29015, "password": "c86d483c-431f-41df-bb6b-c1dcebfc7401", "method": "aes-256-gcm", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "TH", "originalName": "🇹🇭 泰国 001"}, {"type": "trojan", "name": "🇹🇭 泰国 002", "server": "cn2.cdn.xfltd-cdn.top", "port": 12076, "password": "7669a04d-a459-4c96-bc0e-fde51cb984f3", "network": "tcp", "tls": {"enabled": true, "serverName": "cn2.cdn.xfltd-cdn.top", "alpn": [], "fingerprint": "", "skipCertVerify": true}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "TH", "originalName": "🇹🇭 泰国 003"}, {"type": "vmess", "name": "🌐 其他 001", "server": "2fea6447-81c4-2d7d-d592-a4f729209777.7head.icu", "port": 20006, "uuid": "14a8a0db-e922-453a-af8d-843af8734b3b", "alterId": 0, "cipher": "auto", "network": "ws", "tls": {"enabled": false, "serverName": "tms.dingtalk.com"}, "transport": {"path": "/", "headers": {"Host": "tms.dingtalk.com"}}, "_sourceFormat": "base64", "detectedRegion": "OTHER", "originalName": "ð¹ð­ æ³°å½ 002"}, {"type": "vmess", "name": "🌐 其他 002", "server": "2fea6447-81c4-2d7d-d592-a4f729209777.7head.icu", "port": 20000, "uuid": "14a8a0db-e922-453a-af8d-843af8734b3b", "alterId": 0, "cipher": "auto", "network": "ws", "tls": {"enabled": false, "serverName": "tms.dingtalk.com"}, "transport": {"path": "/", "headers": {"Host": "tms.dingtalk.com"}}, "_sourceFormat": "base64", "detectedRegion": "OTHER", "originalName": "ð å¶ä» 001"}, {"type": "vmess", "name": "🌐 其他 003", "server": "2fea6447-81c4-2d7d-d592-a4f729209777.7head.icu", "port": 20014, "uuid": "14a8a0db-e922-453a-af8d-843af8734b3b", "alterId": 0, "cipher": "auto", "network": "ws", "tls": {"enabled": false, "serverName": "tms.dingtalk.com"}, "transport": {"path": "/", "headers": {"Host": "tms.dingtalk.com"}}, "_sourceFormat": "base64", "detectedRegion": "OTHER", "originalName": "ð å¶ä» 002"}, {"type": "vmess", "name": "🌐 其他 004", "server": "2fea6447-81c4-2d7d-d592-a4f729209777.7head.icu", "port": 20046, "uuid": "14a8a0db-e922-453a-af8d-843af8734b3b", "alterId": 0, "cipher": "auto", "network": "ws", "tls": {"enabled": false, "serverName": "tms.dingtalk.com"}, "transport": {"path": "/", "headers": {"Host": "tms.dingtalk.com"}}, "_sourceFormat": "base64", "detectedRegion": "OTHER", "originalName": "ð å¶ä» 003"}, {"type": "vless", "name": "🌐 其他 005", "server": "*************", "port": 8880, "uuid": "53fa8faf-ba4b-4322-9c69-a3e5b1555049", "flow": "", "encryption": "none", "network": "ws", "tls": {"enabled": false, "serverName": "", "alpn": [], "fingerprint": ""}, "transport": {"path": "/Telegram🇨🇳 @WangCai2 /?ed=2560", "host": "reedfree8mahsang2.redorg.ir"}, "reality": {"enabled": false, "publicKey": "", "shortId": "", "spiderX": ""}, "_sourceFormat": "base64", "detectedRegion": "OTHER", "originalName": "🌐 其他 004"}, {"type": "ss", "name": "🌐 其他 006", "server": "neweur.upperlay.xyz", "port": 637, "password": "763bf612-4c66-4fd4-b54b-5349bdea6bca", "method": "aes-128-gcm", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "OTHER", "originalName": "🌐 其他 005"}, {"type": "vmess", "name": "🌐 其他 007", "server": "2fea6447-81c4-2d7d-d592-a4f729209777.7head.icu", "port": 20032, "uuid": "14a8a0db-e922-453a-af8d-843af8734b3b", "alterId": 0, "cipher": "auto", "network": "ws", "tls": {"enabled": false, "serverName": "tms.dingtalk.com"}, "transport": {"path": "/", "headers": {"Host": "tms.dingtalk.com"}}, "_sourceFormat": "base64", "detectedRegion": "OTHER", "originalName": "ð å¶ä» 006"}, {"type": "vmess", "name": "🌐 其他 008", "server": "2fea6447-81c4-2d7d-d592-a4f729209777.7head.icu", "port": 20030, "uuid": "14a8a0db-e922-453a-af8d-843af8734b3b", "alterId": 0, "cipher": "auto", "network": "ws", "tls": {"enabled": false, "serverName": "tms.dingtalk.com"}, "transport": {"path": "/", "headers": {"Host": "tms.dingtalk.com"}}, "_sourceFormat": "base64", "detectedRegion": "OTHER", "originalName": "ð å¶ä» 007"}, {"type": "vless", "name": "🌐 其他 009", "server": "**************", "port": 8880, "uuid": "53fa8faf-ba4b-4322-9c69-a3e5b1555049", "flow": "", "encryption": "none", "network": "ws", "tls": {"enabled": false, "serverName": "", "alpn": [], "fingerprint": ""}, "transport": {"path": "/?ed=2560", "host": "reedfree8mahsang2.redorg.ir"}, "reality": {"enabled": false, "publicKey": "", "shortId": "", "spiderX": ""}, "_sourceFormat": "base64", "detectedRegion": "OTHER", "originalName": "🌐 其他 008"}, {"type": "ss", "name": "🌐 其他 010", "server": "************", "port": 18007, "password": "c86d483c-431f-41df-bb6b-c1dcebfc7401", "method": "aes-256-gcm", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "OTHER", "originalName": "🌐 其他 009"}, {"type": "vmess", "name": "🌐 其他 011", "server": "*************", "port": 30825, "uuid": "cbb3f877-d1fb-344c-87a9-d153bffd5484", "alterId": 2, "cipher": "auto", "network": "ws", "tls": {"enabled": false, "serverName": "baidu.com"}, "transport": {"path": "/oooo", "headers": {"Host": "baidu.com"}}, "_sourceFormat": "base64", "detectedRegion": "OTHER", "originalName": "ð å¶ä» 010"}, {"type": "vless", "name": "🌐 其他 012", "server": "************", "port": 8880, "uuid": "53fa8faf-ba4b-4322-9c69-a3e5b1555049", "flow": "", "encryption": "none", "network": "ws", "tls": {"enabled": false, "serverName": "", "alpn": [], "fingerprint": ""}, "transport": {"path": "/Telegram🇨🇳 @pgkj666 /?ed=2560", "host": "reedfree8mahsang2.redorg.ir"}, "reality": {"enabled": false, "publicKey": "", "shortId": "", "spiderX": ""}, "_sourceFormat": "base64", "detectedRegion": "OTHER", "originalName": "🌐 其他 011"}, {"type": "vless", "name": "🌐 其他 013", "server": "**************", "port": 8880, "uuid": "53fa8faf-ba4b-4322-9c69-a3e5b1555049", "flow": "", "encryption": "none", "network": "ws", "tls": {"enabled": false, "serverName": "", "alpn": [], "fingerprint": ""}, "transport": {"path": "/Telegram🇨🇳 @pgkj666 /?ed=2560", "host": "reedfree8mahsang2.redorg.ir"}, "reality": {"enabled": false, "publicKey": "", "shortId": "", "spiderX": ""}, "_sourceFormat": "base64", "detectedRegion": "OTHER", "originalName": "🌐 其他 012"}, {"type": "vmess", "name": "🌐 其他 014", "server": "sslvpn.51job.com", "port": 1443, "uuid": "a6a0d901-67e9-460a-90b5-634c5c4f9782", "alterId": 64, "cipher": "auto", "network": "ws", "tls": {"enabled": true, "serverName": "centos7"}, "transport": {"path": "/634c5c4f9782", "headers": {"Host": "centos7"}}, "_sourceFormat": "base64", "detectedRegion": "OTHER", "originalName": "ð å¶ä» 013"}, {"type": "vmess", "name": "🌐 其他 015", "server": "tk.hzlt.tkddns.xyz", "port": 22641, "uuid": "98e96c9f-4bb3-39d4-9a2c-fac04257f7c7", "alterId": 2, "cipher": "auto", "network": "ws", "tls": {"enabled": true, "serverName": "zxjp-a.tkong.cc"}, "transport": {"path": "/", "headers": {"Host": "zxjp-a.tkong.cc"}}, "_sourceFormat": "base64", "detectedRegion": "OTHER", "originalName": "ð å¶ä» 014"}, {"type": "vmess", "name": "🌐 其他 016", "server": "2fea6447-81c4-2d7d-d592-a4f729209777.7head.icu", "port": 20012, "uuid": "14a8a0db-e922-453a-af8d-843af8734b3b", "alterId": 0, "cipher": "auto", "network": "ws", "tls": {"enabled": false, "serverName": "tms.dingtalk.com"}, "transport": {"path": "/", "headers": {"Host": "tms.dingtalk.com"}}, "_sourceFormat": "base64", "detectedRegion": "OTHER", "originalName": "ð å¶ä» 015"}, {"type": "vmess", "name": "🌐 其他 017", "server": "2fea6447-81c4-2d7d-d592-a4f729209777.7head.icu", "port": 20036, "uuid": "14a8a0db-e922-453a-af8d-843af8734b3b", "alterId": 0, "cipher": "auto", "network": "ws", "tls": {"enabled": false, "serverName": "tms.dingtalk.com"}, "transport": {"path": "/", "headers": {"Host": "tms.dingtalk.com"}}, "_sourceFormat": "base64", "detectedRegion": "OTHER", "originalName": "ð å¶ä» 016"}, {"type": "vmess", "name": "🌐 其他 018", "server": "2fea6447-81c4-2d7d-d592-a4f729209777.7head.icu", "port": 20024, "uuid": "14a8a0db-e922-453a-af8d-843af8734b3b", "alterId": 0, "cipher": "auto", "network": "ws", "tls": {"enabled": false, "serverName": "tms.dingtalk.com"}, "transport": {"path": "/", "headers": {"Host": "tms.dingtalk.com"}}, "_sourceFormat": "base64", "detectedRegion": "OTHER", "originalName": "ð å¶ä» 017"}, {"type": "vless", "name": "🌐 其他 019", "server": "************", "port": 8880, "uuid": "53fa8faf-ba4b-4322-9c69-a3e5b1555049", "flow": "", "encryption": "none", "network": "ws", "tls": {"enabled": false, "serverName": "", "alpn": [], "fingerprint": ""}, "transport": {"path": "/Telegram🇨🇳 @pgkj666 /?ed=2560", "host": "reedfree8mahsang2.redorg.ir"}, "reality": {"enabled": false, "publicKey": "", "shortId": "", "spiderX": ""}, "_sourceFormat": "base64", "detectedRegion": "OTHER", "originalName": "🌐 其他 018"}, {"type": "ssr", "name": "🌐 其他 020", "server": "9988.mt.mt5888.top", "port": 41115, "password": "mantouyun888", "method": "dummy", "protocol": "auth_chain_a", "protocolParam": "", "obfs": "plain", "obfsParam": "cbcdc8198.microsoft.com", "group": "", "_sourceFormat": "base64", "detectedRegion": "OTHER", "originalName": "ð å¶ä» 019"}, {"type": "ssr", "name": "🌐 其他 021", "server": "88gg.mt.mt5888.top", "port": 44003, "password": "mantouyun888", "method": "dummy", "protocol": "auth_chain_a", "protocolParam": "", "obfs": "plain", "obfsParam": "cbcdc8198.microsoft.com", "group": "", "_sourceFormat": "base64", "detectedRegion": "OTHER", "originalName": "ð å¶ä» 020"}, {"type": "vmess", "name": "🌐 其他 022", "server": "2fea6447-81c4-2d7d-d592-a4f729209777.7head.icu", "port": 20022, "uuid": "14a8a0db-e922-453a-af8d-843af8734b3b", "alterId": 0, "cipher": "auto", "network": "ws", "tls": {"enabled": false, "serverName": "tms.dingtalk.com"}, "transport": {"path": "/", "headers": {"Host": "tms.dingtalk.com"}}, "_sourceFormat": "base64", "detectedRegion": "OTHER", "originalName": "ð å¶ä» 021"}, {"type": "vmess", "name": "🌐 其他 023", "server": "**************", "port": 19700, "uuid": "db1b5367-92d7-4337-90c6-b3b9955d02ba", "alterId": 0, "cipher": "auto", "network": "ws", "tls": {"enabled": false, "serverName": "tms.dingtalk.com"}, "transport": {"path": "/", "headers": {"Host": "tms.dingtalk.com"}}, "_sourceFormat": "base64", "detectedRegion": "OTHER", "originalName": "ð å¶ä» 022"}, {"type": "ssr", "name": "🌐 其他 024", "server": "88gg.mt.mt5888.top", "port": 44006, "password": "mantouyun888", "method": "dummy", "protocol": "auth_chain_a", "protocolParam": "", "obfs": "plain", "obfsParam": "cbcdc8198.microsoft.com", "group": "", "_sourceFormat": "base64", "detectedRegion": "OTHER", "originalName": "ð å¶ä» 023"}, {"type": "ssr", "name": "🌐 其他 025", "server": "88gg.mt.mt5888.top", "port": 44005, "password": "mantouyun888", "method": "dummy", "protocol": "auth_chain_a", "protocolParam": "", "obfs": "plain", "obfsParam": "cbcdc8198.microsoft.com", "group": "", "_sourceFormat": "base64", "detectedRegion": "OTHER", "originalName": "ð å¶ä» 024"}, {"type": "vmess", "name": "🌐 其他 026", "server": "b79d3418-sux8g0-swe4j1-1rfon.cm5.p5pv.com", "port": 17232, "uuid": "aed1cc24-351d-11ef-ba52-f23c9164ca5d", "alterId": 0, "cipher": "auto", "network": "tcp", "tls": {"enabled": false, "serverName": ""}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "OTHER", "originalName": "ð å¶ä» 025"}, {"type": "ssr", "name": "🌐 其他 027", "server": "88gg.mt.mt5888.top", "port": 44008, "password": "mantouyun888", "method": "dummy", "protocol": "auth_chain_a", "protocolParam": "", "obfs": "plain", "obfsParam": "cbcdc8198.microsoft.com", "group": "", "_sourceFormat": "base64", "detectedRegion": "OTHER", "originalName": "ð å¶ä» 026"}, {"type": "vmess", "name": "🌐 其他 028", "server": "**************", "port": 22056, "uuid": "595a346d-078c-4bb3-b426-f9a8b350eab8", "alterId": 0, "cipher": "auto", "network": "ws", "tls": {"enabled": false, "serverName": ""}, "transport": {"path": "/", "headers": {}}, "_sourceFormat": "base64", "detectedRegion": "OTHER", "originalName": "ð å¶ä» 027"}, {"type": "vmess", "name": "🌐 其他 029", "server": "4924b4e1-sux8g0-sxlqgq-1spnr.cm5.p5pv.com", "port": 17232, "uuid": "127e3f92-f714-11ef-bbb0-f23c91cfbbc9", "alterId": 0, "cipher": "auto", "network": "tcp", "tls": {"enabled": false, "serverName": ""}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "OTHER", "originalName": "ð å¶ä» 028"}, {"type": "ss", "name": "🌐 其他 030", "server": "*************", "port": 29001, "password": "b8fa2e30-a210-4d8b-bb1e-bf42ea9c9e7e", "method": "aes-256-gcm", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "OTHER", "originalName": "🌐 其他 029"}, {"type": "ss", "name": "🌐 其他 031", "server": "*************", "port": 16003, "password": "b8fa2e30-a210-4d8b-bb1e-bf42ea9c9e7e", "method": "aes-256-gcm", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "OTHER", "originalName": "🌐 其他 030"}, {"type": "vmess", "name": "🌐 其他 032", "server": "tk.hzlt.tkddns.xyz", "port": 22643, "uuid": "98e96c9f-4bb3-39d4-9a2c-fac04257f7c7", "alterId": 2, "cipher": "auto", "network": "ws", "tls": {"enabled": true, "serverName": "tk.hzlt.tkddns.xyz"}, "transport": {"path": "/", "headers": {"Host": "tk.hzlt.tkddns.xyz"}}, "_sourceFormat": "base64", "detectedRegion": "OTHER", "originalName": "ð å¶ä» 031"}, {"type": "ss", "name": "🌐 其他 033", "server": "eepl1.dhh114514.christmas", "port": 42003, "password": "ed6c8eea-8503-43e1-9563-0a355b0edcac", "method": "aes-256-gcm", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "OTHER", "originalName": "🌐 其他 032"}, {"type": "ss", "name": "🌐 其他 034", "server": "eepl1.dhh114514.christmas", "port": 42015, "password": "ed6c8eea-8503-43e1-9563-0a355b0edcac", "method": "chacha20-ietf-poly1305", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "OTHER", "originalName": "🌐 其他 033"}, {"type": "ss", "name": "🌐 其他 035", "server": "**************", "port": 20004, "password": "KVR39BSSX1S7IH3D", "method": "aes-256-gcm", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "OTHER", "originalName": "🌐 其他 034"}, {"type": "ss", "name": "🇨🇦 加拿大 001", "server": "************", "port": 20032, "password": "9Y8KM6VPSPGMG80I", "method": "aes-256-gcm", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "CA", "originalName": "🇨🇦 加拿大 001"}, {"type": "vmess", "name": "🇨🇦 加拿大 002", "server": "0b17b6cf-2c23-f38e-114f-8f1f3bcd06a3.castlepeakhospital.moe", "port": 80, "uuid": "14a8a0db-e922-453a-af8d-843af8734b3b", "alterId": 0, "cipher": "auto", "network": "ws", "tls": {"enabled": false, "serverName": "tms.dingtalk.com"}, "transport": {"path": "/", "headers": {"Host": "tms.dingtalk.com"}}, "_sourceFormat": "base64", "detectedRegion": "CA", "originalName": "ð¨ð¦ å æ¿å¤§ 002"}, {"type": "vmess", "name": "🇨🇦 加拿大 003", "server": "445522e0-a147-66c0-6b59-becb865f30a5.castlepeakhospital.moe", "port": 80, "uuid": "14a8a0db-e922-453a-af8d-843af8734b3b", "alterId": 0, "cipher": "auto", "network": "ws", "tls": {"enabled": false, "serverName": "tms.dingtalk.com"}, "transport": {"path": "/", "headers": {"Host": "tms.dingtalk.com"}}, "_sourceFormat": "base64", "detectedRegion": "CA", "originalName": "ð¨ð¦ å æ¿å¤§ 003"}, {"type": "vmess", "name": "🇨🇦 加拿大 004", "server": "0989a4f1-856a-4d0b-530b-aed9bf289ad8.castlepeakhospital.moe", "port": 80, "uuid": "14a8a0db-e922-453a-af8d-843af8734b3b", "alterId": 0, "cipher": "auto", "network": "ws", "tls": {"enabled": false, "serverName": "tms.dingtalk.com"}, "transport": {"path": "/", "headers": {"Host": "tms.dingtalk.com"}}, "_sourceFormat": "base64", "detectedRegion": "CA", "originalName": "ð¨ð¦ å æ¿å¤§ 004"}, {"type": "vmess", "name": "🇨🇦 加拿大 005", "server": "06809388-302a-29e7-ec15-006f5353d530.castlepeakhospital.moe", "port": 80, "uuid": "14a8a0db-e922-453a-af8d-843af8734b3b", "alterId": 0, "cipher": "auto", "network": "ws", "tls": {"enabled": false, "serverName": "tms.dingtalk.com"}, "transport": {"path": "/", "headers": {"Host": "tms.dingtalk.com"}}, "_sourceFormat": "base64", "detectedRegion": "CA", "originalName": "ð¨ð¦ å æ¿å¤§ 005"}, {"type": "vmess", "name": "🇨🇦 加拿大 006", "server": "205928ec-61f8-1f90-8438-1b912b849b80.castlepeakhospital.moe", "port": 80, "uuid": "14a8a0db-e922-453a-af8d-843af8734b3b", "alterId": 0, "cipher": "auto", "network": "ws", "tls": {"enabled": false, "serverName": "tms.dingtalk.com"}, "transport": {"path": "/", "headers": {"Host": "tms.dingtalk.com"}}, "_sourceFormat": "base64", "detectedRegion": "CA", "originalName": "ð¨ð¦ å æ¿å¤§ 006"}, {"type": "vmess", "name": "🇨🇦 加拿大 007", "server": "85ef02dd-b03e-94d5-61f4-bb5a8d5f491f.castlepeakhospital.moe", "port": 80, "uuid": "14a8a0db-e922-453a-af8d-843af8734b3b", "alterId": 0, "cipher": "auto", "network": "ws", "tls": {"enabled": false, "serverName": "tms.dingtalk.com"}, "transport": {"path": "/", "headers": {"Host": "tms.dingtalk.com"}}, "_sourceFormat": "base64", "detectedRegion": "CA", "originalName": "ð¨ð¦ å æ¿å¤§ 007"}, {"type": "vmess", "name": "🇨🇦 加拿大 008", "server": "d9479ad0-2f0d-4dd0-253d-67606186a1ed.castlepeakhospital.moe", "port": 80, "uuid": "14a8a0db-e922-453a-af8d-843af8734b3b", "alterId": 0, "cipher": "auto", "network": "ws", "tls": {"enabled": false, "serverName": "tms.dingtalk.com"}, "transport": {"path": "/", "headers": {"Host": "tms.dingtalk.com"}}, "_sourceFormat": "base64", "detectedRegion": "CA", "originalName": "ð¨ð¦ å æ¿å¤§ 008"}, {"type": "vmess", "name": "🇨🇦 加拿大 009", "server": "a8aed529-1960-4a84-695a-58bb33a7d896.castlepeakhospital.moe", "port": 80, "uuid": "14a8a0db-e922-453a-af8d-843af8734b3b", "alterId": 0, "cipher": "auto", "network": "ws", "tls": {"enabled": false, "serverName": "tms.dingtalk.com"}, "transport": {"path": "/", "headers": {"Host": "tms.dingtalk.com"}}, "_sourceFormat": "base64", "detectedRegion": "CA", "originalName": "ð¨ð¦ å æ¿å¤§ 009"}, {"type": "ss", "name": "🇨🇦 加拿大 010", "server": "*************", "port": 20026, "password": "9IFWX2G53HT0ZFD5", "method": "aes-256-gcm", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "CA", "originalName": "🇨🇦 加拿大 010"}, {"type": "ss", "name": "🇨🇦 加拿大 011", "server": "**************", "port": 20035, "password": "OJOW52P84GC951ML", "method": "aes-256-gcm", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "CA", "originalName": "🇨🇦 加拿大 011"}, {"type": "ss", "name": "🇨🇦 加拿大 012", "server": "**************", "port": 20025, "password": "9IHUC79NEREGCCVR", "method": "aes-256-gcm", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "CA", "originalName": "🇨🇦 加拿大 012"}, {"type": "ss", "name": "🇨🇦 加拿大 013", "server": "45.154.207.246", "port": 20030, "password": "GU5F7BLIKNTOW6G2", "method": "aes-256-gcm", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "CA", "originalName": "🇨🇦 加拿大 013"}, {"type": "ss", "name": "🇬🇧 英国 001", "server": "neweur.upperlay.xyz", "port": 635, "password": "763bf612-4c66-4fd4-b54b-5349bdea6bca", "method": "aes-128-gcm", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "UK", "originalName": "🇬🇧 英国 001"}, {"type": "trojan", "name": "🇬🇧 英国 002", "server": "cn2.cdn.xfltd-cdn.top", "port": 12069, "password": "7669a04d-a459-4c96-bc0e-fde51cb984f3", "network": "tcp", "tls": {"enabled": true, "serverName": "cdn.alibaba.com", "alpn": [], "fingerprint": "chrome", "skipCertVerify": true}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "UK", "originalName": "🇬🇧 英国 002"}, {"type": "hysteria2", "name": "🇬🇧 英国 003", "server": "yingguo1.959555.xyz", "port": 10000, "password": "5415e20c-dbe6-46ff-9cc1-1c092184d801", "auth": "5415e20c-dbe6-46ff-9cc1-1c092184d801", "obfs": {"type": "", "password": ""}, "tls": {"enabled": true, "serverName": "yingguo1.959555.xyz", "alpn": ["h3"], "skipCertVerify": true}, "bandwidth": {"up": "", "down": ""}, "congestion": "bbr", "fastOpen": false, "lazy": false, "_sourceFormat": "base64", "detectedRegion": "UK", "originalName": "🇬🇧 英国 003"}, {"type": "vless", "name": "🇬🇧 英国 004", "server": "*************", "port": 2030, "uuid": "ad5e479a-d0f6-4809-902c-e74f5404336c", "flow": "", "encryption": "none", "network": "grpc", "tls": {"enabled": true, "serverName": "refersion.com", "alpn": [], "fingerprint": "chrome"}, "transport": {"serviceName": "CANALTELEGRAM--jet_config--TELEGRAM--jet_config--CANALTELEGRAM--jet_config--TELEGRAM--jet_config--CANALTELEGRAM--jet_config", "mode": "gun"}, "reality": {"enabled": true, "publicKey": "YWfCdTnr4FAOMYTY2dLrMtQUokyxOGpPhYEEszPj20E", "shortId": "7fe29733", "spiderX": ""}, "_sourceFormat": "base64", "detectedRegion": "UK", "originalName": "🇬🇧 英国 004"}, {"type": "hysteria2", "name": "🇬🇧 英国 005", "server": "ld-arm.nfsn666.gq", "port": 8888, "password": "nfsn666", "auth": "nfsn666", "obfs": {"type": "", "password": ""}, "tls": {"enabled": true, "serverName": "ld-arm.nfsn666.gq", "alpn": ["h3"], "skipCertVerify": true}, "bandwidth": {"up": "", "down": ""}, "congestion": "bbr", "fastOpen": false, "lazy": false, "_sourceFormat": "base64", "detectedRegion": "UK", "originalName": "🇬🇧 英国 005"}, {"type": "hysteria2", "name": "🇬🇧 英国 006", "server": "***************", "port": 8888, "password": "nfsn666", "auth": "nfsn666", "obfs": {"type": "", "password": ""}, "tls": {"enabled": true, "serverName": "ld-arm.nfsn666.gq", "alpn": ["h3"], "skipCertVerify": true}, "bandwidth": {"up": "", "down": ""}, "congestion": "bbr", "fastOpen": false, "lazy": false, "_sourceFormat": "base64", "detectedRegion": "UK", "originalName": "🇬🇧 英国 006"}, {"type": "vless", "name": "🇳🇱 荷兰 001", "server": "************", "port": 8880, "uuid": "53fa8faf-ba4b-4322-9c69-a3e5b1555049", "flow": "", "encryption": "none", "network": "ws", "tls": {"enabled": false, "serverName": "", "alpn": [], "fingerprint": ""}, "transport": {"path": "/Telegram🇨🇳 @pgkj666 /?ed=2560", "host": "reedfree8mahsang2.redorg.ir"}, "reality": {"enabled": false, "publicKey": "", "shortId": "", "spiderX": ""}, "_sourceFormat": "base64", "detectedRegion": "NL", "originalName": "🇳🇱 荷兰 001"}, {"type": "vless", "name": "🇳🇱 荷兰 002", "server": "************", "port": 8880, "uuid": "53fa8faf-ba4b-4322-9c69-a3e5b1555049", "flow": "", "encryption": "none", "network": "ws", "tls": {"enabled": false, "serverName": "", "alpn": [], "fingerprint": ""}, "transport": {"path": "/Telegram🇨🇳 @pgkj666 /?ed=2560", "host": "reedfree8mahsang2.redorg.ir"}, "reality": {"enabled": false, "publicKey": "", "shortId": "", "spiderX": ""}, "_sourceFormat": "base64", "detectedRegion": "NL", "originalName": "🇳🇱 荷兰 002"}, {"type": "vless", "name": "🇳🇱 荷兰 003", "server": "***********", "port": 8880, "uuid": "53fa8faf-ba4b-4322-9c69-a3e5b1555049", "flow": "", "encryption": "none", "network": "ws", "tls": {"enabled": false, "serverName": "", "alpn": [], "fingerprint": ""}, "transport": {"path": "/?ed=2560", "host": "reedfree8mahsang2.redorg.ir"}, "reality": {"enabled": false, "publicKey": "", "shortId": "", "spiderX": ""}, "_sourceFormat": "base64", "detectedRegion": "NL", "originalName": "🇳🇱 荷兰 003"}, {"type": "trojan", "name": "🇳🇱 荷兰 004", "server": "cn2.cdn.xfltd-cdn.top", "port": 12074, "password": "7669a04d-a459-4c96-bc0e-fde51cb984f3", "network": "tcp", "tls": {"enabled": true, "serverName": "cdn.alibaba.com", "alpn": [], "fingerprint": "chrome", "skipCertVerify": true}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "NL", "originalName": "🇳🇱 荷兰 004"}, {"type": "vless", "name": "🇳🇱 荷兰 005", "server": "**************", "port": 8880, "uuid": "53fa8faf-ba4b-4322-9c69-a3e5b1555049", "flow": "", "encryption": "none", "network": "ws", "tls": {"enabled": false, "serverName": "", "alpn": [], "fingerprint": ""}, "transport": {"path": "/Telegram🇨🇳 @pgkj666 /?ed=2560", "host": "reedfree8mahsang2.redorg.ir"}, "reality": {"enabled": false, "publicKey": "", "shortId": "", "spiderX": ""}, "_sourceFormat": "base64", "detectedRegion": "NL", "originalName": "🇳🇱 荷兰 005"}, {"type": "vless", "name": "🇳🇱 荷兰 006", "server": "***********", "port": 8880, "uuid": "53fa8faf-ba4b-4322-9c69-a3e5b1555049", "flow": "", "encryption": "none", "network": "ws", "tls": {"enabled": false, "serverName": "", "alpn": [], "fingerprint": ""}, "transport": {"path": "/TelegramU0001F1E8U0001F1F3 @MxlShare @WangCai2 /?ed=2560", "host": "reedfree8mahsang2.redorg.ir"}, "reality": {"enabled": false, "publicKey": "", "shortId": "", "spiderX": ""}, "_sourceFormat": "base64", "detectedRegion": "NL", "originalName": "🇳🇱 荷兰 006"}, {"type": "vless", "name": "🇳🇱 荷兰 007", "server": "************", "port": 8880, "uuid": "53fa8faf-ba4b-4322-9c69-a3e5b1555049", "flow": "", "encryption": "none", "network": "ws", "tls": {"enabled": false, "serverName": "", "alpn": [], "fingerprint": ""}, "transport": {"path": "/TelegramU0001F1E8U0001F1F3 @MxlShare @WangCai2 /?ed=2560", "host": "reedfree8mahsang2.redorg.ir"}, "reality": {"enabled": false, "publicKey": "", "shortId": "", "spiderX": ""}, "_sourceFormat": "base64", "detectedRegion": "NL", "originalName": "🇳🇱 荷兰 007"}, {"type": "trojan", "name": "🇳🇱 荷兰 008", "server": "*************", "port": 8565, "password": "RlzoEILU", "network": "tcp", "tls": {"enabled": true, "serverName": "*************", "alpn": [], "fingerprint": "chrome", "skipCertVerify": true}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "NL", "originalName": "🇳🇱 荷兰 008"}, {"type": "trojan", "name": "🇳🇱 荷兰 009", "server": "*************", "port": 33097, "password": "RlzoEILU", "network": "tcp", "tls": {"enabled": true, "serverName": "*************", "alpn": [], "fingerprint": "", "skipCertVerify": true}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "NL", "originalName": "🇳🇱 荷兰 009"}, {"type": "trojan", "name": "🇳🇱 荷兰 010", "server": "*************", "port": 15407, "password": "RlzoEILU", "network": "tcp", "tls": {"enabled": true, "serverName": "*************", "alpn": [], "fingerprint": "", "skipCertVerify": true}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "NL", "originalName": "🇳🇱 荷兰 010"}, {"type": "vmess", "name": "🇮🇳 印度 001", "server": "v25.heduian.link", "port": 30825, "uuid": "cbb3f877-d1fb-344c-87a9-d153bffd5484", "alterId": 2, "cipher": "auto", "network": "ws", "tls": {"enabled": false, "serverName": "baidu.com"}, "transport": {"path": "/oooo", "headers": {"Host": "baidu.com"}}, "_sourceFormat": "base64", "detectedRegion": "IN", "originalName": "ð®ð³ å°åº¦ 001"}, {"type": "vmess", "name": "🇮🇳 印度 002", "server": "v29.heduian.link", "port": 30829, "uuid": "cbb3f877-d1fb-344c-87a9-d153bffd5484", "alterId": 2, "cipher": "auto", "network": "ws", "tls": {"enabled": false, "serverName": "v29.heduian.link"}, "transport": {"path": "/oooo", "headers": {"Host": "v29.heduian.link"}}, "_sourceFormat": "base64", "detectedRegion": "IN", "originalName": "ð®ð³ å°åº¦ 002"}, {"type": "trojan", "name": "🇮🇳 印度 003", "server": "cm1.d-h-h.in", "port": 41012, "password": "ed6c8eea-8503-43e1-9563-0a355b0edcac", "network": "tcp", "tls": {"enabled": true, "serverName": "v1-my1.776688.best", "alpn": [], "fingerprint": "chrome", "skipCertVerify": true}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "IN", "originalName": "🇮🇳 印度 003"}, {"type": "trojan", "name": "🇮🇳 印度 004", "server": "cn2.cdn.xfltd-cdn.top", "port": 12072, "password": "7669a04d-a459-4c96-bc0e-fde51cb984f3", "network": "tcp", "tls": {"enabled": true, "serverName": "cdn.alibaba.com", "alpn": [], "fingerprint": "chrome", "skipCertVerify": true}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "IN", "originalName": "🇮🇳 印度 004"}, {"type": "trojan", "name": "🇮🇳 印度 005", "server": "starlink-tko5.2513142.xyz", "port": 443, "password": "nktaqlk-1O8bEfVXgIhUvYc_", "network": "tcp", "tls": {"enabled": true, "serverName": "www.cloudflare.com", "alpn": [], "fingerprint": "chrome", "skipCertVerify": true}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "IN", "originalName": "🇮🇳 印度 005"}, {"type": "trojan", "name": "🇮🇳 印度 006", "server": "starlink-tko6.2513142.xyz", "port": 443, "password": "nktaqlk-1O8bEfVXgIhUvYc_", "network": "tcp", "tls": {"enabled": true, "serverName": "www.cloudflare.com", "alpn": [], "fingerprint": "chrome", "skipCertVerify": true}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "IN", "originalName": "🇮🇳 印度 006"}, {"type": "hysteria2", "name": "🇷🇺 俄罗斯 001", "server": "hy2.694463.xyz", "port": 30033, "password": "dongtaiwang.com", "auth": "dongtaiwang.com", "obfs": {"type": "", "password": ""}, "tls": {"enabled": true, "serverName": "www.bing.com", "alpn": ["h3"], "skipCertVerify": true}, "bandwidth": {"up": "", "down": ""}, "congestion": "bbr", "fastOpen": false, "lazy": false, "_sourceFormat": "base64", "detectedRegion": "RU", "originalName": "🇷🇺 俄罗斯 001"}, {"type": "ss", "name": "🇷🇺 俄罗斯 002", "server": "eepl1.dhh114514.christmas", "port": 41001, "password": "ed6c8eea-8503-43e1-9563-0a355b0edcac", "method": "chacha20-ietf-poly1305", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "RU", "originalName": "🇷🇺 俄罗斯 002"}, {"type": "trojan", "name": "🇻🇳 越南 001", "server": "f10011.ylxblkyndjj.sbs", "port": 55474, "password": "74b001ba-7020-443d-b33b-5a403a156b39", "network": "tcp", "tls": {"enabled": true, "serverName": "vn01.ckcloud.info", "alpn": [], "fingerprint": "", "skipCertVerify": true}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "VN", "originalName": "🇻🇳 越南 001"}, {"type": "ss", "name": "🇻🇳 越南 002", "server": "************", "port": 29001, "password": "abb58966-8d7b-4cdf-bae9-b0d8e184d93d", "method": "aes-256-gcm", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "VN", "originalName": "🇻🇳 越南 002"}, {"type": "trojan", "name": "🇦🇺 澳大利亚 001", "server": "f10021.ylxblkyndjj.sbs", "port": 20650, "password": "74b001ba-7020-443d-b33b-5a403a156b39", "network": "tcp", "tls": {"enabled": true, "serverName": "aus01.ckcloud.info", "alpn": [], "fingerprint": "", "skipCertVerify": true}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "AU", "originalName": "🇦🇺 澳大利亚 001"}, {"type": "trojan", "name": "🇦🇺 澳大利亚 002", "server": "f10011.ylxblkyndjj.sbs", "port": 20650, "password": "74b001ba-7020-443d-b33b-5a403a156b39", "network": "tcp", "tls": {"enabled": true, "serverName": "aus01.ckcloud.info", "alpn": [], "fingerprint": "", "skipCertVerify": true}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "AU", "originalName": "🇦🇺 澳大利亚 002"}, {"type": "trojan", "name": "🇦🇺 澳大利亚 003", "server": "**************", "port": 40252, "password": "RlzoEILU", "network": "tcp", "tls": {"enabled": true, "serverName": "**************", "alpn": [], "fingerprint": "chrome", "skipCertVerify": true}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "AU", "originalName": "🇦🇺 澳大利亚 003"}, {"type": "trojan", "name": "🇦🇺 澳大利亚 004", "server": "*************", "port": 11641, "password": "RlzoEILU", "network": "tcp", "tls": {"enabled": true, "serverName": "*************", "alpn": [], "fingerprint": "chrome", "skipCertVerify": true}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "AU", "originalName": "🇦🇺 澳大利亚 004"}, {"type": "trojan", "name": "🇦🇺 澳大利亚 005", "server": "cn2.cdn.xfltd-cdn.top", "port": 12068, "password": "7669a04d-a459-4c96-bc0e-fde51cb984f3", "network": "tcp", "tls": {"enabled": true, "serverName": "cdn.alibaba.com", "alpn": [], "fingerprint": "chrome", "skipCertVerify": true}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "AU", "originalName": "🇦🇺 澳大利亚 005"}, {"type": "trojan", "name": "🇹🇷 土耳其 001", "server": "f10021.ylxblkyndjj.sbs", "port": 11839, "password": "74b001ba-7020-443d-b33b-5a403a156b39", "network": "tcp", "tls": {"enabled": true, "serverName": "tur01.ckcloud.info", "alpn": [], "fingerprint": "", "skipCertVerify": true}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "TR", "originalName": "🇹🇷 土耳其 001"}, {"type": "vmess", "name": "🇹🇷 土耳其 002", "server": "4a89bc7a-swtr40-sxzp1j-1irfn.cm5.p5pv.com", "port": 17233, "uuid": "a33115b6-75fa-11ed-8826-f23c9164ca5d", "alterId": 0, "cipher": "auto", "network": "tcp", "tls": {"enabled": false, "serverName": ""}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "TR", "originalName": "ð¹ð· åè³å¶ 002"}, {"type": "trojan", "name": "🇹🇷 土耳其 003", "server": "cn2.cdn.xfltd-cdn.top", "port": 12064, "password": "7669a04d-a459-4c96-bc0e-fde51cb984f3", "network": "tcp", "tls": {"enabled": true, "serverName": "cn2.cdn.xfltd-cdn.top", "alpn": [], "fingerprint": "", "skipCertVerify": true}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "TR", "originalName": "🇹🇷 土耳其 003"}, {"type": "vmess", "name": "🇹🇷 土耳其 004", "server": "4a89bc7a-swtr40-sxzp1j-1irfn.cm5.p5pv.com", "port": 17234, "uuid": "a33115b6-75fa-11ed-8826-f23c9164ca5d", "alterId": 0, "cipher": "auto", "network": "tcp", "tls": {"enabled": false, "serverName": ""}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "TR", "originalName": "ð¹ð· åè³å¶ 004"}, {"type": "trojan", "name": "🇮🇩 印尼 001", "server": "cn2.cdn.xfltd-cdn.top", "port": 12061, "password": "7669a04d-a459-4c96-bc0e-fde51cb984f3", "network": "tcp", "tls": {"enabled": true, "serverName": "cn2.cdn.xfltd-cdn.top", "alpn": [], "fingerprint": "chrome", "skipCertVerify": true}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "ID", "originalName": "🇮🇩 印尼 001"}, {"type": "ss", "name": "🇦🇷 阿根廷 001", "server": "sz.fanhua.art", "port": 47317, "password": "b86a48e8-542d-4b90-bb6e-ff0a3e7e4e97", "method": "aes-128-gcm", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "AR", "originalName": "🇦🇷 阿根廷 001"}, {"type": "ss", "name": "🇦🇷 阿根廷 002", "server": "sz.fanhua.art", "port": 15423, "password": "b86a48e8-542d-4b90-bb6e-ff0a3e7e4e97", "method": "aes-128-gcm", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "AR", "originalName": "🇦🇷 阿根廷 002"}, {"type": "ss", "name": "🇦🇷 阿根廷 003", "server": "sz.fanhua.art", "port": 60028, "password": "b86a48e8-542d-4b90-bb6e-ff0a3e7e4e97", "method": "aes-128-gcm", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "AR", "originalName": "🇦🇷 阿根廷 003"}, {"type": "trojan", "name": "🇨🇳 中国 001", "server": "cn2.cdn.xfltd-cdn.top", "port": 12062, "password": "7669a04d-a459-4c96-bc0e-fde51cb984f3", "network": "tcp", "tls": {"enabled": true, "serverName": "cdn.alibaba.com", "alpn": [], "fingerprint": "chrome", "skipCertVerify": true}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "CN", "originalName": "🇨🇳 中国 001"}]