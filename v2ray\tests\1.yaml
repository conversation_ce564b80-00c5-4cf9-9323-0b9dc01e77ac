proxies:
  - cipher: aes-256-gcm
    name: "\U0001F1F8\U0001F1EC新加坡1 | ⬇️ 684KB/s"
    password: 3030b609-f36f-4fd1-a4ef-a6e653780536
    port: 18004
    server: *************
    type: ss
    udp: true
  - name: "\U0001F1F8\U0001F1EC新加坡2 | ⬇️ 5.5MB/s"
    obfs: ''
    obfs-password: ''
    password: hf96oOugMgvkOAlVykIA0EKHk
    port: 31667
    server: **************
    skip-cert-verify: true
    sni: bing.com
    type: hysteria2
  - cipher: aes-256-gcm
    name: "\U0001F1F9\U0001F1FC台湾1 | ⬇️ 4.6MB/s"
    password: 3030b609-f36f-4fd1-a4ef-a6e653780536
    port: 17004
    server: *************
    type: ss
    udp: true
  - name: "\U0001F1EF\U0001F1F5日本1 | ⬇️ 4.5MB/s"
    obfs: ''
    obfs-password: ''
    password: 82762002-40f5-4775-a9ad-3da97a2772f7
    port: 20000
    server: jp01.poke-mon.xyz
    skip-cert-verify: true
    sni: www.bing.com
    type: hysteria2
  - client-fingerprint: chrome
    name: "\U0001F1ED\U0001F1F0香港1 | ⬇️ 4.8MB/s"
    password: 2c605663-b89a-5734-a9d6-97d4743d72cf
    port: 8313
    server: dozo01.flztjc.top
    skip-cert-verify: false
    sni: hk-13-568.flztjc.net
    type: trojan
    udp: true
  - cipher: aes-128-gcm
    name: "\U0001F1F8\U0001F1EC新加坡3 | ⬇️ 1.4MB/s"
    password: f16163ec-3c35-4719-a19b-68c864cdc626
    port: 13038
    server: *************
    type: ss
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1FA\U0001F1F8美国1 | ⬇️ 4.2MB/s"
    password: 7669a04d-a459-4c96-bc0e-fde51cb984f3
    port: 12004
    server: cn1.cdn.xfltd-cdn.top
    skip-cert-verify: true
    sni: cdn.alibaba.com
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1ED\U0001F1F0香港2 | ⬇️ 3.4MB/s"
    password: 2c605663-b89a-5734-a9d6-97d4743d72cf
    port: 8313
    server: **************
    skip-cert-verify: true
    sni: **************
    type: trojan
    udp: true
  - down: ''
    fingerprint: ''
    name: "\U0001F1FA\U0001F1F8美国2 | ⬇️ 3.6MB/s"
    obfs: ''
    obfs-password: ''
    password: dongtaiwang.com
    port: 57773
    server: *************
    skip-cert-verify: true
    sni: apple.com
    type: hysteria2
    up: ''
  - down: ''
    fingerprint: ''
    name: "\U0001F1F7\U0001F1FA俄罗斯1 | ⬇️ 3.0MB/s"
    obfs: ''
    obfs-password: ''
    password: dongtaiwang.com
    port: 30033
    server: hy2.694463.xyz
    skip-cert-verify: true
    sni: www.bing.com
    type: hysteria2
    up: ''
  - alpn:
      - ''
    alterId: '0'
    cipher: auto
    name: "\U0001F1EF\U0001F1F5日本2 | ⬇️ 2.3MB/s"
    network: ws
    port: 19700
    server: **************
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: db1b5367-92d7-4337-90c6-b3b9955d02ba
    ws-opts:
      headers:
        Host: tms.dingtalk.com
      path: /
    xudp: true
  - client-fingerprint: chrome
    name: "\U0001F300其他1-ID | ⬇️ 1.2MB/s"
    password: 7669a04d-a459-4c96-bc0e-fde51cb984f3
    port: 12061
    server: cn2.cdn.xfltd-cdn.top
    skip-cert-verify: true
    sni: cn2.cdn.xfltd-cdn.top
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1EF\U0001F1F5日本3 | ⬇️ 3.3MB/s"
    password: 7669a04d-a459-4c96-bc0e-fde51cb984f3
    port: 12032
    server: cn2.cdn.xfltd-cdn.top
    skip-cert-verify: true
    sni: cn2.cdn.xfltd-cdn.top
    type: trojan
    udp: true
  - down: ''
    fingerprint: ''
    name: "\U0001F1EF\U0001F1F5日本4 | ⬇️ 5.9MB/s"
    obfs: ''
    obfs-password: ''
    password: 0A1DDFE6-A16A-43FA-A6E3-70EDE4EA61C3
    port: 7270
    server: jp4.dexlos.com
    skip-cert-verify: false
    sni: jp4.dexlos.com
    type: hysteria2
    up: ''
  - client-fingerprint: chrome
    name: "\U0001F1F8\U0001F1EC新加坡4 | ⬇️ 1.1MB/s"
    password: 7669a04d-a459-4c96-bc0e-fde51cb984f3
    port: 12025
    server: cn2.cdn.xfltd-cdn.top
    skip-cert-verify: true
    sni: cn2.cdn.xfltd-cdn.top
    type: trojan
    udp: true
  - cipher: dummy
    name: "\U0001F1FA\U0001F1F8美国3 | ⬇️ 1.8MB/s"
    obfs: plain
    obfs-param: cbcdc8198.microsoft.com
    password: mantouyun888
    port: 44005
    protocol: auth_chain_a
    protocol-param: '8198:CsJt8BBrKFOSrjv2'
    server: 88gg.mt.mt5888.top
    type: ssr
    udp: true
  - down: ''
    fingerprint: ''
    name: "\U0001F1FA\U0001F1F8美国4 | ⬇️ 4.0MB/s"
    obfs: ''
    obfs-password: ''
    password: a346c669-6374-11ef-bc6a-f23c9313b177
    port: 1443
    server: 5f374286-svtc00-tdex4g-6qyv.la.shifen.uk
    skip-cert-verify: true
    sni: 5f374286-svtc00-tdex4g-6qyv.la.shifen.uk
    type: hysteria2
    up: ''
  - name: "\U0001F1FA\U0001F1F8美国5 | ⬇️ 774KB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: "/Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
  - client-fingerprint: chrome
    name: "\U0001F1EF\U0001F1F5日本5 | ⬇️ 2.3MB/s"
    password: 7669a04d-a459-4c96-bc0e-fde51cb984f3
    port: 12035
    server: cn2.cdn.xfltd-cdn.top
    skip-cert-verify: true
    sni: cn2.cdn.xfltd-cdn.top
    type: trojan
    udp: true
  - cipher: dummy
    name: "\U0001F300其他2-SC | ⬇️ 1002KB/s"
    obfs: plain
    obfs-param: cbcdc8198.microsoft.com
    password: mantouyun888
    port: 41115
    protocol: auth_chain_a
    protocol-param: '8198:CsJt8BBrKFOSrjv2'
    server: 9988.mt.mt5888.top
    type: ssr
    udp: true
  - name: "\U0001F1FA\U0001F1F8美国6 | ⬇️ 2.3MB/s"
    password: bb85e074-b0c2-11ea-ad28-f23c913c8d2b
    port: 15229
    server: 684f1982-swvls0-sx9mz9-m0b9.cu.plebai.net
    type: trojan
    udp: true
    sni: 684f1982-swvls0-sx9mz9-m0b9.cu.plebai.net
  - name: "\U0001F1FA\U0001F1F8美国7 | ⬇️ 2.0MB/s"
    password: 3d7182ce-8dcc-11ef-a3f6-f23c9164ca5d
    port: 15229
    server: f22a91be-swvls0-thdi1k-19yro.cu.plebai.net
    type: trojan
    udp: true
    sni: f22a91be-swvls0-thdi1k-19yro.cu.plebai.net
  - cipher: dummy
    name: "\U0001F1FA\U0001F1F8美国8 | ⬇️ 1.4MB/s"
    obfs: plain
    obfs-param: cbcdc8198.microsoft.com
    password: mantouyun888
    port: 44008
    protocol: auth_chain_a
    protocol-param: '8198:CsJt8BBrKFOSrjv2'
    server: 88gg.mt.mt5888.top
    type: ssr
    udp: true
  - alterId: 2
    cipher: auto
    name: "\U0001F1ED\U0001F1F0香港3 | ⬇️ 1.8MB/s"
    network: ws
    port: 459
    server: 688f6e4a-swxgg0-syb15h-8caj.hkt.east.wctype.com
    tls: false
    type: vmess
    udp: true
    uuid: a67a6c18-3f6d-11ef-ab9c-f23c9313b177
    ws-opts:
      headers:
        Host: a605477178.m.ctrip.com
      path: /
  - alpn:
      - h3
    auth-str: dongtaiwang.com
    auth_str: dongtaiwang.com
    down: 100
    name: "\U0001F1EB\U0001F1F7法国1 | ⬇️ 4.4MB/s"
    port: 14241
    protocol: udp
    server: ************
    skip-cert-verify: true
    sni: apple.com
    type: hysteria
    udp: true
    up: 100
  - name: "\U0001F1E6\U0001F1EA阿拉伯酋长国1 | ⬇️ 822KB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: "/Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
  - client-fingerprint: chrome
    name: "\U0001F1F8\U0001F1EC新加坡5 | ⬇️ 8.1MB/s"
    password: 82762002-40f5-4775-a9ad-3da97a2772f7
    port: 55015
    server: sssg03.521pokemon.com
    skip-cert-verify: true
    sni: **************
    type: trojan
    udp: true
  - name: "\U0001F1F8\U0001F1EC新加坡6 | ⬇️ 1.8MB/s"
    network: ws
    port: 80
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 438f9559-1671-45cf-9d2c-338fe6766acf
    ws-opts:
      headers:
        Host: 14.sahanwickramasinghe.shop
      path: /
  - cipher: aes-256-gcm
    name: "\U0001F1ED\U0001F1F0香港4 | ⬇️ 7.2MB/s"
    password: 9acfc574-acc3-4c2b-ab3b-491d43a6eb83
    port: 21115
    server: okanc.node-is.green
    type: ss
    udp: true
  - alterId: 2
    cipher: auto
    name: "\U0001F1ED\U0001F1F0香港5 | ⬇️ 6.6MB/s"
    network: ws
    port: 459
    server: 9d6c2f7e-swvls0-syb15h-8caj.hkt.east.wctype.com
    tls: false
    type: vmess
    udp: true
    uuid: a67a6c18-3f6d-11ef-ab9c-f23c9313b177
    ws-opts:
      headers:
        Host: a605477178.m.ctrip.com
      path: /
  - cipher: aes-256-gcm
    name: "\U0001F1ED\U0001F1F0香港6 | ⬇️ 5.3MB/s"
    password: 9acfc574-acc3-4c2b-ab3b-491d43a6eb83
    port: 21114
    server: okanc.node-is.green
    type: ss
    udp: true
  - cipher: dummy
    name: "\U0001F1F8\U0001F1EC新加坡7 | ⬇️ 7.4MB/s"
    obfs: plain
    obfs-param: cbcdc8198.microsoft.com
    password: mantouyun888
    port: 44003
    protocol: auth_chain_a
    protocol-param: '8198:CsJt8BBrKFOSrjv2'
    server: 88gg.mt.mt5888.top
    type: ssr
    udp: true
  - name: "\U0001F1FA\U0001F1F8美国9 | ⬇️ 1.9MB/s"
    obfs-opts:
      host: icloud.com
      mode: http
    port: 45768
    psk: SRjsBEEEvNNCL69o
    server: *************
    type: snell
    version: 3
  - auth: dcccacba-fa44-11ef-8400-f23c9164ca5d
    name: "\U0001F1FA\U0001F1F8美国10 | ⬇️ 6.0MB/s"
    password: dcccacba-fa44-11ef-8400-f23c9164ca5d
    port: 8443
    server: d465b594-swvls0-sx0fe4-1j6h0.hy2.gotochinatown.net
    type: hysteria2
    udp: true
    sni: d465b594-swvls0-sx0fe4-1j6h0.hy2.gotochinatown.net
  - auth: 869a0163-456f-4c06-bd4a-2376e4563eae
    name: "\U0001F1E9\U0001F1EA德国1 | ⬇️ 4.5MB/s"
    password: 869a0163-456f-4c06-bd4a-2376e4563eae
    port: 33003
    server: qydg.qy1357.top
    skip-cert-verify: true
    sni: qydg.qy1357.top
    type: hysteria2
    udp: true
  - name: "\U0001F1FA\U0001F1F8美国11 | ⬇️ 911KB/s"
    network: ws
    port: 8880
    server: *************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /?ed=2560
  - auth: 336501b6-51d2-11ee-a993-f23c9164ca5d
    name: "\U0001F1FA\U0001F1F8美国12 | ⬇️ 5.9MB/s"
    password: 336501b6-51d2-11ee-a993-f23c9164ca5d
    port: 8443
    server: 4f88735f-swxgg0-t8kd6j-1c9em.hy2.gotochinatown.net
    type: hysteria2
    udp: true
    sni: 4f88735f-swxgg0-t8kd6j-1c9em.hy2.gotochinatown.net
  - name: "\U0001F1FA\U0001F1F8美国13 | ⬇️ 2.0MB/s"
    password: 91365a7a-46d7-11ee-a8b9-f23c9164ca5d
    port: 15229
    server: 77a2be97-swvls0-t3o6u7-1osdm.cu.plebai.net
    type: trojan
    udp: true
    sni: 77a2be97-swvls0-t3o6u7-1osdm.cu.plebai.net
  - down: ''
    fingerprint: ''
    name: "\U0001F1FA\U0001F1F8美国14 | ⬇️ 7.2MB/s"
    obfs: ''
    obfs-password: ''
    password: b8bd42a9-551f-419d-b70d-4aefdd2cb074
    port: 443
    server: **************
    skip-cert-verify: true
    sni: www.bing.com
    type: hysteria2
    up: ''
  - name: "\U0001F1FA\U0001F1F8美国15 | ⬇️ 784KB/s"
    password: 788328ee-d49f-11ef-bd97-f23c9164ca5d
    port: 15229
    server: 3238ec22-swxgg0-tfnoge-1luqs.cu.plebai.net
    type: trojan
    udp: true
    sni: 3238ec22-swxgg0-tfnoge-1luqs.cu.plebai.net
  - name: "\U0001F1FA\U0001F1F8美国16 | ⬇️ 3.2MB/s"
    password: 34f0cf42-0f8a-11ec-a8bf-f23c91cfbbc9
    port: 15229
    server: ccab8269-swxgg0-szdere-155d9.cu.plebai.net
    type: trojan
    udp: true
    sni: ccab8269-swxgg0-szdere-155d9.cu.plebai.net
  - down: ''
    fingerprint: ''
    name: "\U0001F1FA\U0001F1F8美国17 | ⬇️ 7.2MB/s"
    obfs: ''
    obfs-password: ''
    password: b8bd42a9-551f-419d-b70d-4aefdd2cb074
    port: 443
    server: **************
    skip-cert-verify: true
    sni: www.bing.com
    type: hysteria2
    up: ''
  - down: ''
    fingerprint: ''
    name: "\U0001F1FA\U0001F1F8美国18 | ⬇️ 7.2MB/s"
    obfs: ''
    obfs-password: ''
    password: 16955d72-1794-11f0-a035-f23c95b6f51d
    port: 1443
    server: 34a15372-svlxc0-sw77c8-dnss.la.shifen.uk
    skip-cert-verify: true
    sni: 34a15372-svlxc0-sw77c8-dnss.la.shifen.uk
    type: hysteria2
    up: ''
  - down: ''
    fingerprint: ''
    name: "\U0001F1FA\U0001F1F8美国19 | ⬇️ 6.8MB/s"
    obfs: ''
    obfs-password: ''
    password: b8bd42a9-551f-419d-b70d-4aefdd2cb074
    port: 443
    server: **************
    skip-cert-verify: true
    sni: www.bing.com
    type: hysteria2
    up: ''
  - down: ''
    fingerprint: ''
    name: "\U0001F1FA\U0001F1F8美国20 | ⬇️ 6.6MB/s"
    obfs: ''
    obfs-password: ''
    password: b8bd42a9-551f-419d-b70d-4aefdd2cb074
    port: 443
    server: **************
    skip-cert-verify: true
    sni: www.bing.com
    type: hysteria2
    up: ''
  - down: ''
    fingerprint: ''
    name: "\U0001F1FA\U0001F1F8美国21 | ⬇️ 6.0MB/s"
    obfs: ''
    obfs-password: ''
    password: b8bd42a9-551f-419d-b70d-4aefdd2cb074
    port: 443
    server: *************
    skip-cert-verify: true
    sni: www.bing.com
    type: hysteria2
    up: ''
  - down: ''
    fingerprint: ''
    name: "\U0001F1EB\U0001F1F7法国2 | ⬇️ 4.9MB/s"
    obfs: ''
    obfs-password: ''
    password: dongtaiwang.com
    port: 31180
    server: *************
    skip-cert-verify: true
    sni: apple.com
    type: hysteria2
    up: ''
  - cipher: aes-128-gcm
    name: "\U0001F1ED\U0001F1F0香港7 | ⬇️ 1.2MB/s"
    password: sadujij!@diQojd1254
    port: 49759
    server: *************
    type: ss
    udp: true
  - down: ''
    fingerprint: ''
    name: "\U0001F1FA\U0001F1F8美国22 | ⬇️ 3.1MB/s"
    obfs: ''
    obfs-password: ''
    password: 5CBqBh6MeDq6GajcilBiDg==
    port: 61001
    server: **************
    skip-cert-verify: true
    sni: 192-227-152-86.nip.io
    type: hysteria2
    up: ''
  - down: ''
    fingerprint: ''
    name: "\U0001F1FA\U0001F1F8美国23 | ⬇️ 3.8MB/s"
    obfs: ''
    obfs-password: ''
    password: b72ba5d5-2d5e-45b7-93b5-236d343baa7c
    port: 47262
    server: **************
    skip-cert-verify: true
    sni: www.bing.com
    type: hysteria2
    up: ''
  - down: ''
    fingerprint: ''
    name: "\U0001F1FA\U0001F1F8美国24 | ⬇️ 2.6MB/s"
    obfs: ''
    obfs-password: ''
    password: 5CBqBh6MeDq6GajcilBiDg==
    port: 61001
    server: 192-227-152-86.nip.io
    skip-cert-verify: true
    sni: 192-227-152-86.nip.io
    type: hysteria2
    up: ''
  - down: ''
    fingerprint: ''
    name: "\U0001F1FA\U0001F1F8美国25 | ⬇️ 870KB/s"
    obfs: ''
    obfs-password: ''
    password: 0da8651e-e1f6-11ec-bd7c-f23c913c8d2b
    port: 8443
    server: e06eb389-swq1s0-tcinla-hrtf.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: e06eb389-swq1s0-tcinla-hrtf.hy2.gotochinatown.net
    type: hysteria2
    up: ''
  - down: ''
    fingerprint: ''
    name: "\U0001F1FA\U0001F1F8美国26 | ⬇️ 1.0MB/s"
    obfs: ''
    obfs-password: ''
    password: 6e9cdc20-b92a-11ef-973a-f23c913c8d2b
    port: 8443
    server: 1fec14d5-swrwg0-sww7b0-1qwp5.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 1fec14d5-swrwg0-sww7b0-1qwp5.hy2.gotochinatown.net
    type: hysteria2
    up: ''
  - down: ''
    fingerprint: ''
    name: "\U0001F1FA\U0001F1F8美国27 | ⬇️ 738KB/s"
    obfs: ''
    obfs-password: ''
    password: 93fb69fc-77cf-11ee-85ee-f23c91369f2d
    port: 8443
    server: a132e977-sw5og0-t12cnj-1ol97.hy2.gotochinatown.net
    skip-cert-verify: true
    sni: a132e977-sw5og0-t12cnj-1ol97.hy2.gotochinatown.net
    type: hysteria2
    up: ''
  - down: ''
    fingerprint: ''
    name: "\U0001F1FA\U0001F1F8美国28 | ⬇️ 1.3MB/s"
    obfs: ''
    obfs-password: ''
    password: 6c510073-4ca8-423b-87a5-a6d73c0ca557
    port: 43999
    server: jiangzhidb.54264944.xyz
    skip-cert-verify: true
    sni: jiangzhidb.54264944.xyz
    type: hysteria2
    up: ''
  - down: ''
    fingerprint: ''
    name: "\U0001F1FA\U0001F1F8美国29 | ⬇️ 2.3MB/s"
    obfs: ''
    obfs-password: ''
    password: 14bdcd31-8a9f-4cf1-8fcc-60045900357d
    port: 3002
    server: us.10101251.xyz
    skip-cert-verify: false
    sni: us.10101251.xyz
    type: hysteria2
    up: ''
  - client-fingerprint: chrome
    name: "\U0001F1FA\U0001F1F8美国30 | ⬇️ 2.5MB/s"
    password: 2b1ed981-6547-4094-998b-06a3323d6f6c
    port: 21603
    server: xd-js.timiwc.com
    skip-cert-verify: true
    sni: k61.tudou211.com
    type: trojan
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1ED\U0001F1F0香港8 | ⬇️ 510KB/s"
    password: 0S43J3FD6L7IEKGQ
    port: 15008
    server: 8tv68qhq.slashdevslashnetslashtun.net
    type: ss
    udp: true
  - down: ''
    fingerprint: ''
    name: "\U0001F1FA\U0001F1F8美国31 | ⬇️ 994KB/s"
    obfs: ''
    obfs-password: ''
    password: 0da8651e-e1f6-11ec-bd7c-f23c913c8d2b
    port: 8443
    server: 479aad99-swd340-tcinla-hrtf.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 479aad99-swd340-tcinla-hrtf.hy2.gotochinatown.net
    type: hysteria2
    up: ''
  - down: ''
    fingerprint: ''
    name: "\U0001F1FA\U0001F1F8美国32 | ⬇️ 1.5MB/s"
    obfs: ''
    obfs-password: ''
    password: 48c1e014-28d6-11ec-a0fc-f23c913c8d2b
    port: 8443
    server: a4e3ef78-swin40-sxlsd4-3z3v.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: a4e3ef78-swin40-sxlsd4-3z3v.hy2.gotochinatown.net
    type: hysteria2
    up: ''
  - down: ''
    fingerprint: ''
    name: "\U0001F1EC\U0001F1E7英国1 | ⬇️ 3.6MB/s"
    obfs: ''
    obfs-password: ''
    password: nfsn666
    port: 8888
    server: ld-arm.nfsn666.gq
    skip-cert-verify: true
    sni: ld-arm.nfsn666.gq
    type: hysteria2
    up: ''
  - down: ''
    fingerprint: ''
    name: "\U0001F1FA\U0001F1F8美国33 | ⬇️ 859KB/s"
    obfs: ''
    obfs-password: ''
    password: b79d79ae-8bce-11ef-a2b8-f23c9164ca5d
    port: 8443
    server: 341d6ff9-swb8g0-t67sv3-1snfs.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 341d6ff9-swb8g0-t67sv3-1snfs.hy2.gotochinatown.net
    type: hysteria2
    up: ''
  - client-fingerprint: chrome
    name: "\U0001F1FA\U0001F1F8美国34 | ⬇️ 7.3MB/s"
    password: 2b1ed981-6547-4094-998b-06a3323d6f6c
    port: 59599
    server: xd-js.timiwc.com
    skip-cert-verify: true
    sni: k62.tudou211.com
    type: trojan
    udp: true
  - down: ''
    fingerprint: ''
    name: "\U0001F1E9\U0001F1EA德国2 | ⬇️ 3.5MB/s"
    obfs: ''
    obfs-password: ''
    password: 75e98355-345b-4413-8001-************
    port: 44001
    server: **************
    skip-cert-verify: true
    sni: ''
    type: hysteria2
    up: ''
    disable-sni: true
  - down: ''
    fingerprint: ''
    name: "\U0001F1FA\U0001F1F8美国35 | ⬇️ 4.1MB/s"
    obfs: ''
    obfs-password: ''
    password: 65a43b6e-19c5-4fab-b960-d110a07d66a4
    port: 3234
    server: **************
    skip-cert-verify: true
    sni: ''
    type: hysteria2
    up: ''
    disable-sni: true
  - down: ''
    fingerprint: ''
    name: "\U0001F1ED\U0001F1F0香港9 | ⬇️ 2.6MB/s"
    obfs: ''
    obfs-password: ''
    password: d010926e-0311-4924-a013-b84fbae430f9
    port: 30003
    server: qyhg.qy1357.top
    skip-cert-verify: true
    sni: qyhg.qy1357.top
    type: hysteria2
    up: ''
  - down: ''
    fingerprint: ''
    name: "\U0001F1E9\U0001F1EA德国3 | ⬇️ 3.2MB/s"
    obfs: ''
    obfs-password: ''
    password: 796ec552-f8e4-43c7-ac2f-5c2e668074de
    port: 33003
    server: qydg.qy1357.top
    skip-cert-verify: true
    sni: qydg.qy1357.top
    type: hysteria2
    up: ''
  - down: ''
    fingerprint: ''
    name: "\U0001F1EC\U0001F1E7英国2 | ⬇️ 2.4MB/s"
    obfs: ''
    obfs-password: ''
    password: nfsn666
    port: 8888
    server: ***************
    skip-cert-verify: true
    sni: ld-arm.nfsn666.gq
    type: hysteria2
    up: ''
  - client-fingerprint: chrome
    name: "\U0001F1EF\U0001F1F5日本6 | ⬇️ 2.3MB/s"
    password: 7669a04d-a459-4c96-bc0e-fde51cb984f3
    port: 12033
    server: cn2.cdn.xfltd-cdn.top
    skip-cert-verify: true
    sni: cn2.cdn.xfltd-cdn.top
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1F8\U0001F1EC新加坡8 | ⬇️ 3.4MB/s"
    password: 7669a04d-a459-4c96-bc0e-fde51cb984f3
    port: 12021
    server: cn2.cdn.xfltd-cdn.top
    skip-cert-verify: true
    sni: cn2.cdn.xfltd-cdn.top
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1FA\U0001F1F8美国36 | ⬇️ 3.4MB/s"
    password: 2b1ed981-6547-4094-998b-06a3323d6f6c
    port: 21332
    server: xd-js.timiwc.com
    skip-cert-verify: true
    sni: xd-js.timiwc.com
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F300其他3-TH | ⬇️ 5.9MB/s"
    password: 7669a04d-a459-4c96-bc0e-fde51cb984f3
    port: 12076
    server: cn2.cdn.xfltd-cdn.top
    skip-cert-verify: true
    sni: cn2.cdn.xfltd-cdn.top
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1ED\U0001F1F0香港10 | ⬇️ 4.2MB/s"
    password: 7669a04d-a459-4c96-bc0e-fde51cb984f3
    port: 12003
    server: cn1.cdn.xfltd-cdn.top
    skip-cert-verify: true
    sni: cdn.alibaba.com
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1FA\U0001F1F8美国37 | ⬇️ 1.2MB/s"
    password: 7669a04d-a459-4c96-bc0e-fde51cb984f3
    port: 12053
    server: cn2.cdn.xfltd-cdn.top
    skip-cert-verify: true
    sni: cn2.cdn.xfltd-cdn.top
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F300其他4-TR | ⬇️ 3.9MB/s"
    password: 7669a04d-a459-4c96-bc0e-fde51cb984f3
    port: 12064
    server: cn2.cdn.xfltd-cdn.top
    skip-cert-verify: true
    sni: cn2.cdn.xfltd-cdn.top
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1ED\U0001F1F0香港11 | ⬇️ 1.4MB/s"
    password: 7669a04d-a459-4c96-bc0e-fde51cb984f3
    port: 12006
    server: cn1.cdn.xfltd-cdn.top
    skip-cert-verify: true
    sni: cdn.alibaba.com
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1F8\U0001F1EC新加坡9 | ⬇️ 8.5MB/s"
    password: 7669a04d-a459-4c96-bc0e-fde51cb984f3
    port: 12023
    server: cn2.cdn.xfltd-cdn.top
    skip-cert-verify: true
    sni: cdn.alibaba.com
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1EF\U0001F1F5日本7 | ⬇️ 2.6MB/s"
    password: 7669a04d-a459-4c96-bc0e-fde51cb984f3
    port: 12034
    server: cn2.cdn.xfltd-cdn.top
    skip-cert-verify: true
    sni: cn2.cdn.xfltd-cdn.top
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1ED\U0001F1F0香港12 | ⬇️ 8.3MB/s"
    password: 7669a04d-a459-4c96-bc0e-fde51cb984f3
    port: 12002
    server: cn1.cdn.xfltd-cdn.top
    skip-cert-verify: true
    sni: cdn.alibaba.com
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1E6\U0001F1FA澳大利亚1 | ⬇️ 4.8MB/s"
    password: 7669a04d-a459-4c96-bc0e-fde51cb984f3
    port: 12068
    server: cn2.cdn.xfltd-cdn.top
    skip-cert-verify: true
    sni: cdn.alibaba.com
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1FA\U0001F1F8美国38 | ⬇️ 8.7MB/s"
    password: 7669a04d-a459-4c96-bc0e-fde51cb984f3
    port: 12054
    server: cn2.cdn.xfltd-cdn.top
    skip-cert-verify: true
    sni: cdn.alibaba.com
    type: trojan
    udp: true
  - name: "\U0001F1F0\U0001F1F7韩国1 | ⬇️ 645KB/s"
    network: ws
    port: 8880
    server: ***********
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
        User-Agent: >-
          Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML,
          like Gecko) Chrome/67.0.3396.99 Safari/537.36
      path: "/Telegram\U0001F1E8\U0001F1F3 @pgkj666 /?ed=2560"
    xudp: true
  - client-fingerprint: chrome
    name: "\U0001F1ED\U0001F1F0香港13 | ⬇️ 4.1MB/s"
    password: 7669a04d-a459-4c96-bc0e-fde51cb984f3
    port: 12005
    server: cn1.cdn.xfltd-cdn.top
    skip-cert-verify: true
    sni: cdn.alibaba.com
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1EF\U0001F1F5日本8 | ⬇️ 853KB/s"
    password: 7669a04d-a459-4c96-bc0e-fde51cb984f3
    port: 12031
    server: cn2.cdn.xfltd-cdn.top
    skip-cert-verify: true
    sni: cn2.cdn.xfltd-cdn.top
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1FA\U0001F1F8美国39 | ⬇️ 3.2MB/s"
    password: 7669a04d-a459-4c96-bc0e-fde51cb984f3
    port: 12052
    server: cn2.cdn.xfltd-cdn.top
    skip-cert-verify: true
    sni: cdn.alibaba.com
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1FA\U0001F1F8美国40 | ⬇️ 6.9MB/s"
    password: 7669a04d-a459-4c96-bc0e-fde51cb984f3
    port: 12051
    server: cn2.cdn.xfltd-cdn.top
    skip-cert-verify: true
    sni: cdn.alibaba.com
    type: trojan
    udp: true
  - name: "\U0001F1FA\U0001F1F8美国41 | ⬇️ 697KB/s"
    network: ws
    port: 8880
    server: **************
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
        User-Agent: >-
          Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML,
          like Gecko) Chrome/52.0.2743.116 Safari/537.36
      path: "/Telegram\U0001F1E8\U0001F1F3 @pgkj666 /?ed=2560"
    xudp: true
  - name: "\U0001F1FA\U0001F1F8美国42 | ⬇️ 677KB/s"
    network: ws
    port: 8880
    server: ***********
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
        User-Agent: >-
          Mozilla/5.0 (Windows NT 6.1) AppleWebKit/537.36 (KHTML, like Gecko)
          Chrome/68.0.3440.106 Safari/537.36
      path: /?ed=2560
    xudp: true
  - name: "\U0001F1FA\U0001F1F8美国43 | ⬇️ 2.4MB/s"
    network: ws
    port: 8880
    server: ***********
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
        User-Agent: >-
          Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like
          Gecko) Chrome/53.0.2785.143 Safari/537.36
      path: /?ed=2560
    xudp: true
    servername: reedfree8mahsang2.redorg.ir
  - name: "\U0001F1EB\U0001F1EE芬兰1 | ⬇️ 554KB/s"
    network: ws
    port: 8880
    server: *************
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
        User-Agent: >-
          Mozilla/5.0 (Linux; Android 6.0.1; SM-J500M Build/MMB29M)
          AppleWebKit/537.36 (KHTML, like Gecko) Chrome/69.0.3497.100 Mobile
          Safari/537.36
      path: "/Telegram\U0001F1E8\U0001F1F3 @WangCai2 /?ed=2560"
    xudp: true
  - client-fingerprint: chrome
    name: "\U0001F300其他5-未识别 | ⬇️ 1.1MB/s"
    password: 2c605663-b89a-5734-a9d6-97d4743d72cf
    port: 8313
    server: *************
    skip-cert-verify: true
    sni: hk-13-568.flztjc.net
    type: trojan
    udp: true
  - name: "\U0001F1E9\U0001F1EA德国4 | ⬇️ 1.2MB/s"
    network: ws
    port: 8880
    server: ***********
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
        User-Agent: >-
          Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like
          Gecko) Chrome/40.0.2214.115 Safari/537.36
      path: /?ed=2560
    xudp: true
  - cipher: aes-256-gcm
    name: "\U0001F1ED\U0001F1F0香港14 | ⬇️ 10.0MB/s"
    password: 3030b609-f36f-4fd1-a4ef-a6e653780536
    port: 16008
    server: *************
    type: ss
    udp: true
  - name: "\U0001F1E6\U0001F1EA阿拉伯酋长国2 | ⬇️ 780KB/s"
    network: ws
    port: 80
    server: *************
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
        User-Agent: >-
          Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like
          Gecko) Chrome/70.0.3538.67 Safari/537.36
      path: /
    xudp: true
    servername: reedfree8mahsang2.redorg.ir
  - cipher: aes-256-gcm
    name: "\U0001F1ED\U0001F1F0香港15 | ⬇️ 10.3MB/s"
    password: c86d483c-431f-41df-bb6b-c1dcebfc7401
    port: 16002
    server: ************
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1ED\U0001F1F0香港16 | ⬇️ 850KB/s"
    password: c86d483c-431f-41df-bb6b-c1dcebfc7401
    port: 16006
    server: *************
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F300其他6-MY | ⬇️ 10.5MB/s"
    password: c86d483c-431f-41df-bb6b-c1dcebfc7401
    port: 29010
    server: *************
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1ED\U0001F1F0香港17 | ⬇️ 3.6MB/s"
    password: e04ae67d4e4cd165
    port: 2019
    server: **************
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1FA\U0001F1F8美国44 | ⬇️ 8.3MB/s"
    password: c86d483c-431f-41df-bb6b-c1dcebfc7401
    port: 20007
    server: ************
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1FA\U0001F1F8美国45 | ⬇️ 1.6MB/s"
    password: P1lrnsJwO4
    port: 50631
    server: ************
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1EF\U0001F1F5日本9 | ⬇️ 685KB/s"
    password: c86d483c-431f-41df-bb6b-c1dcebfc7401
    port: 19002
    server: *************
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1ED\U0001F1F0香港18 | ⬇️ 9.4MB/s"
    password: c86d483c-431f-41df-bb6b-c1dcebfc7401
    port: 16005
    server: *************
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1E9\U0001F1EA德国5 | ⬇️ 3.2MB/s"
    password: c86d483c-431f-41df-bb6b-c1dcebfc7401
    port: 28001
    server: ************
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1EF\U0001F1F5日本10 | ⬇️ 8.1MB/s"
    password: c86d483c-431f-41df-bb6b-c1dcebfc7401
    port: 19008
    server: *************
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1FA\U0001F1F8美国46 | ⬇️ 7.3MB/s"
    password: 3030b609-f36f-4fd1-a4ef-a6e653780536
    port: 20002
    server: *************
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F300其他7-未识别 | ⬇️ 8.8MB/s"
    password: c86d483c-431f-41df-bb6b-c1dcebfc7401
    port: 18007
    server: ************
    type: ss
    udp: true
  - name: "\U0001F1FA\U0001F1F8美国47 | ⬇️ 856KB/s"
    network: ws
    port: 8880
    server: ***********
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
        User-Agent: >-
          Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like
          Gecko) Chrome/51.0.2704.63 Safari/537.36
      path: "/Telegram\U0001F1E8\U0001F1F3 @pgkj666 /?ed=2560"
    xudp: true
  - cipher: aes-256-gcm
    name: "\U0001F1F9\U0001F1FC台湾2 | ⬇️ 6.4MB/s"
    password: c86d483c-431f-41df-bb6b-c1dcebfc7401
    port: 17005
    server: ************
    type: ss
    udp: true
  - cipher: aes-256-cfb
    name: "\U0001F1F0\U0001F1F7韩国2 | ⬇️ 3.8MB/s"
    password: qwerREWQ@@
    port: 11389
    server: p231.panda004.net
    type: ss
    udp: true
  - down: ''
    fingerprint: ''
    name: "\U0001F1F0\U0001F1F7韩国3 | ⬇️ 9.2MB/s"
    obfs: ''
    obfs-password: ''
    password: 75e98355-345b-4413-8001-************
    port: 44005
    server: **************
    skip-cert-verify: true
    sni: www.bing.com
    type: hysteria2
    up: ''
  - alpn:
      - ''
    alterId: '0'
    cipher: auto
    name: "\U0001F1FA\U0001F1F8美国48 | ⬇️ 1.1MB/s"
    network: ws
    port: 37906
    server: free-relay.themars.top
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 781807c4-37df-4da3-9942-c6e82032399a
    ws-opts:
      headers:
        Host: www.cctv.com
      path: /cctv1.m3u8
    xudp: true
  - name: "\U0001F1EF\U0001F1F5日本11 | ⬇️ 9.8MB/s"
    obfs: ''
    obfs-password: ''
    password: 0A1DDFE6-A16A-43FA-A6E3-70EDE4EA61C3
    port: 9517
    server: jp5.dexlos.com
    skip-cert-verify: false
    sni: jp5.dexlos.com
    type: hysteria2
  - cipher: aes-256-cfb
    name: "\U0001F300其他8-VN | ⬇️ 1.8MB/s"
    password: Xn8jKdmDM00IeO%#$#fJAMtsEAEUOpH/YWYtYqDFnT0SV
    port: 38388
    server: **************
    type: ss
    udp: true
  - name: "\U0001F1FA\U0001F1F8美国49 | ⬇️ 1.3MB/s"
    obfs: ''
    obfs-password: ''
    password: 088a0bbe-4f9f-11ea-a15d-f23c913c8d2b
    port: 8443
    server: b6ea3219-supts0-swbd0b-716s.hy2.gotochinatown.net
    skip-cert-verify: true
    sni: b6ea3219-supts0-swbd0b-716s.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国50 | ⬇️ 1.3MB/s"
    obfs: ''
    obfs-password: ''
    password: 48c1e014-28d6-11ec-a0fc-f23c913c8d2b
    port: 8443
    server: 3e8cbc42-svwf40-sw0dp4-3z3v.hy2.gotochinatown.net
    skip-cert-verify: true
    sni: 3e8cbc42-svwf40-sw0dp4-3z3v.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国51 | ⬇️ 1.3MB/s"
    obfs: ''
    obfs-password: ''
    password: c3dc8d26-ab2b-11ec-a8bf-f23c91cfbbc9
    port: 8443
    server: 71c9123b-swexs0-szapkm-1hpo0.hy2.gotochinatown.net
    skip-cert-verify: true
    sni: 71c9123b-swexs0-szapkm-1hpo0.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国52 | ⬇️ 1.3MB/s"
    obfs: ''
    obfs-password: ''
    password: 93fb69fc-77cf-11ee-85ee-f23c91369f2d
    port: 8443
    server: 4b3d7ffd-svwf40-t12cnj-1ol97.hy2.gotochinatown.net
    skip-cert-verify: true
    sni: 4b3d7ffd-svwf40-t12cnj-1ol97.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国53 | ⬇️ 1.8MB/s"
    obfs: ''
    obfs-password: ''
    password: 06b2ac52-fcc6-11ec-bb74-f23c9164ca5d
    port: 8443
    server: 7edb7ffb-sw5og0-sznzxg-1jfvb.hy2.gotochinatown.net
    skip-cert-verify: true
    sni: 7edb7ffb-sw5og0-sznzxg-1jfvb.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国54 | ⬇️ 1.9MB/s"
    obfs: ''
    obfs-password: ''
    password: e3975a05-b89e-11ec-a50d-f23c91cfbbc9
    port: 8443
    server: e00973b3-swin40-sxf56z-1f596.hy2.gotochinatown.net
    skip-cert-verify: true
    sni: e00973b3-swin40-sxf56z-1f596.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国55 | ⬇️ 1.6MB/s"
    obfs: ''
    obfs-password: ''
    password: 8e0f8ef2-5fe3-11ec-a8bf-f23c91cfbbc9
    port: 8443
    server: 1ec1d0fd-sw3ts0-swdpud-duku.hy2.gotochinatown.net
    skip-cert-verify: true
    sni: 1ec1d0fd-sw3ts0-swdpud-duku.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1ED\U0001F1F0香港19 | ⬇️ 718KB/s"
    obfs: ''
    obfs-password: ''
    password: 82febca6-8856-41fe-84df-c8bda0b72c7b
    port: 1000
    server: lx.ccwink.cc
    skip-cert-verify: true
    sni: hk003.ccwink.cc
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国56 | ⬇️ 1.0MB/s"
    obfs: ''
    obfs-password: ''
    password: d4018e28-e328-11ed-98a7-f23c913c8d2b
    port: 8443
    server: 9b6af554-swexs0-sx3h07-1g8k0.hy2.gotochinatown.net
    skip-cert-verify: true
    sni: 9b6af554-swexs0-sx3h07-1g8k0.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1ED\U0001F1F0香港20 | ⬇️ 4.7MB/s"
    obfs: ''
    obfs-password: ''
    password: 82762002-40f5-4775-a9ad-3da97a2772f7
    port: 20000
    server: hk02.poke-mon.xyz
    skip-cert-verify: true
    sni: www.bing.com
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国57 | ⬇️ 872KB/s"
    obfs: ''
    obfs-password: ''
    password: affe7124-c118-11ef-b6b2-f23c9164ca5d
    port: 8443
    server: 336bb47a-swkhs0-td1w5f-1t3cz.hy2.gotochinatown.net
    skip-cert-verify: true
    sni: 336bb47a-swkhs0-td1w5f-1t3cz.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国58 | ⬇️ 7.2MB/s"
    obfs: ''
    obfs-password: ''
    password: 82762002-40f5-4775-a9ad-3da97a2772f7
    port: 20000
    server: us01.poke-mon.xyz
    skip-cert-verify: true
    sni: www.bing.com
    type: hysteria2
  - name: "\U0001F1F9\U0001F1FC台湾3 | ⬇️ 4.8MB/s"
    obfs: ''
    obfs-password: ''
    password: 82febca6-8856-41fe-84df-c8bda0b72c7b
    port: 30001
    server: tw.akebi.cc
    skip-cert-verify: true
    sni: tw.akebi.cc
    type: hysteria2
  - name: "\U0001F1F9\U0001F1FC台湾4 | ⬇️ 3.6MB/s"
    obfs: ''
    obfs-password: ''
    password: 82762002-40f5-4775-a9ad-3da97a2772f7
    port: 20000
    server: tw.poke-mon.xyz
    skip-cert-verify: true
    sni: www.bing.com
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国59 | ⬇️ 561KB/s"
    obfs: ''
    obfs-password: ''
    password: 088a0bbe-4f9f-11ea-a15d-f23c913c8d2b
    port: 8443
    server: e15eadd7-swin40-t5vrf3-716s.hy2.gotochinatown.net
    skip-cert-verify: true
    sni: e15eadd7-swin40-t5vrf3-716s.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国60 | ⬇️ 940KB/s"
    obfs: ''
    obfs-password: ''
    password: e3975a05-b89e-11ec-a50d-f23c91cfbbc9
    port: 8443
    server: 4446642d-swexs0-sxf56z-1f596.hy2.gotochinatown.net
    skip-cert-verify: true
    sni: 4446642d-swexs0-sxf56z-1f596.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国61 | ⬇️ 866KB/s"
    obfs: ''
    obfs-password: ''
    password: 861726bb-c61a-b0e0-99fa-5ddfdcf21902
    port: 8443
    server: 8b18ad20-swin40-ta5nd4-e06r.hy2.gotochinatown.net
    skip-cert-verify: true
    sni: 8b18ad20-swin40-ta5nd4-e06r.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国62 | ⬇️ 636KB/s"
    obfs: ''
    obfs-password: ''
    password: 06b2ac52-fcc6-11ec-bb74-f23c9164ca5d
    port: 8443
    server: 24710604-swin40-sznzxg-1jfvb.hy2.gotochinatown.net
    skip-cert-verify: true
    sni: 24710604-swin40-sznzxg-1jfvb.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国63 | ⬇️ 1.2MB/s"
    obfs: ''
    obfs-password: ''
    password: 7d7c111e-fbc1-11ef-ab5f-f23c913c8d2b
    port: 8443
    server: 450cbb6d-swd340-sxhurg-1th8j.hy2.gotochinatown.net
    skip-cert-verify: true
    sni: 450cbb6d-swd340-sxhurg-1th8j.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国64 | ⬇️ 815KB/s"
    obfs: ''
    obfs-password: ''
    password: c3dc8d26-ab2b-11ec-a8bf-f23c91cfbbc9
    port: 8443
    server: cde895e9-swkhs0-szapkm-1hpo0.hy2.gotochinatown.net
    skip-cert-verify: true
    sni: cde895e9-swkhs0-szapkm-1hpo0.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1EC\U0001F1E7英国3 | ⬇️ 6.8MB/s"
    obfs: ''
    obfs-password: ''
    password: 82762002-40f5-4775-a9ad-3da97a2772f7
    port: 20000
    server: uk01.poke-mon.xyz
    skip-cert-verify: true
    sni: www.bing.com
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国65 | ⬇️ 710KB/s"
    obfs: ''
    obfs-password: ''
    password: 6e9cdc20-b92a-11ef-973a-f23c913c8d2b
    port: 8443
    server: 6b99ff62-svy9s0-sww7b0-1qwp5.hy2.gotochinatown.net
    skip-cert-verify: true
    sni: 6b99ff62-svy9s0-sww7b0-1qwp5.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1E9\U0001F1EA德国6 | ⬇️ 5.6MB/s"
    obfs: ''
    obfs-password: ''
    password: 82762002-40f5-4775-a9ad-3da97a2772f7
    port: 20000
    server: deguo.poke-mon.xyz
    skip-cert-verify: true
    sni: www.bing.com
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国66 | ⬇️ 1.3MB/s"
    obfs: salamander
    obfs-password: NDhhNmY5YTY0MGYzOTgxYQ==
    password: db19dadd-8c57-4cfc-8b93-1df12443901e
    port: 30073
    server: **************
    skip-cert-verify: true
    sni: ''
    type: hysteria2
    disable-sni: true
  - name: "\U0001F1FA\U0001F1F8美国67 | ⬇️ 823KB/s"
    obfs: ''
    obfs-password: ''
    password: e3975a05-b89e-11ec-a50d-f23c91cfbbc9
    port: 8443
    server: d5d8af9a-svlb40-sxf56z-1f596.hy2.gotochinatown.net
    skip-cert-verify: true
    sni: d5d8af9a-svlb40-sxf56z-1f596.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1E6\U0001F1FA澳大利亚2 | ⬇️ 6.1MB/s"
    obfs: ''
    obfs-password: ''
    password: 82762002-40f5-4775-a9ad-3da97a2772f7
    port: 20000
    server: aodaliya.poke-mon.xyz
    skip-cert-verify: true
    sni: aodaliya.poke-mon.xyz
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国68 | ⬇️ 825KB/s"
    obfs: ''
    obfs-password: ''
    password: 31676725-b0fd-fed2-c857-2da8ce5a6e4f
    port: 8443
    server: 47b39173-swexs0-t0dyyh-jjv2.hy2.gotochinatown.net
    skip-cert-verify: true
    sni: 47b39173-swexs0-t0dyyh-jjv2.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国69 | ⬇️ 563KB/s"
    obfs: ''
    obfs-password: ''
    password: 91365a7a-46d7-11ee-a8b9-f23c9164ca5d
    port: 8443
    server: 8725de56-swkhs0-t3o6u7-1osdm.hy2.gotochinatown.net
    skip-cert-verify: true
    sni: 8725de56-swkhs0-t3o6u7-1osdm.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国70 | ⬇️ 1.0MB/s"
    obfs: ''
    obfs-password: ''
    password: bcc58e88-e147-11ec-b286-f23c91cfbbc9
    port: 8443
    server: 2f26300e-swkhs0-sxu45k-ggww.hy2.gotochinatown.net
    skip-cert-verify: true
    sni: 2f26300e-swkhs0-sxu45k-ggww.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国71 | ⬇️ 879KB/s"
    obfs: ''
    obfs-password: ''
    password: dcccacba-fa44-11ef-8400-f23c9164ca5d
    port: 8443
    server: 01089b68-swd340-sx0fe4-1j6h0.hy2.gotochinatown.net
    skip-cert-verify: true
    sni: 01089b68-swd340-sx0fe4-1j6h0.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F300其他9-VN | ⬇️ 946KB/s"
    obfs: ''
    obfs-password: ''
    password: 82762002-40f5-4775-a9ad-3da97a2772f7
    port: 20000
    server: yuenan.poke-mon.xyz
    skip-cert-verify: true
    sni: www.bing.com
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国72 | ⬇️ 610KB/s"
    obfs: ''
    obfs-password: ''
    password: 0da8651e-e1f6-11ec-bd7c-f23c913c8d2b
    port: 8443
    server: 49bd66e1-swin40-tcinla-hrtf.hy2.gotochinatown.net
    skip-cert-verify: true
    sni: 49bd66e1-swin40-tcinla-hrtf.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国73 | ⬇️ 835KB/s"
    obfs: ''
    obfs-password: ''
    password: 91365a7a-46d7-11ee-a8b9-f23c9164ca5d
    port: 8443
    server: 2e026bef-swexs0-t3o6u7-1osdm.hy2.gotochinatown.net
    skip-cert-verify: true
    sni: 2e026bef-swexs0-t3o6u7-1osdm.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国74 | ⬇️ 1.7MB/s"
    obfs: salamander
    obfs-password: NDhhNmY5YTY0MGYzOTgxYQ==
    password: ee553181-bfb9-4109-9c1e-e86ce2a314cb
    port: 30073
    server: **************
    skip-cert-verify: true
    sni: ''
    type: hysteria2
    disable-sni: true
  - name: "\U0001F1FA\U0001F1F8美国75 | ⬇️ 716KB/s"
    obfs: ''
    obfs-password: ''
    password: d4018e28-e328-11ed-98a7-f23c913c8d2b
    port: 8443
    server: bdb364cd-swkhs0-sx3h07-1g8k0.hy2.gotochinatown.net
    skip-cert-verify: true
    sni: bdb364cd-swkhs0-sx3h07-1g8k0.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国76 | ⬇️ 730KB/s"
    obfs: ''
    obfs-password: ''
    password: 48c1e014-28d6-11ec-a0fc-f23c913c8d2b
    port: 8443
    server: f11e9c30-swkhs0-sxlsd4-3z3v.hy2.gotochinatown.net
    skip-cert-verify: true
    sni: f11e9c30-swkhs0-sxlsd4-3z3v.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国77 | ⬇️ 724KB/s"
    obfs: ''
    obfs-password: ''
    password: 6e9cdc20-b92a-11ef-973a-f23c913c8d2b
    port: 8443
    server: 46347589-swin40-sww7b0-1qwp5.hy2.gotochinatown.net
    skip-cert-verify: true
    sni: 46347589-swin40-sww7b0-1qwp5.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国78 | ⬇️ 868KB/s"
    obfs: ''
    obfs-password: ''
    password: 861726bb-c61a-b0e0-99fa-5ddfdcf21902
    port: 8443
    server: 5387a16a-swexs0-ta5nd4-e06r.hy2.gotochinatown.net
    skip-cert-verify: true
    sni: 5387a16a-swexs0-ta5nd4-e06r.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国79 | ⬇️ 748KB/s"
    obfs: ''
    obfs-password: ''
    password: 31676725-b0fd-fed2-c857-2da8ce5a6e4f
    port: 8443
    server: b7d516b4-swd340-t0dyyh-jjv2.hy2.gotochinatown.net
    skip-cert-verify: true
    sni: b7d516b4-swd340-t0dyyh-jjv2.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国80 | ⬇️ 741KB/s"
    obfs: ''
    obfs-password: ''
    password: dcccacba-fa44-11ef-8400-f23c9164ca5d
    port: 8443
    server: b5c6e084-swexs0-sx0fe4-1j6h0.hy2.gotochinatown.net
    skip-cert-verify: true
    sni: b5c6e084-swexs0-sx0fe4-1j6h0.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1E8\U0001F1E6加拿大1 | ⬇️ 3.1MB/s"
    obfs: ''
    obfs-password: ''
    password: cfe032e4-1bc3-454f-9d09-301b0aea08ff
    port: 45979
    server: *************
    skip-cert-verify: true
    sni: www.bing.com
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国81 | ⬇️ 1.0MB/s"
    obfs: salamander
    obfs-password: NDhhNmY5YTY0MGYzOTgxYQ==
    password: f2efc2a2-92cf-4f36-adae-4c4336afc661
    port: 30073
    server: **************
    skip-cert-verify: true
    sni: ''
    type: hysteria2
    disable-sni: true
  - name: "\U0001F1FA\U0001F1F8美国82 | ⬇️ 769KB/s"
    obfs: salamander
    obfs-password: NDhhNmY5YTY0MGYzOTgxYQ==
    password: d1037421-3977-4cae-8462-0c7d22745e84
    port: 30073
    server: **************
    skip-cert-verify: true
    sni: ''
    type: hysteria2
    disable-sni: true
  - name: "\U0001F1FA\U0001F1F8美国83 | ⬇️ 3.7MB/s"
    obfs: ''
    obfs-password: ''
    password: 6e9cdc20-b92a-11ef-973a-f23c913c8d2b
    port: 8443
    server: 9d5cde75-sw3ts0-sww7b0-1qwp5.hy2.gotochinatown.net
    skip-cert-verify: true
    sni: 9d5cde75-sw3ts0-sww7b0-1qwp5.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国84 | ⬇️ 2.9MB/s"
    obfs: ''
    obfs-password: ''
    password: 48c1e014-28d6-11ec-a0fc-f23c913c8d2b
    port: 8443
    server: 7c8eb5c8-sw3ts0-sxlsd4-3z3v.hy2.gotochinatown.net
    skip-cert-verify: true
    sni: 7c8eb5c8-sw3ts0-sxlsd4-3z3v.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1F9\U0001F1FC台湾5 | ⬇️ 5.9MB/s"
    obfs: ''
    obfs-password: ''
    password: eaf4d7a9-482f-4d09-9029-691c81748448
    port: 30015
    server: tw1-vds14.anyhk.co
    skip-cert-verify: true
    sni: tw1-vds14.anyhk.co
    type: hysteria2
  - name: "\U0001F300其他10-VN | ⬇️ 2.0MB/s"
    obfs: ''
    obfs-password: ''
    password: ab4c9bf8-6bed-4dac-ba4e-461d8f589eda
    port: 30220
    server: vn1.xiaoliyu.us
    skip-cert-verify: true
    sni: vn1.xiaoliyu.us
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国85 | ⬇️ 5.0MB/s"
    obfs: ''
    obfs-password: ''
    password: 6e9cdc20-b92a-11ef-973a-f23c913c8d2b
    port: 8443
    server: fd4492f0-swexs0-sww7b0-1qwp5.hy2.gotochinatown.net
    skip-cert-verify: true
    sni: fd4492f0-swexs0-sww7b0-1qwp5.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国86 | ⬇️ 5.3MB/s"
    obfs: ''
    obfs-password: ''
    password: affe7124-c118-11ef-b6b2-f23c9164ca5d
    port: 8443
    server: efc7cb29-sw9ds0-td1w5f-1t3cz.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: efc7cb29-sw9ds0-td1w5f-1t3cz.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国87 | ⬇️ 3.4MB/s"
    obfs: ''
    obfs-password: ''
    password: 3fa7157d-efe8-832e-9b3c-582d78efbc52
    port: 8443
    server: b9b5771f-sw9ds0-tf70jh-vm13.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: b9b5771f-sw9ds0-tf70jh-vm13.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国88 | ⬇️ 2.0MB/s"
    obfs: ''
    obfs-password: ''
    password: 861726bb-c61a-b0e0-99fa-5ddfdcf21902
    port: 8443
    server: b9511d62-sw9ds0-ta5nd4-e06r.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: b9511d62-sw9ds0-ta5nd4-e06r.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国89 | ⬇️ 2.1MB/s"
    obfs: ''
    obfs-password: ''
    password: 384d1b42-655f-11ed-a8bf-f23c91cfbbc9
    port: 8443
    server: dae0bfd4-sw9ds0-sw9kdi-11p9g.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: dae0bfd4-sw9ds0-sw9kdi-11p9g.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国90 | ⬇️ 1.1MB/s"
    obfs: ''
    obfs-password: ''
    password: c3dc8d26-ab2b-11ec-a8bf-f23c91cfbbc9
    port: 8443
    server: a7d35550-sw9ds0-szapkm-1hpo0.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: a7d35550-sw9ds0-szapkm-1hpo0.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国91 | ⬇️ 4.0MB/s"
    obfs: ''
    obfs-password: ''
    password: b79d79ae-8bce-11ef-a2b8-f23c9164ca5d
    port: 8443
    server: 01825bf2-sw9ds0-t67sv3-1snfs.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 01825bf2-sw9ds0-t67sv3-1snfs.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国92 | ⬇️ 1.4MB/s"
    obfs: ''
    obfs-password: ''
    password: dcccacba-fa44-11ef-8400-f23c9164ca5d
    port: 8443
    server: a1fa790c-sw9ds0-sx0fe4-1j6h0.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: a1fa790c-sw9ds0-sx0fe4-1j6h0.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国93 | ⬇️ 2.2MB/s"
    obfs: ''
    obfs-password: ''
    password: 8e0f8ef2-5fe3-11ec-a8bf-f23c91cfbbc9
    port: 8443
    server: 6ce693ba-sw5og0-swdpud-duku.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 6ce693ba-sw5og0-swdpud-duku.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国94 | ⬇️ 1.5MB/s"
    obfs: ''
    obfs-password: ''
    password: 93fb69fc-77cf-11ee-85ee-f23c91369f2d
    port: 8443
    server: db34d2a0-sw3ts0-t12cnj-1ol97.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: db34d2a0-sw3ts0-t12cnj-1ol97.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国95 | ⬇️ 1.7MB/s"
    obfs: ''
    obfs-password: ''
    password: 93fb69fc-77cf-11ee-85ee-f23c91369f2d
    port: 8443
    server: d4e110b1-sw04g0-t12cnj-1ol97.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: d4e110b1-sw04g0-t12cnj-1ol97.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国96 | ⬇️ 1.1MB/s"
    obfs: ''
    obfs-password: ''
    password: 48c1e014-28d6-11ec-a0fc-f23c913c8d2b
    port: 8443
    server: 81af9d54-sw7j40-sxlsd4-3z3v.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 81af9d54-sw7j40-sxlsd4-3z3v.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国97 | ⬇️ 758KB/s"
    obfs: ''
    obfs-password: ''
    password: 6e9cdc20-b92a-11ef-973a-f23c913c8d2b
    port: 8443
    server: ac4417c1-sw7j40-sww7b0-1qwp5.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: ac4417c1-sw7j40-sww7b0-1qwp5.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国98 | ⬇️ 2.5MB/s"
    obfs: ''
    obfs-password: ''
    password: ef18aaca-4711-11ec-a8bf-f23c91cfbbc9
    port: 8443
    server: 459185ab-sw5og0-swa8xy-laev.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 459185ab-sw5og0-swa8xy-laev.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国99 | ⬇️ 1.0MB/s"
    obfs: ''
    obfs-password: ''
    password: 8e0f8ef2-5fe3-11ec-a8bf-f23c91cfbbc9
    port: 8443
    server: e7d4eee3-sw7j40-swdpud-duku.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: e7d4eee3-sw7j40-swdpud-duku.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国100 | ⬇️ 1.3MB/s"
    obfs: ''
    obfs-password: ''
    password: 93fb69fc-77cf-11ee-85ee-f23c91369f2d
    port: 8443
    server: bf24a574-sw7j40-t12cnj-1ol97.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: bf24a574-sw7j40-t12cnj-1ol97.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国101 | ⬇️ 1.4MB/s"
    obfs: ''
    obfs-password: ''
    password: e3975a05-b89e-11ec-a50d-f23c91cfbbc9
    port: 8443
    server: 045fb6b5-sw5og0-sxf56z-1f596.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 045fb6b5-sw5og0-sxf56z-1f596.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国102 | ⬇️ 2.1MB/s"
    obfs: ''
    obfs-password: ''
    password: 48c1e014-28d6-11ec-a0fc-f23c913c8d2b
    port: 8443
    server: 14b91eaf-sw5og0-sxlsd4-3z3v.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 14b91eaf-sw5og0-sxlsd4-3z3v.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国103 | ⬇️ 902KB/s"
    obfs: ''
    obfs-password: ''
    password: 6e9cdc20-b92a-11ef-973a-f23c913c8d2b
    port: 8443
    server: 6999f3e7-sw5og0-sww7b0-1qwp5.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 6999f3e7-sw5og0-sww7b0-1qwp5.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国104 | ⬇️ 1.1MB/s"
    obfs: ''
    obfs-password: ''
    password: ef18aaca-4711-11ec-a8bf-f23c91cfbbc9
    port: 8443
    server: eab236f6-sw7j40-swa8xy-laev.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: eab236f6-sw7j40-swa8xy-laev.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国105 | ⬇️ 1.0MB/s"
    obfs: ''
    obfs-password: ''
    password: 06b2ac52-fcc6-11ec-bb74-f23c9164ca5d
    port: 8443
    server: 0675b3a0-sw7j40-sznzxg-1jfvb.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 0675b3a0-sw7j40-sznzxg-1jfvb.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国106 | ⬇️ 2.4MB/s"
    obfs: ''
    obfs-password: ''
    password: e3975a05-b89e-11ec-a50d-f23c91cfbbc9
    port: 8443
    server: 471eceba-sw7j40-sxf56z-1f596.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 471eceba-sw7j40-sxf56z-1f596.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国107 | ⬇️ 856KB/s"
    obfs: ''
    obfs-password: ''
    password: 06b2ac52-fcc6-11ec-bb74-f23c9164ca5d
    port: 8443
    server: cfdf7114-sw3ts0-sznzxg-1jfvb.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: cfdf7114-sw3ts0-sznzxg-1jfvb.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国108 | ⬇️ 2.4MB/s"
    obfs: ''
    obfs-password: ''
    password: e3975a05-b89e-11ec-a50d-f23c91cfbbc9
    port: 8443
    server: a825bc64-sw3ts0-sxf56z-1f596.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: a825bc64-sw3ts0-sxf56z-1f596.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国109 | ⬇️ 3.1MB/s"
    obfs: ''
    obfs-password: ''
    password: ef18aaca-4711-11ec-a8bf-f23c91cfbbc9
    port: 8443
    server: 031a26ec-sw3ts0-swa8xy-laev.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 031a26ec-sw3ts0-swa8xy-laev.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国110 | ⬇️ 1.6MB/s"
    obfs: ''
    obfs-password: ''
    password: 336501b6-51d2-11ee-a993-f23c9164ca5d
    port: 8443
    server: 370d7219-swb8g0-t8kd6j-1c9em.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 370d7219-swb8g0-t8kd6j-1c9em.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国111 | ⬇️ 1.5MB/s"
    obfs: ''
    obfs-password: ''
    password: 31676725-b0fd-fed2-c857-2da8ce5a6e4f
    port: 8443
    server: 1a5a3781-swb8g0-t0dyyh-jjv2.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 1a5a3781-swb8g0-t0dyyh-jjv2.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国112 | ⬇️ 2.5MB/s"
    obfs: ''
    obfs-password: ''
    password: 0da8651e-e1f6-11ec-bd7c-f23c913c8d2b
    port: 8443
    server: c417f892-swb8g0-tcinla-hrtf.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: c417f892-swb8g0-tcinla-hrtf.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国113 | ⬇️ 1.7MB/s"
    obfs: ''
    obfs-password: ''
    password: 384d1b42-655f-11ed-a8bf-f23c91cfbbc9
    port: 8443
    server: bc749975-swb8g0-sw9kdi-11p9g.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: bc749975-swb8g0-sw9kdi-11p9g.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国114 | ⬇️ 1.2MB/s"
    obfs: ''
    obfs-password: ''
    password: e3975a05-b89e-11ec-a50d-f23c91cfbbc9
    port: 8443
    server: a3b0070d-swb8g0-sxf56z-1f596.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: a3b0070d-swb8g0-sxf56z-1f596.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国115 | ⬇️ 2.0MB/s"
    obfs: ''
    obfs-password: ''
    password: 93fb69fc-77cf-11ee-85ee-f23c91369f2d
    port: 8443
    server: 62e01c84-swb8g0-t12cnj-1ol97.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 62e01c84-swb8g0-t12cnj-1ol97.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国116 | ⬇️ 2.4MB/s"
    obfs: ''
    obfs-password: ''
    password: c3dc8d26-ab2b-11ec-a8bf-f23c91cfbbc9
    port: 8443
    server: 880c5b7d-swb8g0-szapkm-1hpo0.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 880c5b7d-swb8g0-szapkm-1hpo0.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国117 | ⬇️ 815KB/s"
    obfs: ''
    obfs-password: ''
    password: 34f0cf42-0f8a-11ec-a8bf-f23c91cfbbc9
    port: 8443
    server: 359805eb-swb8g0-szdere-155d9.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 359805eb-swb8g0-szdere-155d9.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国118 | ⬇️ 1.2MB/s"
    obfs: ''
    obfs-password: ''
    password: 8e0f8ef2-5fe3-11ec-a8bf-f23c91cfbbc9
    port: 8443
    server: 14917b67-swb8g0-swdpud-duku.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 14917b67-swb8g0-swdpud-duku.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国119 | ⬇️ 930KB/s"
    obfs: ''
    obfs-password: ''
    password: 91365a7a-46d7-11ee-a8b9-f23c9164ca5d
    port: 8443
    server: b207d030-swb8g0-t3o6u7-1osdm.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: b207d030-swb8g0-t3o6u7-1osdm.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国120 | ⬇️ 888KB/s"
    obfs: ''
    obfs-password: ''
    password: 06b2ac52-fcc6-11ec-bb74-f23c9164ca5d
    port: 8443
    server: 9ee5e960-swb8g0-sznzxg-1jfvb.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 9ee5e960-swb8g0-sznzxg-1jfvb.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国121 | ⬇️ 1.6MB/s"
    obfs: ''
    obfs-password: ''
    password: 6e9cdc20-b92a-11ef-973a-f23c913c8d2b
    port: 8443
    server: 2598c9cf-swb8g0-sww7b0-1qwp5.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 2598c9cf-swb8g0-sww7b0-1qwp5.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国122 | ⬇️ 1.6MB/s"
    obfs: ''
    obfs-password: ''
    password: d4018e28-e328-11ed-98a7-f23c913c8d2b
    port: 8443
    server: 7554fe25-swb8g0-sx3h07-1g8k0.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 7554fe25-swb8g0-sx3h07-1g8k0.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国123 | ⬇️ 1.3MB/s"
    obfs: ''
    obfs-password: ''
    password: 861726bb-c61a-b0e0-99fa-5ddfdcf21902
    port: 8443
    server: 889ff630-swb8g0-ta5nd4-e06r.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 889ff630-swb8g0-ta5nd4-e06r.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国124 | ⬇️ 1.2MB/s"
    obfs: ''
    obfs-password: ''
    password: ef18aaca-4711-11ec-a8bf-f23c91cfbbc9
    port: 8443
    server: 648d4621-svukg0-swa8xy-laev.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 648d4621-svukg0-swa8xy-laev.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国125 | ⬇️ 2.5MB/s"
    obfs: ''
    obfs-password: ''
    password: 6e9cdc20-b92a-11ef-973a-f23c913c8d2b
    port: 8443
    server: 41490f6a-svsps0-sww7b0-1qwp5.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 41490f6a-svsps0-sww7b0-1qwp5.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国126 | ⬇️ 1.6MB/s"
    obfs: ''
    obfs-password: ''
    password: 06b2ac52-fcc6-11ec-bb74-f23c9164ca5d
    port: 8443
    server: 7e7a9d72-svukg0-sznzxg-1jfvb.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 7e7a9d72-svukg0-sznzxg-1jfvb.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国127 | ⬇️ 2.8MB/s"
    obfs: ''
    obfs-password: ''
    password: 93fb69fc-77cf-11ee-85ee-f23c91369f2d
    port: 8443
    server: 3d6f7a99-svukg0-t12cnj-1ol97.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 3d6f7a99-svukg0-t12cnj-1ol97.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国128 | ⬇️ 2.7MB/s"
    obfs: ''
    obfs-password: ''
    password: ef18aaca-4711-11ec-a8bf-f23c91cfbbc9
    port: 8443
    server: 626852f0-svwf40-swa8xy-laev.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 626852f0-svwf40-swa8xy-laev.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国129 | ⬇️ 3.0MB/s"
    obfs: ''
    obfs-password: ''
    password: 6e9cdc20-b92a-11ef-973a-f23c913c8d2b
    port: 8443
    server: 1aeedbcb-svukg0-sww7b0-1qwp5.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 1aeedbcb-svukg0-sww7b0-1qwp5.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国130 | ⬇️ 1.2MB/s"
    obfs: ''
    obfs-password: ''
    password: 06b2ac52-fcc6-11ec-bb74-f23c9164ca5d
    port: 8443
    server: 4aca605f-svwf40-sznzxg-1jfvb.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 4aca605f-svwf40-sznzxg-1jfvb.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国131 | ⬇️ 1.4MB/s"
    obfs: ''
    obfs-password: ''
    password: 8e0f8ef2-5fe3-11ec-a8bf-f23c91cfbbc9
    port: 8443
    server: 773be4f9-svukg0-swdpud-duku.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 773be4f9-svukg0-swdpud-duku.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国132 | ⬇️ 1.9MB/s"
    obfs: ''
    obfs-password: ''
    password: 48c1e014-28d6-11ec-a0fc-f23c913c8d2b
    port: 8443
    server: 2488e8d7-svukg0-sw0dp4-3z3v.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 2488e8d7-svukg0-sw0dp4-3z3v.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国133 | ⬇️ 1.0MB/s"
    obfs: ''
    obfs-password: ''
    password: 384d1b42-655f-11ed-a8bf-f23c91cfbbc9
    port: 8443
    server: b123baa3-swin40-sxuzn1-11p9g.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: b123baa3-swin40-sxuzn1-11p9g.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国134 | ⬇️ 5.8MB/s"
    obfs: ''
    obfs-password: ''
    password: 0A1DDFE6-A16A-43FA-A6E3-70EDE4EA61C3
    port: 7020
    server: us4.dexlos.com
    skip-cert-verify: false
    sni: us4.dexlos.com
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国135 | ⬇️ 1.2MB/s"
    obfs: ''
    obfs-password: ''
    password: e3975a05-b89e-11ec-a50d-f23c91cfbbc9
    port: 8443
    server: 853ea9e9-svwf40-sxf56z-1f596.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 853ea9e9-svwf40-sxf56z-1f596.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国136 | ⬇️ 1.0MB/s"
    obfs: ''
    obfs-password: ''
    password: 31676725-b0fd-fed2-c857-2da8ce5a6e4f
    port: 8443
    server: ff205e00-swin40-t0dyyh-jjv2.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: ff205e00-swin40-t0dyyh-jjv2.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国137 | ⬇️ 1.2MB/s"
    obfs: ''
    obfs-password: ''
    password: dcccacba-fa44-11ef-8400-f23c9164ca5d
    port: 8443
    server: 16d5db0d-swkhs0-sx0fe4-1j6h0.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 16d5db0d-swkhs0-sx0fe4-1j6h0.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国138 | ⬇️ 748KB/s"
    obfs: ''
    obfs-password: ''
    password: 48c1e014-28d6-11ec-a0fc-f23c913c8d2b
    port: 8443
    server: 2f0a18df-swexs0-sxlsd4-3z3v.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 2f0a18df-swexs0-sxlsd4-3z3v.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国139 | ⬇️ 971KB/s"
    obfs: ''
    obfs-password: ''
    password: 48c1e014-28d6-11ec-a0fc-f23c913c8d2b
    port: 8443
    server: ee6a9d89-swd340-sxlsd4-3z3v.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: ee6a9d89-swd340-sxlsd4-3z3v.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国140 | ⬇️ 1.6MB/s"
    obfs: ''
    obfs-password: ''
    password: b79d79ae-8bce-11ef-a2b8-f23c9164ca5d
    port: 8443
    server: 7065741c-swd340-t67sv3-1snfs.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 7065741c-swd340-t67sv3-1snfs.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国141 | ⬇️ 1.7MB/s"
    obfs: ''
    obfs-password: ''
    password: 6e9cdc20-b92a-11ef-973a-f23c913c8d2b
    port: 8443
    server: 47e6484a-swd340-sww7b0-1qwp5.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 47e6484a-swd340-sww7b0-1qwp5.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国142 | ⬇️ 1.1MB/s"
    obfs: ''
    obfs-password: ''
    password: 06b2ac52-fcc6-11ec-bb74-f23c9164ca5d
    port: 8443
    server: c367281f-swexs0-sznzxg-1jfvb.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: c367281f-swexs0-sznzxg-1jfvb.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国143 | ⬇️ 1.6MB/s"
    obfs: ''
    obfs-password: ''
    password: d4018e28-e328-11ed-98a7-f23c913c8d2b
    port: 8443
    server: 9b454679-swin40-sx3h07-1g8k0.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 9b454679-swin40-sx3h07-1g8k0.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国144 | ⬇️ 1.3MB/s"
    obfs: ''
    obfs-password: ''
    password: affe7124-c118-11ef-b6b2-f23c9164ca5d
    port: 8443
    server: a65dad8f-swexs0-td1w5f-1t3cz.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: a65dad8f-swexs0-td1w5f-1t3cz.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国145 | ⬇️ 1.2MB/s"
    obfs: ''
    obfs-password: ''
    password: 336501b6-51d2-11ee-a993-f23c9164ca5d
    port: 8443
    server: 9b686548-swkhs0-t8kd6j-1c9em.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 9b686548-swkhs0-t8kd6j-1c9em.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国146 | ⬇️ 1.1MB/s"
    obfs: ''
    obfs-password: ''
    password: affe7124-c118-11ef-b6b2-f23c9164ca5d
    port: 8443
    server: 95ca7861-swd340-td1w5f-1t3cz.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 95ca7861-swd340-td1w5f-1t3cz.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国147 | ⬇️ 1.0MB/s"
    obfs: ''
    obfs-password: ''
    password: 384d1b42-655f-11ed-a8bf-f23c91cfbbc9
    port: 8443
    server: 42b39531-swkhs0-sxuzn1-11p9g.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 42b39531-swkhs0-sxuzn1-11p9g.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国148 | ⬇️ 883KB/s"
    obfs: ''
    obfs-password: ''
    password: d4018e28-e328-11ed-98a7-f23c913c8d2b
    port: 8443
    server: 32fe3b1a-swd340-sx3h07-1g8k0.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 32fe3b1a-swd340-sx3h07-1g8k0.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国149 | ⬇️ 889KB/s"
    obfs: ''
    obfs-password: ''
    password: 384d1b42-655f-11ed-a8bf-f23c91cfbbc9
    port: 8443
    server: b46251be-swexs0-sxuzn1-11p9g.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: b46251be-swexs0-sxuzn1-11p9g.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国150 | ⬇️ 1.3MB/s"
    obfs: ''
    obfs-password: ''
    password: 06b2ac52-fcc6-11ec-bb74-f23c9164ca5d
    port: 8443
    server: 477ed50b-swkhs0-sznzxg-1jfvb.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 477ed50b-swkhs0-sznzxg-1jfvb.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国151 | ⬇️ 1.2MB/s"
    obfs: ''
    obfs-password: ''
    password: 93fb69fc-77cf-11ee-85ee-f23c91369f2d
    port: 8443
    server: b80c4608-swin40-t12cnj-1ol97.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: b80c4608-swin40-t12cnj-1ol97.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国152 | ⬇️ 1.8MB/s"
    obfs: ''
    obfs-password: ''
    password: c3dc8d26-ab2b-11ec-a8bf-f23c91cfbbc9
    port: 8443
    server: c970c5df-swin40-szapkm-1hpo0.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: c970c5df-swin40-szapkm-1hpo0.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国153 | ⬇️ 851KB/s"
    obfs: ''
    obfs-password: ''
    password: dcccacba-fa44-11ef-8400-f23c9164ca5d
    port: 8443
    server: be6901de-swin40-sx0fe4-1j6h0.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: be6901de-swin40-sx0fe4-1j6h0.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国154 | ⬇️ 1.1MB/s"
    obfs: ''
    obfs-password: ''
    password: cfe61764-0004-11f0-a910-f23c9164ca5d
    port: 8443
    server: a3385fc5-swd340-sx9j1n-1timk.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: a3385fc5-swd340-sx9j1n-1timk.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国155 | ⬇️ 1.3MB/s"
    obfs: ''
    obfs-password: ''
    password: e3975a05-b89e-11ec-a50d-f23c91cfbbc9
    port: 8443
    server: 5c7931d5-swkhs0-sxf56z-1f596.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 5c7931d5-swkhs0-sxf56z-1f596.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国156 | ⬇️ 939KB/s"
    obfs: ''
    obfs-password: ''
    password: bb85e074-b0c2-11ea-ad28-f23c913c8d2b
    port: 8443
    server: ba50c9e3-swkhs0-sx9mz9-m0b9.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: ba50c9e3-swkhs0-sx9mz9-m0b9.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国157 | ⬇️ 1.4MB/s"
    obfs: ''
    obfs-password: ''
    password: b79d79ae-8bce-11ef-a2b8-f23c9164ca5d
    port: 8443
    server: 4a7cc09c-swin40-t67sv3-1snfs.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 4a7cc09c-swin40-t67sv3-1snfs.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国158 | ⬇️ 880KB/s"
    obfs: ''
    obfs-password: ''
    password: 6e9cdc20-b92a-11ef-973a-f23c913c8d2b
    port: 8443
    server: 53257ad4-swkhs0-sww7b0-1qwp5.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 53257ad4-swkhs0-sww7b0-1qwp5.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国159 | ⬇️ 876KB/s"
    obfs: ''
    obfs-password: ''
    password: 34f0cf42-0f8a-11ec-a8bf-f23c91cfbbc9
    port: 8443
    server: 94e6cde3-swd340-szdere-155d9.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 94e6cde3-swd340-szdere-155d9.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国160 | ⬇️ 813KB/s"
    obfs: ''
    obfs-password: ''
    password: 7d7c111e-fbc1-11ef-ab5f-f23c913c8d2b
    port: 8443
    server: 58e44587-swkhs0-sxhurg-1th8j.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 58e44587-swkhs0-sxhurg-1th8j.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国161 | ⬇️ 840KB/s"
    obfs: ''
    obfs-password: ''
    password: affe7124-c118-11ef-b6b2-f23c9164ca5d
    port: 8443
    server: fbbf8a77-swin40-td1w5f-1t3cz.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: fbbf8a77-swin40-td1w5f-1t3cz.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国162 | ⬇️ 744KB/s"
    obfs: ''
    obfs-password: ''
    password: cfe61764-0004-11f0-a910-f23c9164ca5d
    port: 8443
    server: 8254f960-swin40-sx9j1n-1timk.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 8254f960-swin40-sx9j1n-1timk.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国163 | ⬇️ 782KB/s"
    obfs: ''
    obfs-password: ''
    password: 34f0cf42-0f8a-11ec-a8bf-f23c91cfbbc9
    port: 8443
    server: 343a0166-swkhs0-szdere-155d9.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 343a0166-swkhs0-szdere-155d9.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国164 | ⬇️ 874KB/s"
    obfs: ''
    obfs-password: ''
    password: 7d7c111e-fbc1-11ef-ab5f-f23c913c8d2b
    port: 8443
    server: dc94cb09-swexs0-sxhurg-1th8j.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: dc94cb09-swexs0-sxhurg-1th8j.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国165 | ⬇️ 875KB/s"
    obfs: ''
    obfs-password: ''
    password: 31676725-b0fd-fed2-c857-2da8ce5a6e4f
    port: 8443
    server: af4f9bf4-swkhs0-t0dyyh-jjv2.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: af4f9bf4-swkhs0-t0dyyh-jjv2.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国166 | ⬇️ 1.2MB/s"
    obfs: ''
    obfs-password: ''
    password: 3fa7157d-efe8-832e-9b3c-582d78efbc52
    port: 8443
    server: ab9201b1-swexs0-tf70jh-vm13.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: ab9201b1-swexs0-tf70jh-vm13.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国167 | ⬇️ 799KB/s"
    obfs: ''
    obfs-password: ''
    password: 3fa7157d-efe8-832e-9b3c-582d78efbc52
    port: 8443
    server: ae3e3106-swkhs0-tf70jh-vm13.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: ae3e3106-swkhs0-tf70jh-vm13.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国168 | ⬇️ 1.1MB/s"
    obfs: ''
    obfs-password: ''
    password: cfe61764-0004-11f0-a910-f23c9164ca5d
    port: 8443
    server: 491ec6da-swkhs0-sx9j1n-1timk.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 491ec6da-swkhs0-sx9j1n-1timk.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国169 | ⬇️ 1.2MB/s"
    obfs: ''
    obfs-password: ''
    password: 088a0bbe-4f9f-11ea-a15d-f23c913c8d2b
    port: 8443
    server: 2844f137-swkhs0-t5vrf3-716s.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 2844f137-swkhs0-t5vrf3-716s.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国170 | ⬇️ 1.1MB/s"
    obfs: ''
    obfs-password: ''
    password: 336501b6-51d2-11ee-a993-f23c9164ca5d
    port: 8443
    server: 8368a39a-swexs0-t8kd6j-1c9em.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 8368a39a-swexs0-t8kd6j-1c9em.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国171 | ⬇️ 958KB/s"
    obfs: ''
    obfs-password: ''
    password: 93fb69fc-77cf-11ee-85ee-f23c91369f2d
    port: 8443
    server: 18b45d29-swkhs0-t12cnj-1ol97.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 18b45d29-swkhs0-t12cnj-1ol97.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国172 | ⬇️ 1.0MB/s"
    obfs: ''
    obfs-password: ''
    password: 91365a7a-46d7-11ee-a8b9-f23c9164ca5d
    port: 8443
    server: 0a689679-swd340-t3o6u7-1osdm.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 0a689679-swd340-t3o6u7-1osdm.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国173 | ⬇️ 1.0MB/s"
    obfs: ''
    obfs-password: ''
    password: 0da8651e-e1f6-11ec-bd7c-f23c913c8d2b
    port: 8443
    server: 2f086087-swkhs0-tcinla-hrtf.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 2f086087-swkhs0-tcinla-hrtf.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国174 | ⬇️ 777KB/s"
    obfs: ''
    obfs-password: ''
    password: 8e0f8ef2-5fe3-11ec-a8bf-f23c91cfbbc9
    port: 8443
    server: ce1b755e-swkhs0-t14dgl-duku.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: ce1b755e-swkhs0-t14dgl-duku.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国175 | ⬇️ 1.0MB/s"
    obfs: ''
    obfs-password: ''
    password: cfe61764-0004-11f0-a910-f23c9164ca5d
    port: 8443
    server: 2f4cee5e-swexs0-sx9j1n-1timk.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 2f4cee5e-swexs0-sx9j1n-1timk.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国176 | ⬇️ 1.4MB/s"
    obfs: ''
    obfs-password: ''
    password: bcc58e88-e147-11ec-b286-f23c91cfbbc9
    port: 8443
    server: 03af7657-swin40-sxu45k-ggww.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 03af7657-swin40-sxu45k-ggww.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国177 | ⬇️ 937KB/s"
    obfs: ''
    obfs-password: ''
    password: b79d79ae-8bce-11ef-a2b8-f23c9164ca5d
    port: 8443
    server: 8a049395-swkhs0-t67sv3-1snfs.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 8a049395-swkhs0-t67sv3-1snfs.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国178 | ⬇️ 1.3MB/s"
    obfs: ''
    obfs-password: ''
    password: 861726bb-c61a-b0e0-99fa-5ddfdcf21902
    port: 8443
    server: 72f61c73-swkhs0-ta5nd4-e06r.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 72f61c73-swkhs0-ta5nd4-e06r.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国179 | ⬇️ 1.0MB/s"
    obfs: ''
    obfs-password: ''
    password: e3975a05-b89e-11ec-a50d-f23c91cfbbc9
    port: 8443
    server: ecabd45b-swd340-sxf56z-1f596.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: ecabd45b-swd340-sxf56z-1f596.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国180 | ⬇️ 1019KB/s"
    obfs: ''
    obfs-password: ''
    password: 93fb69fc-77cf-11ee-85ee-f23c91369f2d
    port: 8443
    server: 9d0a15bc-swd340-t12cnj-1ol97.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 9d0a15bc-swd340-t12cnj-1ol97.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国181 | ⬇️ 772KB/s"
    obfs: ''
    obfs-password: ''
    password: 336501b6-51d2-11ee-a993-f23c9164ca5d
    port: 8443
    server: 81c661be-swd340-t8kd6j-1c9em.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 81c661be-swd340-t8kd6j-1c9em.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国182 | ⬇️ 1.3MB/s"
    obfs: ''
    obfs-password: ''
    password: 861726bb-c61a-b0e0-99fa-5ddfdcf21902
    port: 8443
    server: 5409b405-swd340-ta5nd4-e06r.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 5409b405-swd340-ta5nd4-e06r.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国183 | ⬇️ 1.3MB/s"
    obfs: ''
    obfs-password: ''
    password: 93fb69fc-77cf-11ee-85ee-f23c91369f2d
    port: 8443
    server: b3323c49-swexs0-t12cnj-1ol97.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: b3323c49-swexs0-t12cnj-1ol97.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国184 | ⬇️ 1.4MB/s"
    obfs: ''
    obfs-password: ''
    password: 8e0f8ef2-5fe3-11ec-a8bf-f23c91cfbbc9
    port: 8443
    server: 3056684d-swexs0-swdpud-duku.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 3056684d-swexs0-swdpud-duku.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国185 | ⬇️ 943KB/s"
    obfs: ''
    obfs-password: ''
    password: b79d79ae-8bce-11ef-a2b8-f23c9164ca5d
    port: 8443
    server: e7af2fff-swexs0-t67sv3-1snfs.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: e7af2fff-swexs0-t67sv3-1snfs.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国186 | ⬇️ 1.2MB/s"
    obfs: ''
    obfs-password: ''
    password: 34f0cf42-0f8a-11ec-a8bf-f23c91cfbbc9
    port: 8443
    server: 0865ad3c-swexs0-szdere-155d9.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 0865ad3c-swexs0-szdere-155d9.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国187 | ⬇️ 685KB/s"
    obfs: ''
    obfs-password: ''
    password: 0da8651e-e1f6-11ec-bd7c-f23c913c8d2b
    port: 8443
    server: 66a0ae32-swexs0-tcinla-hrtf.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 66a0ae32-swexs0-tcinla-hrtf.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国188 | ⬇️ 736KB/s"
    obfs: ''
    obfs-password: ''
    password: bcc58e88-e147-11ec-b286-f23c91cfbbc9
    port: 8443
    server: b50f3f1e-swexs0-sxu45k-ggww.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: b50f3f1e-swexs0-sxu45k-ggww.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国189 | ⬇️ 689KB/s"
    obfs: ''
    obfs-password: ''
    password: c3dc8d26-ab2b-11ec-a8bf-f23c91cfbbc9
    port: 8443
    server: fa13afea-swd340-szapkm-1hpo0.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: fa13afea-swd340-szapkm-1hpo0.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国190 | ⬇️ 621KB/s"
    obfs: ''
    obfs-password: ''
    password: 8e0f8ef2-5fe3-11ec-a8bf-f23c91cfbbc9
    port: 8443
    server: b43c1e95-swd340-swdpud-duku.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: b43c1e95-swd340-swdpud-duku.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国191 | ⬇️ 635KB/s"
    obfs: ''
    obfs-password: ''
    password: bb85e074-b0c2-11ea-ad28-f23c913c8d2b
    port: 8443
    server: b563ae1f-swexs0-sx9mz9-m0b9.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: b563ae1f-swexs0-sx9mz9-m0b9.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国192 | ⬇️ 2.7MB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: "/Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
  - name: "\U0001F1FA\U0001F1F8美国193 | ⬇️ 701KB/s"
    network: ws
    port: 80
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: '/Telegram:@vpnAndroid2/?ed=2560'
    servername: reedfree8mahsang2.redorg.ir
  - name: "\U0001F1E9\U0001F1EA德国7 | ⬇️ 1.1MB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /TelegramU0001F1E8U0001F1F3 @MxlShare @WangCai2 /?ed=2560
    servername: reedfree8mahsang2.redorg.ir
  - cipher: aes-256-gcm
    name: "\U0001F1F8\U0001F1EC新加坡10 | ⬇️ 920KB/s"
    password: QXH9L1BTV9YQBEPS
    port: 16002
    server: ti3hyra4.slashdevslashnetslashtun.net
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1F8\U0001F1EC新加坡11 | ⬇️ 1.5MB/s"
    password: VVWFEBXUO91OCQSY
    port: 16008
    server: ti3hyra4.slashdevslashnetslashtun.net
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1EF\U0001F1F5日本12 | ⬇️ 615KB/s"
    password: 4M2YKH5SAKJIZMQ3
    port: 18014
    server: 8tv68qhq.slashdevslashnetslashtun.net
    type: ss
    udp: true
  - auth: bb85e074-b0c2-11ea-ad28-f23c913c8d2b
    name: "\U0001F1FA\U0001F1F8美国194 | ⬇️ 4.2MB/s"
    password: bb85e074-b0c2-11ea-ad28-f23c913c8d2b
    port: 8443
    server: 2d6c68cd-swxgg0-sx9mz9-m0b9.hy2.gotochinatown.net
    type: hysteria2
    udp: true
    sni: 2d6c68cd-swxgg0-sx9mz9-m0b9.hy2.gotochinatown.net
  - name: "\U0001F1FA\U0001F1F8美国195 | ⬇️ 1012KB/s"
    network: ws
    port: 8880
    server: ***********
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /TelegramU0001F1E8U0001F1F3 @MxlShare @WangCai2 /?ed=2560
    servername: reedfree8mahsang2.redorg.ir
  - name: "\U0001F1E9\U0001F1EA德国8 | ⬇️ 708KB/s"
    network: ws
    port: 80
    server: *************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: "/Telegram\U0001F1E8\U0001F1F3 @WangCai2 /?ed=2560"
  - cipher: aes-256-gcm
    name: "\U0001F300其他11-GR | ⬇️ 3.2MB/s"
    password: RUCALP5TJJW2C4UF
    port: 20004
    server: **************
    type: ss
    udp: true
  - name: "\U0001F1EB\U0001F1EE芬兰2 | ⬇️ 529KB/s"
    network: ws
    port: 8880
    server: **********
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: "/Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
  - name: "\U0001F1F3\U0001F1F1荷兰1 | ⬇️ 1.8MB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /TelegramU0001F1E8U0001F1F3 @MxlShare @WangCai2 /?ed=2560
    servername: reedfree8mahsang2.redorg.ir
  - name: "\U0001F1FA\U0001F1F8美国196 | ⬇️ 6.9MB/s"
    password: ef18aaca-4711-11ec-a8bf-f23c91cfbbc9
    port: 15229
    server: b02d624d-swxgg0-sy439s-laev.cu.plebai.net
    type: trojan
    udp: true
    sni: b02d624d-swxgg0-sy439s-laev.cu.plebai.net
  - name: "\U0001F1E9\U0001F1EA德国9 | ⬇️ 4.5MB/s"
    network: ws
    port: 8880
    server: ***********
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: "/Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
  - name: "\U0001F1EE\U0001F1F3印度1 | ⬇️ 750KB/s"
    network: ws
    port: 80
    server: *************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: '/Telegram: @vpnAndroid2/?ed=2560'
  - auth: d4018e28-e328-11ed-98a7-f23c913c8d2b
    name: "\U0001F1FA\U0001F1F8美国197 | ⬇️ 2.5MB/s"
    password: d4018e28-e328-11ed-98a7-f23c913c8d2b
    port: 8443
    server: 2179bb5b-swxgg0-sx3h07-1g8k0.hy2.gotochinatown.net
    type: hysteria2
    udp: true
    sni: 2179bb5b-swxgg0-sx3h07-1g8k0.hy2.gotochinatown.net
  - cipher: chacha20-ietf
    name: "\U0001F1F8\U0001F1EC新加坡12 | ⬇️ 14.4MB/s"
    password: asd123456
    port: 8388
    server: ************
    type: ss
    udp: true
  - cipher: aes-256-cfb
    name: "\U0001F300其他12-PT | ⬇️ 2.2MB/s"
    password: f8f7aCzcPKbsF8p3
    port: 989
    server: ***************
    type: ss
    udp: true
  - name: "\U0001F1FA\U0001F1F8美国198 | ⬇️ 1.8MB/s"
    password: 94d40708-8273-11ea-8fc9-f23c913c8d2b
    port: 15229
    server: 74603ad1-swxgg0-sye5o0-mv7m.cu.plebai.net
    type: trojan
    udp: true
    sni: 74603ad1-swxgg0-sye5o0-mv7m.cu.plebai.net
  - alpn:
      - ''
    alterId: '0'
    cipher: auto
    name: "\U0001F1ED\U0001F1F0香港21 | ⬇️ 9.6MB/s"
    network: ws
    port: 8080
    server: 209aeef5-sw1z40-swqixu-1luqs.hgc1.tcpbbr.net
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 788328ee-d49f-11ef-bd97-f23c9164ca5d
    ws-opts:
      headers:
        Host: broadcastlv.chat.bilibili.com
      path: /
    xudp: true
  - alpn:
      - ''
    alterId: '2'
    cipher: auto
    name: "\U0001F300其他13-未识别 | ⬇️ 2.5MB/s"
    network: ws
    port: 30808
    server: v8.heduian.link
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: cbb3f877-d1fb-344c-87a9-d153bffd5484
    ws-opts:
      headers:
        Host: baidu.com
      path: /oooo
    xudp: true
  - client-fingerprint: chrome
    name: "\U0001F1F9\U0001F1FC台湾6 | ⬇️ 4.7MB/s"
    network: ws
    port: 443
    server: nnctwclyx.codesofun.pp.ua
    tls: true
    type: vless
    udp: true
    uuid: 8b9c63ff-6789-449d-e5fd-5b83fef2b440
    ws-opts:
      headers:
        Host: nnctw.codesofun.pp.ua
        User-Agent: >-
          Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML,
          like Gecko) Chrome/66.0.3359.117 Safari/537.36
      path: /2261acb5
    xudp: true
    servername: nnctw.codesofun.pp.ua
  - cipher: aes-128-gcm
    name: "\U0001F1F9\U0001F1FC台湾7 | ⬇️ 6.2MB/s"
    password: 763bf612-4c66-4fd4-b54b-5349bdea6bca
    port: 571
    server: cntwlayer1.labexit.xyz
    type: ss
    udp: true
  - alpn:
      - ''
    alterId: '2'
    cipher: auto
    name: "\U0001F300其他14-未识别 | ⬇️ 645KB/s"
    network: ws
    port: 30837
    server: v37.heduian.link
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: cbb3f877-d1fb-344c-87a9-d153bffd5484
    ws-opts:
      headers:
        Host: v37.heduian.link
      path: /oooo
    xudp: true
  - cipher: aes-256-cfb
    name: "\U0001F1EC\U0001F1E7英国4 | ⬇️ 678KB/s"
    password: f8f7aCzcPKbsF8p3
    port: 989
    server: *************
    type: ss
    udp: true
  - alpn:
      - ''
    alterId: '0'
    cipher: auto
    name: "\U0001F1F0\U0001F1F7韩国4 | ⬇️ 775KB/s"
    network: ws
    port: 2096
    server: dv4.789900.xyz
    skip-cert-verify: false
    tls: true
    type: vmess
    udp: true
    uuid: 0c8f3e29-4ec9-4ba6-a6ef-4487873f5d05
    ws-opts:
      headers:
        Host: dv4.789900.xyz
      path: /0c8f3e29-4ec9-4ba6-a6ef-4487873f5d05-vm
    xudp: true
    servername: dv4.789900.xyz
  - alpn:
      - ''
    alterId: '2'
    cipher: auto
    name: "\U0001F300其他15-未识别 | ⬇️ 884KB/s"
    network: ws
    port: 30835
    server: v35.heduian.link
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: cbb3f877-d1fb-344c-87a9-d153bffd5484
    ws-opts:
      headers:
        Host: baidu.com
      path: /oooo
    xudp: true
  - cipher: aes-128-gcm
    name: "\U0001F1E9\U0001F1EA德国10 | ⬇️ 6.0MB/s"
    password: 763bf612-4c66-4fd4-b54b-5349bdea6bca
    port: 640
    server: neweur.upperlay.xyz
    type: ss
    udp: true
  - cipher: aes-128-gcm
    name: "\U0001F1F7\U0001F1FA俄罗斯2 | ⬇️ 5.8MB/s"
    password: 763bf612-4c66-4fd4-b54b-5349bdea6bca
    port: 631
    server: neweur.upperlay.xyz
    type: ss
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1FA\U0001F1F8美国199 | ⬇️ 6.7MB/s"
    network: ws
    port: 443
    server: **************
    tls: true
    type: vless
    udp: true
    uuid: aa424865-2762-404c-b767-66c9f98e026b
    ws-opts:
      headers:
        Host: edS859886XYzXS.859886.xYZ
        User-Agent: >-
          Mozilla/5.0 (Windows NT 6.1) AppleWebKit/537.36 (KHTML, like Gecko)
          Chrome/55.0.2883.87 Safari/537.36
      path: /25Rajr8UdFGa29fCwxS
    xudp: true
    servername: edS859886XYzXS.859886.xYZ
  - client-fingerprint: chrome
    name: "\U0001F1FA\U0001F1F8美国200 | ⬇️ 8.8MB/s"
    network: ws
    port: 443
    server: **************
    tls: true
    type: vless
    udp: true
    uuid: ca48571a-403f-44b9-b2a6-e17c36d7cacc
    ws-opts:
      headers:
        Host: 10duuuu.288288.shop
        User-Agent: >-
          Mozilla/5.0 (Windows NT 6.3; Win64; x64) AppleWebKit/537.36 (KHTML,
          like Gecko) Chrome/67.0.3396.99 Safari/537.36
      path: /uDLWPKXENxF2ulPWRIwOavJd
    xudp: true
    servername: 10duuuu.288288.shop
  - client-fingerprint: chrome
    name: "\U0001F1FA\U0001F1F8美国201 | ⬇️ 505KB/s"
    network: ws
    port: 443
    server: *************
    tls: true
    type: vless
    udp: true
    uuid: 5453ae26-250d-4e79-b4ec-016baf806865
    ws-opts:
      headers:
        Host: 7d33510a-5dA0-4F85-b1d5-5321a9eaA72a.890603.pP.uA
        User-Agent: >-
          Mozilla/5.0 (Windows NT 6.3; Win64; x64) AppleWebKit/537.36 (KHTML,
          like Gecko) Chrome/66.0.3359.117 Safari/537.36
      path: /XcQF058rNJ3gc4aj
    xudp: true
    servername: 7d33510a-5dA0-4F85-b1d5-5321a9eaA72a.890603.pP.uA
  - client-fingerprint: chrome
    name: "\U0001F1FA\U0001F1F8美国202 | ⬇️ 1.6MB/s"
    network: ws
    port: 443
    server: *************
    tls: true
    type: vless
    udp: true
    uuid: 52c0e96b-b8d7-44a5-a995-bd866bf39ec6
    ws-opts:
      headers:
        Host: M10DdD.457.Pp.ua
        User-Agent: >-
          Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like
          Gecko) Chrome/54.0.2840.71 Safari/537.36
      path: /YzFLmzNIEQsAmceLxv94lPbgMw
    xudp: true
    servername: M10DdD.457.Pp.ua
  - alpn:
      - ''
    alterId: '0'
    cipher: auto
    name: "\U0001F300其他16-未识别 | ⬇️ 1.4MB/s"
    network: ws
    port: 443
    server: cloudgetservice.mcloudservice.site
    skip-cert-verify: false
    tls: true
    type: vmess
    udp: true
    uuid: 81cfc74e-79f6-4187-9c11-58b6acdc0e83
    ws-opts:
      headers:
        Host: uSa-Vp-149.BLazECloudMain.Site
      path: /linkvkws
    xudp: true
    servername: uSa-Vp-149.BLazECloudMain.Site
  - client-fingerprint: chrome
    grpc-opts:
      grpc-service-name: b
    name: "\U0001F300其他17-SE | ⬇️ 1.0MB/s"
    network: grpc
    port: 33000
    reality-opts:
      public-key: JrN7nZEbUeBJFHTAKwtz5Dmd10SGgA8UCHEMLA2xOg0
      short-id: be7f13dd
    server: **************
    tls: true
    type: vless
    udp: true
    uuid: 94c3155b-5252-4d66-bead-06b2e489f658
    xudp: true
    servername: www.speedtest.net
  - client-fingerprint: chrome
    name: "\U0001F1FA\U0001F1F8美国203 | ⬇️ 7.9MB/s"
    network: ws
    port: 443
    server: **************
    tls: true
    type: vless
    udp: true
    uuid: 2734c14b-e4ce-48b3-b3de-f80788cb4c47
    ws-opts:
      headers:
        Host: xCdF4.288288.ShOP
        User-Agent: >-
          Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML,
          like Gecko) Chrome/63.0.3239.84 Safari/537.36
      path: /eq5Bb2boLXSG8DqNAB
    xudp: true
    servername: xCdF4.288288.ShOP
  - client-fingerprint: chrome
    name: "\U0001F1EB\U0001F1F7法国3 | ⬇️ 2.3MB/s"
    password: 7669a04d-a459-4c96-bc0e-fde51cb984f3
    port: 12065
    server: cn2.cdn.xfltd-cdn.top
    skip-cert-verify: true
    sni: cdn.alibaba.com
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1E9\U0001F1EA德国11 | ⬇️ 506KB/s"
    password: 7669a04d-a459-4c96-bc0e-fde51cb984f3
    port: 12070
    server: cn2.cdn.xfltd-cdn.top
    skip-cert-verify: true
    sni: cdn.alibaba.com
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1F9\U0001F1FC台湾8 | ⬇️ 7.0MB/s"
    network: ws
    port: 2053
    server: *************
    tls: true
    type: vless
    udp: true
    uuid: 920d2c36-fb41-4fd3-abd3-9bb11224b704
    ws-opts:
      headers:
        Host: waphk.ovo2024.dpdns.org
        User-Agent: >-
          Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like
          Gecko) Chrome/49.0.2623.110 Safari/537.36
      path: /248cbff8
    xudp: true
    servername: waphk.ovo2024.dpdns.org
  - client-fingerprint: chrome
    name: "\U0001F300其他18-NG | ⬇️ 709KB/s"
    password: 7669a04d-a459-4c96-bc0e-fde51cb984f3
    port: 12073
    server: cn2.cdn.xfltd-cdn.top
    skip-cert-verify: true
    sni: cdn.alibaba.com
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1FA\U0001F1F8美国204 | ⬇️ 6.2MB/s"
    network: ws
    port: 443
    server: *************
    tls: true
    type: vless
    udp: true
    uuid: 585acd30-52bf-4b70-a537-e13649fafefc
    ws-opts:
      headers:
        Host: bBA.890601.pP.UA
        User-Agent: >-
          Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like
          Gecko) Chrome/49.0.2623.112 Safari/537.36
      path: /rU9rSjDSOd4yY2fOe
    xudp: true
    servername: bBA.890601.pP.UA
  - alpn:
      - ''
    alterId: '0'
    cipher: auto
    name: "\U0001F300其他19-未识别 | ⬇️ 2.3MB/s"
    network: ws
    port: 20022
    server: 2fea6447-81c4-2d7d-d592-a4f729209777.7head.icu
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 21c90669-193d-49d3-8e37-f5c1462eb134
    ws-opts:
      headers:
        Host: tms.dingtalk.com
      path: /
    xudp: true
  - alpn:
      - ''
    alterId: '0'
    cipher: auto
    name: "\U0001F1EB\U0001F1EE芬兰3 | ⬇️ 3.2MB/s"
    network: ws
    port: 20036
    server: 2fea6447-81c4-2d7d-d592-a4f729209777.7head.icu
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 14a8a0db-e922-453a-af8d-843af8734b3b
    ws-opts:
      headers:
        Host: tms.dingtalk.com
      path: /
    xudp: true
  - client-fingerprint: chrome
    name: "\U0001F1FA\U0001F1F8美国205 | ⬇️ 693KB/s"
    network: ws
    port: 443
    server: *************
    tls: true
    type: vless
    udp: true
    uuid: 895552fa-6284-4c1d-ba00-3944e0c7c626
    ws-opts:
      headers:
        Host: CccVF34ER4.288288.sHop
        User-Agent: >-
          Mozilla/5.0 (Linux; Android 7.0; BLL-L22 Build/HUAWEIBLL-L22)
          AppleWebKit/537.36 (KHTML, like Gecko) Chrome/55.0.2883.91 Mobile
          Safari/537.36
      path: /C1SukvGdr58yeduy9AOG
    xudp: true
    servername: CccVF34ER4.288288.sHop
  - alpn:
      - ''
    alterId: '0'
    cipher: auto
    name: "\U0001F300其他20-AR | ⬇️ 3.2MB/s"
    network: ws
    port: 20056
    server: 2fea6447-81c4-2d7d-d592-a4f729209777.7head.icu
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 14a8a0db-e922-453a-af8d-843af8734b3b
    ws-opts:
      headers:
        Host: tms.dingtalk.com
      path: /
    xudp: true
  - client-fingerprint: chrome
    name: "\U0001F1FA\U0001F1F8美国206 | ⬇️ 8.5MB/s"
    network: ws
    port: 443
    server: *************
    tls: true
    type: vless
    udp: true
    uuid: 585acd30-52bf-4b70-a537-e13649fafefc
    ws-opts:
      headers:
        Host: DfcVrf.2031.pp.Ua
        User-Agent: >-
          Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML,
          like Gecko) Chrome/61.0.3163.79 Safari/537.36
      path: /rU9rSjDSOd4yY2fOe
    xudp: true
    servername: DfcVrf.2031.pp.Ua
  - client-fingerprint: chrome
    name: "\U0001F1E8\U0001F1E6加拿大2 | ⬇️ 2.1MB/s"
    network: ws
    port: 8881
    server: *************
    tls: true
    type: vless
    udp: true
    uuid: d342d11e-d424-4583-b36e-524ab1f0afa4
    ws-opts:
      headers:
        Host: 3th.go4sharing.free.nf
        User-Agent: >-
          Mozilla/5.0 (Windows; U; Windows NT 6.1; de-DE) AppleWebKit/534.17
          (KHTML, like Gecko) Chrome/10.0.649.0 Safari/534.17
      path: /?ed=2560
    xudp: true
    servername: 3th.go4sharing.free.nf
  - client-fingerprint: chrome
    name: "\U0001F1E8\U0001F1E6加拿大3 | ⬇️ 3.2MB/s"
    network: ws
    port: 20880
    server: **************
    tls: true
    type: vless
    udp: true
    uuid: d342d11e-d424-4583-b36e-524ab1f0afa4
    ws-opts:
      headers:
        Host: 3th.go4sharing.free.nf
        User-Agent: >-
          Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like
          Gecko) Chrome/70.0.3538.67 Safari/537.36
      path: /?ed=2560
    xudp: true
    servername: 3th.go4sharing.free.nf
  - client-fingerprint: chrome
    name: "\U0001F300其他21-未识别 | ⬇️ 833KB/s"
    network: ws
    port: 50443
    server: **************
    tls: true
    type: vless
    udp: true
    uuid: d342d11e-d424-4583-b36e-524ab1f0afa4
    ws-opts:
      headers:
        Host: 3th.go4sharing.free.nf
        User-Agent: >-
          Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like
          Gecko) Chrome/53.0.2785.143 Safari/537.36
      path: /?ed=2560
    xudp: true
    servername: 3th.go4sharing.free.nf
  - client-fingerprint: chrome
    name: "\U0001F1F0\U0001F1F7韩国5 | ⬇️ 1.3MB/s"
    network: ws
    port: 10250
    server: **************
    tls: true
    type: vless
    udp: true
    uuid: d342d11e-d424-4583-b36e-524ab1f0afa4
    ws-opts:
      headers:
        Host: 3th.go4sharing.free.nf
        User-Agent: >-
          Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML,
          like Gecko) Chrome/55.0.2883.87 Safari/537.36
      path: /?ed=2560
    xudp: true
    servername: 3th.go4sharing.free.nf
  - client-fingerprint: chrome
    name: "\U0001F1F0\U0001F1F7韩国6 | ⬇️ 707KB/s"
    network: ws
    port: 55555
    server: **************
    tls: true
    type: vless
    udp: true
    uuid: d342d11e-d424-4583-b36e-524ab1f0afa4
    ws-opts:
      headers:
        Host: 3th.go4sharing.free.nf
        User-Agent: >-
          Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML,
          like Gecko) Chrome/63.0.3239.84 Safari/537.36
      path: /?ed=2560
    xudp: true
    servername: 3th.go4sharing.free.nf
  - client-fingerprint: chrome
    name: "\U0001F1F0\U0001F1F7韩国7 | ⬇️ 549KB/s"
    network: ws
    port: 11000
    server: *************
    tls: true
    type: vless
    udp: true
    uuid: d342d11e-d424-4583-b36e-524ab1f0afa4
    ws-opts:
      headers:
        Host: 3th.go4sharing.free.nf
        User-Agent: >-
          Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like
          Gecko) Chrome/31.0.1650.57 Safari/537.36
      path: /?ed=2560
    xudp: true
    servername: 3th.go4sharing.free.nf
  - client-fingerprint: chrome
    name: "\U0001F1F0\U0001F1F7韩国8 | ⬇️ 2.4MB/s"
    network: ws
    port: 50000
    server: *************
    tls: true
    type: vless
    udp: true
    uuid: d342d11e-d424-4583-b36e-524ab1f0afa4
    ws-opts:
      headers:
        Host: 3th.go4sharing.free.nf
        User-Agent: >-
          Mozilla/5.0 (Linux; Android 8.0.0; FIG-LX3) AppleWebKit/537.36 (KHTML,
          like Gecko) Chrome/70.0.3538.80 Mobile Safari/537.36
      path: /?ed=2560
    xudp: true
    servername: 3th.go4sharing.free.nf
  - client-fingerprint: chrome
    name: "\U0001F1F0\U0001F1F7韩国9 | ⬇️ 660KB/s"
    network: ws
    port: 587
    server: **************
    tls: true
    type: vless
    udp: true
    uuid: d342d11e-d424-4583-b36e-524ab1f0afa4
    ws-opts:
      headers:
        Host: 3th.go4sharing.free.nf
        User-Agent: >-
          Mozilla/5.0 (Linux; Android 7.0; SM-G610F Build/NRD90M)
          AppleWebKit/537.36 (KHTML, like Gecko) Chrome/63.0.3239.111 Mobile
          Safari/537.36
      path: /?ed=2560
    xudp: true
    servername: 3th.go4sharing.free.nf
  - client-fingerprint: chrome
    name: "\U0001F1F0\U0001F1F7韩国10 | ⬇️ 2.3MB/s"
    network: ws
    port: 50000
    server: **************
    tls: true
    type: vless
    udp: true
    uuid: d342d11e-d424-4583-b36e-524ab1f0afa4
    ws-opts:
      headers:
        Host: 3th.go4sharing.free.nf
        User-Agent: >-
          Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like
          Gecko) Chrome/40.0.2214.115 Safari/537.36
      path: /?ed=2560
    xudp: true
    servername: 3th.go4sharing.free.nf
  - client-fingerprint: chrome
    name: "\U0001F1F0\U0001F1F7韩国11 | ⬇️ 1.5MB/s"
    network: ws
    port: 23456
    server: *************
    tls: true
    type: vless
    udp: true
    uuid: d342d11e-d424-4583-b36e-524ab1f0afa4
    ws-opts:
      headers:
        Host: 3th.go4sharing.free.nf
        User-Agent: >-
          Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like
          Gecko) Chrome/36.0.1985.125 Safari/537.36
      path: /?ed=2560
    xudp: true
    servername: 3th.go4sharing.free.nf
  - client-fingerprint: chrome
    name: "\U0001F1F0\U0001F1F7韩国12 | ⬇️ 1.1MB/s"
    network: ws
    port: 50000
    server: **************
    tls: true
    type: vless
    udp: true
    uuid: d342d11e-d424-4583-b36e-524ab1f0afa4
    ws-opts:
      headers:
        Host: 3th.go4sharing.free.nf
        User-Agent: >-
          Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML,
          like Gecko) Chrome/58.0.3029.110 Safari/537.36
      path: /?ed=2560
    xudp: true
    servername: 3th.go4sharing.free.nf
  - client-fingerprint: chrome
    name: "\U0001F1E8\U0001F1E6加拿大4 | ⬇️ 1.5MB/s"
    network: ws
    port: 50000
    server: ***************
    tls: true
    type: vless
    udp: true
    uuid: d342d11e-d424-4583-b36e-524ab1f0afa4
    ws-opts:
      headers:
        Host: 3th.go4sharing.free.nf
        User-Agent: >-
          Mozilla/5.0 (Windows NT 6.1) AppleWebKit/537.36 (KHTML, like Gecko)
          Chrome/55.0.2883.87 Safari/537.36
      path: /?ed=2560
    xudp: true
    servername: 3th.go4sharing.free.nf
  - client-fingerprint: chrome
    name: "\U0001F1E8\U0001F1E6加拿大5 | ⬇️ 5.4MB/s"
    network: ws
    port: 50000
    server: **************
    tls: true
    type: vless
    udp: true
    uuid: d342d11e-d424-4583-b36e-524ab1f0afa4
    ws-opts:
      headers:
        Host: 3th.go4sharing.free.nf
        User-Agent: >-
          Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like
          Gecko) Chrome/46.0.2490.80 Safari/537.36
      path: /?ed=2560
    xudp: true
    servername: 3th.go4sharing.free.nf
  - client-fingerprint: chrome
    name: "\U0001F1F0\U0001F1F7韩国13 | ⬇️ 993KB/s"
    network: ws
    port: 50000
    server: ************
    tls: true
    type: vless
    udp: true
    uuid: d342d11e-d424-4583-b36e-524ab1f0afa4
    ws-opts:
      headers:
        Host: 3th.go4sharing.free.nf
        User-Agent: >-
          Mozilla/5.0 (X11; Datanyze; Linux x86_64) AppleWebKit/537.36 (KHTML,
          like Gecko) Chrome/65.0.3325.181 Safari/537.36
      path: /?ed=2560
    xudp: true
    servername: 3th.go4sharing.free.nf
  - client-fingerprint: chrome
    name: "\U0001F1E8\U0001F1E6加拿大6 | ⬇️ 2.5MB/s"
    network: ws
    port: 28080
    server: **************
    tls: true
    type: vless
    udp: true
    uuid: d342d11e-d424-4583-b36e-524ab1f0afa4
    ws-opts:
      headers:
        Host: 3th.go4sharing.free.nf
        User-Agent: >-
          Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like
          Gecko) Chrome/70.0.3538.102 Safari/537.36
      path: /?ed=2560
    xudp: true
    servername: 3th.go4sharing.free.nf
  - client-fingerprint: chrome
    name: "\U0001F300其他22-未识别 | ⬇️ 1.1MB/s"
    network: ws
    port: 50000
    server: **************
    tls: true
    type: vless
    udp: true
    uuid: d342d11e-d424-4583-b36e-524ab1f0afa4
    ws-opts:
      headers:
        Host: 3th.go4sharing.free.nf
        User-Agent: >-
          Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like
          Gecko) Chrome/55.0.2883.87 Safari/537.36
      path: /?ed=2560
    xudp: true
    servername: 3th.go4sharing.free.nf
  - client-fingerprint: chrome
    name: "\U0001F300其他23-未识别 | ⬇️ 656KB/s"
    network: ws
    port: 11001
    server: **************
    tls: true
    type: vless
    udp: true
    uuid: d342d11e-d424-4583-b36e-524ab1f0afa4
    ws-opts:
      headers:
        Host: 3th.go4sharing.free.nf
        User-Agent: >-
          Mozilla/5.0 (Windows NT 6.2; Win64; x64) AppleWebKit/537.36 (KHTML,
          like Gecko) Chrome/69.0.3497.100 Safari/537.36
      path: /?ed=2560
    xudp: true
    servername: 3th.go4sharing.free.nf
  - client-fingerprint: chrome
    name: "\U0001F300其他24-未识别 | ⬇️ 3.3MB/s"
    network: ws
    port: 50000
    server: **************
    tls: true
    type: vless
    udp: true
    uuid: d342d11e-d424-4583-b36e-524ab1f0afa4
    ws-opts:
      headers:
        Host: 3th.go4sharing.free.nf
        User-Agent: >-
          Mozilla/5.0 (Linux; Android 5.1.1; SM-J111M Build/LMY47V)
          AppleWebKit/537.36 (KHTML, like Gecko) Chrome/69.0.3497.100 Mobile
          Safari/537.36
      path: /?ed=2560
    xudp: true
    servername: 3th.go4sharing.free.nf
  - client-fingerprint: chrome
    name: "\U0001F1F0\U0001F1F7韩国14 | ⬇️ 724KB/s"
    network: ws
    port: 8080
    server: ***************
    tls: true
    type: vless
    udp: true
    uuid: d342d11e-d424-4583-b36e-524ab1f0afa4
    ws-opts:
      headers:
        Host: 3th.go4sharing.free.nf
        User-Agent: >-
          Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML,
          like Gecko) Chrome/68.0.3440.106 Safari/537.36
      path: /?ed=2560
    xudp: true
    servername: 3th.go4sharing.free.nf
  - client-fingerprint: chrome
    name: "\U0001F1F0\U0001F1F7韩国15 | ⬇️ 2.3MB/s"
    network: ws
    port: 587
    server: *************
    tls: true
    type: vless
    udp: true
    uuid: d342d11e-d424-4583-b36e-524ab1f0afa4
    ws-opts:
      headers:
        Host: 3th.go4sharing.free.nf
        User-Agent: >-
          Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML,
          like Gecko) Chrome/63.0.3239.132 Safari/537.36
      path: /?ed=2560
    xudp: true
    servername: 3th.go4sharing.free.nf
  - client-fingerprint: chrome
    name: "\U0001F300其他25-未识别 | ⬇️ 3.6MB/s"
    network: ws
    port: 688
    server: ************
    tls: true
    type: vless
    udp: true
    uuid: d342d11e-d424-4583-b36e-524ab1f0afa4
    ws-opts:
      headers:
        Host: 3th.go4sharing.free.nf
        User-Agent: >-
          Mozilla/5.0 (Windows NT 6.2; WOW64) AppleWebKit/537.36 (KHTML, like
          Gecko) Chrome/42.0.2311.90 Safari/537.36
      path: /?ed=2560
    xudp: true
    servername: 3th.go4sharing.free.nf
  - client-fingerprint: chrome
    name: "\U0001F300其他26-未识别 | ⬇️ 3.8MB/s"
    network: ws
    port: 8002
    server: **************
    tls: true
    type: vless
    udp: true
    uuid: d342d11e-d424-4583-b36e-524ab1f0afa4
    ws-opts:
      headers:
        Host: 3th.go4sharing.free.nf
        User-Agent: >-
          Mozilla/5.0 (Linux; Android 7.0; Moto G (5) Build/NPPS25.137-93-14)
          AppleWebKit/537.36 (KHTML, like Gecko) Chrome/69.0.3497.100 Mobile
          Safari/537.36
      path: /?ed=2560
    xudp: true
    servername: 3th.go4sharing.free.nf
  - client-fingerprint: chrome
    name: "\U0001F1E8\U0001F1E6加拿大7 | ⬇️ 2.5MB/s"
    network: ws
    port: 8080
    server: ************
    tls: true
    type: vless
    udp: true
    uuid: d342d11e-d424-4583-b36e-524ab1f0afa4
    ws-opts:
      headers:
        Host: 3th.go4sharing.free.nf
        User-Agent: >-
          Mozilla/5.0 (Linux; Android 8.0.0; FIG-LX3) AppleWebKit/537.36 (KHTML,
          like Gecko) Chrome/70.0.3538.80 Mobile Safari/537.36
      path: /?ed=2560
    xudp: true
    servername: 3th.go4sharing.free.nf
  - client-fingerprint: chrome
    name: "\U0001F300其他27-未识别 | ⬇️ 1.2MB/s"
    network: ws
    port: 9380
    server: **************
    tls: true
    type: vless
    udp: true
    uuid: d342d11e-d424-4583-b36e-524ab1f0afa4
    ws-opts:
      headers:
        Host: 3th.go4sharing.free.nf
        User-Agent: >-
          Mozilla/5.0 (Windows NT 6.3; WOW64) AppleWebKit/537.36 (KHTML, like
          Gecko) Chrome/46.0.2490.80 Safari/537.36
      path: /?ed=2560
    xudp: true
    servername: 3th.go4sharing.free.nf
  - client-fingerprint: chrome
    name: "\U0001F1F0\U0001F1F7韩国16 | ⬇️ 1.7MB/s"
    network: ws
    port: 8888
    server: ************
    tls: true
    type: vless
    udp: true
    uuid: d342d11e-d424-4583-b36e-524ab1f0afa4
    ws-opts:
      headers:
        Host: 3th.go4sharing.free.nf
        User-Agent: >-
          Mozilla/5.0 (Windows NT 6.0; Win64; x64) AppleWebKit/537.36 (KHTML,
          like Gecko) Chrome/60.0.3112.90 Safari/537.36
      path: /?ed=2560
    xudp: true
    servername: 3th.go4sharing.free.nf
  - client-fingerprint: chrome
    name: "\U0001F300其他28-未识别 | ⬇️ 693KB/s"
    network: ws
    port: 587
    server: *************
    tls: true
    type: vless
    udp: true
    uuid: d342d11e-d424-4583-b36e-524ab1f0afa4
    ws-opts:
      headers:
        Host: 3th.go4sharing.free.nf
        User-Agent: >-
          Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML,
          like Gecko) Chrome/68.0.3440.75 Safari/537.36
      path: /?ed=2560
    xudp: true
    servername: 3th.go4sharing.free.nf
  - client-fingerprint: chrome
    name: "\U0001F300其他29-未识别 | ⬇️ 723KB/s"
    network: ws
    port: 8080
    server: *************
    tls: true
    type: vless
    udp: true
    uuid: d342d11e-d424-4583-b36e-524ab1f0afa4
    ws-opts:
      headers:
        Host: 3th.go4sharing.free.nf
        User-Agent: >-
          Mozilla/5.0 (Linux; Android 5.1.1; SM-J111M Build/LMY47V)
          AppleWebKit/537.36 (KHTML, like Gecko) Chrome/69.0.3497.100 Mobile
          Safari/537.36
      path: /?ed=2560
    xudp: true
    servername: 3th.go4sharing.free.nf
  - client-fingerprint: chrome
    name: "\U0001F1E8\U0001F1E6加拿大8 | ⬇️ 901KB/s"
    network: ws
    port: 2223
    server: ***********
    tls: true
    type: vless
    udp: true
    uuid: d342d11e-d424-4583-b36e-524ab1f0afa4
    ws-opts:
      headers:
        Host: 3th.go4sharing.free.nf
        User-Agent: >-
          Mozilla/5.0 (Windows NT 6.0) AppleWebKit/537.36 (KHTML, like Gecko)
          Chrome/49.0.2623.112 Safari/537.36
      path: /?ed=2560
    xudp: true
    servername: 3th.go4sharing.free.nf
  - client-fingerprint: chrome
    name: "\U0001F1FA\U0001F1F8美国207 | ⬇️ 6.2MB/s"
    network: ws
    port: 443
    server: *************
    tls: true
    type: vless
    udp: true
    uuid: f0f6e76e-e5fe-4e2c-9faf-34832e021eae
    ws-opts:
      headers:
        Host: Ty.457.PP.uA
        User-Agent: >-
          Mozilla/5.0 (Windows NT 6.3; Win64; x64) AppleWebKit/537.36 (KHTML,
          like Gecko) Chrome/67.0.3396.99 Safari/537.36
      path: /AMpQdWyvpaPkb7QHHkQBzYO
    xudp: true
    servername: Ty.457.PP.uA
  - client-fingerprint: chrome
    name: "\U0001F1FA\U0001F1F8美国208 | ⬇️ 2.7MB/s"
    network: ws
    port: 443
    server: *************
    tls: true
    type: vless
    udp: true
    uuid: 585acd30-52bf-4b70-a537-e13649fafefc
    ws-opts:
      headers:
        Host: bBA.890601.pP.UA
        User-Agent: >-
          Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML,
          like Gecko) Chrome/68.0.3440.106 Safari/537.36
      path: /rU9rSjDSOd4yY2fOe
    xudp: true
    servername: bBA.890601.pP.UA
  - client-fingerprint: chrome
    name: "\U0001F1FA\U0001F1F8美国209 | ⬇️ 8.8MB/s"
    network: ws
    port: 443
    server: *************
    tls: true
    type: vless
    udp: true
    uuid: 15b24b56-d667-4fa8-b548-f3dc942fb461
    ws-opts:
      headers:
        Host: e6422f63-7b49-4b50-b862-9f38.2031.pp.ua
        User-Agent: >-
          Mozilla/5.0 (Windows NT 6.1) AppleWebKit/537.36 (KHTML, like Gecko)
          Chrome/41.0.2228.0 Safari/537.36
      path: /ok1UBnf7txQ8YJFJxz
    xudp: true
    servername: e6422f63-7b49-4b50-b862-9f38.2031.pp.ua
  - client-fingerprint: chrome
    name: "\U0001F1FA\U0001F1F8美国210 | ⬇️ 8.3MB/s"
    network: ws
    port: 443
    server: **************
    tls: true
    type: vless
    udp: true
    uuid: 4e41764d-ac31-43f5-8e9b-231ddf252d1f
    ws-opts:
      headers:
        Host: 899.288288.shoP
        User-Agent: >-
          Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML,
          like Gecko) Chrome/62.0.3202.89 Safari/537.36
      path: /wAlPWPCQWnsVcNgLsMVz
    xudp: true
    servername: 899.288288.shoP
  - client-fingerprint: chrome
    grpc-opts:
      grpc-service-name: >-
        CANALTELEGRAM--jet_config--TELEGRAM--jet_config--CANALTELEGRAM--jet_config--TELEGRAM--jet_config--CANALTELEGRAM--jet_config
    name: "\U0001F1EC\U0001F1E7英国5 | ⬇️ 6.5MB/s"
    network: grpc
    port: 2030
    reality-opts:
      public-key: YWfCdTnr4FAOMYTY2dLrMtQUokyxOGpPhYEEszPj20E
      short-id: 7fe29733
    server: ns7.esfahansiman.com
    tls: true
    type: vless
    udp: true
    uuid: 98e5553e-abcc-4c9a-941a-8f0e9741d541
    xudp: true
    servername: refersion.com
  - alpn:
      - ''
    alterId: '0'
    cipher: auto
    name: "\U0001F1ED\U0001F1F0香港22 | ⬇️ 9.5MB/s"
    network: ws
    port: 8080
    server: fa59e334-swd340-tf70jh-vm13.hgc1.tcpbbr.net
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 3fa7157d-efe8-832e-9b3c-582d78efbc52
    ws-opts:
      headers:
        Host: broadcastlv.chat.bilibili.com
      path: /
    xudp: true
  - alpn:
      - ''
    alterId: '0'
    cipher: auto
    name: "\U0001F1ED\U0001F1F0香港23 | ⬇️ 8.2MB/s"
    network: ws
    port: 8080
    server: f1028021-swd340-sx9j1n-1timk.hgc1.tcpbbr.net
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: cfe61764-0004-11f0-a910-f23c9164ca5d
    ws-opts:
      headers:
        Host: broadcastlv.chat.bilibili.com
      path: /
    xudp: true
  - alpn:
      - ''
    alterId: '0'
    cipher: auto
    name: "\U0001F1ED\U0001F1F0香港24 | ⬇️ 8.5MB/s"
    network: ws
    port: 8080
    server: 507465c4-swd340-td1w5f-1t3cz.hgc1.tcpbbr.net
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: affe7124-c118-11ef-b6b2-f23c9164ca5d
    ws-opts:
      headers:
        Host: broadcastlv.chat.bilibili.com
      path: /
    xudp: true
  - alpn:
      - ''
    alterId: '0'
    cipher: auto
    name: "\U0001F1ED\U0001F1F0香港25 | ⬇️ 7.6MB/s"
    network: ws
    port: 8080
    server: 5c273aaf-swd340-t0dyyh-jjv2.hgc1.tcpbbr.net
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 31676725-b0fd-fed2-c857-2da8ce5a6e4f
    ws-opts:
      headers:
        Host: broadcastlv.chat.bilibili.com
      path: /
    xudp: true
  - client-fingerprint: chrome
    name: "\U0001F1E8\U0001F1E6加拿大9 | ⬇️ 2.5MB/s"
    network: ws
    port: 12196
    server: ***************
    tls: true
    type: vless
    udp: true
    uuid: d342d11e-d424-4583-b36e-524ab1f0afa4
    ws-opts:
      headers:
        Host: panson.go4sharing.free.nf
        User-Agent: >-
          Mozilla/5.0 (Windows NT 6.3; Win64; x64) AppleWebKit/537.36 (KHTML,
          like Gecko) Chrome/65.0.3325.181 Safari/537.36
      path: /?ed=2560
    xudp: true
    servername: panson.go4sharing.free.nf
  - alpn:
      - ''
    alterId: '0'
    cipher: auto
    name: "\U0001F1ED\U0001F1F0香港26 | ⬇️ 7.0MB/s"
    network: ws
    port: 8080
    server: 46194f76-swd340-szdere-155d9.hgc1.tcpbbr.net
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 34f0cf42-0f8a-11ec-a8bf-f23c91cfbbc9
    ws-opts:
      headers:
        Host: broadcastlv.chat.bilibili.com
      path: /
    xudp: true
  - client-fingerprint: chrome
    name: "\U0001F1E6\U0001F1EA阿拉伯酋长国3 | ⬇️ 6.3MB/s"
    network: ws
    port: 443
    server: ************
    tls: true
    type: vless
    udp: true
    uuid: 72d7ab94-4849-4a27-a840-ece6c8b53355
    ws-opts:
      headers:
        Host: BeSTVPN-7qP.pagES.dEv
        User-Agent: >-
          Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML,
          like Gecko) Chrome/63.0.3239.84 Safari/537.36
      path: /KF0tSQa0F6CajVAI?ed=2560flow=-udp443
    xudp: true
    servername: BeSTVPN-7qP.pagES.dEv
  - alpn:
      - ''
    alterId: '0'
    cipher: auto
    name: "\U0001F1ED\U0001F1F0香港27 | ⬇️ 4.4MB/s"
    network: ws
    port: 8080
    server: 212b7541-swd340-sxhurg-1th8j.hgc1.tcpbbr.net
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 7d7c111e-fbc1-11ef-ab5f-f23c913c8d2b
    ws-opts:
      headers:
        Host: broadcastlv.chat.bilibili.com
      path: /
    xudp: true
  - name: "\U0001F1E9\U0001F1EA德国12 | ⬇️ 793KB/s"
    network: ws
    port: 8880
    server: ************
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
        User-Agent: >-
          Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML,
          like Gecko) Chrome/70.0.3538.102 Safari/537.36
      path: "/Telegram\U0001F1E8\U0001F1F3 @pgkj666 /?ed=2560"
    xudp: true
  - name: "\U0001F1EB\U0001F1EE芬兰4 | ⬇️ 891KB/s"
    network: ws
    port: 8880
    server: ************
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
        User-Agent: >-
          Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML,
          like Gecko) Chrome/60.0.3112.101 Safari/537.36
      path: "/Telegram\U0001F1E8\U0001F1F3 @pgkj666 /?ed=2560"
    xudp: true
  - name: "\U0001F1E9\U0001F1EA德国13 | ⬇️ 1.2MB/s"
    network: ws
    port: 8880
    server: ***********
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
        User-Agent: >-
          Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML,
          like Gecko) Chrome/54.0.2840.99 Safari/537.36
      path: "/Telegram\U0001F1E8\U0001F1F3 @pgkj666 /?ed=2560"
    xudp: true
  - name: "\U0001F1E9\U0001F1EA德国14 | ⬇️ 679KB/s"
    network: ws
    port: 8880
    server: ************
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
        User-Agent: >-
          Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like
          Gecko) Chrome/41.0.2272.76 Safari/537.36
      path: "/Telegram\U0001F1E8\U0001F1F3 @pgkj666 /?ed=2560"
    xudp: true
  - alpn:
      - ''
    alterId: '0'
    cipher: auto
    name: "\U0001F1ED\U0001F1F0香港28 | ⬇️ 4.5MB/s"
    network: ws
    port: 8080
    server: 67892b95-swd340-t3o6u7-1osdm.hgc1.tcpbbr.net
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 91365a7a-46d7-11ee-a8b9-f23c9164ca5d
    ws-opts:
      headers:
        Host: broadcastlv.chat.bilibili.com
      path: /
    xudp: true
  - alpn:
      - ''
    alterId: '0'
    cipher: auto
    name: "\U0001F1ED\U0001F1F0香港29 | ⬇️ 6.1MB/s"
    network: ws
    port: 8080
    server: 2e88b440-swd340-sx3h07-1g8k0.hgc1.tcpbbr.net
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: d4018e28-e328-11ed-98a7-f23c913c8d2b
    ws-opts:
      headers:
        Host: broadcastlv.chat.bilibili.com
      path: /
    xudp: true
  - name: "\U0001F1FA\U0001F1F8美国211 | ⬇️ 532KB/s"
    network: ws
    port: 8880
    server: ***********2
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
        User-Agent: >-
          Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML,
          like Gecko) Chrome/58.0.3029.110 Safari/537.36
      path: "/Telegram\U0001F1E8\U0001F1F3 @pgkj666 /?ed=2560"
    xudp: true
  - alpn:
      - ''
    alterId: '0'
    cipher: auto
    name: "\U0001F1ED\U0001F1F0香港30 | ⬇️ 4.7MB/s"
    network: ws
    port: 8080
    server: 3cd49d00-swd340-t8kd6j-1c9em.hgc1.tcpbbr.net
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 336501b6-51d2-11ee-a993-f23c9164ca5d
    ws-opts:
      headers:
        Host: broadcastlv.chat.bilibili.com
      path: /
    xudp: true
  - name: "\U0001F1FA\U0001F1F8美国212 | ⬇️ 15.1MB/s"
    network: ws
    port: 8880
    server: ************
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
        User-Agent: >-
          Mozilla/5.0 (Windows NT 6.2; Win64; x64) AppleWebKit/537.36 (KHTML,
          like Gecko) Chrome/60.0.3112.90 Safari/537.36
      path: "/Telegram\U0001F1E8\U0001F1F3 @pgkj666 /?ed=2560"
    xudp: true
  - name: "\U0001F1FA\U0001F1F8美国213 | ⬇️ 502KB/s"
    network: ws
    port: 8880
    server: ************
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
        User-Agent: >-
          Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML,
          like Gecko) Chrome/60.0.3112.90 Safari/537.36
      path: "/Telegram\U0001F1E8\U0001F1F3 @pgkj666 /?ed=2560"
    xudp: true
  - name: "\U0001F1E9\U0001F1EA德国15 | ⬇️ 1.5MB/s"
    network: ws
    port: 8880
    server: ************
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
        User-Agent: >-
          Mozilla/5.0 (Windows; U; Windows NT 5.1; en-US) AppleWebKit/534.3
          (KHTML, like Gecko) Chrome/6.0.472.63 Safari/534.3
      path: "/Telegram\U0001F1E8\U0001F1F3 @pgkj666 /?ed=2560"
    xudp: true
  - name: "\U0001F1FA\U0001F1F8美国214 | ⬇️ 4.5MB/s"
    network: ws
    port: 8880
    server: *************
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
        User-Agent: >-
          Mozilla/5.0 (Windows NT 6.1) AppleWebKit/537.36 (KHTML, like Gecko)
          Chrome/55.0.2883.87 Safari/537.36
      path: "/Telegram\U0001F1E8\U0001F1F3 @pgkj666 /?ed=2560"
    xudp: true
  - name: "\U0001F1EE\U0001F1F3印度2 | ⬇️ 6.1MB/s"
    network: ws
    port: 8880
    server: ************
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
        User-Agent: >-
          Mozilla/5.0 (Windows NT 6.1) AppleWebKit/537.36 (KHTML, like Gecko)
          Chrome/55.0.2883.87 Safari/537.36
      path: "/Telegram\U0001F1E8\U0001F1F3 @pgkj666 /?ed=2560"
    xudp: true
  - name: "\U0001F1F0\U0001F1F7韩国17 | ⬇️ 4.6MB/s"
    network: ws
    port: 8880
    server: ************
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
        User-Agent: >-
          Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML,
          like Gecko) Chrome/67.0.3396.87 Safari/537.36
      path: "/Telegram\U0001F1E8\U0001F1F3 @pgkj666 /?ed=2560"
    xudp: true
  - name: "\U0001F1ED\U0001F1F0香港31 | ⬇️ 1.5MB/s"
    network: ws
    port: 8880
    server: ************
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
        User-Agent: >-
          Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML,
          like Gecko) Chrome/60.0.3112.90 Safari/537.36
      path: "/Telegram\U0001F1E8\U0001F1F3 @pgkj666 /?ed=2560"
    xudp: true
  - name: "\U0001F1E9\U0001F1EA德国16 | ⬇️ 9.8MB/s"
    network: ws
    port: 8880
    server: ************
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
        User-Agent: >-
          Mozilla/5.0 (Windows; U; Windows NT 6.1; en-US) AppleWebKit/534.7
          (KHTML, like Gecko) Chrome/7.0.517.44 Safari/534.7
      path: "/Telegram\U0001F1E8\U0001F1F3 @pgkj666 /?ed=2560"
    xudp: true
  - name: "\U0001F1FA\U0001F1F8美国215 | ⬇️ 541KB/s"
    network: ws
    port: 8880
    server: ***********1
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
        User-Agent: >-
          Mozilla/5.0 (Linux; Android 8.0.0; WAS-LX3 Build/HUAWEIWAS-LX3)
          AppleWebKit/537.36 (KHTML, like Gecko) Chrome/69.0.3497.100 Mobile
          Safari/537.36
      path: "/Telegram\U0001F1E8\U0001F1F3 @pgkj666 /?ed=2560"
    xudp: true
  - name: "\U0001F1E9\U0001F1EA德国17 | ⬇️ 2.3MB/s"
    network: ws
    port: 8880
    server: ***********
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
        User-Agent: >-
          Mozilla/5.0 (X11; Linux i686) AppleWebKit/534.30 (KHTML, like Gecko)
          Slackware/Chrome/12.0.742.100 Safari/534.30
      path: "/Telegram\U0001F1E8\U0001F1F3 @pgkj666 /?ed=2560"
    xudp: true
  - name: "\U0001F1FA\U0001F1F8美国216 | ⬇️ 708KB/s"
    network: ws
    port: 8880
    server: **************
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
        User-Agent: >-
          Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like
          Gecko) Chrome/55.0.2883.87 Safari/537.36
      path: "/Telegram\U0001F1E8\U0001F1F3 @pgkj666 /?ed=2560"
    xudp: true
  - name: "\U0001F1F0\U0001F1F7韩国18 | ⬇️ 508KB/s"
    network: ws
    port: 8880
    server: *************
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
        User-Agent: >-
          Mozilla/5.0 (Windows NT 6.3; WOW64) AppleWebKit/537.36 (KHTML, like
          Gecko) Chrome/51.0.2704.106 Safari/537.36
      path: "/Telegram\U0001F1E8\U0001F1F3 @pgkj666 /?ed=2560"
    xudp: true
  - name: "\U0001F1FA\U0001F1F8美国217 | ⬇️ 1.2MB/s"
    network: ws
    port: 8880
    server: ************
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
        User-Agent: >-
          Mozilla/5.0 (Linux; Android 7.0; TRT-LX2 Build/HUAWEITRT-LX2; wv)
          AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0
          Chrome/59.0.3071.125 Mobile Safari/537.36
      path: "/Telegram\U0001F1E8\U0001F1F3 @pgkj666 /?ed=2560"
    xudp: true
  - name: "\U0001F1EB\U0001F1EE芬兰5 | ⬇️ 903KB/s"
    network: ws
    port: 8880
    server: *************
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
        User-Agent: >-
          Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_1) AppleWebKit/537.36
          (KHTML, like Gecko) Chrome/70.0.3538.77 Safari/537.36
      path: "/Telegram\U0001F1E8\U0001F1F3 @pgkj666 /?ed=2560"
    xudp: true
  - name: "\U0001F1FA\U0001F1F8美国218 | ⬇️ 8.7MB/s"
    network: ws
    port: 8880
    server: *************
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
        User-Agent: >-
          Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML,
          like Gecko) Chrome/55.0.2883.87 Safari/537.36
      path: "/Telegram\U0001F1E8\U0001F1F3 @pgkj666 /?ed=2560"
    xudp: true
  - name: "\U0001F1E9\U0001F1EA德国18 | ⬇️ 1.8MB/s"
    network: ws
    port: 8880
    server: ************
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
        User-Agent: >-
          Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML,
          like Gecko) Chrome/65.0.3325.162 Safari/537.36
      path: "/Telegram\U0001F1E8\U0001F1F3 @pgkj666 /?ed=2560"
    xudp: true
  - name: "\U0001F1FA\U0001F1F8美国219 | ⬇️ 2.7MB/s"
    network: ws
    port: 8880
    server: ************
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
        User-Agent: >-
          Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML,
          like Gecko) Chrome/67.0.3396.87 Safari/537.36
      path: "/Telegram\U0001F1E8\U0001F1F3 @pgkj666 /?ed=2560"
    xudp: true
  - name: "\U0001F1FA\U0001F1F8美国220 | ⬇️ 7.9MB/s"
    network: ws
    port: 8880
    server: ************
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
        User-Agent: >-
          Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML,
          like Gecko) Chrome/56.0.2924.87 Safari/537.36
      path: "/Telegram\U0001F1E8\U0001F1F3 @pgkj666 /?ed=2560"
    xudp: true
  - name: "\U0001F1E9\U0001F1EA德国19 | ⬇️ 2.5MB/s"
    network: ws
    port: 8880
    server: ************
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
        User-Agent: >-
          Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML,
          like Gecko) Chrome/63.0.3239.84 Safari/537.36
      path: "/Telegram\U0001F1E8\U0001F1F3 @pgkj666 /?ed=2560"
    xudp: true
  - name: "\U0001F1FA\U0001F1F8美国221 | ⬇️ 1.1MB/s"
    network: ws
    port: 8880
    server: **************
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
        User-Agent: >-
          Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML,
          like Gecko) Chrome/69.0.3497.100 Safari/537.36
      path: "/Telegram\U0001F1E8\U0001F1F3 @pgkj666 /?ed=2560"
    xudp: true
  - name: "\U0001F1E9\U0001F1EA德国20 | ⬇️ 781KB/s"
    network: ws
    port: 8880
    server: ***********5
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
        User-Agent: >-
          Mozilla/5.0 (Windows NT 6.3; WOW64) AppleWebKit/537.36 (KHTML, like
          Gecko) Chrome/51.0.2704.106 Safari/537.36
      path: "/Telegram\U0001F1E8\U0001F1F3 @pgkj666 /?ed=2560"
    xudp: true
  - name: "\U0001F1E9\U0001F1EA德国21 | ⬇️ 4.1MB/s"
    network: ws
    port: 8880
    server: ***********3
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
        User-Agent: >-
          Mozilla/5.0 (Linux; Android 6.0; vivo 1610 Build/MMB29M)
          AppleWebKit/537.36 (KHTML, like Gecko) Chrome/53.0.2785.124 Mobile
          Safari/537.36
      path: "/Telegram\U0001F1E8\U0001F1F3 @pgkj666 /?ed=2560"
    xudp: true
  - name: "\U0001F1F0\U0001F1F7韩国19 | ⬇️ 2.5MB/s"
    network: ws
    port: 8880
    server: ************
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
        User-Agent: >-
          Mozilla/5.0 (Windows NT 5.1; Win64; x64) AppleWebKit/537.36 (KHTML,
          like Gecko) Chrome/60.0.3112.90 Safari/537.36
      path: "/Telegram\U0001F1E8\U0001F1F3 @pgkj666 /?ed=2560"
    xudp: true
  - name: "\U0001F1EB\U0001F1EE芬兰6 | ⬇️ 2.2MB/s"
    network: ws
    port: 8880
    server: *************
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
        User-Agent: >-
          Mozilla/5.0 (X11; U; Linux i586; en-US) AppleWebKit/533.2 (KHTML, like
          Gecko) Chrome/5.0.342.1 Safari/533.2
      path: "/Telegram\U0001F1E8\U0001F1F3 @pgkj666 /?ed=2560"
    xudp: true
  - name: "\U0001F1ED\U0001F1F0香港32 | ⬇️ 8.4MB/s"
    network: ws
    port: 8880
    server: ************
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
        User-Agent: >-
          Mozilla/5.0 (Linux; Android 6.0; vivo 1606 Build/MMB29M)
          AppleWebKit/537.36 (KHTML, like Gecko) Chrome/53.0.2785.124 Mobile
          Safari/537.36
      path: "/Telegram\U0001F1E8\U0001F1F3 @pgkj666 /?ed=2560"
    xudp: true
  - name: "\U0001F1FA\U0001F1F8美国222 | ⬇️ 2.2MB/s"
    network: ws
    port: 8880
    server: *************
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
        User-Agent: >-
          Mozilla/5.0 (Linux; Android 7.0; TRT-LX2 Build/HUAWEITRT-LX2; wv)
          AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0
          Chrome/59.0.3071.125 Mobile Safari/537.36
      path: "/Telegram\U0001F1E8\U0001F1F3 @pgkj666 /?ed=2560"
    xudp: true
  - alpn:
      - ''
    alterId: '0'
    cipher: auto
    name: "\U0001F1ED\U0001F1F0香港33 | ⬇️ 1.8MB/s"
    network: ws
    port: 8080
    server: 83ea3fa8-sw7j40-sww7b0-1qwp5.hgc1.tcpbbr.net
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 6e9cdc20-b92a-11ef-973a-f23c913c8d2b
    ws-opts:
      headers:
        Host: broadcastlv.chat.bilibili.com
      path: /
    xudp: true
  - alpn:
      - ''
    alterId: '0'
    cipher: auto
    name: "\U0001F1ED\U0001F1F0香港34 | ⬇️ 3.4MB/s"
    network: ws
    port: 8080
    server: 92cd71e6-sw7j40-sxlsd4-3z3v.hgc1.tcpbbr.net
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 48c1e014-28d6-11ec-a0fc-f23c913c8d2b
    ws-opts:
      headers:
        Host: broadcastlv.chat.bilibili.com
      path: /
    xudp: true
  - alpn:
      - ''
    alterId: '0'
    cipher: auto
    name: "\U0001F1ED\U0001F1F0香港35 | ⬇️ 3.2MB/s"
    network: ws
    port: 8080
    server: ff4511d3-sw7j40-sxf56z-1f596.hgc1.tcpbbr.net
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: e3975a05-b89e-11ec-a50d-f23c91cfbbc9
    ws-opts:
      headers:
        Host: broadcastlv.chat.bilibili.com
      path: /
    xudp: true
  - name: "\U0001F1E9\U0001F1EA德国22 | ⬇️ 3.9MB/s"
    network: ws
    port: 8880
    server: *************
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
        User-Agent: >-
          Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML,
          like Gecko) Chrome/55.0.2883.87 Safari/537.36
      path: "/Telegram\U0001F1E8\U0001F1F3 @pgkj666 /?ed=2560"
    xudp: true
  - alpn:
      - h2
      - http/1.1
    client-fingerprint: randomized
    name: "\U0001F1E6\U0001F1EA阿拉伯酋长国4 | ⬇️ 998KB/s"
    network: ws
    port: 443
    server: ************
    tls: true
    type: vless
    udp: true
    uuid: 72d7ab94-4849-4a27-a840-ece6c8b53355
    ws-opts:
      headers:
        Host: bEstvpn-7Qp.pagES.dEV
        User-Agent: >-
          Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like
          Gecko) Chrome/55.0.2883.87 Safari/537.36
      path: /LpzVdXD0ymOpZWeV?ed=2560
    xudp: true
    servername: bEstvpn-7Qp.pagES.dEV
  - alpn:
      - http/1.1
    client-fingerprint: chrome
    name: "\U0001F1FA\U0001F1F8美国223 | ⬇️ 8.7MB/s"
    network: ws
    port: 443
    server: ***********
    tls: true
    type: vless
    udp: true
    uuid: ca48571a-403f-44b9-b2a6-e17c36d7cacc
    ws-opts:
      headers:
        Host: 10t.445.pp.ua
        User-Agent: >-
          Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML,
          like Gecko) Chrome/60.0.3112.90 Safari/537.36
      path: /uDLWPKXENxF2ulPWRIwOavJd
    xudp: true
    servername: 10t.445.pp.ua
  - alpn:
      - ''
    alterId: '0'
    cipher: auto
    name: "\U0001F1ED\U0001F1F0香港36 | ⬇️ 9.4MB/s"
    network: ws
    port: 8080
    server: 980f6095-sw9ds0-sw9kdi-11p9g.hgc1.tcpbbr.net
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 384d1b42-655f-11ed-a8bf-f23c91cfbbc9
    ws-opts:
      headers:
        Host: broadcastlv.chat.bilibili.com
      path: /
    xudp: true
  - alpn:
      - ''
    alterId: '0'
    cipher: auto
    name: "\U0001F1ED\U0001F1F0香港37 | ⬇️ 8.8MB/s"
    network: ws
    port: 8080
    server: 8650a063-svwf40-sww7b0-1qwp5.hgc1.tcpbbr.net
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 6e9cdc20-b92a-11ef-973a-f23c913c8d2b
    ws-opts:
      headers:
        Host: broadcastlv.chat.bilibili.com
      path: /
    xudp: true
  - alpn:
      - ''
    alterId: '0'
    cipher: auto
    name: "\U0001F1ED\U0001F1F0香港38 | ⬇️ 5.0MB/s"
    network: ws
    port: 8080
    server: 629ea6c1-svwf40-swdpud-duku.hgc1.tcpbbr.net
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 8e0f8ef2-5fe3-11ec-a8bf-f23c91cfbbc9
    ws-opts:
      headers:
        Host: broadcastlv.chat.bilibili.com
      path: /
    xudp: true
  - alpn:
      - ''
    alterId: '0'
    cipher: auto
    name: "\U0001F1ED\U0001F1F0香港39 | ⬇️ 1.9MB/s"
    network: ws
    port: 8080
    server: 29b6531a-svwf40-sw0dp4-3z3v.hgc1.tcpbbr.net
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 48c1e014-28d6-11ec-a0fc-f23c913c8d2b
    ws-opts:
      headers:
        Host: broadcastlv.chat.bilibili.com
      path: /
    xudp: true
  - name: "\U0001F1FA\U0001F1F8美国224 | ⬇️ 880KB/s"
    network: ws
    port: 8880
    server: *************
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
        User-Agent: >-
          Mozilla/5.0 (Windows NT 6.1) AppleWebKit/537.36 (KHTML, like Gecko)
          Chrome/41.0.2228.0 Safari/537.36
      path: "/Telegram\U0001F1E8\U0001F1F3 @pgkj666 /?ed=2560"
    xudp: true
  - alterId: '0'
    cipher: auto
    name: "\U0001F1ED\U0001F1F0香港40 | ⬇️ 2.6MB/s"
    network: ws
    port: 8080
    server: 4b97b4f9-swkhs0-sxlsd4-3z3v.hgc1.tcpbbr.net
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 48c1e014-28d6-11ec-a0fc-f23c913c8d2b
    ws-opts:
      headers:
        Host: broadcastlv.chat.bilibili.com
      path: /
    xudp: true
  - alterId: '0'
    cipher: auto
    name: "\U0001F1ED\U0001F1F0香港41 | ⬇️ 6.1MB/s"
    network: ws
    port: 8080
    server: e7cfefed-swkhs0-td1w5f-1t3cz.hgc1.tcpbbr.net
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: affe7124-c118-11ef-b6b2-f23c9164ca5d
    ws-opts:
      headers:
        Host: broadcastlv.chat.bilibili.com
      path: /
    xudp: true
  - name: "\U0001F1EB\U0001F1F7法国4 | ⬇️ 4.2MB/s"
    network: ws
    port: 8080
    server: *************
    type: vless
    udp: true
    uuid: 4ea841c1-0dc1-4563-9f47-deba8407cb4e
    ws-opts:
      headers:
        Host: J9.oDOtZrHUoO.ZuLAIR.ORg.
        User-Agent: >-
          Mozilla/5.0 (Windows NT 6.1) AppleWebKit/537.36 (KHTML, like Gecko)
          Chrome/70.0.3538.102 Safari/537.36
      path: /?ed=2048
    xudp: true
  - alterId: '0'
    cipher: auto
    name: "\U0001F1ED\U0001F1F0香港42 | ⬇️ 9.8MB/s"
    network: ws
    port: 8080
    server: 78759a3f-swkhs0-t3o6u7-1osdm.hgc1.tcpbbr.net
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 91365a7a-46d7-11ee-a8b9-f23c9164ca5d
    ws-opts:
      headers:
        Host: broadcastlv.chat.bilibili.com
      path: /
    xudp: true
  - alterId: '0'
    cipher: auto
    name: "\U0001F1ED\U0001F1F0香港43 | ⬇️ 7.4MB/s"
    network: ws
    port: 8080
    server: 69608311-swkhs0-sxuzn1-11p9g.hgc1.tcpbbr.net
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 384d1b42-655f-11ed-a8bf-f23c91cfbbc9
    ws-opts:
      headers:
        Host: broadcastlv.chat.bilibili.com
      path: /
    xudp: true
  - alterId: '0'
    cipher: auto
    name: "\U0001F1ED\U0001F1F0香港44 | ⬇️ 752KB/s"
    network: ws
    port: 8080
    server: a759528f-swkhs0-sww7b0-1qwp5.hgc1.tcpbbr.net
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 6e9cdc20-b92a-11ef-973a-f23c913c8d2b
    ws-opts:
      headers:
        Host: broadcastlv.chat.bilibili.com
      path: /
    xudp: true
  - alterId: '0'
    cipher: auto
    name: "\U0001F1ED\U0001F1F0香港45 | ⬇️ 4.4MB/s"
    network: ws
    port: 8080
    server: 15a4b57b-swexs0-sxf56z-1f596.hgc1.tcpbbr.net
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: e3975a05-b89e-11ec-a50d-f23c91cfbbc9
    ws-opts:
      headers:
        Host: broadcastlv.chat.bilibili.com
      path: /
    xudp: true
  - alterId: '0'
    cipher: auto
    name: "\U0001F1ED\U0001F1F0香港46 | ⬇️ 3.8MB/s"
    network: ws
    port: 8080
    server: 2eb241f9-swexs0-t3o6u7-1osdm.hgc1.tcpbbr.net
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 91365a7a-46d7-11ee-a8b9-f23c9164ca5d
    ws-opts:
      headers:
        Host: broadcastlv.chat.bilibili.com
      path: /
    xudp: true
  - alterId: '0'
    cipher: auto
    name: "\U0001F1ED\U0001F1F0香港47 | ⬇️ 2.0MB/s"
    network: ws
    port: 8080
    server: 2bdf2559-swexs0-sxlsd4-3z3v.hgc1.tcpbbr.net
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 48c1e014-28d6-11ec-a0fc-f23c913c8d2b
    ws-opts:
      headers:
        Host: broadcastlv.chat.bilibili.com
      path: /
    xudp: true
  - alterId: '0'
    cipher: auto
    name: "\U0001F1ED\U0001F1F0香港48 | ⬇️ 2.0MB/s"
    network: ws
    port: 8080
    server: f0f81700-swexs0-sxuzn1-11p9g.hgc1.tcpbbr.net
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 384d1b42-655f-11ed-a8bf-f23c91cfbbc9
    ws-opts:
      headers:
        Host: broadcastlv.chat.bilibili.com
      path: /
    xudp: true
  - alterId: '0'
    cipher: auto
    name: "\U0001F1ED\U0001F1F0香港49 | ⬇️ 7.6MB/s"
    network: ws
    port: 8080
    server: b1f6976e-swexs0-td1w5f-1t3cz.hgc1.tcpbbr.net
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: affe7124-c118-11ef-b6b2-f23c9164ca5d
    ws-opts:
      headers:
        Host: broadcastlv.chat.bilibili.com
      path: /
    xudp: true
  - client-fingerprint: chrome
    flow: xtls-rprx-vision
    name: "\U0001F1F7\U0001F1FA俄罗斯3 | ⬇️ 9.5MB/s"
    network: tcp
    port: 42632
    reality-opts:
      public-key: qtqcKrPLXaN-jpo7Zs1_80QDmA2uvCZKFGanJLJFFXA
      short-id: 1f8b5201
    server: **************
    tls: true
    type: vless
    udp: true
    uuid: f3ba3679-f2f4-465f-92a5-de320b775a88
    xudp: true
    servername: www.yahoo.com
  - alterId: 2
    cipher: auto
    name: "\U0001F300其他30-未识别 | ⬇️ 1.1MB/s"
    network: ws
    port: 30837
    server: v37.heduian.link
    skip-cert-verify: true
    tls: false
    type: vmess
    uuid: cbb3f877-d1fb-344c-87a9-d153bffd5484
    ws-opts:
      headers:
        Host: v37.heduian.link
      path: /oooo
    servername: ocbc.com
  - alterId: 0
    cipher: auto
    name: "\U0001F1FA\U0001F1F8美国225 | ⬇️ 7.5MB/s"
    network: ws
    port: 443
    server: cloudgetservice.mcloudservice.site
    skip-cert-verify: true
    tls: true
    type: vmess
    uuid: 81cfc74e-79f6-4187-9c11-58b6acdc0e83
    ws-opts:
      headers:
        Host: uSa-Vp-149.BLazECloudMain.Site
      path: /linkvkws
    servername: cloudgetservice.mcloudservice.site
  - alterId: 0
    cipher: auto
    name: "\U0001F1F0\U0001F1F7韩国20 | ⬇️ 1.1MB/s"
    network: ws
    port: 2096
    server: dv4.789900.xyz
    skip-cert-verify: true
    tls: true
    type: vmess
    uuid: 0c8f3e29-4ec9-4ba6-a6ef-4487873f5d05
    ws-opts:
      headers:
        Host: dv4.789900.xyz
      path: /0c8f3e29-4ec9-4ba6-a6ef-4487873f5d05-vm
    servername: dv4.789900.xyz
  - name: "\U0001F1FA\U0001F1F8美国226 | ⬇️ 6.0MB/s"
    network: ws
    port: 8880
    server: **************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /?ed=2560
  - client-fingerprint: chrome
    name: "\U0001F300其他31-IS | ⬇️ 506KB/s"
    network: ws
    port: 443
    server: *************
    skip-cert-verify: true
    tfo: false
    tls: true
    type: vless
    uuid: 753fa8e8-a3e8-442e-abf7-875ed776eacb
    ws-opts:
      headers:
        Host: is.oldcloud.online
      path: /
    servername: is.oldcloud.online
  - name: "\U0001F1FA\U0001F1F8美国227 | ⬇️ 2.5MB/s"
    network: ws
    port: 80
    server: ***********
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: '/Telegram:'
  - name: "\U0001F1FA\U0001F1F8美国228 | ⬇️ 566KB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: "/Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
  - name: "\U0001F1F3\U0001F1F1荷兰2 | ⬇️ 2.9MB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: "/Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
  - cipher: aes-256-gcm
    name: "\U0001F1F8\U0001F1EC新加坡13 | ⬇️ 1.3MB/s"
    password: RFUKD9DMSTSXGAJ8
    port: 16015
    server: ti3hyra4.slashdevslashnetslashtun.net
    type: ss
    udp: true
  - name: "\U0001F1FA\U0001F1F8美国229 | ⬇️ 5.5MB/s"
    password: b79d79ae-8bce-11ef-a2b8-f23c9164ca5d
    port: 15229
    server: 7cde4333-swvls0-t67sv3-1snfs.cu.plebai.net
    type: trojan
    udp: true
    sni: 7cde4333-swvls0-t67sv3-1snfs.cu.plebai.net
  - name: "\U0001F1F8\U0001F1EC新加坡14 | ⬇️ 1.1MB/s"
    network: ws
    port: 80
    server: **************
    tls: false
    type: vless
    udp: true
    uuid: 438f9559-1671-45cf-9d2c-338fe6766acf
    ws-opts:
      headers:
        Host: 14.sahanwickramasinghe.shop
      path: /
    servername: 14.sahanwickramasinghe.shop
  - auth: 796ec552-f8e4-43c7-ac2f-5c2e668074de
    name: "\U0001F1FA\U0001F1F8美国230 | ⬇️ 4.8MB/s"
    password: 796ec552-f8e4-43c7-ac2f-5c2e668074de
    port: 30003
    server: qymg.qy1357.top
    skip-cert-verify: true
    sni: qymg.qy1357.top
    type: hysteria2
    udp: true
  - name: "\U0001F1FA\U0001F1F8美国231 | ⬇️ 806KB/s"
    network: ws
    port: 80
    server: **************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: '/Telegram:'
  - name: "\U0001F1FA\U0001F1F8美国232 | ⬇️ 611KB/s"
    network: ws
    port: 8880
    server: **************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /?ed=2560
  - name: "\U0001F1FA\U0001F1F8美国233 | ⬇️ 3.6MB/s"
    password: 0a335fd6-be0b-11ec-8dfa-f23c91cfbbc9
    port: 15229
    server: eac1462b-swxgg0-sxkd63-17z95.cu.plebai.net
    sni: eac1462b-swxgg0-sxkd63-17z95.cu.plebai.net
    type: trojan
    udp: true
  - name: "\U0001F1FA\U0001F1F8美国234 | ⬇️ 6.6MB/s"
    password: 48c1e014-28d6-11ec-a0fc-f23c913c8d2b
    port: 15229
    server: d078fddf-swvls0-sxlsd4-3z3v.cu.plebai.net
    type: trojan
    udp: true
    sni: d078fddf-swvls0-sxlsd4-3z3v.cu.plebai.net
  - auth: 861726bb-c61a-b0e0-99fa-5ddfdcf21902
    name: "\U0001F1FA\U0001F1F8美国235 | ⬇️ 1.9MB/s"
    password: 861726bb-c61a-b0e0-99fa-5ddfdcf21902
    port: 8443
    server: 3ec98078-swvls0-ta5nd4-e06r.hy2.gotochinatown.net
    type: hysteria2
    udp: true
    sni: 3ec98078-swvls0-ta5nd4-e06r.hy2.gotochinatown.net
  - auth: 34f0cf42-0f8a-11ec-a8bf-f23c91cfbbc9
    name: "\U0001F1FA\U0001F1F8美国236 | ⬇️ 2.0MB/s"
    password: 34f0cf42-0f8a-11ec-a8bf-f23c91cfbbc9
    port: 8443
    server: d9137bac-swvls0-szdere-155d9.hy2.gotochinatown.net
    type: hysteria2
    udp: true
    sni: d9137bac-swvls0-szdere-155d9.hy2.gotochinatown.net
  - name: "\U0001F1FA\U0001F1F8美国237 | ⬇️ 728KB/s"
    network: ws
    port: 80
    server: ***********
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: '/Telegram: @vpnAndroid2/?ed=2560'
  - name: "\U0001F1FA\U0001F1F8美国238 | ⬇️ 777KB/s"
    password: 6e9cdc20-b92a-11ef-973a-f23c913c8d2b
    port: 15229
    server: a666d7a4-swvls0-sww7b0-1qwp5.cu.plebai.net
    type: trojan
    udp: true
    sni: a666d7a4-swvls0-sww7b0-1qwp5.cu.plebai.net
  - name: "\U0001F1FA\U0001F1F8美国239 | ⬇️ 2.2MB/s"
    password: c1785de0-212f-11ef-92aa-f23c9164ca5d
    port: 15229
    server: 9622193a-swvls0-t6570q-1q3dm.cu.plebai.net
    type: trojan
    udp: true
    sni: 9622193a-swvls0-t6570q-1q3dm.cu.plebai.net
  - name: "\U0001F1FA\U0001F1F8美国240 | ⬇️ 631KB/s"
    network: ws
    port: 8880
    server: ***********
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: "/Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
  - client-fingerprint: chrome
    name: "\U0001F1FA\U0001F1F8美国241 | ⬇️ 9.0MB/s"
    network: ws
    port: 443
    server: ************
    tls: true
    type: vless
    udp: true
    uuid: dc66e2ad-31ed-42e8-ab03-96c5cdc2a6cd
    ws-opts:
      headers:
        Host: us2s.globals-download.com
      path: /pq/us2
    servername: us2s.globals-download.com
  - auth: d1bd5276-7bf1-4527-878c-fbd9f22d39de
    down: 50
    name: "\U0001F1FA\U0001F1F8美国242 | ⬇️ 2.3MB/s"
    obfs: salamander
    obfs-password: NDhhNmY5YTY0MGYzOTgxYQ==
    password: d1bd5276-7bf1-4527-878c-fbd9f22d39de
    port: 31005
    server: **************
    skip-cert-verify: true
    type: hysteria2
    udp: true
    up: 50
  - client-fingerprint: chrome
    flow: xtls-rprx-vision
    name: "\U0001F300其他32-TR | ⬇️ 1.5MB/s"
    network: tcp
    port: 20411
    reality-opts:
      public-key: 9rx7JwMO-KRZZEM9TQBO19BOAmmGjJyjN86ll2J7uVc
    server: **************
    tls: true
    type: vless
    udp: true
    uuid: 54ffbe99-7aaf-4ceb-9a82-048fb2489757
    servername: www.speedtest.net
  - name: "\U0001F1EB\U0001F1F7法国5 | ⬇️ 2.9MB/s"
    network: ws
    port: 8080
    server: *************
    tls: false
    type: vless
    udp: true
    uuid: 690ab90e-3f21-422b-8cb1-bc9845c7af1e
    ws-opts:
      headers:
        Host: IO.IhJtq0hklC.ZULAiR.OrG.
      path: /
  - name: "\U0001F1FA\U0001F1F8美国243 | ⬇️ 648KB/s"
    password: d4018e28-e328-11ed-98a7-f23c913c8d2b
    port: 15229
    server: 9210f21f-swvls0-sx3h07-1g8k0.cu.plebai.net
    type: trojan
    udp: true
    sni: 9210f21f-swvls0-sx3h07-1g8k0.cu.plebai.net
  - name: "\U0001F1FA\U0001F1F8美国244 | ⬇️ 5.2MB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: "/Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
  - name: "\U0001F1FA\U0001F1F8美国245 | ⬇️ 1.2MB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: "/Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
  - name: "\U0001F300其他33-未识别 | ⬇️ 554KB/s"
    network: ws
    port: 8880
    server: *************
    tls: false
    type: vless
    udp: true
    uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
    ws-opts:
      headers:
        Host: us.laoyoutiao.link
      path: "/Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
  - name: "\U0001F1FA\U0001F1F8美国246 | ⬇️ 2.3MB/s"
    password: bcc58e88-e147-11ec-b286-f23c91cfbbc9
    port: 15229
    server: 7c90d3ac-swvls0-sxu45k-ggww.cu.plebai.net
    type: trojan
    udp: true
    sni: 7c90d3ac-swvls0-sxu45k-ggww.cu.plebai.net
  - auth: 0da8651e-e1f6-11ec-bd7c-f23c913c8d2b
    name: "\U0001F1FA\U0001F1F8美国247 | ⬇️ 3.6MB/s"
    password: 0da8651e-e1f6-11ec-bd7c-f23c913c8d2b
    port: 8443
    server: d329acde-swvls0-tcinla-hrtf.hy2.gotochinatown.net
    type: hysteria2
    udp: true
    sni: d329acde-swvls0-tcinla-hrtf.hy2.gotochinatown.net
  - name: "\U0001F1E9\U0001F1EA德国23 | ⬇️ 2.4MB/s"
    network: ws
    port: 8880
    server: ***********
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: "/Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
  - alpn:
      - h3
    auth-str: dongtaiwang.com
    auth_str: dongtaiwang.com
    down: 55 Mbps
    name: "\U0001F1FA\U0001F1F8美国248 | ⬇️ 4.6MB/s"
    port: 11512
    protocol: udp
    server: *************
    skip-cert-verify: true
    sni: apple.com
    type: hysteria
    udp: true
    up: 11 Mbps
  - cipher: aes-256-gcm
    name: "\U0001F1F8\U0001F1EC新加坡15 | ⬇️ 503KB/s"
    password: 3IJTYIFVEXKSJ92G
    port: 16015
    server: ti3hyra4.slashdevslashnetslashtun.net
    type: ss
    udp: true
  - name: "\U0001F1FA\U0001F1F8美国249 | ⬇️ 5.9MB/s"
    password: 93fb69fc-77cf-11ee-85ee-f23c91369f2d
    port: 15229
    server: 4f921f15-swvls0-t12cnj-1ol97.cu.plebai.net
    type: trojan
    udp: true
    sni: 4f921f15-swvls0-t12cnj-1ol97.cu.plebai.net
  - name: "\U0001F1E9\U0001F1EA德国24 | ⬇️ 2.3MB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /?ed=2560
    servername: reedfree8mahsang2.redorg.ir
  - auth: d010926e-0311-4924-a013-b84fbae430f9
    name: "\U0001F1EC\U0001F1E7英国6 | ⬇️ 5.2MB/s"
    password: d010926e-0311-4924-a013-b84fbae430f9
    port: 30003
    server: qyyg.qy1357.top
    skip-cert-verify: true
    sni: qyyg.qy1357.top
    type: hysteria2
    udp: true
  - name: "\U0001F1FA\U0001F1F8美国250 | ⬇️ 609KB/s"
    network: ws
    port: 80
    server: **************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: '/Telegram: @vpnAndroid2/?ed=2560'
  - name: "\U0001F1FA\U0001F1F8美国251 | ⬇️ 949KB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: "/Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
  - auth: 498c79e0-d4bb-43b0-a6c6-4bd5031c2405
    name: "\U0001F1EE\U0001F1F3印度3 | ⬇️ 4.5MB/s"
    password: 498c79e0-d4bb-43b0-a6c6-4bd5031c2405
    port: 43999
    server: jiangzhiyd.54264944.xyz
    skip-cert-verify: true
    sni: jiangzhiyd.54264944.xyz
    type: hysteria2
    udp: true
  - name: "\U0001F1EF\U0001F1F5日本13 | ⬇️ 9.5MB/s"
    password: CXCu72eya8wFeRySSFpDz3CN6jBYac5OIl3q0gSl8xZOADY3EK94pxZanDA3RT
    port: 443
    server: ***********
    skip-cert-verify: true
    sni: broker.superpokemon.com
    type: trojan
    udp: true
  - alpn:
      - h3
    auth-str: dongtaiwang.com
    auth_str: dongtaiwang.com
    down: 100
    name: "\U0001F1FA\U0001F1F8美国252 | ⬇️ 9.2MB/s"
    port: 36194
    protocol: udp
    server: ************
    skip-cert-verify: true
    sni: apple.com
    type: hysteria
    udp: true
    up: 100
  - name: "\U0001F1E9\U0001F1EA德国25 | ⬇️ 2.1MB/s"
    network: ws
    port: 8880
    server: *************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /?ed=2560
  - name: "\U0001F1EB\U0001F1EE芬兰7 | ⬇️ 559KB/s"
    network: ws
    port: 8880
    server: **************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /?ed=2560
  - name: "\U0001F1E9\U0001F1EA德国26 | ⬇️ 3.2MB/s"
    network: ws
    port: 8880
    server: ***********
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: "/Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
  - name: "\U0001F1E9\U0001F1EA德国27 | ⬇️ 1.1MB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: "/Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
  - name: "\U0001F1F3\U0001F1F1荷兰3 | ⬇️ 3.0MB/s"
    network: ws
    port: 8880
    server: ***********
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: "/Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
  - auth: 71aef91d-ab68-4dc8-8061-a1f8a32cb24b
    down: 50
    name: "\U0001F1FA\U0001F1F8美国253 | ⬇️ 1.0MB/s"
    obfs: salamander
    obfs-password: NDhhNmY5YTY0MGYzOTgxYQ==
    password: 71aef91d-ab68-4dc8-8061-a1f8a32cb24b
    port: 33076
    server: **************
    skip-cert-verify: true
    type: hysteria2
    udp: true
    up: 50
  - name: "\U0001F1E6\U0001F1EA阿拉伯酋长国5 | ⬇️ 8.5MB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /?ed=2560
  - auth: 869a0163-456f-4c06-bd4a-2376e4563eae
    name: "\U0001F1ED\U0001F1F0香港50 | ⬇️ 3.8MB/s"
    password: 869a0163-456f-4c06-bd4a-2376e4563eae
    port: 30003
    server: qyhg.qy1357.top
    skip-cert-verify: true
    sni: qyhg.qy1357.top
    type: hysteria2
    udp: true
  - name: "\U0001F1FA\U0001F1F8美国254 | ⬇️ 571KB/s"
    network: ws
    port: 8880
    server: ***********
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: "/Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
  - name: "\U0001F1EB\U0001F1EE芬兰8 | ⬇️ 1.1MB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: "/Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
  - name: "\U0001F1FA\U0001F1F8美国255 | ⬇️ 1.1MB/s"
    password: ca506e08-ce3d-5e5a-c128-6358caca15e5
    port: 15229
    server: 22a112ae-swvls0-syatom-8bem.cu.plebai.net
    type: trojan
    udp: true
    sni: 22a112ae-swvls0-syatom-8bem.cu.plebai.net
  - name: "\U0001F1FA\U0001F1F8美国256 | ⬇️ 4.8MB/s"
    network: ws
    port: 80
    server: *************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: '/Telegram: @vpnAndroid2/?ed=2560'
  - alpn:
      - http/1.1
    name: "\U0001F1FA\U0001F1F8美国257 | ⬇️ 814KB/s"
    network: ws
    password: 061ec9d5-ba40-428a-8be0-1947b10b5cfc
    port: 443
    server: *************
    skip-cert-verify: false
    sni: 16gG.irAN.pP.UA
    type: trojan
    udp: true
    ws-opts:
      headers:
        Host: 16gG.irAN.pP.UA
      path: /Dmby2AKiZKvMo1YauoQwY5
  - name: "\U0001F1FA\U0001F1F8美国258 | ⬇️ 2.6MB/s"
    password: ZZaBe4T8xeESCl9YODNpc5SnaFl7x0KFR32IY33upqy3zA6aDA8OgRSDCCXyjw
    port: 28333
    server: *************
    skip-cert-verify: true
    sni: penguin.missionsec.io
    type: trojan
    udp: true
  - name: "\U0001F1FA\U0001F1F8美国259 | ⬇️ 2.2MB/s"
    password: 0a335fd6-be0b-11ec-8dfa-f23c91cfbbc9
    port: 15229
    server: 8a1a9600-swvls0-sxkd63-17z95.cu.plebai.net
    skip-cert-verify: false
    type: trojan
    udp: true
    sni: 8a1a9600-swvls0-sxkd63-17z95.cu.plebai.net
  - name: "\U0001F1F3\U0001F1F1荷兰4 | ⬇️ 826KB/s"
    network: ws
    port: 80
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: '/Telegram: @vpnAndroid2/?ed=2560'
  - name: "\U0001F1FA\U0001F1F8美国260 | ⬇️ 4.5MB/s"
    network: ws
    port: 8880
    server: *************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /?ed=2560
  - name: "\U0001F1FA\U0001F1F8美国261 | ⬇️ 7.0MB/s"
    password: 34f0cf42-0f8a-11ec-a8bf-f23c91cfbbc9
    port: 15229
    server: 60bb23b9-swvls0-szdere-155d9.cu.plebai.net
    type: trojan
    udp: true
    sni: 60bb23b9-swvls0-szdere-155d9.cu.plebai.net
  - name: "\U0001F1FA\U0001F1F8美国262 | ⬇️ 1.4MB/s"
    password: 3fa7157d-efe8-832e-9b3c-582d78efbc52
    port: 15229
    server: 373866b1-swvls0-tf70jh-vm13.cu.plebai.net
    type: trojan
    udp: true
    sni: 373866b1-swvls0-tf70jh-vm13.cu.plebai.net
  - name: "\U0001F1ED\U0001F1F0香港51 | ⬇️ 1.5MB/s"
    network: ws
    port: 8880
    server: *************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /?ed=2560
  - alterId: 64
    cipher: auto
    name: "\U0001F1F8\U0001F1EC新加坡16 | ⬇️ 1.0MB/s"
    port: 49302
    server: *************
    tls: false
    type: vmess
    udp: true
    uuid: 418048af-a293-4b99-9b0c-98ca3580dd24
  - name: "\U0001F1FA\U0001F1F8美国263 | ⬇️ 858KB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: "/Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
  - name: "\U0001F1ED\U0001F1F0香港52 | ⬇️ 2.3MB/s"
    network: ws
    port: 8880
    server: *************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /?ed=2560
  - client-fingerprint: chrome
    flow: xtls-rprx-vision
    name: "\U0001F1E8\U0001F1E6加拿大10 | ⬇️ 1.7MB/s"
    network: tcp
    port: 59303
    reality-opts:
      public-key: FHm9vc9hi1aK9jz8CqCjhJvT0bxzsD470FvOyubB6EA
      short-id: bbe06294
    server: **************
    tls: true
    type: vless
    udp: true
    uuid: 862e90e1-2f26-4506-b8b9-0adf20da05fc
    servername: www.yahoo.com
  - name: "\U0001F1E9\U0001F1EA德国28 | ⬇️ 877KB/s"
    network: ws
    port: 8880
    server: *************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /?ed=2560
  - client-fingerprint: randomized
    name: "\U0001F1F0\U0001F1F7韩国21 | ⬇️ 1.4MB/s"
    network: ws
    port: 2053
    server: **************
    tls: true
    type: vless
    udp: true
    uuid: d6b8011a-c725-435a-9fec-bf6d3530392c
    ws-opts:
      headers:
        Host: vle.amclubsapp.dpdns.org
      path: /?ed=2560&PROT_TYPE=vless
    servername: vle.amclubsapp.dpdns.org
  - name: "\U0001F1FA\U0001F1F8美国264 | ⬇️ 3.1MB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: "/Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
  - name: "\U0001F1FA\U0001F1F8美国265 | ⬇️ 5.7MB/s"
    password: 31676725-b0fd-fed2-c857-2da8ce5a6e4f
    port: 15229
    server: 73e237fd-swvls0-t0dyyh-jjv2.cu.plebai.net
    type: trojan
    udp: true
    sni: 73e237fd-swvls0-t0dyyh-jjv2.cu.plebai.net
  - auth: 498c79e0-d4bb-43b0-a6c6-4bd5031c2405
    name: "\U0001F1FA\U0001F1F8美国266 | ⬇️ 3.5MB/s"
    password: 498c79e0-d4bb-43b0-a6c6-4bd5031c2405
    port: 43999
    server: jiangzhidb.54264944.xyz
    skip-cert-verify: true
    sni: jiangzhidb.54264944.xyz
    type: hysteria2
    udp: true
  - name: "\U0001F1FA\U0001F1F8美国267 | ⬇️ 4.1MB/s"
    password: cfe61764-0004-11f0-a910-f23c9164ca5d
    port: 15229
    server: 82ab6fb4-swvls0-sx9j1n-1timk.cu.plebai.net
    type: trojan
    udp: true
    sni: 82ab6fb4-swvls0-sx9j1n-1timk.cu.plebai.net
  - name: "\U0001F1FA\U0001F1F8美国268 | ⬇️ 4.1MB/s"
    password: fd224a6c-addc-11ed-a8bf-f23c91cfbbc9
    port: 15229
    server: 31219580-swvls0-sybxeq-1ldl3.cu.plebai.net
    type: trojan
    udp: true
    sni: 31219580-swvls0-sybxeq-1ldl3.cu.plebai.net
  - name: "\U0001F1FA\U0001F1F8美国269 | ⬇️ 2.4MB/s"
    password: 0de37cdc-abff-11ef-b7c6-f23c913c8d2b
    port: 15229
    server: 2c7b4b56-swvls0-syhw90-1rsuw.cu.plebai.net
    type: trojan
    udp: true
    sni: 2c7b4b56-swvls0-syhw90-1rsuw.cu.plebai.net
  - name: "\U0001F1FA\U0001F1F8美国270 | ⬇️ 1.6MB/s"
    network: ws
    port: 8880
    server: ***********
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: "/Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
  - name: "\U0001F1FA\U0001F1F8美国271 | ⬇️ 810KB/s"
    network: ws
    port: 8880
    server: ***********
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: "/Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
  - name: "\U0001F1F0\U0001F1F7韩国22 | ⬇️ 669KB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: "/Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
  - name: "\U0001F1F0\U0001F1F7韩国23 | ⬇️ 3.3MB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: "/Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
  - name: "\U0001F1EB\U0001F1EE芬兰9 | ⬇️ 534KB/s"
    network: ws
    port: 8880
    server: ***********
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: "/Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
  - name: "\U0001F1FA\U0001F1F8美国272 | ⬇️ 800KB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: "/Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
  - alterId: 2
    cipher: auto
    name: "\U0001F1ED\U0001F1F0香港53 | ⬇️ 13.0MB/s"
    network: ws
    port: 459
    server: f77b7ed3-swtr40-syb15h-8caj.hkt.east.wctype.com
    tls: false
    type: vmess
    udp: true
    uuid: a67a6c18-3f6d-11ef-ab9c-f23c9313b177
    ws-opts:
      headers:
        Host: a605477178.m.ctrip.com
      path: /
  - name: "\U0001F1FA\U0001F1F8美国273 | ⬇️ 3.4MB/s"
    password: 0da8651e-e1f6-11ec-bd7c-f23c913c8d2b
    port: 15229
    server: 32f54781-swvls0-tcinla-hrtf.cu.plebai.net
    type: trojan
    udp: true
    sni: 32f54781-swvls0-tcinla-hrtf.cu.plebai.net
  - alpn:
      - h3%2Ch2%2Chttp%2F1.1
    client-fingerprint: chrome
    http-opts:
      headers:
        Host:
          - hkg.gtnet.uk
      method: GET
      path:
        - /
    name: "\U0001F1ED\U0001F1F0香港54 | ⬇️ 766KB/s"
    network: http
    port: 8443
    server: hkg.gtnet.uk
    tls: true
    type: vless
    udp: true
    uuid: 42e062f1-38f3-47c9-9ed7-7294163012e4
    servername: hkg.gtnet.uk
  - auth: 796ec552-f8e4-43c7-ac2f-5c2e668074de
    name: "\U0001F1EC\U0001F1E7英国7 | ⬇️ 6.8MB/s"
    password: 796ec552-f8e4-43c7-ac2f-5c2e668074de
    port: 30003
    server: qyyg.qy1357.top
    skip-cert-verify: true
    sni: qyyg.qy1357.top
    type: hysteria2
    udp: true
  - name: "\U0001F1FA\U0001F1F8美国274 | ⬇️ 697KB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: "/Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
  - auth: 498c79e0-d4bb-43b0-a6c6-4bd5031c2405
    name: "\U0001F1F8\U0001F1EC新加坡17 | ⬇️ 1.4MB/s"
    password: 498c79e0-d4bb-43b0-a6c6-4bd5031c2405
    port: 43999
    server: jiangzhiylk.54264944.xyz
    skip-cert-verify: true
    sni: jiangzhiylk.54264944.xyz
    type: hysteria2
    udp: true
  - name: "\U0001F1E9\U0001F1EA德国29 | ⬇️ 1.4MB/s"
    network: ws
    port: 8880
    server: ***********
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: "/Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
  - name: "\U0001F1ED\U0001F1F0香港55 | ⬇️ 786KB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: "/Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
  - auth: ba4672b3-e0af-4699-a180-f711cfaa95ed
    name: "\U0001F1FA\U0001F1F8美国275 | ⬇️ 7.4MB/s"
    password: ba4672b3-e0af-4699-a180-f711cfaa95ed
    port: 43999
    server: jiangzhidb.54264944.xyz
    skip-cert-verify: true
    sni: jiangzhidb.54264944.xyz
    type: hysteria2
    udp: true
  - name: "\U0001F1E9\U0001F1EA德国30 | ⬇️ 925KB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: "/Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
  - auth: fceff025-b4ea-4fd2-9093-151b3bd503cd
    down: 10
    name: "\U0001F300其他34-未识别 | ⬇️ 524KB/s"
    obfs: salamander
    obfs-password: NDhhNmY5YTY0MGYzOTgxYQ==
    password: fceff025-b4ea-4fd2-9093-151b3bd503cd
    port: 33076
    server: **************
    skip-cert-verify: true
    type: hysteria2
    udp: true
    up: 10
  - auth: 4a6b73d6-697a-4195-9f3c-11034a579c5e
    down: 50
    name: "\U0001F300其他35-未识别 | ⬇️ 1.7MB/s"
    obfs: salamander
    obfs-password: NDhhNmY5YTY0MGYzOTgxYQ==
    password: 4a6b73d6-697a-4195-9f3c-11034a579c5e
    port: 33076
    server: **************
    skip-cert-verify: true
    type: hysteria2
    udp: true
    up: 50
  - name: "\U0001F1FA\U0001F1F8美国276 | ⬇️ 1.1MB/s"
    password: 06b2ac52-fcc6-11ec-bb74-f23c9164ca5d
    port: 15229
    server: 4ff95e70-swvls0-sznzxg-1jfvb.cu.plebai.net
    type: trojan
    udp: true
    sni: 4ff95e70-swvls0-sznzxg-1jfvb.cu.plebai.net
  - name: "\U0001F1E6\U0001F1EA阿拉伯酋长国6 | ⬇️ 1.8MB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /?ed=2560
  - name: "\U0001F1FA\U0001F1F8美国277 | ⬇️ 4.7MB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: "/Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
  - name: "\U0001F1FA\U0001F1F8美国278 | ⬇️ 5.4MB/s"
    network: ws
    port: 8880
    server: ***********
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: "/Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
  - name: "\U0001F1F0\U0001F1F7韩国24 | ⬇️ 842KB/s"
    network: ws
    port: 8880
    server: *************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /?ed=2560
  - auth: 8ac1c3f6-d65c-4892-a6b0-025d5e64bea8
    down: 100
    name: "\U0001F1FA\U0001F1F8美国279 | ⬇️ 1.5MB/s"
    obfs: salamander
    obfs-password: NDhhNmY5YTY0MGYzOTgxYQ==
    password: 8ac1c3f6-d65c-4892-a6b0-025d5e64bea8
    port: 30073
    server: **************
    skip-cert-verify: true
    type: hysteria2
    udp: true
    up: 100
  - name: "\U0001F1FA\U0001F1F8美国280 | ⬇️ 3.7MB/s"
    password: e3975a05-b89e-11ec-a50d-f23c91cfbbc9
    port: 15229
    server: 10271933-swvls0-sxf56z-1f596.cu.plebai.net
    type: trojan
    udp: true
    sni: 10271933-swvls0-sxf56z-1f596.cu.plebai.net
  - name: "\U0001F1FA\U0001F1F8美国281 | ⬇️ 4.1MB/s"
    password: ef18aaca-4711-11ec-a8bf-f23c91cfbbc9
    port: 15229
    server: f4484bf5-swvls0-sy439s-laev.cu.plebai.net
    type: trojan
    udp: true
    sni: f4484bf5-swvls0-sy439s-laev.cu.plebai.net
  - name: "\U0001F1FA\U0001F1F8美国282 | ⬇️ 3.2MB/s"
    network: ws
    port: 8880
    server: ***********
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: "/Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
  - alpn:
      - h3h2
    client-fingerprint: chrome
    name: "\U0001F300其他36-CY | ⬇️ 6.5MB/s"
    network: ws
    port: 443
    server: *************
    tls: true
    type: vless
    udp: true
    uuid: 96a1b724-68d2-4f4d-ab25-38ecd83577bc
    ws-opts:
      headers:
        Host: U3sK98eJnV.sOlIjOnItO.cOm
      path: /
    servername: U3sK98eJnV.sOlIjOnItO.cOm
  - auth: 869a0163-456f-4c06-bd4a-2376e4563eae
    name: "\U0001F1FA\U0001F1F8美国283 | ⬇️ 7.5MB/s"
    password: 869a0163-456f-4c06-bd4a-2376e4563eae
    port: 30003
    server: qymg.qy1357.top
    skip-cert-verify: true
    sni: qymg.qy1357.top
    type: hysteria2
    udp: true
  - name: "\U0001F1FA\U0001F1F8美国284 | ⬇️ 555KB/s"
    password: 088a0bbe-4f9f-11ea-a15d-f23c913c8d2b
    port: 15229
    server: b6b67e0e-swvls0-t5vrf3-716s.cu.plebai.net
    type: trojan
    udp: true
    sni: b6b67e0e-swvls0-t5vrf3-716s.cu.plebai.net
  - name: "\U0001F1EE\U0001F1F3印度4 | ⬇️ 1.7MB/s"
    network: ws
    port: 8880
    server: **********6
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: "/Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
  - name: "\U0001F1E9\U0001F1EA德国31 | ⬇️ 785KB/s"
    network: ws
    port: 8880
    server: **************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /?ed=2560
  - name: "\U0001F1FA\U0001F1F8美国285 | ⬇️ 3.3MB/s"
    password: dcccacba-fa44-11ef-8400-f23c9164ca5d
    port: 15229
    server: b009f292-swvls0-sx0fe4-1j6h0.cu.plebai.net
    type: trojan
    udp: true
    sni: b009f292-swvls0-sx0fe4-1j6h0.cu.plebai.net
  - name: "\U0001F1FA\U0001F1F8美国286 | ⬇️ 7.0MB/s"
    password: 591ac730-bd6f-11ed-a8bf-f23c91cfbbc9
    port: 15229
    server: a2f012ea-swvls0-sy3zfb-ezjz.cu.plebai.net
    type: trojan
    udp: true
    sni: a2f012ea-swvls0-sy3zfb-ezjz.cu.plebai.net
  - client-fingerprint: chrome
    flow: xtls-rprx-vision
    name: "\U0001F1E8\U0001F1E6加拿大11 | ⬇️ 588KB/s"
    network: tcp
    port: 42557
    reality-opts:
      public-key: LiHpb4jWrgHBSpi1mjKH3I8m2ahpVNexeNDh-sMW3Xo
      short-id: f430927d
    server: *************
    tls: true
    type: vless
    udp: true
    uuid: 2429bee3-e0c1-47dd-b420-75e6512b184b
    servername: www.yahoo.com
  - name: "\U0001F1E9\U0001F1EA德国32 | ⬇️ 1.5MB/s"
    network: ws
    port: 8880
    server: ***********
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: "/Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
  - name: "\U0001F1F3\U0001F1F1荷兰5 | ⬇️ 654KB/s"
    network: ws
    port: 8880
    server: **********0
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: "/Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
  - name: "\U0001F1E6\U0001F1EA阿拉伯酋长国7 | ⬇️ 2.1MB/s"
    network: ws
    port: 8880
    server: ***********
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: "/Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
  - client-fingerprint: safari
    flow: xtls-rprx-vision
    name: "\U0001F1F9\U0001F1FC台湾9 | ⬇️ 10.2MB/s"
    network: tcp
    port: 14550
    reality-opts:
      public-key: 4rzMLQEcZ2mppYMthXeq8nQk9A_tmny5qhwh-Fn9A2k
    server: tw00.yuanbaojc.site
    tls: true
    type: vless
    udp: true
    uuid: 646d2022-4f69-4a6e-b142-d00e475b36e6
    servername: s0.awsstatic.com
  - name: "\U0001F1F8\U0001F1EC新加坡18 | ⬇️ 921KB/s"
    network: tcp
    port: 443
    server: hkg.gtnet.uk
    tls: false
    type: vless
    udp: true
    uuid: de2036da-d30f-4bad-a7f6-389fe1b00ef5
  - name: "\U0001F1ED\U0001F1F0香港56 | ⬇️ 5.2MB/s"
    network: ws
    port: 80
    server: ***************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: '/Telegram:'
  - name: "\U0001F1F3\U0001F1F1荷兰6 | ⬇️ 2.3MB/s"
    network: ws
    port: 8880
    server: *************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /?ed=2560
  - name: "\U0001F1FA\U0001F1F8美国287 | ⬇️ 1.9MB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: "/Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
  - name: "\U0001F300其他37-SE | ⬇️ 3.9MB/s"
    password: E3gATlaOeIYBCc09x6apnS8yYSyCx5AF2RSuqpzF4wRZZlNKCDD3jaO833De7X
    port: 443
    server: *************
    skip-cert-verify: true
    sni: bribery.freetrade.link
    type: trojan
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F300其他38-MY | ⬇️ 6.7MB/s"
    password: TPCIM1dLUG64W3Lom1k9NavzLx14ArpNJmr1PUXVrJU=
    port: 54111
    server: *************
    type: ss
    udp: true
  - name: "\U0001F1E9\U0001F1EA德国33 | ⬇️ 587KB/s"
    network: ws
    port: 80
    server: **************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: '/Telegram:'
  - name: "\U0001F1FA\U0001F1F8美国288 | ⬇️ 6.5MB/s"
    password: 8e0f8ef2-5fe3-11ec-a8bf-f23c91cfbbc9
    port: 15229
    server: 29c067c5-swvls0-t14dgl-duku.cu.plebai.net
    type: trojan
    udp: true
    sni: 29c067c5-swvls0-t14dgl-duku.cu.plebai.net
  - name: "\U0001F1EB\U0001F1EE芬兰10 | ⬇️ 533KB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /?ed=2560
  - name: "\U0001F1FA\U0001F1F8美国289 | ⬇️ 6.3MB/s"
    password: 861b5684-062c-11f0-8eb0-f23c9164ca5d
    port: 15229
    server: 949d369d-swvls0-sx9b4u-1sd8z.cu.plebai.net
    type: trojan
    udp: true
    sni: 949d369d-swvls0-sx9b4u-1sd8z.cu.plebai.net
  - name: "\U0001F1ED\U0001F1F0香港57 | ⬇️ 704KB/s"
    network: ws
    port: 8880
    server: *************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /
  - client-fingerprint: firefox
    flow: xtls-rprx-vision
    name: "\U0001F1F0\U0001F1F7韩国25 | ⬇️ 6.0MB/s"
    network: tcp
    port: 33001
    reality-opts:
      public-key: rckWtXghGii1mFHTK0UzeXYjtUsqkiQSGz0eBScn1E4
    server: hg01.yuanbaojc.site
    tls: true
    type: vless
    udp: true
    uuid: 646d2022-4f69-4a6e-b142-d00e475b36e6
    servername: s0.awsstatic.com
  - name: "\U0001F1FA\U0001F1F8美国290 | ⬇️ 3.7MB/s"
    password: affe7124-c118-11ef-b6b2-f23c9164ca5d
    port: 15229
    server: f7c9656d-swvls0-td1w5f-1t3cz.cu.plebai.net
    type: trojan
    udp: true
    sni: f7c9656d-swvls0-td1w5f-1t3cz.cu.plebai.net
  - auth: 862e90e1-2f26-4506-b8b9-0adf20da05fc
    name: "\U0001F1E8\U0001F1E6加拿大12 | ⬇️ 556KB/s"
    password: 862e90e1-2f26-4506-b8b9-0adf20da05fc
    port: 54530
    server: **************
    skip-cert-verify: true
    sni: www.bing.com
    type: hysteria2
    udp: true
  - name: "\U0001F1F3\U0001F1F1荷兰7 | ⬇️ 5.3MB/s"
    network: ws
    port: 80
    server: *************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: '/Telegram: @vpnAndroid2/?ed=2560'
  - auth: 869a0163-456f-4c06-bd4a-2376e4563eae
    name: "\U0001F1EC\U0001F1E7英国8 | ⬇️ 6.1MB/s"
    password: 869a0163-456f-4c06-bd4a-2376e4563eae
    port: 30003
    server: qyyg.qy1357.top
    skip-cert-verify: true
    sni: qyyg.qy1357.top
    type: hysteria2
    udp: true
  - name: "\U0001F1EB\U0001F1F7法国6 | ⬇️ 11.8MB/s"
    network: ws
    port: 8080
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 56652249-2e67-4429-b86c-3df8b838ad85
    ws-opts:
      headers:
        Host: 4j.QBAo1g5z6k.ZulaIR.org.
      path: /
  - client-fingerprint: chrome
    flow: xtls-rprx-vision
    name: "\U0001F1E8\U0001F1E6加拿大13 | ⬇️ 2.0MB/s"
    network: tcp
    port: 26113
    reality-opts:
      public-key: GUcD8LK1DFNuVun5yhsqeEego_p3PF2yIdvE2KY2Nng
      short-id: 1fbd20fd
    server: *************
    tls: true
    type: vless
    udp: true
    uuid: cfe032e4-1bc3-454f-9d09-301b0aea08ff
    servername: www.yahoo.com
  - auth: 591ac730-bd6f-11ed-a8bf-f23c91cfbbc9
    name: "\U0001F1FA\U0001F1F8美国291 | ⬇️ 4.1MB/s"
    password: 591ac730-bd6f-11ed-a8bf-f23c91cfbbc9
    port: 8443
    server: 637fafba-swvls0-sy3zfb-ezjz.hy2.gotochinatown.net
    type: hysteria2
    udp: true
    sni: 637fafba-swvls0-sy3zfb-ezjz.hy2.gotochinatown.net
  - name: "\U0001F1E9\U0001F1EA德国34 | ⬇️ 7.3MB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: "/Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
  - auth: fbZYdngMs7US5yJHmFNEpqOtfUQ
    name: "\U0001F300其他39-AT | ⬇️ 1.1MB/s"
    password: fbZYdngMs7US5yJHmFNEpqOtfUQ
    port: 13852
    server: ************
    skip-cert-verify: true
    sni: bing.com
    type: hysteria2
    udp: true
  - alpn:
      - http%2F1.1
    client-fingerprint: chrome
    flow: xtls-rprx-vision
    name: "\U0001F1FA\U0001F1F8美国292 | ⬇️ 1.1MB/s"
    network: tcp
    port: 443
    reality-opts:
      public-key: qM1XlrSeyJsPBUvrGWi46okbeljt0ksMuMFPraXrmFg
      short-id: 4b61d108
    server: ***************
    tls: true
    type: vless
    udp: true
    uuid: fefba36d-5142-42f1-b14d-db5249511d93
    servername: iyfglobal.org
  - auth: 6c510073-4ca8-423b-87a5-a6d73c0ca557
    name: "\U0001F300其他40-IQ | ⬇️ 7.4MB/s"
    password: 6c510073-4ca8-423b-87a5-a6d73c0ca557
    port: 43999
    server: jiangzhiylk.54264944.xyz
    skip-cert-verify: true
    sni: jiangzhiylk.54264944.xyz
    type: hysteria2
    udp: true
  - client-fingerprint: chrome
    grpc-opts:
      grpc-mode: gun
      grpc-service-name: >-
        CANALTELEGRAM--jet_config--TELEGRAM--jet_config--CANALTELEGRAM--jet_config--TELEGRAM--jet_config--CANALTELEGRAM--jet_config
    name: "\U0001F1EC\U0001F1E7英国9 | ⬇️ 3.8MB/s"
    network: grpc
    port: 2030
    reality-opts:
      public-key: YWfCdTnr4FAOMYTY2dLrMtQUokyxOGpPhYEEszPj20E
      short-id: 7fe29733
    server: *************
    tls: true
    type: vless
    udp: true
    uuid: 2eed18db-ebf7-4ed0-9244-cf1679bc6dd6
    servername: refersion.com
  - auth: 6e9cdc20-b92a-11ef-973a-f23c913c8d2b
    name: "\U0001F1FA\U0001F1F8美国293 | ⬇️ 1.4MB/s"
    password: 6e9cdc20-b92a-11ef-973a-f23c913c8d2b
    port: 8443
    server: d518699a-swxgg0-sww7b0-1qwp5.hy2.gotochinatown.net
    type: hysteria2
    udp: true
    sni: d518699a-swxgg0-sww7b0-1qwp5.hy2.gotochinatown.net
  - cipher: aes-256-gcm
    name: "\U0001F1ED\U0001F1F0香港58 | ⬇️ 1018KB/s"
    password: PSGZOPQW8YFSZOCJ
    port: 15005
    server: 8tv68qhq.slashdevslashnetslashtun.net
    type: ss
    udp: true
  - name: "\U0001F1EE\U0001F1F3印度5 | ⬇️ 653KB/s"
    network: ws
    port: 8880
    server: **************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /Telegram@ShadowProxy66/?ed=2560
  - auth: 93fb69fc-77cf-11ee-85ee-f23c91369f2d
    name: "\U0001F1FA\U0001F1F8美国294 | ⬇️ 1.1MB/s"
    password: 93fb69fc-77cf-11ee-85ee-f23c91369f2d
    port: 8443
    server: 9d881534-swxgg0-t12cnj-1ol97.hy2.gotochinatown.net
    type: hysteria2
    udp: true
    sni: 9d881534-swxgg0-t12cnj-1ol97.hy2.gotochinatown.net
  - cipher: aes-256-gcm
    name: "\U0001F1FA\U0001F1F8美国295 | ⬇️ 883KB/s"
    password: BWT1EACZXKEY1V6K
    port: 17002
    server: *************
    type: ss
    udp: true
  - name: "\U0001F1FA\U0001F1F8美国296 | ⬇️ 4.0MB/s"
    password: 7ddc7156-57be-11ee-9acd-f23c9164ca5d
    port: 15229
    server: 29d1920a-swxgg0-tfmoda-25xl.cu.plebai.net
    type: trojan
    udp: true
    sni: 29d1920a-swxgg0-tfmoda-25xl.cu.plebai.net
  - cipher: aes-256-gcm
    name: "\U0001F1E8\U0001F1E6加拿大14 | ⬇️ 7.2MB/s"
    password: 5CUB6RJAELMTNHHA
    port: 20025
    server: **************
    type: ss
    udp: true
  - name: "\U0001F1FA\U0001F1F8美国297 | ⬇️ 3.2MB/s"
    password: 7d7c111e-fbc1-11ef-ab5f-f23c913c8d2b
    port: 15229
    server: 73f77226-swxgg0-sxhurg-1th8j.cu.plebai.net
    type: trojan
    udp: true
    sni: 73f77226-swxgg0-sxhurg-1th8j.cu.plebai.net
  - cipher: aes-256-gcm
    name: "\U0001F1F8\U0001F1EC新加坡19 | ⬇️ 947KB/s"
    password: QQT24A5FM1OAYMPS
    port: 16013
    server: ti3hyra4.slashdevslashnetslashtun.net
    type: ss
    udp: true
  - name: "\U0001F1EE\U0001F1F3印度6 | ⬇️ 4.9MB/s"
    network: ws
    port: 8880
    server: ***********
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: "/Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
  - cipher: aes-256-gcm
    name: "\U0001F1ED\U0001F1F0香港59 | ⬇️ 1.9MB/s"
    password: G5UYEUFON27A3ATP
    port: 15012
    server: 8tv68qhq.slashdevslashnetslashtun.net
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1E8\U0001F1E6加拿大15 | ⬇️ 2.2MB/s"
    password: HPT6QKRI0BIIPQOZ
    port: 20036
    server: **************
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1EF\U0001F1F5日本14 | ⬇️ 1.1MB/s"
    password: 22OSZOASC0IUC132
    port: 18004
    server: ti3hyra4.slashdevslashnetslashtun.net
    type: ss
    udp: true
  - name: "\U0001F1FA\U0001F1F8美国298 | ⬇️ 574KB/s"
    password: c1785de0-212f-11ef-92aa-f23c9164ca5d
    port: 15229
    server: 7b90a168-swxgg0-t6570q-1q3dm.cu.plebai.net
    type: trojan
    udp: true
    sni: 7b90a168-swxgg0-t6570q-1q3dm.cu.plebai.net
  - name: "\U0001F1F3\U0001F1F1荷兰8 | ⬇️ 861KB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: "/Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
  - cipher: aes-256-gcm
    name: "\U0001F1E8\U0001F1E6加拿大16 | ⬇️ 4.5MB/s"
    password: T7J7LMC1NN9PUI3J
    port: 20027
    server: **************
    type: ss
    udp: true
  - grpc-opts:
      grpc-mode: gun
      grpc-service-name: iran.bonyadedocker.ir
    name: "\U0001F1E9\U0001F1EA德国35 | ⬇️ 1.9MB/s"
    network: grpc
    port: 684
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: df8b1eef-ece7-4e28-b416-800b9ae88c45
  - name: "\U0001F1E9\U0001F1EA德国36 | ⬇️ 2.2MB/s"
    network: ws
    port: 8880
    server: **************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /Telegram@ShadowProxy66/?ed=2560
  - cipher: aes-256-gcm
    name: "\U0001F1ED\U0001F1F0香港60 | ⬇️ 757KB/s"
    password: Z78JQTL5YGPNOWFX
    port: 15009
    server: 8tv68qhq.slashdevslashnetslashtun.net
    type: ss
    udp: true
  - name: "\U0001F1FA\U0001F1F8美国299 | ⬇️ 1.1MB/s"
    password: 591ac730-bd6f-11ed-a8bf-f23c91cfbbc9
    port: 15229
    server: 94e89b13-swxgg0-sy3zfb-ezjz.cu.plebai.net
    type: trojan
    udp: true
    sni: 94e89b13-swxgg0-sy3zfb-ezjz.cu.plebai.net
  - name: "\U0001F1FA\U0001F1F8美国300 | ⬇️ 4.0MB/s"
    password: bb85e074-b0c2-11ea-ad28-f23c913c8d2b
    port: 15229
    server: 47bb9962-swxgg0-sx9mz9-m0b9.cu.plebai.net
    type: trojan
    udp: true
    sni: 47bb9962-swxgg0-sx9mz9-m0b9.cu.plebai.net
  - name: "\U0001F1FA\U0001F1F8美国301 | ⬇️ 4.4MB/s"
    password: 0a335fd6-be0b-11ec-8dfa-f23c91cfbbc9
    port: 15229
    server: 68f1a514-sx4v40-sxkd63-17z95.cu.plebai.net
    skip-cert-verify: false
    type: trojan
    udp: true
    sni: 68f1a514-sx4v40-sxkd63-17z95.cu.plebai.net
  - cipher: aes-256-gcm
    name: "\U0001F1ED\U0001F1F0香港61 | ⬇️ 661KB/s"
    password: GMB5RP19CE51DJ6J
    port: 15007
    server: ti3hyra4.slashdevslashnetslashtun.net
    type: ss
    udp: true
  - name: "\U0001F1E9\U0001F1EA德国37 | ⬇️ 550KB/s"
    network: ws
    port: 8880
    server: **************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /Telegram@ShadowProxy66/?ed=2560
  - cipher: aes-256-gcm
    name: "\U0001F1EF\U0001F1F5日本15 | ⬇️ 508KB/s"
    password: 7J1VD0VJGLP8IB57
    port: 18016
    server: ti3hyra4.slashdevslashnetslashtun.net
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F300其他41-PE | ⬇️ 947KB/s"
    password: AARDHP6T88RMA84P
    port: 20009
    server: *************
    type: ss
    udp: true
  - name: "\U0001F1FA\U0001F1F8美国302 | ⬇️ 2.2MB/s"
    network: ws
    port: 8880
    server: **********29
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: "/Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
  - cipher: aes-256-gcm
    name: "\U0001F1E8\U0001F1E6加拿大17 | ⬇️ 4.6MB/s"
    password: J0ETCCQH1OB3BMZ7
    port: 20034
    server: *************
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1E8\U0001F1E6加拿大18 | ⬇️ 6.3MB/s"
    password: MG279Q3C5KLF7HGE
    port: 20015
    server: **************
    type: ss
    udp: true
  - name: "\U0001F1E6\U0001F1EA阿拉伯酋长国8 | ⬇️ 808KB/s"
    network: ws
    port: 8880
    server: ***********
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: "/Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
  - cipher: aes-256-gcm
    name: "\U0001F1FA\U0001F1F8美国303 | ⬇️ 927KB/s"
    password: 5E56KR2KXN0ZG3QL
    port: 17007
    server: ti3hyra4.slashdevslashnetslashtun.net
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1E8\U0001F1E6加拿大19 | ⬇️ 1.7MB/s"
    password: Z343JAT1LWKCJVRQ
    port: 20010
    server: **************
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1E8\U0001F1E6加拿大20 | ⬇️ 2.9MB/s"
    password: TWOQKRHH53YN2YKF
    port: 20035
    server: **************
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1F3\U0001F1F1荷兰9 | ⬇️ 1.7MB/s"
    password: VQ8NM1XK319YM9J0
    port: 20000
    server: ***************
    type: ss
    udp: true
  - auth: 91365a7a-46d7-11ee-a8b9-f23c9164ca5d
    name: "\U0001F1FA\U0001F1F8美国304 | ⬇️ 5.7MB/s"
    password: 91365a7a-46d7-11ee-a8b9-f23c9164ca5d
    port: 8443
    server: 798b60f9-swxgg0-t3o6u7-1osdm.hy2.gotochinatown.net
    type: hysteria2
    udp: true
    sni: 798b60f9-swxgg0-t3o6u7-1osdm.hy2.gotochinatown.net
  - auth: b585dac6-dc69-11ef-9f1c-f23c913c8d2b
    name: "\U0001F1FA\U0001F1F8美国305 | ⬇️ 5.6MB/s"
    password: b585dac6-dc69-11ef-9f1c-f23c913c8d2b
    port: 8443
    server: 089afcf5-swxgg0-sy1j4r-1mqag.hy2.gotochinatown.net
    type: hysteria2
    udp: true
    sni: 089afcf5-swxgg0-sy1j4r-1mqag.hy2.gotochinatown.net
  - cipher: aes-256-gcm
    name: "\U0001F1E8\U0001F1E6加拿大21 | ⬇️ 506KB/s"
    password: TDBT3LH5U9JD65PK
    port: 20032
    server: ************
    type: ss
    udp: true
  - name: "\U0001F1FA\U0001F1F8美国306 | ⬇️ 1.2MB/s"
    network: ws
    port: 8880
    server: **************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /?ed=2560
  - cipher: aes-256-gcm
    name: "\U0001F1F8\U0001F1EC新加坡20 | ⬇️ 576KB/s"
    password: GAIJP655ZYFMN22R
    port: 16011
    server: ti3hyra4.slashdevslashnetslashtun.net
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1E8\U0001F1E6加拿大22 | ⬇️ 668KB/s"
    password: N12BY6B8U2YZ6WR3
    port: 20016
    server: ***************
    type: ss
    udp: true
  - alpn:
      - h3
      - h2
    client-fingerprint: chrome
    name: "\U0001F300其他42-MD | ⬇️ 8.4MB/s"
    network: ws
    port: 2096
    server: ************
    tls: true
    type: vless
    udp: true
    uuid: 96a1b724-68d2-4f4d-ab25-38ecd83577bc
    ws-opts:
      headers:
        Host: HiDyVnDe14.bInTeRmA.OrG
      path: /
    servername: HiDyVnDe14.bInTeRmA.OrG
  - cipher: aes-256-gcm
    name: "\U0001F1F8\U0001F1EC新加坡21 | ⬇️ 1.8MB/s"
    password: S13NYGOMXSL7F9NA
    port: 16004
    server: ti3hyra4.slashdevslashnetslashtun.net
    type: ss
    udp: true
  - name: "\U0001F1E6\U0001F1EA阿拉伯酋长国9 | ⬇️ 3.3MB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: "/Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
  - name: "\U0001F1F0\U0001F1F7韩国26 | ⬇️ 857KB/s"
    network: ws
    port: 8880
    server: ***********
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: "/Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
  - cipher: aes-256-gcm
    name: "\U0001F1EF\U0001F1F5日本16 | ⬇️ 1.1MB/s"
    password: 3SX9TMBC7QL6P28X
    port: 18012
    server: ti3hyra4.slashdevslashnetslashtun.net
    type: ss
    udp: true
  - name: "\U0001F1E9\U0001F1EA德国38 | ⬇️ 1.2MB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /Telegram@ShadowProxy66/?ed=2560
  - name: "\U0001F1FA\U0001F1F8美国307 | ⬇️ 1.1MB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: "/Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
  - name: "\U0001F1FA\U0001F1F8美国308 | ⬇️ 2.4MB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: "/Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
  - auth: b79d79ae-8bce-11ef-a2b8-f23c9164ca5d
    name: "\U0001F1FA\U0001F1F8美国309 | ⬇️ 3.2MB/s"
    password: b79d79ae-8bce-11ef-a2b8-f23c9164ca5d
    port: 8443
    server: 2d17f52b-swxgg0-t67sv3-1snfs.hy2.gotochinatown.net
    type: hysteria2
    udp: true
    sni: 2d17f52b-swxgg0-t67sv3-1snfs.hy2.gotochinatown.net
  - cipher: aes-256-gcm
    name: "\U0001F300其他43-TR | ⬇️ 2.0MB/s"
    password: ZWPD3479FWRUHQWC
    port: 20024
    server: ***************
    type: ss
    udp: true
  - name: "\U0001F1FA\U0001F1F8美国310 | ⬇️ 3.6MB/s"
    password: 06b2ac52-fcc6-11ec-bb74-f23c9164ca5d
    port: 15229
    server: ed9232ba-swxgg0-sznzxg-1jfvb.cu.plebai.net
    type: trojan
    udp: true
    sni: ed9232ba-swxgg0-sznzxg-1jfvb.cu.plebai.net
  - cipher: aes-256-gcm
    name: "\U0001F1EF\U0001F1F5日本17 | ⬇️ 736KB/s"
    password: PUXUB2FJ86BJ6VUH
    port: 18003
    server: ti3hyra4.slashdevslashnetslashtun.net
    type: ss
    udp: true
  - name: "\U0001F1FA\U0001F1F8美国311 | ⬇️ 1.1MB/s"
    network: ws
    port: 8880
    server: **************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /Telegram@ShadowProxy66/?ed=2560
  - cipher: aes-256-gcm
    name: "\U0001F1E8\U0001F1E6加拿大23 | ⬇️ 5.7MB/s"
    password: T9Q4G0RUMW4L9VEY
    port: 20007
    server: **************
    type: ss
    udp: true
  - alpn:
      - h3
    auth-str: dongtaiwang.com
    auth_str: dongtaiwang.com
    down: 55
    name: "\U0001F1EB\U0001F1F7法国7 | ⬇️ 5.8MB/s"
    port: 46938
    protocol: udp
    server: ***************
    skip-cert-verify: true
    sni: apple.com
    type: hysteria
    udp: true
    up: 11
  - auth: 8e0f8ef2-5fe3-11ec-a8bf-f23c91cfbbc9
    name: "\U0001F1FA\U0001F1F8美国312 | ⬇️ 1.1MB/s"
    password: 8e0f8ef2-5fe3-11ec-a8bf-f23c91cfbbc9
    port: 8443
    server: 4496cf9b-swxgg0-t14dgl-duku.hy2.gotochinatown.net
    type: hysteria2
    udp: true
    sni: 4496cf9b-swxgg0-t14dgl-duku.hy2.gotochinatown.net
  - cipher: aes-256-gcm
    name: "\U0001F1E8\U0001F1E6加拿大24 | ⬇️ 3.8MB/s"
    password: USAAWWY4IGI7M6O6
    port: 20026
    server: *************
    type: ss
    udp: true
  - name: "\U0001F1FA\U0001F1F8美国313 | ⬇️ 3.4MB/s"
    password: 3d7182ce-8dcc-11ef-a3f6-f23c9164ca5d
    port: 15229
    server: 7670ad60-swxgg0-thdi1k-19yro.cu.plebai.net
    type: trojan
    udp: true
    sni: 7670ad60-swxgg0-thdi1k-19yro.cu.plebai.net
  - name: "\U0001F1FA\U0001F1F8美国314 | ⬇️ 2.8MB/s"
    password: bcc58e88-e147-11ec-b286-f23c91cfbbc9
    port: 15229
    server: 792c2394-swxgg0-sxu45k-ggww.cu.plebai.net
    type: trojan
    udp: true
    sni: 792c2394-swxgg0-sxu45k-ggww.cu.plebai.net
  - name: "\U0001F1FA\U0001F1F8美国315 | ⬇️ 4.0MB/s"
    password: a5a81a34-f257-11ef-ba82-f23c913c8d2b
    port: 15229
    server: e3bd1706-swxgg0-syc5sd-1teuc.cu.plebai.net
    type: trojan
    udp: true
    sni: e3bd1706-swxgg0-syc5sd-1teuc.cu.plebai.net
  - name: "\U0001F1E9\U0001F1EA德国39 | ⬇️ 5.0MB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /?ed=2560
  - cipher: aes-256-gcm
    name: "\U0001F1E8\U0001F1E6加拿大25 | ⬇️ 4.2MB/s"
    password: 89T5NOHW9RN3DSG1
    port: 20006
    server: *************
    type: ss
    udp: true
  - name: "\U0001F1FA\U0001F1F8美国316 | ⬇️ 651KB/s"
    password: 31676725-b0fd-fed2-c857-2da8ce5a6e4f
    port: 15229
    server: 5bc21cdb-swxgg0-t0dyyh-jjv2.cu.plebai.net
    type: trojan
    udp: true
    sni: 5bc21cdb-swxgg0-t0dyyh-jjv2.cu.plebai.net
  - name: "\U0001F1FA\U0001F1F8美国317 | ⬇️ 912KB/s"
    network: ws
    port: 8880
    server: **********46
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /TelegramU0001F1E8U0001F1F3 @MxlShare @WangCai2 /?ed=2560
    servername: reedfree8mahsang2.redorg.ir
  - cipher: aes-256-gcm
    name: "\U0001F1E6\U0001F1EA阿拉伯酋长国10 | ⬇️ 522KB/s"
    password: OU9S9DU30HJKZFHU
    port: 20017
    server: 8tv68qhq.slashdevslashnetslashtun.net
    type: ss
    udp: true
  - name: "\U0001F1F0\U0001F1F7韩国27 | ⬇️ 803KB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /TelegramU0001F1E8U0001F1F3 @MxlShare @WangCai2 /?ed=2560
    servername: reedfree8mahsang2.redorg.ir
  - name: "\U0001F1FA\U0001F1F8美国318 | ⬇️ 1.4MB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /TelegramU0001F1E8U0001F1F3 @MxlShare @WangCai2 /?ed=2560
    servername: reedfree8mahsang2.redorg.ir
  - name: "\U0001F1FA\U0001F1F8美国319 | ⬇️ 681KB/s"
    password: d4018e28-e328-11ed-98a7-f23c913c8d2b
    port: 15229
    server: 4d0c3c41-swxgg0-sx3h07-1g8k0.cu.plebai.net
    type: trojan
    udp: true
    sni: 4d0c3c41-swxgg0-sx3h07-1g8k0.cu.plebai.net
  - name: "\U0001F1FA\U0001F1F8美国320 | ⬇️ 4.0MB/s"
    password: 861726bb-c61a-b0e0-99fa-5ddfdcf21902
    port: 15229
    server: 12f1476f-swxgg0-ta5nd4-e06r.cu.plebai.net
    type: trojan
    udp: true
    sni: 12f1476f-swxgg0-ta5nd4-e06r.cu.plebai.net
  - auth: 127e3f92-f714-11ef-bbb0-f23c91cfbbc9
    name: "\U0001F1FA\U0001F1F8美国321 | ⬇️ 3.0MB/s"
    password: 127e3f92-f714-11ef-bbb0-f23c91cfbbc9
    port: 8443
    server: eccca7a6-swxgg0-t2c3sq-1spnr.hy2.gotochinatown.net
    type: hysteria2
    udp: true
    sni: eccca7a6-swxgg0-t2c3sq-1spnr.hy2.gotochinatown.net
  - auth: bcc58e88-e147-11ec-b286-f23c91cfbbc9
    name: "\U0001F1FA\U0001F1F8美国322 | ⬇️ 3.6MB/s"
    password: bcc58e88-e147-11ec-b286-f23c91cfbbc9
    port: 8443
    server: 6d722f2c-swxgg0-sxu45k-ggww.hy2.gotochinatown.net
    type: hysteria2
    udp: true
    sni: 6d722f2c-swxgg0-sxu45k-ggww.hy2.gotochinatown.net
  - name: "\U0001F1EB\U0001F1EE芬兰11 | ⬇️ 827KB/s"
    network: ws
    port: 8880
    server: ***********
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: "/Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
    servername: reedfree8mahsang2.redorg.ir
  - name: "\U0001F1EB\U0001F1F7法国8 | ⬇️ 4.5MB/s"
    network: ws
    port: 8080
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 4ea841c1-0dc1-4563-9f47-deba8407cb4e
    ws-opts:
      headers:
        Host: J9.oDOtZrHUoO.ZuLAIR.ORg.
      path: /?ed=2048
  - name: "\U0001F1E9\U0001F1EA德国40 | ⬇️ 2.2MB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /TelegramU0001F1E8U0001F1F3 @MxlShare @WangCai2 /?ed=2560
    servername: reedfree8mahsang2.redorg.ir
  - name: "\U0001F1EB\U0001F1EE芬兰12 | ⬇️ 1.8MB/s"
    network: ws
    port: 8880
    server: ***********
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /TelegramU0001F1E8U0001F1F3 @MxlShare @WangCai2 /?ed=2560
    servername: reedfree8mahsang2.redorg.ir
  - name: "\U0001F1F3\U0001F1F1荷兰10 | ⬇️ 1.3MB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /TelegramU0001F1E8U0001F1F3 @pgkj666 /?ed=2560
    servername: reedfree8mahsang2.redorg.ir
  - name: "\U0001F1EE\U0001F1F3印度7 | ⬇️ 1.7MB/s"
    network: ws
    port: 8880
    server: ***********
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /TelegramU0001F1E8U0001F1F3 @MxlShare @WangCai2 /?ed=2560
    servername: reedfree8mahsang2.redorg.ir
  - name: "\U0001F1FA\U0001F1F8美国323 | ⬇️ 5.0MB/s"
    network: ws
    port: 8880
    server: ***********
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /TelegramU0001F1E8U0001F1F3 @MxlShare @WangCai2 /?ed=2560
    servername: reedfree8mahsang2.redorg.ir
  - name: "\U0001F1FA\U0001F1F8美国324 | ⬇️ 7.9MB/s"
    network: ws
    port: 8880
    server: **********
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /TelegramU0001F1E8U0001F1F3 @MxlShare @WangCai2 /?ed=2560
    servername: reedfree8mahsang2.redorg.ir
  - auth: 861726bb-c61a-b0e0-99fa-5ddfdcf21902
    name: "\U0001F1FA\U0001F1F8美国325 | ⬇️ 2.5MB/s"
    password: 861726bb-c61a-b0e0-99fa-5ddfdcf21902
    port: 8443
    server: 62148413-swxgg0-ta5nd4-e06r.hy2.gotochinatown.net
    type: hysteria2
    udp: true
    sni: 62148413-swxgg0-ta5nd4-e06r.hy2.gotochinatown.net
  - auth: cfe61764-0004-11f0-a910-f23c9164ca5d
    name: "\U0001F1FA\U0001F1F8美国326 | ⬇️ 1.1MB/s"
    password: cfe61764-0004-11f0-a910-f23c9164ca5d
    port: 8443
    server: 3ff6b8af-swxgg0-sx9j1n-1timk.hy2.gotochinatown.net
    type: hysteria2
    udp: true
    sni: 3ff6b8af-swxgg0-sx9j1n-1timk.hy2.gotochinatown.net
  - name: "\U0001F1FA\U0001F1F8美国327 | ⬇️ 870KB/s"
    network: ws
    port: 8880
    server: ***********7
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: "/Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
  - name: "\U0001F1FA\U0001F1F8美国328 | ⬇️ 2.2MB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /TelegramU0001F1E8U0001F1F3 @MxlShare @WangCai2 /?ed=2560
    servername: reedfree8mahsang2.redorg.ir
  - name: "\U0001F1FA\U0001F1F8美国329 | ⬇️ 704KB/s"
    network: ws
    port: 8880
    server: ***********
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /TelegramU0001F1E8U0001F1F3 @MxlShare @WangCai2 /?ed=2560
    servername: reedfree8mahsang2.redorg.ir
  - name: "\U0001F1FA\U0001F1F8美国330 | ⬇️ 5.0MB/s"
    password: dcccacba-fa44-11ef-8400-f23c9164ca5d
    port: 15229
    server: 7a467131-swxgg0-sx0fe4-1j6h0.cu.plebai.net
    type: trojan
    udp: true
    sni: 7a467131-swxgg0-sx0fe4-1j6h0.cu.plebai.net
  - name: "\U0001F1EE\U0001F1F3印度8 | ⬇️ 961KB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /TelegramU0001F1E8U0001F1F3 @MxlShare @WangCai2 /?ed=2560
    servername: reedfree8mahsang2.redorg.ir
  - name: "\U0001F1FA\U0001F1F8美国331 | ⬇️ 1.7MB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /TelegramU0001F1E8U0001F1F3 @MxlShare @WangCai2 /?ed=2560
    servername: reedfree8mahsang2.redorg.ir
  - auth: 384d1b42-655f-11ed-a8bf-f23c91cfbbc9
    name: "\U0001F1FA\U0001F1F8美国332 | ⬇️ 1.6MB/s"
    password: 384d1b42-655f-11ed-a8bf-f23c91cfbbc9
    port: 8443
    server: 1e2fa6c6-swxgg0-sxuzn1-11p9g.hy2.gotochinatown.net
    type: hysteria2
    udp: true
    sni: 1e2fa6c6-swxgg0-sxuzn1-11p9g.hy2.gotochinatown.net
  - auth: 34f0cf42-0f8a-11ec-a8bf-f23c91cfbbc9
    name: "\U0001F1FA\U0001F1F8美国333 | ⬇️ 3.7MB/s"
    password: 34f0cf42-0f8a-11ec-a8bf-f23c91cfbbc9
    port: 8443
    server: 99ea4f21-swxgg0-szdere-155d9.hy2.gotochinatown.net
    type: hysteria2
    udp: true
    sni: 99ea4f21-swxgg0-szdere-155d9.hy2.gotochinatown.net
  - name: "\U0001F1ED\U0001F1F0香港62 | ⬇️ 502KB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /TelegramU0001F1E8U0001F1F3 @MxlShare @WangCai2 /?ed=2560
    servername: reedfree8mahsang2.redorg.ir
  - name: "\U0001F1E9\U0001F1EA德国41 | ⬇️ 660KB/s"
    network: ws
    port: 8880
    server: ***********
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /TelegramU0001F1E8U0001F1F3 @MxlShare @WangCai2 /?ed=2560
    servername: reedfree8mahsang2.redorg.ir
  - auth: 47e91fb6-25b8-df52-bc18-3cdc8548192f
    name: "\U0001F1FA\U0001F1F8美国334 | ⬇️ 1.0MB/s"
    password: 47e91fb6-25b8-df52-bc18-3cdc8548192f
    port: 8443
    server: c536160e-swxgg0-sydzqp-81nv.hy2.gotochinatown.net
    type: hysteria2
    udp: true
    sni: c536160e-swxgg0-sydzqp-81nv.hy2.gotochinatown.net
  - auth: 48c1e014-28d6-11ec-a0fc-f23c913c8d2b
    name: "\U0001F1FA\U0001F1F8美国335 | ⬇️ 1.5MB/s"
    password: 48c1e014-28d6-11ec-a0fc-f23c913c8d2b
    port: 8443
    server: 7a48defb-swxgg0-sxlsd4-3z3v.hy2.gotochinatown.net
    type: hysteria2
    udp: true
    sni: 7a48defb-swxgg0-sxlsd4-3z3v.hy2.gotochinatown.net
  - name: "\U0001F1ED\U0001F1F0香港63 | ⬇️ 1.2MB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /TelegramU0001F1E8U0001F1F3 @MxlShare @WangCai2 /?ed=2560
    servername: reedfree8mahsang2.redorg.ir
  - auth: 088a0bbe-4f9f-11ea-a15d-f23c913c8d2b
    name: "\U0001F1FA\U0001F1F8美国336 | ⬇️ 1.3MB/s"
    password: 088a0bbe-4f9f-11ea-a15d-f23c913c8d2b
    port: 8443
    server: eba507f2-swxgg0-t5vrf3-716s.hy2.gotochinatown.net
    type: hysteria2
    udp: true
    sni: eba507f2-swxgg0-t5vrf3-716s.hy2.gotochinatown.net
  - auth: ba4672b3-e0af-4699-a180-f711cfaa95ed
    name: "\U0001F300其他44-未识别 | ⬇️ 6.7MB/s"
    password: ba4672b3-e0af-4699-a180-f711cfaa95ed
    port: 43999
    server: jiangzhifg.54264944.xyz
    skip-cert-verify: true
    sni: jiangzhifr.54264944.xyz
    type: hysteria2
    udp: true
  - name: "\U0001F1E9\U0001F1EA德国42 | ⬇️ 783KB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /TelegramU0001F1E8U0001F1F3 @MxlShare @WangCai2 /?ed=2560
    servername: reedfree8mahsang2.redorg.ir
  - name: "\U0001F1EE\U0001F1F3印度9 | ⬇️ 7.5MB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /TelegramU0001F1E8U0001F1F3 @MxlShare @WangCai2 /?ed=2560
    servername: reedfree8mahsang2.redorg.ir
  - auth: 31676725-b0fd-fed2-c857-2da8ce5a6e4f
    name: "\U0001F1FA\U0001F1F8美国337 | ⬇️ 5.6MB/s"
    password: 31676725-b0fd-fed2-c857-2da8ce5a6e4f
    port: 8443
    server: 7f1f2efa-swxgg0-t0dyyh-jjv2.hy2.gotochinatown.net
    type: hysteria2
    udp: true
    sni: 7f1f2efa-swxgg0-t0dyyh-jjv2.hy2.gotochinatown.net
  - name: "\U0001F1ED\U0001F1F0香港64 | ⬇️ 3.8MB/s"
    network: ws
    port: 8880
    server: ***********
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /TelegramU0001F1E8U0001F1F3 @MxlShare @WangCai2 /?ed=2560
    servername: reedfree8mahsang2.redorg.ir
  - auth: 3fa7157d-efe8-832e-9b3c-582d78efbc52
    name: "\U0001F1FA\U0001F1F8美国338 | ⬇️ 5.5MB/s"
    password: 3fa7157d-efe8-832e-9b3c-582d78efbc52
    port: 8443
    server: 11590083-swxgg0-tf70jh-vm13.hy2.gotochinatown.net
    type: hysteria2
    udp: true
    sni: 11590083-swxgg0-tf70jh-vm13.hy2.gotochinatown.net
  - name: "\U0001F1FA\U0001F1F8美国339 | ⬇️ 2.1MB/s"
    network: ws
    port: 8880
    server: ***********
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /TelegramU0001F1E8U0001F1F3 @MxlShare @WangCai2 /?ed=2560
    servername: reedfree8mahsang2.redorg.ir
  - auth: 06b2ac52-fcc6-11ec-bb74-f23c9164ca5d
    name: "\U0001F1FA\U0001F1F8美国340 | ⬇️ 4.9MB/s"
    password: 06b2ac52-fcc6-11ec-bb74-f23c9164ca5d
    port: 8443
    server: 2c055931-swxgg0-sznzxg-1jfvb.hy2.gotochinatown.net
    type: hysteria2
    udp: true
    sni: 2c055931-swxgg0-sznzxg-1jfvb.hy2.gotochinatown.net
  - name: "\U0001F1FA\U0001F1F8美国341 | ⬇️ 983KB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /TelegramU0001F1E8U0001F1F3 @MxlShare @WangCai2 /?ed=2560
    servername: reedfree8mahsang2.redorg.ir
  - name: "\U0001F1F3\U0001F1F1荷兰11 | ⬇️ 3.1MB/s"
    network: ws
    port: 8880
    server: ***********
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /TelegramU0001F1E8U0001F1F3 @MxlShare @WangCai2 /?ed=2560
    servername: reedfree8mahsang2.redorg.ir
  - cipher: aes-256-gcm
    name: "\U0001F1ED\U0001F1F0香港65 | ⬇️ 1.8MB/s"
    password: 4KGHWKCKQAJOVPHO
    port: 15003
    server: 8tv68qhq.slashdevslashnetslashtun.net
    type: ss
    udp: true
  - name: "\U0001F1ED\U0001F1F0香港66 | ⬇️ 942KB/s"
    network: ws
    port: 8880
    server: ***********
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /TelegramU0001F1E8U0001F1F3 @MxlShare @WangCai2 /?ed=2560
    servername: reedfree8mahsang2.redorg.ir
  - name: "\U0001F1E9\U0001F1EA德国43 | ⬇️ 2.2MB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: "/Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
  - name: "\U0001F1FA\U0001F1F8美国342 | ⬇️ 12.4MB/s"
    network: ws
    port: 80
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: '/Telegram:@vpnAndroid2/?ed=2560'
    servername: reedfree8mahsang2.redorg.ir
  - auth: ba4672b3-e0af-4699-a180-f711cfaa95ed
    name: "\U0001F1E9\U0001F1EA德国44 | ⬇️ 6.5MB/s"
    password: ba4672b3-e0af-4699-a180-f711cfaa95ed
    port: 43999
    server: jiangzhidg.54264944.xyz
    skip-cert-verify: true
    sni: jiangzhidg.54264944.xyz
    type: hysteria2
    udp: true
  - auth: 591ac730-bd6f-11ed-a8bf-f23c91cfbbc9
    name: "\U0001F1FA\U0001F1F8美国343 | ⬇️ 5.9MB/s"
    password: 591ac730-bd6f-11ed-a8bf-f23c91cfbbc9
    port: 8443
    server: 76aa25f7-swxgg0-sy3zfb-ezjz.hy2.gotochinatown.net
    type: hysteria2
    udp: true
    sni: 76aa25f7-swxgg0-sy3zfb-ezjz.hy2.gotochinatown.net
  - name: "\U0001F1E6\U0001F1EA阿拉伯酋长国11 | ⬇️ 571KB/s"
    network: ws
    port: 8880
    server: ***********
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /TelegramU0001F1E8U0001F1F3 @MxlShare @WangCai2 /?ed=2560
    servername: reedfree8mahsang2.redorg.ir
  - name: "\U0001F1E6\U0001F1EA阿拉伯酋长国12 | ⬇️ 1.1MB/s"
    network: ws
    port: 8880
    server: ***********
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: "/Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
  - name: "\U0001F1FA\U0001F1F8美国344 | ⬇️ 1.1MB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /TelegramU0001F1E8U0001F1F3 @MxlShare @WangCai2 /?ed=2560
    servername: reedfree8mahsang2.redorg.ir
  - name: "\U0001F1F0\U0001F1F7韩国28 | ⬇️ 713KB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /TelegramU0001F1E8U0001F1F3 @MxlShare @WangCai2 /?ed=2560
    servername: reedfree8mahsang2.redorg.ir
  - name: "\U0001F1E9\U0001F1EA德国45 | ⬇️ 7.6MB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /TelegramU0001F1E8U0001F1F3 @MxlShare @WangCai2 /?ed=2560
    servername: reedfree8mahsang2.redorg.ir
  - name: "\U0001F1FA\U0001F1F8美国345 | ⬇️ 763KB/s"
    network: ws
    port: 8880
    server: **********
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /TelegramU0001F1E8U0001F1F3 @MxlShare @WangCai2 /?ed=2560
    servername: reedfree8mahsang2.redorg.ir
  - name: "\U0001F1FA\U0001F1F8美国346 | ⬇️ 1.4MB/s"
    network: ws
    port: 8880
    server: ***********
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /TelegramU0001F1E8U0001F1F3 @MxlShare @WangCai2 /?ed=2560
    servername: reedfree8mahsang2.redorg.ir
  - auth: e3975a05-b89e-11ec-a50d-f23c91cfbbc9
    name: "\U0001F1FA\U0001F1F8美国347 | ⬇️ 3.0MB/s"
    password: e3975a05-b89e-11ec-a50d-f23c91cfbbc9
    port: 8443
    server: 77ccec48-swxgg0-sxf56z-1f596.hy2.gotochinatown.net
    type: hysteria2
    udp: true
    sni: 77ccec48-swxgg0-sxf56z-1f596.hy2.gotochinatown.net
  - auth: 0da8651e-e1f6-11ec-bd7c-f23c913c8d2b
    name: "\U0001F1FA\U0001F1F8美国348 | ⬇️ 1.6MB/s"
    password: 0da8651e-e1f6-11ec-bd7c-f23c913c8d2b
    port: 8443
    server: b864a754-swxgg0-tcinla-hrtf.hy2.gotochinatown.net
    type: hysteria2
    udp: true
    sni: b864a754-swxgg0-tcinla-hrtf.hy2.gotochinatown.net
  - auth: 94d40708-8273-11ea-8fc9-f23c913c8d2b
    name: "\U0001F1FA\U0001F1F8美国349 | ⬇️ 775KB/s"
    password: 94d40708-8273-11ea-8fc9-f23c913c8d2b
    port: 8443
    server: 92addeea-swxgg0-sye5o0-mv7m.hy2.gotochinatown.net
    type: hysteria2
    udp: true
    sni: 92addeea-swxgg0-sye5o0-mv7m.hy2.gotochinatown.net
  - name: "\U0001F1ED\U0001F1F0香港67 | ⬇️ 2.9MB/s"
    network: ws
    port: 8880
    server: ***********
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /TelegramU0001F1E8U0001F1F3 @MxlShare @WangCai2 /?ed=2560
    servername: reedfree8mahsang2.redorg.ir
  - name: "\U0001F1F0\U0001F1F7韩国29 | ⬇️ 742KB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /TelegramU0001F1E8U0001F1F3 @MxlShare @WangCai2 /?ed=2560
    servername: reedfree8mahsang2.redorg.ir
  - name: "\U0001F1FA\U0001F1F8美国350 | ⬇️ 1.1MB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /TelegramU0001F1E8U0001F1F3 @MxlShare @WangCai2 /?ed=2560
    servername: reedfree8mahsang2.redorg.ir
  - name: "\U0001F1F0\U0001F1F7韩国30 | ⬇️ 3.6MB/s"
    network: ws
    port: 8880
    server: **********
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /TelegramU0001F1E8U0001F1F3 @MxlShare @WangCai2 /?ed=2560
    servername: reedfree8mahsang2.redorg.ir
  - name: "\U0001F1ED\U0001F1F0香港68 | ⬇️ 729KB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /TelegramU0001F1E8U0001F1F3 @MxlShare @WangCai2 /?ed=2560
    servername: reedfree8mahsang2.redorg.ir
  - name: "\U0001F1FA\U0001F1F8美国351 | ⬇️ 3.3MB/s"
    network: ws
    port: 8880
    server: ***********
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /TelegramU0001F1E8U0001F1F3 @MxlShare @WangCai2 /?ed=2560
    servername: reedfree8mahsang2.redorg.ir
  - name: "\U0001F1FA\U0001F1F8美国352 | ⬇️ 1.5MB/s"
    network: ws
    port: 8880
    server: ***********
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /TelegramU0001F1E8U0001F1F3 @MxlShare @WangCai2 /?ed=2560
    servername: reedfree8mahsang2.redorg.ir
  - name: "\U0001F1ED\U0001F1F0香港69 | ⬇️ 883KB/s"
    network: ws
    port: 8880
    server: ***********
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /TelegramU0001F1E8U0001F1F3 @MxlShare @WangCai2 /?ed=2560
    servername: reedfree8mahsang2.redorg.ir
  - name: "\U0001F1FA\U0001F1F8美国353 | ⬇️ 627KB/s"
    network: ws
    port: 8880
    server: ************
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
        User-Agent: >-
          Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML,
          like Gecko) Chrome/63.0.3239.84 Safari/537.36
      path: /TelegramU0001F1E8U0001F1F3 @MxlShare @WangCai2 /?ed=2560
    xudp: true
    servername: reedfree8mahsang2.redorg.ir
  - name: "\U0001F1FA\U0001F1F8美国354 | ⬇️ 2.2MB/s"
    network: ws
    port: 8880
    server: ************
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
        User-Agent: >-
          Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML,
          like Gecko) Chrome/65.0.3325.162 Safari/537.36
      path: /?ed=2560
    xudp: true
    servername: reedfree8mahsang2.redorg.ir
  - name: "\U0001F1E6\U0001F1EA阿拉伯酋长国13 | ⬇️ 1.4MB/s"
    network: ws
    port: 80
    server: *************
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
        User-Agent: >-
          Mozilla/5.0 (X11; Linux i686) AppleWebKit/534.30 (KHTML, like Gecko)
          Chrome/12.0.742.100 Safari/534.30
      path: '/Telegram:@vpnAndroid2/?ed=2560'
    xudp: true
    servername: reedfree8mahsang2.redorg.ir
  - name: "\U0001F1E6\U0001F1EA阿拉伯酋长国14 | ⬇️ 4.1MB/s"
    network: ws
    port: 80
    server: **************
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
        User-Agent: >-
          Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML,
          like Gecko) Chrome/59.0.3071.115 Safari/537.36
      path: '/Telegram:@vpnAndroid2/?ed=2560'
    xudp: true
    servername: reedfree8mahsang2.redorg.ir
  - name: "\U0001F1E9\U0001F1EA德国46 | ⬇️ 2.6MB/s"
    network: ws
    port: 80
    server: ***********
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
        User-Agent: >-
          Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like
          Gecko) Chrome/68.0.3440.1805 Safari/537.36 MVisionPlayer/*******
      path: '/Telegram:@vpnAndroid2/?ed=2560'
    xudp: true
    servername: reedfree8mahsang2.redorg.ir
  - name: "\U0001F1E9\U0001F1EA德国47 | ⬇️ 4.1MB/s"
    network: ws
    port: 80
    server: ***********
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
        User-Agent: >-
          Mozilla/5.0 (Linux; Android 6.0; CAM-L03 Build/HUAWEICAM-L03)
          AppleWebKit/537.36 (KHTML, like Gecko) Chrome/69.0.3497.100 Mobile
          Safari/537.36
      path: '/Telegram:@vpnAndroid2/?ed=2560'
    xudp: true
  - name: "\U0001F1E9\U0001F1EA德国48 | ⬇️ 4.2MB/s"
    network: ws
    port: 80
    server: ************
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
        User-Agent: >-
          Mozilla/5.0 (Windows NT 6.1) AppleWebKit/537.36 (KHTML, like Gecko)
          Chrome/70.0.3538.67 Safari/537.36
      path: '/Telegram:@vpnAndroid2/?ed=2560'
    xudp: true
port: 7890
socks-port: 7891
redir-port: 7892
mixed-port: 7893
tproxy-port: 7894
ipv6: false
allow-lan: true
unified-delay: true
tcp-concurrent: true
geodata-mode: false
geodata-loader: standard
geo-auto-update: true
geo-update-interval: 48
geox-url:
  geoip: 'https://testingcf.jsdelivr.net/gh/MetaCubeX/meta-rules-dat@release/geoip.dat'
  geosite: >-
    https://testingcf.jsdelivr.net/gh/MetaCubeX/meta-rules-dat@release/geosite.dat
  mmdb: >-
    https://testingcf.jsdelivr.net/gh/MetaCubeX/meta-rules-dat@release/country.mmdb
  asn: >-
    https://github.com/xishang0128/geoip/releases/download/latest/GeoLite2-ASN.mmdb
profile:
  store-selected: true
  store-fake-ip: true
sniffer:
  enable: true
  sniff:
    HTTP:
      ports:
        - 80
        - 8080-8880
      override-destination: true
    TLS:
      ports:
        - 443
        - 8443
    QUIC:
      ports:
        - 443
        - 8443
  force-domain:
    - +.v2ex.com
  skip-domain:
    - Mijia Cloud
    - dlg.io.mi.com
    - +.push.apple.com
    - +.apple.com
dns:
  enable: true
  listen: '0.0.0.0:1053'
  ipv6: false
  respect-rules: true
  enhanced-mode: fake-ip
  fake-ip-range: ********/8
  fake-ip-filter-mode: blacklist
  fake-ip-filter:
    - +.lan
    - +.local
    - 'geosite:private'
    - 'geosite:cn'
  default-nameserver:
    - *********
    - ************
  proxy-server-nameserver:
    - *********
    - ************
  nameserver:
    - *********
    - ************
  nameserver-policy:
    'rule-set:private_domain,cn_domain':
      - *********
      - ************
    'rule-set:geolocation-!cn':
      - 'https://dns.cloudflare.com/dns-query'
      - 'https://dns.google/dns-query'
pr:
  type: select
  proxies:
    - "\U0001F680 节点选择"
    - "\U0001F1ED\U0001F1F0 香港负载均衡"
    - "\U0001F1EF\U0001F1F5 日本负载均衡"
    - "\U0001F1F0\U0001F1F7 韩国负载均衡"
    - "\U0001F1F8\U0001F1EC 新加坡负载均衡"
    - "\U0001F1FA\U0001F1F8 美国负载均衡"
    - "\U0001F1ED\U0001F1F0 香港自动"
    - "\U0001F1EF\U0001F1F5 日本自动"
    - "\U0001F1F0\U0001F1F7 韩国自动"
    - "\U0001F1F8\U0001F1EC 新加坡自动"
    - "\U0001F1FA\U0001F1F8 美国自动"
    - ♻️ 自动选择
    - "\U0001F1ED\U0001F1F0 香港节点"
    - "\U0001F1EF\U0001F1F5 日本节点"
    - "\U0001F1F0\U0001F1F7 韩国节点"
    - "\U0001F1F8\U0001F1EC 新加坡节点"
    - "\U0001F1FA\U0001F1F8 美国节点"
    - "\U0001F310 全部节点"
proxy-groups:
  - name: "\U0001F310 全部节点"
    type: select
    include-all: true
  - name: "\U0001F680 节点选择"
    type: select
    proxies:
      - "\U0001F1ED\U0001F1F0 香港负载均衡"
      - "\U0001F1EF\U0001F1F5 日本负载均衡"
      - "\U0001F1F0\U0001F1F7 韩国负载均衡"
      - "\U0001F1F8\U0001F1EC 新加坡负载均衡"
      - "\U0001F1FA\U0001F1F8 美国负载均衡"
      - "\U0001F1ED\U0001F1F0 香港自动"
      - "\U0001F1EF\U0001F1F5 日本自动"
      - "\U0001F1F0\U0001F1F7 韩国自动"
      - "\U0001F1F8\U0001F1EC 新加坡自动"
      - "\U0001F1FA\U0001F1F8 美国自动"
      - ♻️ 自动选择
      - "\U0001F1ED\U0001F1F0 香港节点"
      - "\U0001F1EF\U0001F1F5 日本节点"
      - "\U0001F1F0\U0001F1F7 韩国节点"
      - "\U0001F1F8\U0001F1EC 新加坡节点"
      - "\U0001F1FA\U0001F1F8 美国节点"
      - "\U0001F310 全部节点"
  - name: "\U0001F4AC ChatGPT"
    type: select
    proxies:
      - "\U0001F680 节点选择"
      - "\U0001F1ED\U0001F1F0 香港负载均衡"
      - "\U0001F1EF\U0001F1F5 日本负载均衡"
      - "\U0001F1F0\U0001F1F7 韩国负载均衡"
      - "\U0001F1F8\U0001F1EC 新加坡负载均衡"
      - "\U0001F1FA\U0001F1F8 美国负载均衡"
      - "\U0001F1ED\U0001F1F0 香港自动"
      - "\U0001F1EF\U0001F1F5 日本自动"
      - "\U0001F1F0\U0001F1F7 韩国自动"
      - "\U0001F1F8\U0001F1EC 新加坡自动"
      - "\U0001F1FA\U0001F1F8 美国自动"
      - ♻️ 自动选择
      - "\U0001F1ED\U0001F1F0 香港节点"
      - "\U0001F1EF\U0001F1F5 日本节点"
      - "\U0001F1F0\U0001F1F7 韩国节点"
      - "\U0001F1F8\U0001F1EC 新加坡节点"
      - "\U0001F1FA\U0001F1F8 美国节点"
      - "\U0001F310 全部节点"
  - name: "\U0001F4FA YouTube"
    type: select
    proxies:
      - "\U0001F680 节点选择"
      - "\U0001F1ED\U0001F1F0 香港负载均衡"
      - "\U0001F1EF\U0001F1F5 日本负载均衡"
      - "\U0001F1F0\U0001F1F7 韩国负载均衡"
      - "\U0001F1F8\U0001F1EC 新加坡负载均衡"
      - "\U0001F1FA\U0001F1F8 美国负载均衡"
      - "\U0001F1ED\U0001F1F0 香港自动"
      - "\U0001F1EF\U0001F1F5 日本自动"
      - "\U0001F1F0\U0001F1F7 韩国自动"
      - "\U0001F1F8\U0001F1EC 新加坡自动"
      - "\U0001F1FA\U0001F1F8 美国自动"
      - ♻️ 自动选择
      - "\U0001F1ED\U0001F1F0 香港节点"
      - "\U0001F1EF\U0001F1F5 日本节点"
      - "\U0001F1F0\U0001F1F7 韩国节点"
      - "\U0001F1F8\U0001F1EC 新加坡节点"
      - "\U0001F1FA\U0001F1F8 美国节点"
      - "\U0001F310 全部节点"
  - name: "\U0001F3B5 TikTok"
    type: select
    proxies:
      - "\U0001F680 节点选择"
      - "\U0001F1ED\U0001F1F0 香港负载均衡"
      - "\U0001F1EF\U0001F1F5 日本负载均衡"
      - "\U0001F1F0\U0001F1F7 韩国负载均衡"
      - "\U0001F1F8\U0001F1EC 新加坡负载均衡"
      - "\U0001F1FA\U0001F1F8 美国负载均衡"
      - "\U0001F1ED\U0001F1F0 香港自动"
      - "\U0001F1EF\U0001F1F5 日本自动"
      - "\U0001F1F0\U0001F1F7 韩国自动"
      - "\U0001F1F8\U0001F1EC 新加坡自动"
      - "\U0001F1FA\U0001F1F8 美国自动"
      - ♻️ 自动选择
      - "\U0001F1ED\U0001F1F0 香港节点"
      - "\U0001F1EF\U0001F1F5 日本节点"
      - "\U0001F1F0\U0001F1F7 韩国节点"
      - "\U0001F1F8\U0001F1EC 新加坡节点"
      - "\U0001F1FA\U0001F1F8 美国节点"
      - "\U0001F310 全部节点"
  - name: "\U0001F3AC NETFLIX"
    type: select
    proxies:
      - "\U0001F680 节点选择"
      - "\U0001F1ED\U0001F1F0 香港负载均衡"
      - "\U0001F1EF\U0001F1F5 日本负载均衡"
      - "\U0001F1F0\U0001F1F7 韩国负载均衡"
      - "\U0001F1F8\U0001F1EC 新加坡负载均衡"
      - "\U0001F1FA\U0001F1F8 美国负载均衡"
      - "\U0001F1ED\U0001F1F0 香港自动"
      - "\U0001F1EF\U0001F1F5 日本自动"
      - "\U0001F1F0\U0001F1F7 韩国自动"
      - "\U0001F1F8\U0001F1EC 新加坡自动"
      - "\U0001F1FA\U0001F1F8 美国自动"
      - ♻️ 自动选择
      - "\U0001F1ED\U0001F1F0 香港节点"
      - "\U0001F1EF\U0001F1F5 日本节点"
      - "\U0001F1F0\U0001F1F7 韩国节点"
      - "\U0001F1F8\U0001F1EC 新加坡节点"
      - "\U0001F1FA\U0001F1F8 美国节点"
      - "\U0001F310 全部节点"
  - name: "\U0001F1ED\U0001F1F0 香港节点"
    type: select
    include-all: true
    filter: (?i)港|hk|hongkong|hong kong
  - name: "\U0001F1EF\U0001F1F5 日本节点"
    type: select
    include-all: true
    filter: (?i)日|jp|japan
  - name: "\U0001F1F0\U0001F1F7 韩国节点"
    type: select
    include-all: true
    filter: (?i)韩|kr|korea
  - name: "\U0001F1F8\U0001F1EC 新加坡节点"
    type: select
    include-all: true
    filter: (?i)新|狮|sg|singapore|新加坡
  - name: "\U0001F1FA\U0001F1F8 美国节点"
    type: select
    include-all: true
    filter: (?i)美|us|unitedstates|united states
  - name: "\U0001F1ED\U0001F1F0 香港自动"
    type: url-test
    include-all: true
    tolerance: 20
    interval: 300
    filter: (?=.*(港|HK|(?i)Hong))^((?!(台|日|韩|新|深|美)).)*$
  - name: "\U0001F1EF\U0001F1F5 日本自动"
    type: url-test
    include-all: true
    tolerance: 20
    interval: 300
    filter: (?=.*(日|JP|(?i)Japan))^((?!(港|台|韩|新|美)).)*$
  - name: "\U0001F1F0\U0001F1F7 韩国自动"
    type: url-test
    include-all: true
    tolerance: 20
    interval: 300
    filter: (?=.*(韩|KR|(?i)Korea))^((?!(港|台|日|新|美)).)*$
  - name: "\U0001F1F8\U0001F1EC 新加坡自动"
    type: url-test
    include-all: true
    tolerance: 20
    interval: 300
    filter: (?=.*(新|狮|SG|(?i)Singapore))^((?!(港|台|日|韩|美)).)*$
  - name: "\U0001F1FA\U0001F1F8 美国自动"
    type: url-test
    include-all: true
    tolerance: 20
    interval: 300
    filter: (?=.*(美|US|(?i)States|America))^((?!(港|台|日|韩|新)).)*$
  - name: ♻️ 自动选择
    type: url-test
    include-all: true
    tolerance: 20
    interval: 300
    filter: ^((?!(直连)).)*$
  - name: "\U0001F1ED\U0001F1F0 香港负载均衡"
    type: load-balance
    include-all: true
    interval: 60
    lazy: true
    url: 'http://www.google.com/blank.html'
    disable-udp: false
    strategy: round-robin
    timeout: 2000
    max-failed-times: 3
    filter: (?=.*(港|HK|(?i)Hong))^((?!(台|日|韩|新|深|美)).)*$
  - name: "\U0001F1EF\U0001F1F5 日本负载均衡"
    type: load-balance
    include-all: true
    interval: 60
    lazy: true
    url: 'http://www.google.com/blank.html'
    disable-udp: false
    strategy: round-robin
    timeout: 2000
    max-failed-times: 3
    filter: (?=.*(日|JP|(?i)Japan))^((?!(港|台|韩|新|美)).)*$
  - name: "\U0001F1F0\U0001F1F7 韩国负载均衡"
    type: load-balance
    include-all: true
    interval: 60
    lazy: true
    url: 'http://www.google.com/blank.html'
    disable-udp: false
    strategy: round-robin
    timeout: 2000
    max-failed-times: 3
    filter: (?=.*(韩|KR|(?i)Korea))^((?!(港|台|日|新|美)).)*$
  - name: "\U0001F1F8\U0001F1EC 新加坡负载均衡"
    type: load-balance
    include-all: true
    interval: 60
    lazy: true
    url: 'http://www.google.com/blank.html'
    disable-udp: false
    strategy: round-robin
    timeout: 2000
    max-failed-times: 3
    filter: (?=.*(新|狮|SG|(?i)Singapore))^((?!(港|台|日|韩|美)).)*$
  - name: "\U0001F1FA\U0001F1F8 美国负载均衡"
    type: load-balance
    include-all: true
    interval: 60
    lazy: true
    url: 'http://www.google.com/blank.html'
    disable-udp: false
    strategy: round-robin
    timeout: 2000
    max-failed-times: 3
    filter: (?=.*(美|US|(?i)States|America))^((?!(港|台|日|韩|新)).)*$
  - name: "\U0001F3AF 全球直连"
    type: select
    proxies:
      - DIRECT
      - "\U0001F680 节点选择"
  - name: "\U0001F420 漏网之鱼"
    type: select
    proxies:
      - "\U0001F680 节点选择"
      - "\U0001F1ED\U0001F1F0 香港负载均衡"
      - "\U0001F1EF\U0001F1F5 日本负载均衡"
      - "\U0001F1F0\U0001F1F7 韩国负载均衡"
      - "\U0001F1F8\U0001F1EC 新加坡负载均衡"
      - "\U0001F1FA\U0001F1F8 美国负载均衡"
      - "\U0001F1ED\U0001F1F0 香港自动"
      - "\U0001F1EF\U0001F1F5 日本自动"
      - "\U0001F1F0\U0001F1F7 韩国自动"
      - "\U0001F1F8\U0001F1EC 新加坡自动"
      - "\U0001F1FA\U0001F1F8 美国自动"
      - ♻️ 自动选择
      - "\U0001F1ED\U0001F1F0 香港节点"
      - "\U0001F1EF\U0001F1F5 日本节点"
      - "\U0001F1F0\U0001F1F7 韩国节点"
      - "\U0001F1F8\U0001F1EC 新加坡节点"
      - "\U0001F1FA\U0001F1F8 美国节点"
      - "\U0001F310 全部节点"
rules:
  - 'RULE-SET,BanAD,REJECT'
  - 'RULE-SET,BanProgramAD,REJECT'
  - 'RULE-SET,adobe,REJECT'
  - "RULE-SET,youtube_domain,\U0001F4FA YouTube"
  - 'RULE-SET,tencent,DIRECT'
  - 'RULE-SET,private_domain,DIRECT'
  - 'RULE-SET,TencentVideo,DIRECT'
  - "RULE-SET,apple_domain,\U0001F3AF 全球直连"
  - "RULE-SET,ai,\U0001F4AC ChatGPT"
  - "RULE-SET,Spotify,\U0001F680 节点选择"
  - "RULE-SET,github_domain,\U0001F680 节点选择"
  - "RULE-SET,google_domain,\U0001F680 节点选择"
  - "RULE-SET,onedrive_domain,\U0001F3AF 全球直连"
  - "RULE-SET,microsoft_domain,\U0001F3AF 全球直连"
  - "RULE-SET,tiktok_domain,\U0001F3B5 TikTok"
  - "RULE-SET,speedtest_domain,\U0001F680 节点选择"
  - "RULE-SET,telegram_domain,\U0001F680 节点选择"
  - "RULE-SET,netflix_domain,\U0001F3AC NETFLIX"
  - "RULE-SET,Netflix,\U0001F3AC NETFLIX"
  - "RULE-SET,paypal_domain,\U0001F680 节点选择"
  - "RULE-SET,geolocation-!cn,\U0001F680 节点选择"
  - "RULE-SET,cn_domain,\U0001F3AF 全球直连"
  - "RULE-SET,google_ip,\U0001F680 节点选择,no-resolve"
  - "RULE-SET,netflix_ip,\U0001F3AC NETFLIX,no-resolve"
  - "RULE-SET,telegram_ip,\U0001F680 节点选择,no-resolve"
  - "RULE-SET,cn_ip,\U0001F3AF 全球直连"
  - "RULE-SET,proxylite,\U0001F680 节点选择"
  - "MATCH,\U0001F420 漏网之鱼"
rule-anchor:
  ip:
    type: http
    interval: 86400
    behavior: ipcidr
    format: mrs
  domain:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
  qcy:
    type: http
    interval: 86400
    behavior: domain
    format: text
  class:
    type: http
    interval: 86400
    behavior: classical
    format: text
rule-providers:
  BanAD:
    type: http
    interval: 86400
    behavior: classical
    format: text
    url: 'https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/Clash/BanAD.list'
  tencent:
    type: http
    interval: 86400
    behavior: classical
    format: text
    url: >-
      https://cdn.jsdelivr.net/gh/blackmatrix7/ios_rule_script@master/rule/Shadowrocket/Tencent/Tencent.list
  TencentVideo:
    type: http
    interval: 86400
    behavior: classical
    format: text
    url: >-
      https://cdn.jsdelivr.net/gh/blackmatrix7/ios_rule_script@master/rule/Shadowrocket/TencentVideo/TencentVideo.list
  BanProgramAD:
    type: http
    interval: 86400
    behavior: classical
    format: text
    url: >-
      https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/Clash/BanProgramAD.list
  adobe:
    type: http
    interval: 86400
    behavior: classical
    format: text
    url: 'https://raw.githubusercontent.com/cmliu/ACL4SSR/main/Clash/adobe.list'
  private_domain:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/private.mrs
  ai:
    type: http
    interval: 86400
    behavior: classical
    format: text
    url: >-
      https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/a98edee7cd64d64cb58ce26d63d2dbd2475575ad/rule/Clash/OpenAI/OpenAI.list
  Spotify:
    type: http
    interval: 86400
    behavior: classical
    format: text
    url: >-
      https://cdn.jsdelivr.net/gh/blackmatrix7/ios_rule_script@release/rule/Shadowrocket/Spotify/Spotify.list
  Netflix:
    type: http
    interval: 86400
    behavior: classical
    format: text
    url: >-
      https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/a98edee7cd64d64cb58ce26d63d2dbd2475575ad/rule/Clash/Netflix/Netflix.list
  youtube_domain:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/youtube.mrs
  google_domain:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/google.mrs
  github_domain:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/github.mrs
  telegram_domain:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/telegram.mrs
  netflix_domain:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/netflix.mrs
  paypal_domain:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/paypal.mrs
  onedrive_domain:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/onedrive.mrs
  microsoft_domain:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/microsoft.mrs
  apple_domain:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/apple-cn.mrs
  speedtest_domain:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/ookla-speedtest.mrs
  tiktok_domain:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/tiktok.mrs
  gfw_domain:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/gfw.mrs
  geolocation-!cn:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/geolocation-!cn.mrs
  cn_domain:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/cn.mrs
  proxylite:
    type: http
    interval: 86400
    behavior: classical
    format: text
    url: >-
      https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/refs/heads/master/rule/Clash/ProxyLite/ProxyLite.list
  cn_ip:
    type: http
    interval: 86400
    behavior: ipcidr
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geoip/cn.mrs
  google_ip:
    type: http
    interval: 86400
    behavior: ipcidr
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geoip/google.mrs
  telegram_ip:
    type: http
    interval: 86400
    behavior: ipcidr
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geoip/telegram.mrs
  netflix_ip:
    type: http
    interval: 86400
    behavior: ipcidr
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geoip/netflix.mrs
