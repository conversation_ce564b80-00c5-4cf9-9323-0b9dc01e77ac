[{"type": "hysteria2", "name": "🇸🇬 新加坡 001", "server": "**************", "port": 31667, "password": "hf96oOugMgvkOAlVykIA0EKHk", "auth": "hf96oOugMgvkOAlVykIA0EKHk", "obfs": {"type": "", "password": ""}, "tls": {"enabled": true, "serverName": "bing.com", "alpn": ["h3"], "skipCertVerify": true}, "bandwidth": {"up": "", "down": ""}, "congestion": "bbr", "fastOpen": false, "lazy": false, "_sourceFormat": "base64", "detectedRegion": "SG", "originalName": "🇸🇬新加坡1 | ⬇️ 11.9MB/s"}, {"type": "ss", "name": "🇸🇬 新加坡 002", "server": "*************", "port": 13038, "password": "f16163ec-3c35-4719-a19b-68c864cdc626", "method": "aes-128-gcm", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "SG", "originalName": "🇸🇬新加坡2 | ⬇️ 10.2MB/s"}, {"type": "trojan", "name": "🇸🇬 新加坡 003", "server": "cn2.cdn.xfltd-cdn.top", "port": 12021, "password": "7669a04d-a459-4c96-bc0e-fde51cb984f3", "network": "tcp", "tls": {"enabled": true, "serverName": "cn2.cdn.xfltd-cdn.top", "alpn": [], "fingerprint": "chrome", "skipCertVerify": true}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "SG", "originalName": "🇸🇬新加坡4 | ⬇️ 10.4MB/s"}, {"type": "ss", "name": "🇸🇬 新加坡 004", "server": "ti3hyra4.slashdevslashnetslashtun.net", "port": 16015, "password": "RFUKD9DMSTSXGAJ8", "method": "aes-256-gcm", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "SG", "originalName": "🇸🇬新加坡5 | ⬇️ 9.8MB/s"}, {"type": "trojan", "name": "🇸🇬 新加坡 005", "server": "cn2.cdn.xfltd-cdn.top", "port": 12024, "password": "7669a04d-a459-4c96-bc0e-fde51cb984f3", "network": "tcp", "tls": {"enabled": true, "serverName": "cn2.cdn.xfltd-cdn.top", "alpn": [], "fingerprint": "", "skipCertVerify": true}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "SG", "originalName": "🇸🇬新加坡6 | ⬇️ 11.4MB/s"}, {"type": "hysteria2", "name": "🇸🇬 新加坡 006", "server": "starlink-sgp.2513142.xyz", "port": 443, "password": "V3bDL91w89Uh5n65tXawOrHt", "auth": "V3bDL91w89Uh5n65tXawOrHt", "obfs": {"type": "", "password": ""}, "tls": {"enabled": true, "serverName": "www.cloudflare.com", "alpn": ["h3"], "skipCertVerify": true}, "bandwidth": {"up": "", "down": ""}, "congestion": "bbr", "fastOpen": false, "lazy": false, "_sourceFormat": "base64", "detectedRegion": "SG", "originalName": "🇸🇬新加坡8 | ⬇️ 9.4MB/s"}, {"type": "hysteria2", "name": "🇯🇵 日本 001", "server": "jp4.dexlos.com", "port": 7270, "password": "0A1DDFE6-A16A-43FA-A6E3-70EDE4EA61C3", "auth": "0A1DDFE6-A16A-43FA-A6E3-70EDE4EA61C3", "obfs": {"type": "", "password": ""}, "tls": {"enabled": true, "serverName": "jp4.dexlos.com", "alpn": ["h3"], "skipCertVerify": false}, "bandwidth": {"up": "", "down": ""}, "congestion": "bbr", "fastOpen": false, "lazy": false, "_sourceFormat": "base64", "detectedRegion": "JP", "originalName": "🇯🇵日本1 | ⬇️ 11.7MB/s"}, {"type": "hysteria2", "name": "🇯🇵 日本 002", "server": "jp5.dexlos.com", "port": 9517, "password": "0A1DDFE6-A16A-43FA-A6E3-70EDE4EA61C3", "auth": "0A1DDFE6-A16A-43FA-A6E3-70EDE4EA61C3", "obfs": {"type": "", "password": ""}, "tls": {"enabled": true, "serverName": "jp5.dexlos.com", "alpn": ["h3"], "skipCertVerify": false}, "bandwidth": {"up": "", "down": ""}, "congestion": "bbr", "fastOpen": false, "lazy": false, "_sourceFormat": "base64", "detectedRegion": "JP", "originalName": "🇯🇵日本2 | ⬇️ 9.7MB/s"}, {"type": "trojan", "name": "🇯🇵 日本 003", "server": "***********", "port": 443, "password": "CXCu72eya8wFeRySSFpDz3CN6jBYac5OIl3q0gSl8xZOADY3EK94pxZanDA3RT", "network": "tcp", "tls": {"enabled": true, "serverName": "broker.superpokemon.com", "alpn": [], "fingerprint": "", "skipCertVerify": true}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "JP", "originalName": "🇯🇵日本3 | ⬇️ 9.2MB/s"}, {"type": "vless", "name": "🇯🇵 日本 004", "server": "jp002.421421.xyz", "port": 20230, "uuid": "f08d6a6b-a0b3-410e-a0ba-eae71b521904", "flow": "", "encryption": "none", "network": "tcp", "tls": {"enabled": true, "serverName": "www.nvidia.com", "alpn": [], "fingerprint": "chrome"}, "transport": {}, "reality": {"enabled": true, "publicKey": "yNOemxnmT-HQ2HiR9fdHSN2B8HpHsRyMIsir_iAlaHE", "shortId": "5c3c9f11", "spiderX": ""}, "_sourceFormat": "base64", "detectedRegion": "JP", "originalName": "🇯🇵日本4 | ⬇️ 9.9MB/s"}, {"type": "ssr", "name": "🌐 其他 001", "server": "88gg.mt.mt5888.top", "port": 44005, "password": "mantouyun888", "method": "dummy", "protocol": "auth_chain_a", "protocolParam": "", "obfs": "plain", "obfsParam": "cbcdc8198.microsoft.com", "group": "", "_sourceFormat": "base64", "detectedRegion": "OTHER", "originalName": "��'��9f�LH\u001f\b8�!��#�\u000eK�SP��"}, {"type": "ssr", "name": "🌐 其他 002", "server": "9988.mt.mt5888.top", "port": 41115, "password": "mantouyun888", "method": "dummy", "protocol": "auth_chain_a", "protocolParam": "", "obfs": "plain", "obfsParam": "cbcdc8198.microsoft.com", "group": "", "_sourceFormat": "base64", "detectedRegion": "OTHER", "originalName": "� 9am�.�KT��\u001f\b8�!��#�\fMK�SP��"}, {"type": "ssr", "name": "🌐 其他 003", "server": "88gg.mt.mt5888.top", "port": 44003, "password": "mantouyun888", "method": "dummy", "protocol": "auth_chain_a", "protocolParam": "", "obfs": "plain", "obfsParam": "cbcdc8198.microsoft.com", "group": "", "_sourceFormat": "base64", "detectedRegion": "OTHER", "originalName": "ðî<'áë9¥¬9b¨9ghLÈ\u001f\b8«!ûî#È\fLÓPÜ"}, {"type": "ssr", "name": "🌐 其他 004", "server": "88gg.mt.mt5888.top", "port": 44006, "password": "mantouyun888", "method": "dummy", "protocol": "auth_chain_a", "protocolParam": "", "obfs": "plain", "obfsParam": "cbcdc8198.microsoft.com", "group": "", "_sourceFormat": "base64", "detectedRegion": "OTHER", "originalName": "��|'��:i�y�+�L\b\u001f\b8�!�<��ظ�5\b��"}, {"type": "ss", "name": "🌐 其他 005", "server": "180.188.47.63", "port": 17007, "password": "c86d483c-431f-41df-bb6b-c1dcebfc7401", "method": "aes-256-gcm", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "OTHER", "originalName": "🌀其他2-未识别 | ⬇️ 10.2MB/s"}, {"type": "ssr", "name": "🌐 其他 006", "server": "aieq.mt.mt5888.top", "port": 24001, "password": "mantouyun888", "method": "dummy", "protocol": "auth_chain_a", "protocolParam": "", "obfs": "plain", "obfsParam": "cbcdc8198.microsoft.com", "group": "", "_sourceFormat": "base64", "detectedRegion": "OTHER", "originalName": "��|'��:i�y�+�M\b\u001f\b8�!�<��ظ�5\b��"}, {"type": "ssr", "name": "🌐 其他 007", "server": "9988.mt.mt5888.top", "port": 41114, "password": "mantouyun888", "method": "dummy", "protocol": "auth_chain_a", "protocolParam": "", "obfs": "plain", "obfsParam": "cbcdc8198.microsoft.com", "group": "", "_sourceFormat": "base64", "detectedRegion": "OTHER", "originalName": "��|'��:i�y�+�MH\u001f\b8�!�<��Ը�5\b��"}, {"type": "ssr", "name": "🌐 其他 008", "server": "9988.mt.mt5888.top", "port": 44005, "password": "mantouyun888", "method": "dummy", "protocol": "auth_chain_a", "protocolParam": "", "obfs": "plain", "obfsParam": "cbcdc8198.microsoft.com", "group": "", "_sourceFormat": "base64", "detectedRegion": "OTHER", "originalName": "ðî¼'áî9ï£¹fïM\u001f\b8«!ûî#È\fLËÓPÜ"}, {"type": "vmess", "name": "🇭🇰 香港 001", "server": "688f6e4a-swxgg0-syb15h-8caj.hkt.east.wctype.com", "port": 459, "uuid": "a67a6c18-3f6d-11ef-ab9c-f23c9313b177", "alterId": 2, "cipher": "auto", "network": "ws", "tls": {"enabled": false, "serverName": "a605477178.m.ctrip.com"}, "transport": {"path": "/", "headers": {"Host": "a605477178.m.ctrip.com"}}, "_sourceFormat": "base64", "detectedRegion": "HK", "originalName": "ð­ð°é¦æ¸¯1 | â¬ï¸ 13.9MB/s"}, {"type": "vmess", "name": "🇭🇰 香港 002", "server": "9d6c2f7e-swvls0-syb15h-8caj.hkt.east.wctype.com", "port": 459, "uuid": "a67a6c18-3f6d-11ef-ab9c-f23c9313b177", "alterId": 2, "cipher": "auto", "network": "ws", "tls": {"enabled": false, "serverName": "a605477178.m.ctrip.com"}, "transport": {"path": "/", "headers": {"Host": "a605477178.m.ctrip.com"}}, "_sourceFormat": "base64", "detectedRegion": "HK", "originalName": "ð­ð°é¦æ¸¯2 | â¬ï¸ 13.8MB/s"}, {"type": "ss", "name": "🇭🇰 香港 003", "server": "*************", "port": 49759, "password": "sadujij!@diQojd1254", "method": "aes-128-gcm", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "HK", "originalName": "🇭🇰香港3 | ⬇️ 11.4MB/s"}, {"type": "trojan", "name": "🇭🇰 香港 004", "server": "cn1.cdn.xfltd-cdn.top", "port": 12003, "password": "7669a04d-a459-4c96-bc0e-fde51cb984f3", "network": "tcp", "tls": {"enabled": true, "serverName": "cdn.alibaba.com", "alpn": [], "fingerprint": "chrome", "skipCertVerify": true}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "HK", "originalName": "🇭🇰香港4 | ⬇️ 11.5MB/s"}, {"type": "trojan", "name": "🇭🇰 香港 005", "server": "cn1.cdn.xfltd-cdn.top", "port": 12006, "password": "7669a04d-a459-4c96-bc0e-fde51cb984f3", "network": "tcp", "tls": {"enabled": true, "serverName": "cdn.alibaba.com", "alpn": [], "fingerprint": "chrome", "skipCertVerify": true}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "HK", "originalName": "🇭🇰香港5 | ⬇️ 10.9MB/s"}, {"type": "trojan", "name": "🇭🇰 香港 006", "server": "cn1.cdn.xfltd-cdn.top", "port": 12005, "password": "7669a04d-a459-4c96-bc0e-fde51cb984f3", "network": "tcp", "tls": {"enabled": true, "serverName": "cdn.alibaba.com", "alpn": [], "fingerprint": "chrome", "skipCertVerify": true}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "HK", "originalName": "🇭🇰香港6 | ⬇️ 10.8MB/s"}, {"type": "vmess", "name": "🇭🇰 香港 007", "server": "f77b7ed3-swtr40-syb15h-8caj.hkt.east.wctype.com", "port": 459, "uuid": "a67a6c18-3f6d-11ef-ab9c-f23c9313b177", "alterId": 2, "cipher": "auto", "network": "ws", "tls": {"enabled": false, "serverName": "a605477178.m.ctrip.com"}, "transport": {"path": "/", "headers": {"Host": "a605477178.m.ctrip.com"}}, "_sourceFormat": "base64", "detectedRegion": "HK", "originalName": "ð­ð°é¦æ¸¯7 | â¬ï¸ 14.8MB/s"}, {"type": "trojan", "name": "🇭🇰 香港 008", "server": "*************", "port": 46861, "password": "RlzoEILU", "network": "tcp", "tls": {"enabled": true, "serverName": "*************", "alpn": [], "fingerprint": "chrome", "skipCertVerify": true}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "HK", "originalName": "🇭🇰香港8 | ⬇️ 11.9MB/s"}, {"type": "trojan", "name": "🇭🇰 香港 009", "server": "cn1.cdn.xfltd-cdn.top", "port": 12007, "password": "7669a04d-a459-4c96-bc0e-fde51cb984f3", "network": "tcp", "tls": {"enabled": true, "serverName": "cdn.alibaba.com", "alpn": [], "fingerprint": "chrome", "skipCertVerify": true}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "HK", "originalName": "🇭🇰香港9 | ⬇️ 11.3MB/s"}, {"type": "trojan", "name": "🇭🇰 香港 010", "server": "**************", "port": 50723, "password": "RlzoEILU", "network": "tcp", "tls": {"enabled": true, "serverName": "**************", "alpn": [], "fingerprint": "", "skipCertVerify": true}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "HK", "originalName": "🇭🇰香港11 | ⬇️ 10.3MB/s"}, {"type": "trojan", "name": "🇭🇰 香港 011", "server": "*************", "port": 3754, "password": "RlzoEILU", "network": "tcp", "tls": {"enabled": true, "serverName": "*************", "alpn": [], "fingerprint": "chrome", "skipCertVerify": true}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "HK", "originalName": "🇭🇰香港12 | ⬇️ 9.6MB/s"}, {"type": "vmess", "name": "🇭🇰 香港 012", "server": "e512a77b-swzb40-syfewh-64gc.hkt.east.wctype.com", "port": 459, "uuid": "d720a490-d56b-11ef-a1cb-f23c9313b177", "alterId": 2, "cipher": "auto", "network": "ws", "tls": {"enabled": false, "serverName": "a605477178.m.ctrip.com"}, "transport": {"path": "/", "headers": {"Host": "a605477178.m.ctrip.com"}}, "_sourceFormat": "base64", "detectedRegion": "HK", "originalName": "ð­ð°é¦æ¸¯13 | â¬ï¸ 15.7MB/s"}, {"type": "vmess", "name": "🇭🇰 香港 013", "server": "c0d86f90-swzb40-syb15h-8caj.hkt.east.wctype.com", "port": 459, "uuid": "a67a6c18-3f6d-11ef-ab9c-f23c9313b177", "alterId": 2, "cipher": "auto", "network": "ws", "tls": {"enabled": false, "serverName": "a605477178.m.ctrip.com"}, "transport": {"path": "/", "headers": {"Host": "a605477178.m.ctrip.com"}}, "_sourceFormat": "base64", "detectedRegion": "HK", "originalName": "ð­ð°é¦æ¸¯16 | â¬ï¸ 14.4MB/s"}, {"type": "trojan", "name": "🇰🇷 韩国 001", "server": "*************", "port": 47655, "password": "RlzoEILU", "network": "tcp", "tls": {"enabled": true, "serverName": "*************", "alpn": [], "fingerprint": "chrome", "skipCertVerify": true}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "KR", "originalName": "🇰🇷韩国1 | ⬇️ 11.4MB/s"}, {"type": "trojan", "name": "🇰🇷 韩国 002", "server": "*************", "port": 28910, "password": "RlzoEILU", "network": "tcp", "tls": {"enabled": true, "serverName": "cdn.egvra.cn", "alpn": [], "fingerprint": "", "skipCertVerify": true}, "transport": {}, "_sourceFormat": "base64", "detectedRegion": "KR", "originalName": "🇰🇷韩国2 | ⬇️ 10.2MB/s"}, {"type": "vless", "name": "🇺🇸 美国 001", "server": "**************", "port": 8880, "uuid": "53fa8faf-ba4b-4322-9c69-a3e5b1555049", "flow": "", "encryption": "none", "network": "ws", "tls": {"enabled": false, "serverName": "", "alpn": [], "fingerprint": ""}, "transport": {"path": "/?ed=2560", "host": "reedfree8mahsang2.redorg.ir"}, "reality": {"enabled": false, "publicKey": "", "shortId": "", "spiderX": ""}, "_sourceFormat": "base64", "detectedRegion": "US", "originalName": "🇺🇸美国3 | ⬇️ 15.3MB/s"}, {"type": "vless", "name": "🇺🇸 美国 002", "server": "************", "port": 8880, "uuid": "53fa8faf-ba4b-4322-9c69-a3e5b1555049", "flow": "", "encryption": "none", "network": "ws", "tls": {"enabled": false, "serverName": "reedfree8mahsang2.redorg.ir", "alpn": [], "fingerprint": ""}, "transport": {"path": "/?ed=2560", "host": "reedfree8mahsang2.redorg.ir"}, "reality": {"enabled": false, "publicKey": "", "shortId": "", "spiderX": ""}, "_sourceFormat": "base64", "detectedRegion": "US", "originalName": "🇺🇸美国4 | ⬇️ 10.4MB/s"}, {"type": "ss", "name": "🇺🇸 美国 003", "server": "*************", "port": 20010, "password": "c86d483c-431f-41df-bb6b-c1dcebfc7401", "method": "aes-256-gcm", "plugin": null, "pluginOpts": null, "_sourceFormat": "base64", "detectedRegion": "US", "originalName": "🇺🇸美国5 | ⬇️ 8.5MB/s"}, {"type": "vmess", "name": "🇨🇦 加拿大 001", "server": "06809388-302a-29e7-ec15-006f5353d530.castlepeakhospital.moe", "port": 80, "uuid": "14a8a0db-e922-453a-af8d-843af8734b3b", "alterId": 0, "cipher": "auto", "network": "ws", "tls": {"enabled": false, "serverName": "tms.dingtalk.com"}, "transport": {"path": "/", "headers": {"Host": "tms.dingtalk.com"}}, "_sourceFormat": "base64", "detectedRegion": "CA", "originalName": "ð¸ð¬æ°å å¡7 | â¬ï¸ 14.2MB/s"}]