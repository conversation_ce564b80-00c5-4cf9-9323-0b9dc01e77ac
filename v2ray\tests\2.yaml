proxies:
  - down: ''
    fingerprint: ''
    name: "\U0001F1FA\U0001F1F8美国1 | ⬇️ 4.8MB/s"
    obfs: ''
    obfs-password: ''
    password: b8bd42a9-551f-419d-b70d-4aefdd2cb074
    port: 443
    server: **************
    skip-cert-verify: true
    sni: www.bing.com
    type: hysteria2
    up: ''
  - down: ''
    fingerprint: ''
    name: "\U0001F1F7\U0001F1FA俄罗斯1 | ⬇️ 5.3MB/s"
    obfs: ''
    obfs-password: ''
    password: dongtaiwang.com
    port: 30033
    server: hy2.694463.xyz
    skip-cert-verify: true
    sni: www.bing.com
    type: hysteria2
    up: ''
  - down: ''
    fingerprint: ''
    name: "\U0001F1FA\U0001F1F8美国2 | ⬇️ 4.9MB/s"
    obfs: ''
    obfs-password: ''
    password: b8bd42a9-551f-419d-b70d-4aefdd2cb074
    port: 443
    server: **************
    skip-cert-verify: true
    sni: www.bing.com
    type: hysteria2
    up: ''
  - down: ''
    fingerprint: ''
    name: "\U0001F1F7\U0001F1FA俄罗斯2 | ⬇️ 2.3MB/s"
    obfs: ''
    obfs-password: ''
    password: 7b8dc3c9-cd74-4c22-9b29-************
    port: 35174
    server: rua.node.cross666.top
    skip-cert-verify: true
    sni: mirror.hyperdedic.ru
    type: hysteria2
    up: ''
  - down: ''
    fingerprint: ''
    name: "\U0001F1FA\U0001F1F8美国3 | ⬇️ 4.9MB/s"
    obfs: ''
    obfs-password: ''
    password: b8bd42a9-551f-419d-b70d-4aefdd2cb074
    port: 443
    server: **************
    skip-cert-verify: true
    sni: www.bing.com
    type: hysteria2
    up: ''
  - down: ''
    fingerprint: ''
    name: "\U0001F1FA\U0001F1F8美国4 | ⬇️ 4.8MB/s"
    obfs: ''
    obfs-password: ''
    password: a346c669-6374-11ef-bc6a-f23c9313b177
    port: 1443
    server: 5f374286-svtc00-tdex4g-6qyv.la.shifen.uk
    skip-cert-verify: true
    sni: 5f374286-svtc00-tdex4g-6qyv.la.shifen.uk
    type: hysteria2
    up: ''
  - down: ''
    fingerprint: ''
    name: "\U0001F1F7\U0001F1FA俄罗斯3 | ⬇️ 2.2MB/s"
    obfs: ''
    obfs-password: ''
    password: 7b8dc3c9-cd74-4c22-9b29-************
    port: 37952
    server: rua.node.cross666.top
    skip-cert-verify: true
    sni: mirror.hyperdedic.ru
    type: hysteria2
    up: ''
  - down: ''
    fingerprint: ''
    name: "\U0001F1FA\U0001F1F8美国5 | ⬇️ 4.6MB/s"
    obfs: ''
    obfs-password: ''
    password: dongtaiwang.com
    port: 57773
    server: *************
    skip-cert-verify: true
    sni: apple.com
    type: hysteria2
    up: ''
  - down: ''
    fingerprint: ''
    name: "\U0001F1FA\U0001F1F8美国6 | ⬇️ 3.3MB/s"
    obfs: ''
    obfs-password: ''
    password: b8bd42a9-551f-419d-b70d-4aefdd2cb074
    port: 443
    server: **************
    skip-cert-verify: true
    sni: www.bing.com
    type: hysteria2
    up: ''
  - down: ''
    fingerprint: ''
    name: "\U0001F1FA\U0001F1F8美国7 | ⬇️ 2.0MB/s"
    obfs: ''
    obfs-password: ''
    password: 7b8dc3c9-cd74-4c22-9b29-************
    port: 36515
    server: us3.node.cross666.top
    skip-cert-verify: true
    sni: www.yahoo.com
    type: hysteria2
    up: ''
  - down: ''
    fingerprint: ''
    name: "\U0001F1FA\U0001F1F8美国8 | ⬇️ 5.1MB/s"
    obfs: ''
    obfs-password: ''
    password: b8bd42a9-551f-419d-b70d-4aefdd2cb074
    port: 443
    server: *************
    skip-cert-verify: true
    sni: www.bing.com
    type: hysteria2
    up: ''
  - down: ''
    fingerprint: ''
    name: "\U0001F1EB\U0001F1F7法国1 | ⬇️ 5.0MB/s"
    obfs: ''
    obfs-password: ''
    password: dongtaiwang.com
    port: 31180
    server: *************
    skip-cert-verify: true
    sni: apple.com
    type: hysteria2
    up: ''
  - down: ''
    fingerprint: ''
    name: "\U0001F1FA\U0001F1F8美国9 | ⬇️ 2.2MB/s"
    obfs: ''
    obfs-password: ''
    password: 7b8dc3c9-cd74-4c22-9b29-************
    port: 35165
    server: us4.node.cross666.top
    skip-cert-verify: true
    sni: www.yahoo.com
    type: hysteria2
    up: ''
  - down: ''
    fingerprint: ''
    name: "\U0001F1EB\U0001F1F7法国2 | ⬇️ 2.0MB/s"
    obfs: ''
    obfs-password: ''
    password: 7b8dc3c9-cd74-4c22-9b29-************
    port: 35229
    server: fr1.node.cross666.top
    skip-cert-verify: true
    sni: ubuntu.mirrors.ovh.net
    type: hysteria2
    up: ''
  - down: ''
    fingerprint: ''
    name: "\U0001F300其他1-TR | ⬇️ 1.9MB/s"
    obfs: ''
    obfs-password: ''
    password: 7b8dc3c9-cd74-4c22-9b29-************
    port: 38494
    server: tr01.node.cross666.top
    skip-cert-verify: true
    sni: mirror.dogado.de
    type: hysteria2
    up: ''
  - cipher: aes-256-gcm
    name: "\U0001F1ED\U0001F1F0香港1 | ⬇️ 782KB/s"
    password: R2F7VQ5ABAAJQIBH
    port: 15013
    server: 8tv68qhq.slashdevslashnetslashtun.net
    type: ss
    udp: true
  - down: ''
    fingerprint: ''
    name: "\U0001F1FA\U0001F1F8美国10 | ⬇️ 5.6MB/s"
    obfs: ''
    obfs-password: ''
    password: b72ba5d5-2d5e-45b7-93b5-236d343baa7c
    port: 47262
    server: **************
    skip-cert-verify: true
    sni: www.bing.com
    type: hysteria2
    up: ''
  - down: ''
    fingerprint: ''
    name: "\U0001F1FA\U0001F1F8美国11 | ⬇️ 4.1MB/s"
    obfs: ''
    obfs-password: ''
    password: 5CBqBh6MeDq6GajcilBiDg==
    port: 61001
    server: **************
    skip-cert-verify: true
    sni: 192-227-152-86.nip.io
    type: hysteria2
    up: ''
  - cipher: aes-128-gcm
    name: "\U0001F1ED\U0001F1F0香港2 | ⬇️ 890KB/s"
    password: sadujij!@diQojd1254
    port: 49759
    server: *************
    type: ss
    udp: true
  - down: ''
    fingerprint: ''
    name: "\U0001F1F7\U0001F1FA俄罗斯4 | ⬇️ 1.4MB/s"
    obfs: ''
    obfs-password: ''
    password: 7b8dc3c9-cd74-4c22-9b29-************
    port: 35912
    server: ru2.node.cross666.top
    skip-cert-verify: true
    sni: mirror.hyperdedic.ru
    type: hysteria2
    up: ''
  - cipher: aes-128-gcm
    name: "\U0001F1F8\U0001F1EC新加坡1 | ⬇️ 884KB/s"
    password: f16163ec-3c35-4719-a19b-68c864cdc626
    port: 13038
    server: *************
    type: ss
    udp: true
  - down: ''
    fingerprint: ''
    name: "\U0001F1F8\U0001F1EC新加坡2 | ⬇️ 994KB/s"
    obfs: ''
    obfs-password: ''
    password: 7b8dc3c9-cd74-4c22-9b29-************
    port: 36807
    server: sg02.node.cross666.top
    skip-cert-verify: true
    sni: mirror.sg.gs
    type: hysteria2
    up: ''
  - cipher: aes-256-gcm
    name: "\U0001F1ED\U0001F1F0香港3 | ⬇️ 584KB/s"
    password: 0S43J3FD6L7IEKGQ
    port: 15008
    server: 8tv68qhq.slashdevslashnetslashtun.net
    type: ss
    udp: true
  - down: ''
    fingerprint: ''
    name: "\U0001F1FA\U0001F1F8美国12 | ⬇️ 708KB/s"
    obfs: ''
    obfs-password: ''
    password: 48c1e014-28d6-11ec-a0fc-f23c913c8d2b
    port: 8443
    server: a4e3ef78-swin40-sxlsd4-3z3v.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: a4e3ef78-swin40-sxlsd4-3z3v.hy2.gotochinatown.net
    type: hysteria2
    up: ''
  - down: ''
    fingerprint: ''
    name: "\U0001F1FA\U0001F1F8美国13 | ⬇️ 781KB/s"
    obfs: ''
    obfs-password: ''
    password: 0da8651e-e1f6-11ec-bd7c-f23c913c8d2b
    port: 8443
    server: e06eb389-swq1s0-tcinla-hrtf.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: e06eb389-swq1s0-tcinla-hrtf.hy2.gotochinatown.net
    type: hysteria2
    up: ''
  - client-fingerprint: chrome
    name: "\U0001F1FA\U0001F1F8美国14 | ⬇️ 1.0MB/s"
    password: 2b1ed981-6547-4094-998b-06a3323d6f6c
    port: 59599
    server: xd-js.timiwc.com
    skip-cert-verify: true
    sni: k62.tudou211.com
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1FA\U0001F1F8美国15 | ⬇️ 6.9MB/s"
    network: ws
    port: 443
    server: dmit.jhyl.bid
    tls: true
    type: vless
    udp: true
    uuid: 0cc14bae-0703-4c2d-e9de-ed4672eadd30
    ws-opts:
      headers:
        Host: dmit.jhyl.bid
        User-Agent: >-
          Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like
          Gecko) Chrome/54.0.2840.71 Safari/537.36
      path: /download
    xudp: true
    servername: dmit.jhyl.bid
  - down: ''
    fingerprint: ''
    name: "\U0001F1FA\U0001F1F8美国16 | ⬇️ 776KB/s"
    obfs: ''
    obfs-password: ''
    password: 93fb69fc-77cf-11ee-85ee-f23c91369f2d
    port: 8443
    server: a132e977-sw5og0-t12cnj-1ol97.hy2.gotochinatown.net
    skip-cert-verify: true
    sni: a132e977-sw5og0-t12cnj-1ol97.hy2.gotochinatown.net
    type: hysteria2
    up: ''
  - client-fingerprint: chrome
    flow: xtls-rprx-vision
    name: "\U0001F1FA\U0001F1F8美国17 | ⬇️ 2.0MB/s"
    network: tcp
    port: 30158
    reality-opts:
      public-key: n7Ptb-3f4pw0D-hG-UP8Dp5Gf8J8Uxn029JMwdTyEFc
      short-id: ee9e2505
    server: **************
    tls: true
    type: vless
    udp: true
    uuid: b72ba5d5-2d5e-45b7-93b5-236d343baa7c
    xudp: true
    servername: www.yahoo.com
  - client-fingerprint: chrome
    name: "\U0001F1FA\U0001F1F8美国18 | ⬇️ 765KB/s"
    password: 2b1ed981-6547-4094-998b-06a3323d6f6c
    port: 21603
    server: xd-js.timiwc.com
    skip-cert-verify: true
    sni: k61.tudou211.com
    type: trojan
    udp: true
  - down: ''
    fingerprint: ''
    name: "\U0001F1FA\U0001F1F8美国19 | ⬇️ 2.5MB/s"
    obfs: ''
    obfs-password: ''
    password: 6c510073-4ca8-423b-87a5-a6d73c0ca557
    port: 43999
    server: jiangzhidb.54264944.xyz
    skip-cert-verify: true
    sni: jiangzhidb.54264944.xyz
    type: hysteria2
    up: ''
  - down: ''
    fingerprint: ''
    name: "\U0001F1EB\U0001F1F7法国3 | ⬇️ 5.0MB/s"
    obfs: ''
    obfs-password: ''
    password: 6c510073-4ca8-423b-87a5-a6d73c0ca557
    port: 43999
    server: jiangzhifg.54264944.xyz
    skip-cert-verify: true
    sni: jiangzhifr.54264944.xyz
    type: hysteria2
    up: ''
  - down: ''
    fingerprint: ''
    name: "\U0001F1FA\U0001F1F8美国20 | ⬇️ 1.3MB/s"
    obfs: ''
    obfs-password: ''
    password: 6e9cdc20-b92a-11ef-973a-f23c913c8d2b
    port: 8443
    server: 1fec14d5-swrwg0-sww7b0-1qwp5.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 1fec14d5-swrwg0-sww7b0-1qwp5.hy2.gotochinatown.net
    type: hysteria2
    up: ''
  - down: ''
    fingerprint: ''
    name: "\U0001F1FA\U0001F1F8美国21 | ⬇️ 1.0MB/s"
    obfs: ''
    obfs-password: ''
    password: b79d79ae-8bce-11ef-a2b8-f23c9164ca5d
    port: 8443
    server: 341d6ff9-swb8g0-t67sv3-1snfs.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 341d6ff9-swb8g0-t67sv3-1snfs.hy2.gotochinatown.net
    type: hysteria2
    up: ''
  - down: ''
    fingerprint: ''
    name: "\U0001F1FA\U0001F1F8美国22 | ⬇️ 3.1MB/s"
    obfs: ''
    obfs-password: ''
    password: d010926e-0311-4924-a013-b84fbae430f9
    port: 30003
    server: qymg.qy1357.top
    skip-cert-verify: true
    sni: qymg.qy1357.top
    type: hysteria2
    up: ''
  - down: ''
    fingerprint: ''
    name: "\U0001F1EC\U0001F1E7英国1 | ⬇️ 3.2MB/s"
    obfs: ''
    obfs-password: ''
    password: nfsn666
    port: 8888
    server: ld-arm.nfsn666.gq
    skip-cert-verify: true
    sni: ld-arm.nfsn666.gq
    type: hysteria2
    up: ''
  - down: ''
    fingerprint: ''
    name: "\U0001F1E9\U0001F1EA德国1 | ⬇️ 5.2MB/s"
    obfs: ''
    obfs-password: ''
    password: 75e98355-345b-4413-8001-729835854030
    port: 44001
    server: **************
    skip-cert-verify: true
    sni: ''
    type: hysteria2
    up: ''
    disable-sni: true
  - down: ''
    fingerprint: ''
    name: "\U0001F1FA\U0001F1F8美国23 | ⬇️ 5.3MB/s"
    obfs: ''
    obfs-password: ''
    password: 65a43b6e-19c5-4fab-b960-d110a07d66a4
    port: 3234
    server: **************
    skip-cert-verify: true
    sni: ''
    type: hysteria2
    up: ''
    disable-sni: true
  - down: ''
    fingerprint: ''
    name: "\U0001F1FA\U0001F1F8美国24 | ⬇️ 1.4MB/s"
    obfs: ''
    obfs-password: ''
    password: 0da8651e-e1f6-11ec-bd7c-f23c913c8d2b
    port: 8443
    server: 479aad99-swd340-tcinla-hrtf.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 479aad99-swd340-tcinla-hrtf.hy2.gotochinatown.net
    type: hysteria2
    up: ''
  - down: ''
    fingerprint: ''
    name: "\U0001F1E9\U0001F1EA德国2 | ⬇️ 4.6MB/s"
    obfs: ''
    obfs-password: ''
    password: 796ec552-f8e4-43c7-ac2f-5c2e668074de
    port: 33003
    server: qydg.qy1357.top
    skip-cert-verify: true
    sni: qydg.qy1357.top
    type: hysteria2
    up: ''
  - down: ''
    fingerprint: ''
    name: "\U0001F1EC\U0001F1E7英国2 | ⬇️ 3.1MB/s"
    obfs: ''
    obfs-password: ''
    password: nfsn666
    port: 8888
    server: ***************
    skip-cert-verify: true
    sni: ld-arm.nfsn666.gq
    type: hysteria2
    up: ''
  - down: ''
    fingerprint: ''
    name: "\U0001F1ED\U0001F1F0香港4 | ⬇️ 1.8MB/s"
    obfs: ''
    obfs-password: ''
    password: d010926e-0311-4924-a013-b84fbae430f9
    port: 30003
    server: qyhg.qy1357.top
    skip-cert-verify: true
    sni: qyhg.qy1357.top
    type: hysteria2
    up: ''
  - client-fingerprint: chrome
    name: "\U0001F1F0\U0001F1F7韩国1 | ⬇️ 656KB/s"
    password: RlzoEILU
    port: 28548
    server: *************
    skip-cert-verify: true
    sni: cdn.egvra.cn
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1EF\U0001F1F5日本1 | ⬇️ 5.8MB/s"
    password: 7669a04d-a459-4c96-bc0e-fde51cb984f3
    port: 12032
    server: cn2.cdn.xfltd-cdn.top
    skip-cert-verify: true
    sni: cn2.cdn.xfltd-cdn.top
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1ED\U0001F1F0香港5 | ⬇️ 545KB/s"
    password: RlzoEILU
    port: 46861
    server: *************
    skip-cert-verify: true
    sni: *************
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1F0\U0001F1F7韩国2 | ⬇️ 6.1MB/s"
    password: 7669a04d-a459-4c96-bc0e-fde51cb984f3
    port: 12041
    server: cn2.cdn.xfltd-cdn.top
    skip-cert-verify: true
    sni: cn2.cdn.xfltd-cdn.top
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1F8\U0001F1EC新加坡3 | ⬇️ 6.2MB/s"
    password: 7669a04d-a459-4c96-bc0e-fde51cb984f3
    port: 12025
    server: cn2.cdn.xfltd-cdn.top
    skip-cert-verify: true
    sni: cn2.cdn.xfltd-cdn.top
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1F8\U0001F1EC新加坡4 | ⬇️ 5.5MB/s"
    password: 7669a04d-a459-4c96-bc0e-fde51cb984f3
    port: 12022
    server: cn2.cdn.xfltd-cdn.top
    skip-cert-verify: true
    sni: cn2.cdn.xfltd-cdn.top
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1F0\U0001F1F7韩国3 | ⬇️ 516KB/s"
    password: RlzoEILU
    port: 47655
    server: *************
    skip-cert-verify: true
    sni: *************
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1ED\U0001F1F0香港6 | ⬇️ 7.7MB/s"
    password: 7669a04d-a459-4c96-bc0e-fde51cb984f3
    port: 12003
    server: cn1.cdn.xfltd-cdn.top
    skip-cert-verify: true
    sni: cdn.alibaba.com
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1ED\U0001F1F0香港7 | ⬇️ 2.5MB/s"
    password: 7669a04d-a459-4c96-bc0e-fde51cb984f3
    port: 12007
    server: cn1.cdn.xfltd-cdn.top
    skip-cert-verify: true
    sni: cdn.alibaba.com
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1FA\U0001F1F8美国25 | ⬇️ 6.6MB/s"
    password: 7669a04d-a459-4c96-bc0e-fde51cb984f3
    port: 12004
    server: cn1.cdn.xfltd-cdn.top
    skip-cert-verify: true
    sni: cdn.alibaba.com
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1F9\U0001F1FC台湾1 | ⬇️ 1.4MB/s"
    password: 7669a04d-a459-4c96-bc0e-fde51cb984f3
    port: 12011
    server: cn1.cdn.xfltd-cdn.top
    skip-cert-verify: true
    sni: cdn.alibaba.com
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1F8\U0001F1EC新加坡5 | ⬇️ 5.8MB/s"
    password: 7669a04d-a459-4c96-bc0e-fde51cb984f3
    port: 12023
    server: cn2.cdn.xfltd-cdn.top
    skip-cert-verify: true
    sni: cdn.alibaba.com
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1F8\U0001F1EC新加坡6 | ⬇️ 5.3MB/s"
    password: 7669a04d-a459-4c96-bc0e-fde51cb984f3
    port: 12021
    server: cn2.cdn.xfltd-cdn.top
    skip-cert-verify: true
    sni: cn2.cdn.xfltd-cdn.top
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1EF\U0001F1F5日本2 | ⬇️ 4.8MB/s"
    password: 7669a04d-a459-4c96-bc0e-fde51cb984f3
    port: 12031
    server: cn2.cdn.xfltd-cdn.top
    skip-cert-verify: true
    sni: cn2.cdn.xfltd-cdn.top
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1EF\U0001F1F5日本3 | ⬇️ 6.3MB/s"
    password: 7669a04d-a459-4c96-bc0e-fde51cb984f3
    port: 12034
    server: cn2.cdn.xfltd-cdn.top
    skip-cert-verify: true
    sni: cn2.cdn.xfltd-cdn.top
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1E6\U0001F1FA澳大利亚1 | ⬇️ 3.2MB/s"
    password: 7669a04d-a459-4c96-bc0e-fde51cb984f3
    port: 12068
    server: cn2.cdn.xfltd-cdn.top
    skip-cert-verify: true
    sni: cdn.alibaba.com
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1FA\U0001F1F8美国26 | ⬇️ 4.3MB/s"
    password: 7669a04d-a459-4c96-bc0e-fde51cb984f3
    port: 12054
    server: cn2.cdn.xfltd-cdn.top
    skip-cert-verify: true
    sni: cdn.alibaba.com
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1ED\U0001F1F0香港8 | ⬇️ 7.9MB/s"
    password: 7669a04d-a459-4c96-bc0e-fde51cb984f3
    port: 12002
    server: cn1.cdn.xfltd-cdn.top
    skip-cert-verify: true
    sni: cdn.alibaba.com
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1FA\U0001F1F8美国27 | ⬇️ 4.8MB/s"
    password: 7669a04d-a459-4c96-bc0e-fde51cb984f3
    port: 12052
    server: cn2.cdn.xfltd-cdn.top
    skip-cert-verify: true
    sni: cdn.alibaba.com
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1E8\U0001F1ED瑞士1 | ⬇️ 783KB/s"
    password: 7669a04d-a459-4c96-bc0e-fde51cb984f3
    port: 12062
    server: cn2.cdn.xfltd-cdn.top
    skip-cert-verify: true
    sni: cdn.alibaba.com
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1ED\U0001F1F0香港9 | ⬇️ 3.6MB/s"
    password: 7669a04d-a459-4c96-bc0e-fde51cb984f3
    port: 12005
    server: cn1.cdn.xfltd-cdn.top
    skip-cert-verify: true
    sni: cdn.alibaba.com
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1FA\U0001F1F8美国28 | ⬇️ 518KB/s"
    password: 7669a04d-a459-4c96-bc0e-fde51cb984f3
    port: 12051
    server: cn2.cdn.xfltd-cdn.top
    skip-cert-verify: true
    sni: cdn.alibaba.com
    type: trojan
    udp: true
  - name: "\U0001F1FA\U0001F1F8美国29 | ⬇️ 1.8MB/s"
    network: ws
    port: 8880
    server: ************
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
        User-Agent: >-
          Mozilla/5.0 (Windows NT 6.3; WOW64) AppleWebKit/537.36 (KHTML, like
          Gecko) Chrome/56.0.2924.87 Safari/537.36
      path: "/Telegram\U0001F1E8\U0001F1F3 @pgkj666 /?ed=2560"
    xudp: true
  - client-fingerprint: chrome
    name: "\U0001F300其他2-ID | ⬇️ 614KB/s"
    password: 7669a04d-a459-4c96-bc0e-fde51cb984f3
    port: 12061
    server: cn2.cdn.xfltd-cdn.top
    skip-cert-verify: true
    sni: cn2.cdn.xfltd-cdn.top
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1F7\U0001F1FA俄罗斯5 | ⬇️ 733KB/s"
    password: 7669a04d-a459-4c96-bc0e-fde51cb984f3
    port: 12075
    server: cn2.cdn.xfltd-cdn.top
    skip-cert-verify: true
    sni: cdn.alibaba.com
    type: trojan
    udp: true
  - client-fingerprint: chrome
    grpc-opts:
      grpc-service-name: >-
        CANALTELEGRAM--jet_config--TELEGRAM--jet_config--CANALTELEGRAM--jet_config--TELEGRAM--jet_config--CANALTELEGRAM--jet_config
    name: "\U0001F1EC\U0001F1E7英国3 | ⬇️ 5.9MB/s"
    network: grpc
    port: 2030
    reality-opts:
      public-key: YWfCdTnr4FAOMYTY2dLrMtQUokyxOGpPhYEEszPj20E
      short-id: 7fe29733
    server: *************
    tls: true
    type: vless
    udp: true
    uuid: ad5e479a-d0f6-4809-902c-e74f5404336c
    xudp: true
    servername: refersion.com
  - cipher: aes-256-gcm
    name: "\U0001F1ED\U0001F1F0香港10 | ⬇️ 12.2MB/s"
    password: 3030b609-f36f-4fd1-a4ef-a6e653780536
    port: 16008
    server: *************
    type: ss
    udp: true
  - client-fingerprint: chrome
    flow: xtls-rprx-vision
    name: "\U0001F1E9\U0001F1EA德国3 | ⬇️ 3.5MB/s"
    network: tcp
    port: 443
    server: *************
    tls: true
    type: vless
    udp: true
    uuid: b8bd42a9-551f-419d-b70d-4aefdd2cb074
    xudp: true
    servername: yfnl1.xn--4gq62f52gppi29k.com
  - cipher: aes-256-gcm
    name: "\U0001F1ED\U0001F1F0香港11 | ⬇️ 10.4MB/s"
    password: 3030b609-f36f-4fd1-a4ef-a6e653780536
    port: 16004
    server: ************
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1F9\U0001F1FC台湾2 | ⬇️ 10.7MB/s"
    password: 3030b609-f36f-4fd1-a4ef-a6e653780536
    port: 17004
    server: *************
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1ED\U0001F1F0香港12 | ⬇️ 1.1MB/s"
    password: e04ae67d4e4cd165
    port: 2019
    server: **************
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1EF\U0001F1F5日本4 | ⬇️ 6.4MB/s"
    password: 9FUHILBF7J8FJOUP
    port: 18010
    server: ti3hyra4.slashdevslashnetslashtun.net
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1FA\U0001F1F8美国30 | ⬇️ 964KB/s"
    password: P1lrnsJwO4
    port: 50631
    server: ************
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1ED\U0001F1F0香港13 | ⬇️ 6.4MB/s"
    password: 3030b609-f36f-4fd1-a4ef-a6e653780536
    port: 16007
    server: ************
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1EF\U0001F1F5日本5 | ⬇️ 8.3MB/s"
    password: 3030b609-f36f-4fd1-a4ef-a6e653780536
    port: 19002
    server: ************
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1F8\U0001F1EC新加坡7 | ⬇️ 8.6MB/s"
    password: 3030b609-f36f-4fd1-a4ef-a6e653780536
    port: 18004
    server: *************
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1ED\U0001F1F0香港14 | ⬇️ 9.7MB/s"
    password: 3030b609-f36f-4fd1-a4ef-a6e653780536
    port: 16002
    server: *************
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1FA\U0001F1F8美国31 | ⬇️ 6.7MB/s"
    password: 3030b609-f36f-4fd1-a4ef-a6e653780536
    port: 20002
    server: *************
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1FA\U0001F1F8美国32 | ⬇️ 8.7MB/s"
    password: 3030b609-f36f-4fd1-a4ef-a6e653780536
    port: 20004
    server: *************
    type: ss
    udp: true
  - alpn:
      - ''
    alterId: '0'
    cipher: auto
    name: "\U0001F300其他3-未识别 | ⬇️ 2.4MB/s"
    network: ws
    port: 8080
    server: d9eaa203-swexs0-sww7b0-1qwp5.hgc1.tcpbbr.net
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 6e9cdc20-b92a-11ef-973a-f23c913c8d2b
    ws-opts:
      headers:
        Host: d9eaa203-swexs0-sww7b0-1qwp5.hgc1.tcpbbr.net
      path: /
    xudp: true
  - alpn:
      - ''
    alterId: '0'
    cipher: auto
    name: "\U0001F300其他4-未识别 | ⬇️ 591KB/s"
    network: ws
    port: 8080
    server: fe950ba9-swexs0-tcinla-hrtf.hgc1.tcpbbr.net
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 0da8651e-e1f6-11ec-bd7c-f23c913c8d2b
    ws-opts:
      headers:
        Host: broadcastlv.chat.bilibili.com
      path: /
    xudp: true
  - alpn:
      - ''
    alterId: '0'
    cipher: auto
    name: "\U0001F300其他5-未识别 | ⬇️ 736KB/s"
    network: ws
    port: 8080
    server: de975b02-swd340-sxlsd4-3z3v.hgc1.tcpbbr.net
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 48c1e014-28d6-11ec-a0fc-f23c913c8d2b
    ws-opts:
      headers:
        Host: broadcastlv.chat.bilibili.com
      path: /
    xudp: true
  - alpn:
      - ''
    alterId: '0'
    cipher: auto
    name: "\U0001F300其他6-未识别 | ⬇️ 2.1MB/s"
    network: ws
    port: 8080
    server: ba686a88-swd340-tcinla-hrtf.hgc1.tcpbbr.net
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 0da8651e-e1f6-11ec-bd7c-f23c913c8d2b
    ws-opts:
      headers:
        Host: broadcastlv.chat.bilibili.com
      path: /
    xudp: true
  - alpn:
      - ''
    alterId: '0'
    cipher: auto
    name: "\U0001F300其他7-未识别 | ⬇️ 1.1MB/s"
    network: ws
    port: 8080
    server: cff06f1d-swd340-sznzxg-1jfvb.hgc1.tcpbbr.net
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 06b2ac52-fcc6-11ec-bb74-f23c9164ca5d
    ws-opts:
      headers:
        Host: broadcastlv.chat.bilibili.com
      path: /
    xudp: true
  - alpn:
      - ''
    alterId: '0'
    cipher: auto
    name: "\U0001F300其他8-未识别 | ⬇️ 6.0MB/s"
    network: ws
    port: 8080
    server: 0862d860-swb8g0-swdpud-duku.hgc1.tcpbbr.net
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 8e0f8ef2-5fe3-11ec-a8bf-f23c91cfbbc9
    ws-opts:
      headers:
        Host: broadcastlv.chat.bilibili.com
      path: /
    xudp: true
  - alpn:
      - ''
    alterId: '0'
    cipher: auto
    name: "\U0001F300其他9-未识别 | ⬇️ 5.0MB/s"
    network: ws
    port: 8080
    server: 01a3c6f0-svukg0-swdpud-duku.hgc1.tcpbbr.net
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 8e0f8ef2-5fe3-11ec-a8bf-f23c91cfbbc9
    ws-opts:
      headers:
        Host: broadcastlv.chat.bilibili.com
      path: /
    xudp: true
  - alpn:
      - ''
    alterId: '0'
    cipher: auto
    name: "\U0001F300其他10-未识别 | ⬇️ 2.7MB/s"
    network: ws
    port: 8080
    server: b8c34ce7-sw7j40-swa8xy-laev.hgc1.tcpbbr.net
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: ef18aaca-4711-11ec-a8bf-f23c91cfbbc9
    ws-opts:
      headers:
        Host: broadcastlv.chat.bilibili.com
      path: /
    xudp: true
  - name: "\U0001F1E9\U0001F1EA德国4 | ⬇️ 823KB/s"
    network: ws
    port: 8880
    server: **************
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
        User-Agent: >-
          Mozilla/5.0 (Linux; Android 7.0; SM-G570M Build/NRD90M)
          AppleWebKit/537.36 (KHTML, like Gecko) Chrome/69.0.3497.100 Mobile
          Safari/537.36
      path: "/Telegram\U0001F1E8\U0001F1F3 @pgkj666 /?ed=2560"
    xudp: true
  - name: "\U0001F1EF\U0001F1F5日本6 | ⬇️ 10.9MB/s"
    obfs: ''
    obfs-password: ''
    password: 0A1DDFE6-A16A-43FA-A6E3-70EDE4EA61C3
    port: 9517
    server: jp5.dexlos.com
    skip-cert-verify: false
    sni: jp5.dexlos.com
    type: hysteria2
  - name: "\U0001F1EF\U0001F1F5日本7 | ⬇️ 8.6MB/s"
    obfs: ''
    obfs-password: ''
    password: 0A1DDFE6-A16A-43FA-A6E3-70EDE4EA61C3
    port: 7270
    server: jp4.dexlos.com
    skip-cert-verify: false
    sni: jp4.dexlos.com
    type: hysteria2
  - name: "\U0001F1F8\U0001F1EC新加坡8 | ⬇️ 9.8MB/s"
    obfs: ''
    obfs-password: ''
    password: hf96oOugMgvkOAlVykIA0EKHk
    port: 31667
    server: **************
    skip-cert-verify: true
    sni: bing.com
    type: hysteria2
  - down: ''
    fingerprint: ''
    name: "\U0001F1F0\U0001F1F7韩国4 | ⬇️ 7.4MB/s"
    obfs: ''
    obfs-password: ''
    password: 75e98355-345b-4413-8001-729835854030
    port: 44005
    server: **************
    skip-cert-verify: true
    sni: www.bing.com
    type: hysteria2
    up: ''
  - name: "\U0001F1FA\U0001F1F8美国33 | ⬇️ 1.5MB/s"
    obfs: ''
    obfs-password: ''
    password: 088a0bbe-4f9f-11ea-a15d-f23c913c8d2b
    port: 8443
    server: b6ea3219-supts0-swbd0b-716s.hy2.gotochinatown.net
    skip-cert-verify: true
    sni: b6ea3219-supts0-swbd0b-716s.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国34 | ⬇️ 782KB/s"
    obfs: ''
    obfs-password: ''
    password: 8e0f8ef2-5fe3-11ec-a8bf-f23c91cfbbc9
    port: 8443
    server: 1ec1d0fd-sw3ts0-swdpud-duku.hy2.gotochinatown.net
    skip-cert-verify: true
    sni: 1ec1d0fd-sw3ts0-swdpud-duku.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国35 | ⬇️ 771KB/s"
    obfs: ''
    obfs-password: ''
    password: c3dc8d26-ab2b-11ec-a8bf-f23c91cfbbc9
    port: 8443
    server: 71c9123b-swexs0-szapkm-1hpo0.hy2.gotochinatown.net
    skip-cert-verify: true
    sni: 71c9123b-swexs0-szapkm-1hpo0.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国36 | ⬇️ 1.0MB/s"
    obfs: ''
    obfs-password: ''
    password: 06b2ac52-fcc6-11ec-bb74-f23c9164ca5d
    port: 8443
    server: 7edb7ffb-sw5og0-sznzxg-1jfvb.hy2.gotochinatown.net
    skip-cert-verify: true
    sni: 7edb7ffb-sw5og0-sznzxg-1jfvb.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1F8\U0001F1EC新加坡9 | ⬇️ 1.5MB/s"
    obfs: ''
    obfs-password: ''
    password: f9153abe-88d9-4ad7-a3a7-01eff2790b3e
    port: 37726
    server: sg02.node.cross666.top
    skip-cert-verify: true
    sni: mirror.sg.gs
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国37 | ⬇️ 590KB/s"
    obfs: ''
    obfs-password: ''
    password: 93fb69fc-77cf-11ee-85ee-f23c91369f2d
    port: 8443
    server: 4b3d7ffd-svwf40-t12cnj-1ol97.hy2.gotochinatown.net
    skip-cert-verify: true
    sni: 4b3d7ffd-svwf40-t12cnj-1ol97.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1EF\U0001F1F5日本8 | ⬇️ 8.8MB/s"
    obfs: ''
    obfs-password: ''
    password: 82762002-40f5-4775-a9ad-3da97a2772f7
    port: 20000
    server: jp01.poke-mon.xyz
    skip-cert-verify: true
    sni: www.bing.com
    type: hysteria2
  - name: "\U0001F1F8\U0001F1EC新加坡10 | ⬇️ 1.6MB/s"
    obfs: ''
    obfs-password: ''
    password: abef57a5-ac14-4356-ba5d-c3fdc85df62b
    port: 36180
    server: sg02.node.cross666.top
    skip-cert-verify: true
    sni: mirror.sg.gs
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国38 | ⬇️ 521KB/s"
    obfs: ''
    obfs-password: ''
    password: e3975a05-b89e-11ec-a50d-f23c91cfbbc9
    port: 8443
    server: e00973b3-swin40-sxf56z-1f596.hy2.gotochinatown.net
    skip-cert-verify: true
    sni: e00973b3-swin40-sxf56z-1f596.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国39 | ⬇️ 1.6MB/s"
    obfs: ''
    obfs-password: ''
    password: abef57a5-ac14-4356-ba5d-c3fdc85df62b
    port: 38767
    server: us4.node.cross666.top
    skip-cert-verify: true
    sni: www.yahoo.com
    type: hysteria2
  - name: "\U0001F300其他11-VN | ⬇️ 1.9MB/s"
    obfs: ''
    obfs-password: ''
    password: f9153abe-88d9-4ad7-a3a7-01eff2790b3e
    port: 38973
    server: vn1r.node.cross666.top
    skip-cert-verify: true
    sni: mirror.sg.gs
    type: hysteria2
  - name: "\U0001F1ED\U0001F1F0香港15 | ⬇️ 547KB/s"
    obfs: ''
    obfs-password: ''
    password: 82febca6-8856-41fe-84df-c8bda0b72c7b
    port: 1000
    server: lx.ccwink.cc
    skip-cert-verify: true
    sni: hk003.ccwink.cc
    type: hysteria2
  - name: "\U0001F1ED\U0001F1F0香港16 | ⬇️ 3.7MB/s"
    obfs: ''
    obfs-password: ''
    password: 82762002-40f5-4775-a9ad-3da97a2772f7
    port: 20000
    server: hk02.poke-mon.xyz
    skip-cert-verify: true
    sni: www.bing.com
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国40 | ⬇️ 6.5MB/s"
    obfs: ''
    obfs-password: ''
    password: 82762002-40f5-4775-a9ad-3da97a2772f7
    port: 20000
    server: us01.poke-mon.xyz
    skip-cert-verify: true
    sni: www.bing.com
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国41 | ⬇️ 550KB/s"
    obfs: ''
    obfs-password: ''
    password: d4018e28-e328-11ed-98a7-f23c913c8d2b
    port: 8443
    server: 9b6af554-swexs0-sx3h07-1g8k0.hy2.gotochinatown.net
    skip-cert-verify: true
    sni: 9b6af554-swexs0-sx3h07-1g8k0.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国42 | ⬇️ 510KB/s"
    obfs: ''
    obfs-password: ''
    password: 861726bb-c61a-b0e0-99fa-5ddfdcf21902
    port: 8443
    server: 8b18ad20-swin40-ta5nd4-e06r.hy2.gotochinatown.net
    skip-cert-verify: true
    sni: 8b18ad20-swin40-ta5nd4-e06r.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1EC\U0001F1E7英国4 | ⬇️ 4.5MB/s"
    obfs: ''
    obfs-password: ''
    password: 82762002-40f5-4775-a9ad-3da97a2772f7
    port: 20000
    server: uk01.poke-mon.xyz
    skip-cert-verify: true
    sni: www.bing.com
    type: hysteria2
  - name: "\U0001F1E9\U0001F1EA德国5 | ⬇️ 6.5MB/s"
    obfs: ''
    obfs-password: ''
    password: 82762002-40f5-4775-a9ad-3da97a2772f7
    port: 20000
    server: deguo.poke-mon.xyz
    skip-cert-verify: true
    sni: www.bing.com
    type: hysteria2
  - name: "\U0001F1F9\U0001F1FC台湾3 | ⬇️ 5.9MB/s"
    obfs: ''
    obfs-password: ''
    password: 82762002-40f5-4775-a9ad-3da97a2772f7
    port: 20000
    server: tw.poke-mon.xyz
    skip-cert-verify: true
    sni: www.bing.com
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国43 | ⬇️ 561KB/s"
    obfs: ''
    obfs-password: ''
    password: 91365a7a-46d7-11ee-a8b9-f23c9164ca5d
    port: 8443
    server: 8725de56-swkhs0-t3o6u7-1osdm.hy2.gotochinatown.net
    skip-cert-verify: true
    sni: 8725de56-swkhs0-t3o6u7-1osdm.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国44 | ⬇️ 624KB/s"
    obfs: ''
    obfs-password: ''
    password: 6e9cdc20-b92a-11ef-973a-f23c913c8d2b
    port: 8443
    server: 6b99ff62-svy9s0-sww7b0-1qwp5.hy2.gotochinatown.net
    skip-cert-verify: true
    sni: 6b99ff62-svy9s0-sww7b0-1qwp5.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1E6\U0001F1FA澳大利亚2 | ⬇️ 5.8MB/s"
    obfs: ''
    obfs-password: ''
    password: 82762002-40f5-4775-a9ad-3da97a2772f7
    port: 20000
    server: aodaliya.poke-mon.xyz
    skip-cert-verify: true
    sni: aodaliya.poke-mon.xyz
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国45 | ⬇️ 517KB/s"
    obfs: ''
    obfs-password: ''
    password: e3975a05-b89e-11ec-a50d-f23c91cfbbc9
    port: 8443
    server: d5d8af9a-svlb40-sxf56z-1f596.hy2.gotochinatown.net
    skip-cert-verify: true
    sni: d5d8af9a-svlb40-sxf56z-1f596.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国46 | ⬇️ 805KB/s"
    obfs: ''
    obfs-password: ''
    password: 31676725-b0fd-fed2-c857-2da8ce5a6e4f
    port: 8443
    server: 47b39173-swexs0-t0dyyh-jjv2.hy2.gotochinatown.net
    skip-cert-verify: true
    sni: 47b39173-swexs0-t0dyyh-jjv2.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F300其他12-VN | ⬇️ 1.0MB/s"
    obfs: ''
    obfs-password: ''
    password: 82762002-40f5-4775-a9ad-3da97a2772f7
    port: 20000
    server: yuenan.poke-mon.xyz
    skip-cert-verify: true
    sni: www.bing.com
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国47 | ⬇️ 920KB/s"
    obfs: ''
    obfs-password: ''
    password: dcccacba-fa44-11ef-8400-f23c9164ca5d
    port: 8443
    server: 01089b68-swd340-sx0fe4-1j6h0.hy2.gotochinatown.net
    skip-cert-verify: true
    sni: 01089b68-swd340-sx0fe4-1j6h0.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国48 | ⬇️ 1.4MB/s"
    obfs: ''
    obfs-password: ''
    password: 91365a7a-46d7-11ee-a8b9-f23c9164ca5d
    port: 8443
    server: 2e026bef-swexs0-t3o6u7-1osdm.hy2.gotochinatown.net
    skip-cert-verify: true
    sni: 2e026bef-swexs0-t3o6u7-1osdm.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国49 | ⬇️ 666KB/s"
    obfs: ''
    obfs-password: ''
    password: 0da8651e-e1f6-11ec-bd7c-f23c913c8d2b
    port: 8443
    server: 49bd66e1-swin40-tcinla-hrtf.hy2.gotochinatown.net
    skip-cert-verify: true
    sni: 49bd66e1-swin40-tcinla-hrtf.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国50 | ⬇️ 718KB/s"
    obfs: ''
    obfs-password: ''
    password: d4018e28-e328-11ed-98a7-f23c913c8d2b
    port: 8443
    server: bdb364cd-swkhs0-sx3h07-1g8k0.hy2.gotochinatown.net
    skip-cert-verify: true
    sni: bdb364cd-swkhs0-sx3h07-1g8k0.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国51 | ⬇️ 856KB/s"
    obfs: ''
    obfs-password: ''
    password: 48c1e014-28d6-11ec-a0fc-f23c913c8d2b
    port: 8443
    server: f11e9c30-swkhs0-sxlsd4-3z3v.hy2.gotochinatown.net
    skip-cert-verify: true
    sni: f11e9c30-swkhs0-sxlsd4-3z3v.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国52 | ⬇️ 746KB/s"
    obfs: ''
    obfs-password: ''
    password: dcccacba-fa44-11ef-8400-f23c9164ca5d
    port: 8443
    server: b5c6e084-swexs0-sx0fe4-1j6h0.hy2.gotochinatown.net
    skip-cert-verify: true
    sni: b5c6e084-swexs0-sx0fe4-1j6h0.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国53 | ⬇️ 1.4MB/s"
    obfs: ''
    obfs-password: ''
    password: 6e9cdc20-b92a-11ef-973a-f23c913c8d2b
    port: 8443
    server: 46347589-swin40-sww7b0-1qwp5.hy2.gotochinatown.net
    skip-cert-verify: true
    sni: 46347589-swin40-sww7b0-1qwp5.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国54 | ⬇️ 583KB/s"
    obfs: ''
    obfs-password: ''
    password: 31676725-b0fd-fed2-c857-2da8ce5a6e4f
    port: 8443
    server: b7d516b4-swd340-t0dyyh-jjv2.hy2.gotochinatown.net
    skip-cert-verify: true
    sni: b7d516b4-swd340-t0dyyh-jjv2.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国55 | ⬇️ 1.1MB/s"
    obfs: ''
    obfs-password: ''
    password: 861726bb-c61a-b0e0-99fa-5ddfdcf21902
    port: 8443
    server: 5387a16a-swexs0-ta5nd4-e06r.hy2.gotochinatown.net
    skip-cert-verify: true
    sni: 5387a16a-swexs0-ta5nd4-e06r.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1F9\U0001F1FC台湾4 | ⬇️ 4.9MB/s"
    obfs: ''
    obfs-password: ''
    password: eaf4d7a9-482f-4d09-9029-691c81748448
    port: 30010
    server: tw2-vds21.anyhk.co
    skip-cert-verify: true
    sni: tw2-vds21.anyhk.co
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国56 | ⬇️ 2.2MB/s"
    obfs: ''
    obfs-password: ''
    password: 85ac1c2d-f3c4-496d-9a01-8d0d84eab17d
    port: 39911
    server: us01.node.cross666.top
    skip-cert-verify: true
    sni: www.yahoo.com
    type: hysteria2
  - name: "\U0001F1F8\U0001F1EC新加坡11 | ⬇️ 1.6MB/s"
    obfs: ''
    obfs-password: ''
    password: 85ac1c2d-f3c4-496d-9a01-8d0d84eab17d
    port: 35726
    server: sg02.node.cross666.top
    skip-cert-verify: true
    sni: mirror.sg.gs
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国57 | ⬇️ 2.2MB/s"
    obfs: ''
    obfs-password: ''
    password: 85ac1c2d-f3c4-496d-9a01-8d0d84eab17d
    port: 39472
    server: us3.node.cross666.top
    skip-cert-verify: true
    sni: www.yahoo.com
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国58 | ⬇️ 930KB/s"
    obfs: ''
    obfs-password: ''
    password: 48c1e014-28d6-11ec-a0fc-f23c913c8d2b
    port: 8443
    server: 7c8eb5c8-sw3ts0-sxlsd4-3z3v.hy2.gotochinatown.net
    skip-cert-verify: true
    sni: 7c8eb5c8-sw3ts0-sxlsd4-3z3v.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国59 | ⬇️ 1.9MB/s"
    obfs: ''
    obfs-password: ''
    password: 85ac1c2d-f3c4-496d-9a01-8d0d84eab17d
    port: 36807
    server: us4.node.cross666.top
    skip-cert-verify: true
    sni: www.yahoo.com
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国60 | ⬇️ 1.5MB/s"
    obfs: ''
    obfs-password: ''
    password: 6e9cdc20-b92a-11ef-973a-f23c913c8d2b
    port: 8443
    server: 9d5cde75-sw3ts0-sww7b0-1qwp5.hy2.gotochinatown.net
    skip-cert-verify: true
    sni: 9d5cde75-sw3ts0-sww7b0-1qwp5.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1EB\U0001F1F7法国4 | ⬇️ 2.0MB/s"
    obfs: ''
    obfs-password: ''
    password: 85ac1c2d-f3c4-496d-9a01-8d0d84eab17d
    port: 35689
    server: fr1.node.cross666.top
    skip-cert-verify: true
    sni: ubuntu.mirrors.ovh.net
    type: hysteria2
  - name: "\U0001F300其他13-VN | ⬇️ 2.2MB/s"
    obfs: ''
    obfs-password: ''
    password: 85ac1c2d-f3c4-496d-9a01-8d0d84eab17d
    port: 38311
    server: vn1r.node.cross666.top
    skip-cert-verify: true
    sni: mirror.sg.gs
    type: hysteria2
  - name: "\U0001F1F7\U0001F1FA俄罗斯6 | ⬇️ 2.1MB/s"
    obfs: ''
    obfs-password: ''
    password: 85ac1c2d-f3c4-496d-9a01-8d0d84eab17d
    port: 36872
    server: rua.node.cross666.top
    skip-cert-verify: true
    sni: mirror.hyperdedic.ru
    type: hysteria2
  - name: "\U0001F1F7\U0001F1FA俄罗斯7 | ⬇️ 2.0MB/s"
    obfs: ''
    obfs-password: ''
    password: 85ac1c2d-f3c4-496d-9a01-8d0d84eab17d
    port: 37779
    server: rua.node.cross666.top
    skip-cert-verify: true
    sni: mirror.hyperdedic.ru
    type: hysteria2
  - name: "\U0001F300其他14-TR | ⬇️ 1.8MB/s"
    obfs: ''
    obfs-password: ''
    password: 85ac1c2d-f3c4-496d-9a01-8d0d84eab17d
    port: 38318
    server: tr01.node.cross666.top
    skip-cert-verify: true
    sni: mirror.dogado.de
    type: hysteria2
  - name: "\U0001F1F7\U0001F1FA俄罗斯8 | ⬇️ 2.3MB/s"
    obfs: ''
    obfs-password: ''
    password: abef57a5-ac14-4356-ba5d-c3fdc85df62b
    port: 38126
    server: rua.node.cross666.top
    skip-cert-verify: true
    sni: mirror.hyperdedic.ru
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国61 | ⬇️ 2.0MB/s"
    obfs: ''
    obfs-password: ''
    password: f9153abe-88d9-4ad7-a3a7-01eff2790b3e
    port: 37478
    server: us4.node.cross666.top
    skip-cert-verify: true
    sni: www.yahoo.com
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国62 | ⬇️ 1.1MB/s"
    obfs: ''
    obfs-password: ''
    password: abef57a5-ac14-4356-ba5d-c3fdc85df62b
    port: 38220
    server: us3.node.cross666.top
    skip-cert-verify: true
    sni: www.yahoo.com
    type: hysteria2
  - name: "\U0001F1F8\U0001F1EC新加坡12 | ⬇️ 1.4MB/s"
    obfs: ''
    obfs-password: ''
    password: abef57a5-ac14-4356-ba5d-c3fdc85df62b
    port: 36832
    server: sg02.node.cross666.top
    skip-cert-verify: true
    sni: mirror.sg.gs
    type: hysteria2
  - name: "\U0001F1EB\U0001F1F7法国5 | ⬇️ 1.6MB/s"
    obfs: ''
    obfs-password: ''
    password: abef57a5-ac14-4356-ba5d-c3fdc85df62b
    port: 36011
    server: fr1.node.cross666.top
    skip-cert-verify: true
    sni: ubuntu.mirrors.ovh.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国63 | ⬇️ 578KB/s"
    obfs: ''
    obfs-password: ''
    password: abef57a5-ac14-4356-ba5d-c3fdc85df62b
    port: 38970
    server: us3.node.cross666.top
    skip-cert-verify: true
    sni: www.yahoo.com
    type: hysteria2
  - name: "\U0001F300其他15-VN | ⬇️ 1.6MB/s"
    obfs: ''
    obfs-password: ''
    password: ab4c9bf8-6bed-4dac-ba4e-461d8f589eda
    port: 30220
    server: vn1.xiaoliyu.us
    skip-cert-verify: true
    sni: vn1.xiaoliyu.us
    type: hysteria2
  - name: "\U0001F1F7\U0001F1FA俄罗斯9 | ⬇️ 1.7MB/s"
    obfs: ''
    obfs-password: ''
    password: 85ac1c2d-f3c4-496d-9a01-8d0d84eab17d
    port: 38200
    server: ru2.node.cross666.top
    skip-cert-verify: true
    sni: mirror.hyperdedic.ru
    type: hysteria2
  - name: "\U0001F1EB\U0001F1F7法国6 | ⬇️ 1.7MB/s"
    obfs: ''
    obfs-password: ''
    password: c6d93228-6393-41ec-8403-da852407390a
    port: 39794
    server: fr1.node.cross666.top
    skip-cert-verify: true
    sni: ubuntu.mirrors.ovh.net
    type: hysteria2
  - name: "\U0001F1F7\U0001F1FA俄罗斯10 | ⬇️ 1.9MB/s"
    obfs: ''
    obfs-password: ''
    password: abef57a5-ac14-4356-ba5d-c3fdc85df62b
    port: 35025
    server: ru2.node.cross666.top
    skip-cert-verify: true
    sni: mirror.hyperdedic.ru
    type: hysteria2
  - name: "\U0001F1F7\U0001F1FA俄罗斯11 | ⬇️ 2.3MB/s"
    obfs: ''
    obfs-password: ''
    password: 5479f2d5-a0c4-437e-95c9-f79a0c485af1
    port: 39461
    server: ru1.node.cross666.top
    skip-cert-verify: true
    sni: mirror.hyperdedic.ru
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国64 | ⬇️ 1.7MB/s"
    obfs: ''
    obfs-password: ''
    password: 5479f2d5-a0c4-437e-95c9-f79a0c485af1
    port: 37864
    server: us3.node.cross666.top
    skip-cert-verify: true
    sni: www.yahoo.com
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国65 | ⬇️ 1.6MB/s"
    obfs: ''
    obfs-password: ''
    password: 5479f2d5-a0c4-437e-95c9-f79a0c485af1
    port: 39398
    server: us4.node.cross666.top
    skip-cert-verify: true
    sni: www.yahoo.com
    type: hysteria2
  - name: "\U0001F1F7\U0001F1FA俄罗斯12 | ⬇️ 2.2MB/s"
    obfs: ''
    obfs-password: ''
    password: f9153abe-88d9-4ad7-a3a7-01eff2790b3e
    port: 38856
    server: rua.node.cross666.top
    skip-cert-verify: true
    sni: mirror.hyperdedic.ru
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国66 | ⬇️ 1.3MB/s"
    obfs: ''
    obfs-password: ''
    password: c6d93228-6393-41ec-8403-da852407390a
    port: 37137
    server: us4.node.cross666.top
    skip-cert-verify: true
    sni: www.yahoo.com
    type: hysteria2
  - name: "\U0001F300其他16-VN | ⬇️ 1.2MB/s"
    obfs: ''
    obfs-password: ''
    password: 5479f2d5-a0c4-437e-95c9-f79a0c485af1
    port: 36689
    server: vn1r.node.cross666.top
    skip-cert-verify: true
    sni: mirror.sg.gs
    type: hysteria2
  - name: "\U0001F300其他17-VN | ⬇️ 1.9MB/s"
    obfs: ''
    obfs-password: ''
    password: f9153abe-88d9-4ad7-a3a7-01eff2790b3e
    port: 36721
    server: vn1r.node.cross666.top
    skip-cert-verify: true
    sni: mirror.sg.gs
    type: hysteria2
  - name: "\U0001F300其他18-VN | ⬇️ 1.4MB/s"
    obfs: ''
    obfs-password: ''
    password: c6d93228-6393-41ec-8403-da852407390a
    port: 36299
    server: vn1r.node.cross666.top
    skip-cert-verify: true
    sni: mirror.sg.gs
    type: hysteria2
  - name: "\U0001F1F7\U0001F1FA俄罗斯13 | ⬇️ 2.1MB/s"
    obfs: ''
    obfs-password: ''
    password: c6d93228-6393-41ec-8403-da852407390a
    port: 37559
    server: ru1.node.cross666.top
    skip-cert-verify: true
    sni: mirror.hyperdedic.ru
    type: hysteria2
  - name: "\U0001F300其他19-TR | ⬇️ 1.9MB/s"
    obfs: ''
    obfs-password: ''
    password: f9153abe-88d9-4ad7-a3a7-01eff2790b3e
    port: 38729
    server: tr01.node.cross666.top
    skip-cert-verify: true
    sni: mirror.dogado.de
    type: hysteria2
  - name: "\U0001F1F8\U0001F1EC新加坡13 | ⬇️ 1.8MB/s"
    obfs: ''
    obfs-password: ''
    password: c6d93228-6393-41ec-8403-da852407390a
    port: 37624
    server: sg02.node.cross666.top
    skip-cert-verify: true
    sni: mirror.sg.gs
    type: hysteria2
  - name: "\U0001F1F7\U0001F1FA俄罗斯14 | ⬇️ 1.3MB/s"
    obfs: ''
    obfs-password: ''
    password: c6d93228-6393-41ec-8403-da852407390a
    port: 37747
    server: ru2.node.cross666.top
    skip-cert-verify: true
    sni: mirror.hyperdedic.ru
    type: hysteria2
  - name: "\U0001F1EB\U0001F1F7法国7 | ⬇️ 2.3MB/s"
    obfs: ''
    obfs-password: ''
    password: 5479f2d5-a0c4-437e-95c9-f79a0c485af1
    port: 36024
    server: fr1.node.cross666.top
    skip-cert-verify: true
    sni: ubuntu.mirrors.ovh.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国67 | ⬇️ 2.3MB/s"
    obfs: ''
    obfs-password: ''
    password: f9153abe-88d9-4ad7-a3a7-01eff2790b3e
    port: 36333
    server: us4.node.cross666.top
    skip-cert-verify: true
    sni: www.yahoo.com
    type: hysteria2
  - name: "\U0001F1F7\U0001F1FA俄罗斯15 | ⬇️ 1.6MB/s"
    obfs: ''
    obfs-password: ''
    password: c6d93228-6393-41ec-8403-da852407390a
    port: 38364
    server: ru1.node.cross666.top
    skip-cert-verify: true
    sni: mirror.hyperdedic.ru
    type: hysteria2
  - name: "\U0001F1F7\U0001F1FA俄罗斯16 | ⬇️ 1.9MB/s"
    obfs: ''
    obfs-password: ''
    password: 5479f2d5-a0c4-437e-95c9-f79a0c485af1
    port: 35937
    server: ru2.node.cross666.top
    skip-cert-verify: true
    sni: mirror.hyperdedic.ru
    type: hysteria2
  - name: "\U0001F1EB\U0001F1F7法国8 | ⬇️ 2.2MB/s"
    obfs: ''
    obfs-password: ''
    password: f9153abe-88d9-4ad7-a3a7-01eff2790b3e
    port: 35177
    server: fr1.node.cross666.top
    skip-cert-verify: true
    sni: ubuntu.mirrors.ovh.net
    type: hysteria2
  - name: "\U0001F300其他20-VN | ⬇️ 1.2MB/s"
    obfs: ''
    obfs-password: ''
    password: c6d93228-6393-41ec-8403-da852407390a
    port: 37645
    server: vn1r.node.cross666.top
    skip-cert-verify: true
    sni: mirror.sg.gs
    type: hysteria2
  - name: "\U0001F1EB\U0001F1F7法国9 | ⬇️ 1.9MB/s"
    obfs: ''
    obfs-password: ''
    password: c6d93228-6393-41ec-8403-da852407390a
    port: 38470
    server: fr1.node.cross666.top
    skip-cert-verify: true
    sni: ubuntu.mirrors.ovh.net
    type: hysteria2
  - name: "\U0001F1F7\U0001F1FA俄罗斯17 | ⬇️ 2.1MB/s"
    obfs: ''
    obfs-password: ''
    password: f9153abe-88d9-4ad7-a3a7-01eff2790b3e
    port: 38734
    server: rua.node.cross666.top
    skip-cert-verify: true
    sni: mirror.hyperdedic.ru
    type: hysteria2
  - name: "\U0001F300其他21-TR | ⬇️ 870KB/s"
    obfs: ''
    obfs-password: ''
    password: c6d93228-6393-41ec-8403-da852407390a
    port: 36708
    server: tr01.node.cross666.top
    skip-cert-verify: true
    sni: mirror.dogado.de
    type: hysteria2
  - name: "\U0001F1F7\U0001F1FA俄罗斯18 | ⬇️ 1.4MB/s"
    obfs: ''
    obfs-password: ''
    password: c6d93228-6393-41ec-8403-da852407390a
    port: 35790
    server: ru2.node.cross666.top
    skip-cert-verify: true
    sni: mirror.hyperdedic.ru
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国68 | ⬇️ 1.1MB/s"
    obfs: ''
    obfs-password: ''
    password: c6d93228-6393-41ec-8403-da852407390a
    port: 36434
    server: us4.node.cross666.top
    skip-cert-verify: true
    sni: www.yahoo.com
    type: hysteria2
  - name: "\U0001F1F7\U0001F1FA俄罗斯19 | ⬇️ 1.9MB/s"
    obfs: ''
    obfs-password: ''
    password: f9153abe-88d9-4ad7-a3a7-01eff2790b3e
    port: 36642
    server: ru2.node.cross666.top
    skip-cert-verify: true
    sni: mirror.hyperdedic.ru
    type: hysteria2
  - name: "\U0001F1F8\U0001F1EC新加坡14 | ⬇️ 1.1MB/s"
    obfs: ''
    obfs-password: ''
    password: f9153abe-88d9-4ad7-a3a7-01eff2790b3e
    port: 38077
    server: sg02.node.cross666.top
    skip-cert-verify: true
    sni: mirror.sg.gs
    type: hysteria2
  - name: "\U0001F300其他22-VN | ⬇️ 770KB/s"
    obfs: ''
    obfs-password: ''
    password: c6d93228-6393-41ec-8403-da852407390a
    port: 36974
    server: vn1r.node.cross666.top
    skip-cert-verify: true
    sni: mirror.sg.gs
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国69 | ⬇️ 711KB/s"
    obfs: ''
    obfs-password: ''
    password: 3fa7157d-efe8-832e-9b3c-582d78efbc52
    port: 8443
    server: b9b5771f-sw9ds0-tf70jh-vm13.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: b9b5771f-sw9ds0-tf70jh-vm13.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国70 | ⬇️ 618KB/s"
    obfs: ''
    obfs-password: ''
    password: affe7124-c118-11ef-b6b2-f23c9164ca5d
    port: 8443
    server: efc7cb29-sw9ds0-td1w5f-1t3cz.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: efc7cb29-sw9ds0-td1w5f-1t3cz.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国71 | ⬇️ 539KB/s"
    obfs: ''
    obfs-password: ''
    password: b79d79ae-8bce-11ef-a2b8-f23c9164ca5d
    port: 8443
    server: 01825bf2-sw9ds0-t67sv3-1snfs.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 01825bf2-sw9ds0-t67sv3-1snfs.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国72 | ⬇️ 1.7MB/s"
    obfs: ''
    obfs-password: ''
    password: c6d93228-6393-41ec-8403-da852407390a
    port: 36041
    server: us3.node.cross666.top
    skip-cert-verify: true
    sni: www.yahoo.com
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国73 | ⬇️ 581KB/s"
    obfs: ''
    obfs-password: ''
    password: dcccacba-fa44-11ef-8400-f23c9164ca5d
    port: 8443
    server: a1fa790c-sw9ds0-sx0fe4-1j6h0.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: a1fa790c-sw9ds0-sx0fe4-1j6h0.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国74 | ⬇️ 534KB/s"
    obfs: ''
    obfs-password: ''
    password: 8e0f8ef2-5fe3-11ec-a8bf-f23c91cfbbc9
    port: 8443
    server: 6ce693ba-sw5og0-swdpud-duku.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 6ce693ba-sw5og0-swdpud-duku.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国75 | ⬇️ 587KB/s"
    obfs: ''
    obfs-password: ''
    password: 48c1e014-28d6-11ec-a0fc-f23c913c8d2b
    port: 8443
    server: 81af9d54-sw7j40-sxlsd4-3z3v.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 81af9d54-sw7j40-sxlsd4-3z3v.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国76 | ⬇️ 617KB/s"
    obfs: ''
    obfs-password: ''
    password: 93fb69fc-77cf-11ee-85ee-f23c91369f2d
    port: 8443
    server: db34d2a0-sw3ts0-t12cnj-1ol97.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: db34d2a0-sw3ts0-t12cnj-1ol97.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国77 | ⬇️ 655KB/s"
    obfs: ''
    obfs-password: ''
    password: ef18aaca-4711-11ec-a8bf-f23c91cfbbc9
    port: 8443
    server: 459185ab-sw5og0-swa8xy-laev.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 459185ab-sw5og0-swa8xy-laev.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国78 | ⬇️ 585KB/s"
    obfs: ''
    obfs-password: ''
    password: 6e9cdc20-b92a-11ef-973a-f23c913c8d2b
    port: 8443
    server: ac4417c1-sw7j40-sww7b0-1qwp5.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: ac4417c1-sw7j40-sww7b0-1qwp5.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国79 | ⬇️ 642KB/s"
    obfs: ''
    obfs-password: ''
    password: 93fb69fc-77cf-11ee-85ee-f23c91369f2d
    port: 8443
    server: bf24a574-sw7j40-t12cnj-1ol97.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: bf24a574-sw7j40-t12cnj-1ol97.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国80 | ⬇️ 683KB/s"
    obfs: ''
    obfs-password: ''
    password: 93fb69fc-77cf-11ee-85ee-f23c91369f2d
    port: 8443
    server: d4e110b1-sw04g0-t12cnj-1ol97.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: d4e110b1-sw04g0-t12cnj-1ol97.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国81 | ⬇️ 532KB/s"
    obfs: ''
    obfs-password: ''
    password: 6e9cdc20-b92a-11ef-973a-f23c913c8d2b
    port: 8443
    server: 6999f3e7-sw5og0-sww7b0-1qwp5.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 6999f3e7-sw5og0-sww7b0-1qwp5.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国82 | ⬇️ 643KB/s"
    obfs: ''
    obfs-password: ''
    password: e3975a05-b89e-11ec-a50d-f23c91cfbbc9
    port: 8443
    server: 045fb6b5-sw5og0-sxf56z-1f596.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 045fb6b5-sw5og0-sxf56z-1f596.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国83 | ⬇️ 654KB/s"
    obfs: ''
    obfs-password: ''
    password: ef18aaca-4711-11ec-a8bf-f23c91cfbbc9
    port: 8443
    server: eab236f6-sw7j40-swa8xy-laev.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: eab236f6-sw7j40-swa8xy-laev.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1F8\U0001F1EC新加坡15 | ⬇️ 1.7MB/s"
    obfs: ''
    obfs-password: ''
    password: c6d93228-6393-41ec-8403-da852407390a
    port: 38375
    server: sg02.node.cross666.top
    skip-cert-verify: true
    sni: mirror.sg.gs
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国84 | ⬇️ 528KB/s"
    obfs: ''
    obfs-password: ''
    password: 48c1e014-28d6-11ec-a0fc-f23c913c8d2b
    port: 8443
    server: 14b91eaf-sw5og0-sxlsd4-3z3v.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 14b91eaf-sw5og0-sxlsd4-3z3v.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国85 | ⬇️ 511KB/s"
    obfs: ''
    obfs-password: ''
    password: 8e0f8ef2-5fe3-11ec-a8bf-f23c91cfbbc9
    port: 8443
    server: e7d4eee3-sw7j40-swdpud-duku.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: e7d4eee3-sw7j40-swdpud-duku.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国86 | ⬇️ 741KB/s"
    obfs: ''
    obfs-password: ''
    password: 06b2ac52-fcc6-11ec-bb74-f23c9164ca5d
    port: 8443
    server: 0675b3a0-sw7j40-sznzxg-1jfvb.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 0675b3a0-sw7j40-sznzxg-1jfvb.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国87 | ⬇️ 709KB/s"
    obfs: ''
    obfs-password: ''
    password: e3975a05-b89e-11ec-a50d-f23c91cfbbc9
    port: 8443
    server: 471eceba-sw7j40-sxf56z-1f596.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 471eceba-sw7j40-sxf56z-1f596.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国88 | ⬇️ 607KB/s"
    obfs: ''
    obfs-password: ''
    password: e3975a05-b89e-11ec-a50d-f23c91cfbbc9
    port: 8443
    server: a825bc64-sw3ts0-sxf56z-1f596.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: a825bc64-sw3ts0-sxf56z-1f596.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国89 | ⬇️ 883KB/s"
    obfs: ''
    obfs-password: ''
    password: 06b2ac52-fcc6-11ec-bb74-f23c9164ca5d
    port: 8443
    server: cfdf7114-sw3ts0-sznzxg-1jfvb.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: cfdf7114-sw3ts0-sznzxg-1jfvb.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1F7\U0001F1FA俄罗斯20 | ⬇️ 2.2MB/s"
    obfs: ''
    obfs-password: ''
    password: abef57a5-ac14-4356-ba5d-c3fdc85df62b
    port: 35904
    server: ru1.node.cross666.top
    skip-cert-verify: true
    sni: mirror.hyperdedic.ru
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国90 | ⬇️ 1.6MB/s"
    obfs: ''
    obfs-password: ''
    password: abef57a5-ac14-4356-ba5d-c3fdc85df62b
    port: 36566
    server: us3.node.cross666.top
    skip-cert-verify: true
    sni: www.yahoo.com
    type: hysteria2
  - name: "\U0001F300其他23-TR | ⬇️ 2.0MB/s"
    obfs: ''
    obfs-password: ''
    password: abef57a5-ac14-4356-ba5d-c3fdc85df62b
    port: 39737
    server: tr01.node.cross666.top
    skip-cert-verify: true
    sni: mirror.dogado.de
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国91 | ⬇️ 864KB/s"
    obfs: ''
    obfs-password: ''
    password: ef18aaca-4711-11ec-a8bf-f23c91cfbbc9
    port: 8443
    server: 031a26ec-sw3ts0-swa8xy-laev.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 031a26ec-sw3ts0-swa8xy-laev.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1F7\U0001F1FA俄罗斯21 | ⬇️ 2.0MB/s"
    obfs: ''
    obfs-password: ''
    password: abef57a5-ac14-4356-ba5d-c3fdc85df62b
    port: 38313
    server: ru2.node.cross666.top
    skip-cert-verify: true
    sni: mirror.hyperdedic.ru
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国92 | ⬇️ 1.2MB/s"
    obfs: ''
    obfs-password: ''
    password: abef57a5-ac14-4356-ba5d-c3fdc85df62b
    port: 36128
    server: us4.node.cross666.top
    skip-cert-verify: true
    sni: www.yahoo.com
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国93 | ⬇️ 638KB/s"
    obfs: ''
    obfs-password: ''
    password: 336501b6-51d2-11ee-a993-f23c9164ca5d
    port: 8443
    server: 370d7219-swb8g0-t8kd6j-1c9em.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 370d7219-swb8g0-t8kd6j-1c9em.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国94 | ⬇️ 664KB/s"
    obfs: ''
    obfs-password: ''
    password: 0da8651e-e1f6-11ec-bd7c-f23c913c8d2b
    port: 8443
    server: c417f892-swb8g0-tcinla-hrtf.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: c417f892-swb8g0-tcinla-hrtf.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国95 | ⬇️ 727KB/s"
    obfs: ''
    obfs-password: ''
    password: 384d1b42-655f-11ed-a8bf-f23c91cfbbc9
    port: 8443
    server: bc749975-swb8g0-sw9kdi-11p9g.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: bc749975-swb8g0-sw9kdi-11p9g.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国96 | ⬇️ 608KB/s"
    obfs: ''
    obfs-password: ''
    password: e3975a05-b89e-11ec-a50d-f23c91cfbbc9
    port: 8443
    server: a3b0070d-swb8g0-sxf56z-1f596.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: a3b0070d-swb8g0-sxf56z-1f596.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国97 | ⬇️ 584KB/s"
    obfs: ''
    obfs-password: ''
    password: 91365a7a-46d7-11ee-a8b9-f23c9164ca5d
    port: 8443
    server: b207d030-swb8g0-t3o6u7-1osdm.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: b207d030-swb8g0-t3o6u7-1osdm.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国98 | ⬇️ 627KB/s"
    obfs: ''
    obfs-password: ''
    password: 6e9cdc20-b92a-11ef-973a-f23c913c8d2b
    port: 8443
    server: 2598c9cf-swb8g0-sww7b0-1qwp5.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 2598c9cf-swb8g0-sww7b0-1qwp5.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国99 | ⬇️ 522KB/s"
    obfs: ''
    obfs-password: ''
    password: 06b2ac52-fcc6-11ec-bb74-f23c9164ca5d
    port: 8443
    server: 9ee5e960-swb8g0-sznzxg-1jfvb.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 9ee5e960-swb8g0-sznzxg-1jfvb.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国100 | ⬇️ 504KB/s"
    obfs: ''
    obfs-password: ''
    password: 8e0f8ef2-5fe3-11ec-a8bf-f23c91cfbbc9
    port: 8443
    server: 14917b67-swb8g0-swdpud-duku.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 14917b67-swb8g0-swdpud-duku.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国101 | ⬇️ 721KB/s"
    obfs: ''
    obfs-password: ''
    password: 861726bb-c61a-b0e0-99fa-5ddfdcf21902
    port: 8443
    server: 889ff630-swb8g0-ta5nd4-e06r.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 889ff630-swb8g0-ta5nd4-e06r.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国102 | ⬇️ 765KB/s"
    obfs: ''
    obfs-password: ''
    password: 34f0cf42-0f8a-11ec-a8bf-f23c91cfbbc9
    port: 8443
    server: 359805eb-swb8g0-szdere-155d9.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 359805eb-swb8g0-szdere-155d9.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国103 | ⬇️ 2.0MB/s"
    obfs: ''
    obfs-password: ''
    password: f9153abe-88d9-4ad7-a3a7-01eff2790b3e
    port: 39147
    server: us3.node.cross666.top
    skip-cert-verify: true
    sni: www.yahoo.com
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国104 | ⬇️ 668KB/s"
    obfs: ''
    obfs-password: ''
    password: ef18aaca-4711-11ec-a8bf-f23c91cfbbc9
    port: 8443
    server: 648d4621-svukg0-swa8xy-laev.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 648d4621-svukg0-swa8xy-laev.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国105 | ⬇️ 1.6MB/s"
    obfs: ''
    obfs-password: ''
    password: 6e9cdc20-b92a-11ef-973a-f23c913c8d2b
    port: 8443
    server: 41490f6a-svsps0-sww7b0-1qwp5.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 41490f6a-svsps0-sww7b0-1qwp5.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国106 | ⬇️ 546KB/s"
    obfs: ''
    obfs-password: ''
    password: 93fb69fc-77cf-11ee-85ee-f23c91369f2d
    port: 8443
    server: 3d6f7a99-svukg0-t12cnj-1ol97.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 3d6f7a99-svukg0-t12cnj-1ol97.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国107 | ⬇️ 561KB/s"
    obfs: ''
    obfs-password: ''
    password: ef18aaca-4711-11ec-a8bf-f23c91cfbbc9
    port: 8443
    server: 626852f0-svwf40-swa8xy-laev.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 626852f0-svwf40-swa8xy-laev.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国108 | ⬇️ 594KB/s"
    obfs: ''
    obfs-password: ''
    password: 6e9cdc20-b92a-11ef-973a-f23c913c8d2b
    port: 8443
    server: 1aeedbcb-svukg0-sww7b0-1qwp5.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 1aeedbcb-svukg0-sww7b0-1qwp5.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国109 | ⬇️ 577KB/s"
    obfs: ''
    obfs-password: ''
    password: 06b2ac52-fcc6-11ec-bb74-f23c9164ca5d
    port: 8443
    server: 7e7a9d72-svukg0-sznzxg-1jfvb.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 7e7a9d72-svukg0-sznzxg-1jfvb.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国110 | ⬇️ 659KB/s"
    obfs: ''
    obfs-password: ''
    password: 06b2ac52-fcc6-11ec-bb74-f23c9164ca5d
    port: 8443
    server: 4aca605f-svwf40-sznzxg-1jfvb.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 4aca605f-svwf40-sznzxg-1jfvb.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国111 | ⬇️ 725KB/s"
    obfs: ''
    obfs-password: ''
    password: 48c1e014-28d6-11ec-a0fc-f23c913c8d2b
    port: 8443
    server: 2488e8d7-svukg0-sw0dp4-3z3v.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 2488e8d7-svukg0-sw0dp4-3z3v.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国112 | ⬇️ 543KB/s"
    obfs: ''
    obfs-password: ''
    password: e3975a05-b89e-11ec-a50d-f23c91cfbbc9
    port: 8443
    server: 853ea9e9-svwf40-sxf56z-1f596.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 853ea9e9-svwf40-sxf56z-1f596.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国113 | ⬇️ 6.0MB/s"
    obfs: ''
    obfs-password: ''
    password: 0A1DDFE6-A16A-43FA-A6E3-70EDE4EA61C3
    port: 7020
    server: us4.dexlos.com
    skip-cert-verify: false
    sni: us4.dexlos.com
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国114 | ⬇️ 770KB/s"
    obfs: ''
    obfs-password: ''
    password: 31676725-b0fd-fed2-c857-2da8ce5a6e4f
    port: 8443
    server: ff205e00-swin40-t0dyyh-jjv2.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: ff205e00-swin40-t0dyyh-jjv2.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国115 | ⬇️ 783KB/s"
    obfs: ''
    obfs-password: ''
    password: 48c1e014-28d6-11ec-a0fc-f23c913c8d2b
    port: 8443
    server: 2f0a18df-swexs0-sxlsd4-3z3v.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 2f0a18df-swexs0-sxlsd4-3z3v.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1F7\U0001F1FA俄罗斯22 | ⬇️ 1.9MB/s"
    obfs: ''
    obfs-password: ''
    password: abef57a5-ac14-4356-ba5d-c3fdc85df62b
    port: 35534
    server: rua.node.cross666.top
    skip-cert-verify: true
    sni: mirror.hyperdedic.ru
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国116 | ⬇️ 641KB/s"
    obfs: ''
    obfs-password: ''
    password: dcccacba-fa44-11ef-8400-f23c9164ca5d
    port: 8443
    server: 16d5db0d-swkhs0-sx0fe4-1j6h0.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 16d5db0d-swkhs0-sx0fe4-1j6h0.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国117 | ⬇️ 923KB/s"
    obfs: ''
    obfs-password: ''
    password: 48c1e014-28d6-11ec-a0fc-f23c913c8d2b
    port: 8443
    server: ee6a9d89-swd340-sxlsd4-3z3v.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: ee6a9d89-swd340-sxlsd4-3z3v.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1F7\U0001F1FA俄罗斯23 | ⬇️ 2.1MB/s"
    obfs: ''
    obfs-password: ''
    password: f9153abe-88d9-4ad7-a3a7-01eff2790b3e
    port: 36068
    server: rua.node.cross666.top
    skip-cert-verify: true
    sni: mirror.hyperdedic.ru
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国118 | ⬇️ 654KB/s"
    obfs: ''
    obfs-password: ''
    password: b79d79ae-8bce-11ef-a2b8-f23c9164ca5d
    port: 8443
    server: 7065741c-swd340-t67sv3-1snfs.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 7065741c-swd340-t67sv3-1snfs.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F300其他24-TR | ⬇️ 2.2MB/s"
    obfs: ''
    obfs-password: ''
    password: f9153abe-88d9-4ad7-a3a7-01eff2790b3e
    port: 37795
    server: tr01.node.cross666.top
    skip-cert-verify: true
    sni: mirror.dogado.de
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国119 | ⬇️ 640KB/s"
    obfs: ''
    obfs-password: ''
    password: 06b2ac52-fcc6-11ec-bb74-f23c9164ca5d
    port: 8443
    server: c367281f-swexs0-sznzxg-1jfvb.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: c367281f-swexs0-sznzxg-1jfvb.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国120 | ⬇️ 663KB/s"
    obfs: ''
    obfs-password: ''
    password: d4018e28-e328-11ed-98a7-f23c913c8d2b
    port: 8443
    server: 9b454679-swin40-sx3h07-1g8k0.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 9b454679-swin40-sx3h07-1g8k0.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国121 | ⬇️ 636KB/s"
    obfs: ''
    obfs-password: ''
    password: 6e9cdc20-b92a-11ef-973a-f23c913c8d2b
    port: 8443
    server: 47e6484a-swd340-sww7b0-1qwp5.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 47e6484a-swd340-sww7b0-1qwp5.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国122 | ⬇️ 611KB/s"
    obfs: ''
    obfs-password: ''
    password: affe7124-c118-11ef-b6b2-f23c9164ca5d
    port: 8443
    server: a65dad8f-swexs0-td1w5f-1t3cz.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: a65dad8f-swexs0-td1w5f-1t3cz.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国123 | ⬇️ 546KB/s"
    obfs: ''
    obfs-password: ''
    password: 336501b6-51d2-11ee-a993-f23c9164ca5d
    port: 8443
    server: 9b686548-swkhs0-t8kd6j-1c9em.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 9b686548-swkhs0-t8kd6j-1c9em.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国124 | ⬇️ 519KB/s"
    obfs: ''
    obfs-password: ''
    password: affe7124-c118-11ef-b6b2-f23c9164ca5d
    port: 8443
    server: 95ca7861-swd340-td1w5f-1t3cz.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 95ca7861-swd340-td1w5f-1t3cz.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国125 | ⬇️ 518KB/s"
    obfs: ''
    obfs-password: ''
    password: 384d1b42-655f-11ed-a8bf-f23c91cfbbc9
    port: 8443
    server: b46251be-swexs0-sxuzn1-11p9g.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: b46251be-swexs0-sxuzn1-11p9g.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国126 | ⬇️ 631KB/s"
    obfs: ''
    obfs-password: ''
    password: 06b2ac52-fcc6-11ec-bb74-f23c9164ca5d
    port: 8443
    server: 477ed50b-swkhs0-sznzxg-1jfvb.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 477ed50b-swkhs0-sznzxg-1jfvb.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国127 | ⬇️ 511KB/s"
    obfs: ''
    obfs-password: ''
    password: cfe61764-0004-11f0-a910-f23c9164ca5d
    port: 8443
    server: a3385fc5-swd340-sx9j1n-1timk.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: a3385fc5-swd340-sx9j1n-1timk.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国128 | ⬇️ 595KB/s"
    obfs: ''
    obfs-password: ''
    password: b79d79ae-8bce-11ef-a2b8-f23c9164ca5d
    port: 8443
    server: 4a7cc09c-swin40-t67sv3-1snfs.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 4a7cc09c-swin40-t67sv3-1snfs.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国129 | ⬇️ 506KB/s"
    obfs: ''
    obfs-password: ''
    password: 7d7c111e-fbc1-11ef-ab5f-f23c913c8d2b
    port: 8443
    server: 58e44587-swkhs0-sxhurg-1th8j.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 58e44587-swkhs0-sxhurg-1th8j.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国130 | ⬇️ 584KB/s"
    obfs: ''
    obfs-password: ''
    password: 34f0cf42-0f8a-11ec-a8bf-f23c91cfbbc9
    port: 8443
    server: 94e6cde3-swd340-szdere-155d9.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 94e6cde3-swd340-szdere-155d9.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国131 | ⬇️ 656KB/s"
    obfs: ''
    obfs-password: ''
    password: e3975a05-b89e-11ec-a50d-f23c91cfbbc9
    port: 8443
    server: 5c7931d5-swkhs0-sxf56z-1f596.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 5c7931d5-swkhs0-sxf56z-1f596.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国132 | ⬇️ 506KB/s"
    obfs: ''
    obfs-password: ''
    password: bb85e074-b0c2-11ea-ad28-f23c913c8d2b
    port: 8443
    server: ba50c9e3-swkhs0-sx9mz9-m0b9.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: ba50c9e3-swkhs0-sx9mz9-m0b9.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国133 | ⬇️ 554KB/s"
    obfs: ''
    obfs-password: ''
    password: cfe61764-0004-11f0-a910-f23c9164ca5d
    port: 8443
    server: 8254f960-swin40-sx9j1n-1timk.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 8254f960-swin40-sx9j1n-1timk.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国134 | ⬇️ 754KB/s"
    obfs: ''
    obfs-password: ''
    password: 31676725-b0fd-fed2-c857-2da8ce5a6e4f
    port: 8443
    server: af4f9bf4-swkhs0-t0dyyh-jjv2.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: af4f9bf4-swkhs0-t0dyyh-jjv2.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国135 | ⬇️ 540KB/s"
    obfs: ''
    obfs-password: ''
    password: cfe61764-0004-11f0-a910-f23c9164ca5d
    port: 8443
    server: 491ec6da-swkhs0-sx9j1n-1timk.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 491ec6da-swkhs0-sx9j1n-1timk.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国136 | ⬇️ 679KB/s"
    obfs: ''
    obfs-password: ''
    password: 3fa7157d-efe8-832e-9b3c-582d78efbc52
    port: 8443
    server: ae3e3106-swkhs0-tf70jh-vm13.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: ae3e3106-swkhs0-tf70jh-vm13.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国137 | ⬇️ 748KB/s"
    obfs: ''
    obfs-password: ''
    password: 336501b6-51d2-11ee-a993-f23c9164ca5d
    port: 8443
    server: 8368a39a-swexs0-t8kd6j-1c9em.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 8368a39a-swexs0-t8kd6j-1c9em.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国138 | ⬇️ 680KB/s"
    obfs: ''
    obfs-password: ''
    password: 91365a7a-46d7-11ee-a8b9-f23c9164ca5d
    port: 8443
    server: 0a689679-swd340-t3o6u7-1osdm.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 0a689679-swd340-t3o6u7-1osdm.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国139 | ⬇️ 661KB/s"
    obfs: ''
    obfs-password: ''
    password: 8e0f8ef2-5fe3-11ec-a8bf-f23c91cfbbc9
    port: 8443
    server: ce1b755e-swkhs0-t14dgl-duku.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: ce1b755e-swkhs0-t14dgl-duku.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国140 | ⬇️ 763KB/s"
    obfs: ''
    obfs-password: ''
    password: 0da8651e-e1f6-11ec-bd7c-f23c913c8d2b
    port: 8443
    server: 2f086087-swkhs0-tcinla-hrtf.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 2f086087-swkhs0-tcinla-hrtf.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国141 | ⬇️ 710KB/s"
    obfs: ''
    obfs-password: ''
    password: 93fb69fc-77cf-11ee-85ee-f23c91369f2d
    port: 8443
    server: 18b45d29-swkhs0-t12cnj-1ol97.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 18b45d29-swkhs0-t12cnj-1ol97.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国142 | ⬇️ 509KB/s"
    obfs: ''
    obfs-password: ''
    password: bcc58e88-e147-11ec-b286-f23c91cfbbc9
    port: 8443
    server: 03af7657-swin40-sxu45k-ggww.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 03af7657-swin40-sxu45k-ggww.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国143 | ⬇️ 697KB/s"
    obfs: ''
    obfs-password: ''
    password: b79d79ae-8bce-11ef-a2b8-f23c9164ca5d
    port: 8443
    server: 8a049395-swkhs0-t67sv3-1snfs.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 8a049395-swkhs0-t67sv3-1snfs.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国144 | ⬇️ 756KB/s"
    obfs: ''
    obfs-password: ''
    password: 861726bb-c61a-b0e0-99fa-5ddfdcf21902
    port: 8443
    server: 72f61c73-swkhs0-ta5nd4-e06r.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 72f61c73-swkhs0-ta5nd4-e06r.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国145 | ⬇️ 528KB/s"
    obfs: ''
    obfs-password: ''
    password: cfe61764-0004-11f0-a910-f23c9164ca5d
    port: 8443
    server: 2f4cee5e-swexs0-sx9j1n-1timk.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 2f4cee5e-swexs0-sx9j1n-1timk.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1F7\U0001F1FA俄罗斯24 | ⬇️ 1.8MB/s"
    obfs: ''
    obfs-password: ''
    password: abef57a5-ac14-4356-ba5d-c3fdc85df62b
    port: 36634
    server: rua.node.cross666.top
    skip-cert-verify: true
    sni: mirror.hyperdedic.ru
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国146 | ⬇️ 533KB/s"
    obfs: ''
    obfs-password: ''
    password: e3975a05-b89e-11ec-a50d-f23c91cfbbc9
    port: 8443
    server: ecabd45b-swd340-sxf56z-1f596.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: ecabd45b-swd340-sxf56z-1f596.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国147 | ⬇️ 1.7MB/s"
    obfs: ''
    obfs-password: ''
    password: abef57a5-ac14-4356-ba5d-c3fdc85df62b
    port: 35665
    server: us3.node.cross666.top
    skip-cert-verify: true
    sni: www.yahoo.com
    type: hysteria2
  - name: "\U0001F1EB\U0001F1F7法国10 | ⬇️ 1.8MB/s"
    obfs: ''
    obfs-password: ''
    password: abef57a5-ac14-4356-ba5d-c3fdc85df62b
    port: 38551
    server: fr1.node.cross666.top
    skip-cert-verify: true
    sni: ubuntu.mirrors.ovh.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国148 | ⬇️ 973KB/s"
    obfs: ''
    obfs-password: ''
    password: 336501b6-51d2-11ee-a993-f23c9164ca5d
    port: 8443
    server: 81c661be-swd340-t8kd6j-1c9em.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 81c661be-swd340-t8kd6j-1c9em.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1F8\U0001F1EC新加坡16 | ⬇️ 1.0MB/s"
    obfs: ''
    obfs-password: ''
    password: abef57a5-ac14-4356-ba5d-c3fdc85df62b
    port: 37726
    server: sg02.node.cross666.top
    skip-cert-verify: true
    sni: mirror.sg.gs
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国149 | ⬇️ 1.3MB/s"
    obfs: ''
    obfs-password: ''
    password: 93fb69fc-77cf-11ee-85ee-f23c91369f2d
    port: 8443
    server: 9d0a15bc-swd340-t12cnj-1ol97.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 9d0a15bc-swd340-t12cnj-1ol97.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国150 | ⬇️ 578KB/s"
    obfs: ''
    obfs-password: ''
    password: 861726bb-c61a-b0e0-99fa-5ddfdcf21902
    port: 8443
    server: 5409b405-swd340-ta5nd4-e06r.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 5409b405-swd340-ta5nd4-e06r.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F300其他25-TR | ⬇️ 569KB/s"
    obfs: ''
    obfs-password: ''
    password: abef57a5-ac14-4356-ba5d-c3fdc85df62b
    port: 37223
    server: tr01.node.cross666.top
    skip-cert-verify: true
    sni: mirror.dogado.de
    type: hysteria2
  - name: "\U0001F1F7\U0001F1FA俄罗斯25 | ⬇️ 1.6MB/s"
    obfs: ''
    obfs-password: ''
    password: abef57a5-ac14-4356-ba5d-c3fdc85df62b
    port: 36781
    server: ru2.node.cross666.top
    skip-cert-verify: true
    sni: mirror.hyperdedic.ru
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国151 | ⬇️ 857KB/s"
    obfs: ''
    obfs-password: ''
    password: 93fb69fc-77cf-11ee-85ee-f23c91369f2d
    port: 8443
    server: b3323c49-swexs0-t12cnj-1ol97.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: b3323c49-swexs0-t12cnj-1ol97.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1F7\U0001F1FA俄罗斯26 | ⬇️ 2.2MB/s"
    obfs: ''
    obfs-password: ''
    password: abef57a5-ac14-4356-ba5d-c3fdc85df62b
    port: 38515
    server: rua.node.cross666.top
    skip-cert-verify: true
    sni: mirror.hyperdedic.ru
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国152 | ⬇️ 857KB/s"
    obfs: ''
    obfs-password: ''
    password: 8e0f8ef2-5fe3-11ec-a8bf-f23c91cfbbc9
    port: 8443
    server: 3056684d-swexs0-swdpud-duku.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 3056684d-swexs0-swdpud-duku.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1F7\U0001F1FA俄罗斯27 | ⬇️ 1.7MB/s"
    obfs: ''
    obfs-password: ''
    password: abef57a5-ac14-4356-ba5d-c3fdc85df62b
    port: 39770
    server: rua.node.cross666.top
    skip-cert-verify: true
    sni: mirror.hyperdedic.ru
    type: hysteria2
  - name: "\U0001F1F7\U0001F1FA俄罗斯28 | ⬇️ 2.2MB/s"
    obfs: ''
    obfs-password: ''
    password: f9153abe-88d9-4ad7-a3a7-01eff2790b3e
    port: 39205
    server: rua.node.cross666.top
    skip-cert-verify: true
    sni: mirror.hyperdedic.ru
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国153 | ⬇️ 1.6MB/s"
    obfs: ''
    obfs-password: ''
    password: bcc58e88-e147-11ec-b286-f23c91cfbbc9
    port: 8443
    server: b50f3f1e-swexs0-sxu45k-ggww.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: b50f3f1e-swexs0-sxu45k-ggww.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国154 | ⬇️ 1.6MB/s"
    obfs: ''
    obfs-password: ''
    password: b79d79ae-8bce-11ef-a2b8-f23c9164ca5d
    port: 8443
    server: e7af2fff-swexs0-t67sv3-1snfs.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: e7af2fff-swexs0-t67sv3-1snfs.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1F7\U0001F1FA俄罗斯29 | ⬇️ 1.8MB/s"
    obfs: ''
    obfs-password: ''
    password: f9153abe-88d9-4ad7-a3a7-01eff2790b3e
    port: 35344
    server: ru2.node.cross666.top
    skip-cert-verify: true
    sni: mirror.hyperdedic.ru
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国155 | ⬇️ 1.4MB/s"
    obfs: ''
    obfs-password: ''
    password: 34f0cf42-0f8a-11ec-a8bf-f23c91cfbbc9
    port: 8443
    server: 0865ad3c-swexs0-szdere-155d9.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 0865ad3c-swexs0-szdere-155d9.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国156 | ⬇️ 1.2MB/s"
    obfs: ''
    obfs-password: ''
    password: 0da8651e-e1f6-11ec-bd7c-f23c913c8d2b
    port: 8443
    server: 66a0ae32-swexs0-tcinla-hrtf.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: 66a0ae32-swexs0-tcinla-hrtf.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国157 | ⬇️ 1.2MB/s"
    obfs: ''
    obfs-password: ''
    password: 8e0f8ef2-5fe3-11ec-a8bf-f23c91cfbbc9
    port: 8443
    server: b43c1e95-swd340-swdpud-duku.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: b43c1e95-swd340-swdpud-duku.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国158 | ⬇️ 957KB/s"
    obfs: ''
    obfs-password: ''
    password: c3dc8d26-ab2b-11ec-a8bf-f23c91cfbbc9
    port: 8443
    server: fa13afea-swd340-szapkm-1hpo0.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: fa13afea-swd340-szapkm-1hpo0.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1EB\U0001F1F7法国11 | ⬇️ 2.1MB/s"
    obfs: ''
    obfs-password: ''
    password: f9153abe-88d9-4ad7-a3a7-01eff2790b3e
    port: 39413
    server: fr1.node.cross666.top
    skip-cert-verify: true
    sni: ubuntu.mirrors.ovh.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国159 | ⬇️ 2.1MB/s"
    obfs: ''
    obfs-password: ''
    password: f9153abe-88d9-4ad7-a3a7-01eff2790b3e
    port: 39052
    server: us3.node.cross666.top
    skip-cert-verify: true
    sni: www.yahoo.com
    type: hysteria2
  - name: "\U0001F300其他26-VN | ⬇️ 1.3MB/s"
    obfs: ''
    obfs-password: ''
    password: abef57a5-ac14-4356-ba5d-c3fdc85df62b
    port: 38850
    server: vn1r.node.cross666.top
    skip-cert-verify: true
    sni: mirror.sg.gs
    type: hysteria2
  - name: "\U0001F300其他27-TR | ⬇️ 1.8MB/s"
    obfs: ''
    obfs-password: ''
    password: f9153abe-88d9-4ad7-a3a7-01eff2790b3e
    port: 36184
    server: tr01.node.cross666.top
    skip-cert-verify: true
    sni: mirror.dogado.de
    type: hysteria2
  - name: "\U0001F1EB\U0001F1F7法国12 | ⬇️ 1.6MB/s"
    obfs: ''
    obfs-password: ''
    password: abef57a5-ac14-4356-ba5d-c3fdc85df62b
    port: 37878
    server: fr1.node.cross666.top
    skip-cert-verify: true
    sni: ubuntu.mirrors.ovh.net
    type: hysteria2
  - name: "\U0001F1F7\U0001F1FA俄罗斯30 | ⬇️ 1.5MB/s"
    obfs: ''
    obfs-password: ''
    password: abef57a5-ac14-4356-ba5d-c3fdc85df62b
    port: 38231
    server: ru2.node.cross666.top
    skip-cert-verify: true
    sni: mirror.hyperdedic.ru
    type: hysteria2
  - name: "\U0001F1F8\U0001F1EC新加坡17 | ⬇️ 773KB/s"
    obfs: ''
    obfs-password: ''
    password: f9153abe-88d9-4ad7-a3a7-01eff2790b3e
    port: 39686
    server: sg02.node.cross666.top
    skip-cert-verify: true
    sni: mirror.sg.gs
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国160 | ⬇️ 4.5MB/s"
    obfs: ''
    obfs-password: ''
    password: bb85e074-b0c2-11ea-ad28-f23c913c8d2b
    port: 8443
    server: b563ae1f-swexs0-sx9mz9-m0b9.hy2.gotochinatown.net
    skip-cert-verify: false
    sni: b563ae1f-swexs0-sx9mz9-m0b9.hy2.gotochinatown.net
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国161 | ⬇️ 963KB/s"
    obfs: ''
    obfs-password: ''
    password: f9153abe-88d9-4ad7-a3a7-01eff2790b3e
    port: 37096
    server: us3.node.cross666.top
    skip-cert-verify: true
    sni: www.yahoo.com
    type: hysteria2
  - name: "\U0001F1F7\U0001F1FA俄罗斯31 | ⬇️ 2.2MB/s"
    obfs: ''
    obfs-password: ''
    password: f9153abe-88d9-4ad7-a3a7-01eff2790b3e
    port: 37283
    server: rua.node.cross666.top
    skip-cert-verify: true
    sni: mirror.hyperdedic.ru
    type: hysteria2
  - name: "\U0001F1F7\U0001F1FA俄罗斯32 | ⬇️ 2.0MB/s"
    obfs: ''
    obfs-password: ''
    password: f9153abe-88d9-4ad7-a3a7-01eff2790b3e
    port: 35445
    server: rua.node.cross666.top
    skip-cert-verify: true
    sni: mirror.hyperdedic.ru
    type: hysteria2
  - name: "\U0001F1FA\U0001F1F8美国162 | ⬇️ 1.2MB/s"
    network: ws
    port: 8880
    server: ***********8
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
        User-Agent: >-
          Mozilla/5.0 (Linux; Android 6.0.1; SM-G532M Build/MMB29T)
          AppleWebKit/537.36 (KHTML, like Gecko) Chrome/55.0.2883.91 Mobile
          Safari/537.36
      path: "/Telegram\U0001F1E8\U0001F1F3 @pgkj666 /?ed=2560"
    xudp: true
  - cipher: aes-256-cfb
    name: "\U0001F1F0\U0001F1F7韩国5 | ⬇️ 7.9MB/s"
    password: qwerREWQ@@
    port: 15098
    server: p222.panda001.net
    type: ss
    udp: true
  - name: "\U0001F1FA\U0001F1F8美国163 | ⬇️ 802KB/s"
    network: ws
    port: 8880
    server: ************
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
        User-Agent: >-
          Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML,
          like Gecko) Chrome/55.0.2883.87 Safari/537.36
      path: "/Telegram\U0001F1E8\U0001F1F3 @pgkj666 /?ed=2560"
    xudp: true
  - name: "\U0001F1E9\U0001F1EA德国6 | ⬇️ 1.4MB/s"
    network: ws
    port: 8880
    server: ************
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
        User-Agent: >-
          Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML,
          like Gecko) Chrome/61.0.3163.100 Safari/537.36
      path: "/Telegram\U0001F1E8\U0001F1F3 @pgkj666 /?ed=2560"
    xudp: true
  - cipher: chacha20-ietf
    name: "\U0001F1F8\U0001F1EC新加坡18 | ⬇️ 1018KB/s"
    password: asd123456
    port: 8388
    server: ************
    type: ss
    udp: true
  - cipher: aes-256-cfb
    name: "\U0001F1F0\U0001F1F7韩国6 | ⬇️ 1.9MB/s"
    password: qwerREWQ@@
    port: 15098
    server: *************
    type: ss
    udp: true
  - name: "\U0001F1E6\U0001F1EA阿拉伯酋长国1 | ⬇️ 516KB/s"
    network: ws
    port: 8880
    server: ************
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
        User-Agent: >-
          Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML,
          like Gecko) Chrome/69.0.3497.100 Safari/537.36
      path: /TelegramU0001F1E8U0001F1F3 @MxlShare @WangCai2 /?ed=2560
    xudp: true
    servername: reedfree8mahsang2.redorg.ir
  - name: "\U0001F1FA\U0001F1F8美国164 | ⬇️ 574KB/s"
    network: ws
    port: 8880
    server: ***********
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: "/Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
  - name: "\U0001F1ED\U0001F1F0香港17 | ⬇️ 1.0MB/s"
    network: ws
    port: 8880
    server: ************
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
        User-Agent: >-
          Mozilla/5.0 (Windows NT 6.0; Win64; x64) AppleWebKit/537.36 (KHTML,
          like Gecko) Chrome/55.0.2883.87 Safari/537.36
      path: "/Telegram\U0001F1E8\U0001F1F3 @pgkj666 /?ed=2560"
    xudp: true
  - name: "\U0001F1FA\U0001F1F8美国165 | ⬇️ 10.0MB/s"
    obfs-opts:
      host: icloud.com
      mode: http
    port: 45768
    psk: SRjsBEEEvNNCL69o
    server: *************
    type: snell
    version: 3
  - alpn:
      - ''
    alterId: '0'
    cipher: auto
    name: "\U0001F1EF\U0001F1F5日本9 | ⬇️ 1.1MB/s"
    network: ws
    port: 19700
    server: **************
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: db1b5367-92d7-4337-90c6-b3b9955d02ba
    ws-opts:
      headers:
        Host: tms.dingtalk.com
      path: /
    xudp: true
  - name: "\U0001F1E9\U0001F1EA德国7 | ⬇️ 727KB/s"
    network: ws
    port: 8880
    server: ***********
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
        User-Agent: >-
          Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like
          Gecko) Chrome/53.0.2785.143 Safari/537.36
      path: "/Telegram\U0001F1E8\U0001F1F3 @pgkj666 /?ed=2560"
    xudp: true
  - cipher: dummy
    name: "\U0001F300其他28-XX | ⬇️ 1.8MB/s"
    obfs: plain
    obfs-param: cbcdc8198.microsoft.com
    password: mantouyun888
    port: 44006
    protocol: auth_chain_a
    protocol-param: '8198:CsJt8BBrKFOSrjv2'
    server: 88gg.mt.mt5888.top
    type: ssr
    udp: true
  - alpn:
      - h3
    auth-str: dongtaiwang.com
    auth_str: dongtaiwang.com
    down: 100
    name: "\U0001F1FA\U0001F1F8美国166 | ⬇️ 8.9MB/s"
    port: 36194
    protocol: udp
    server: ************
    skip-cert-verify: true
    sni: apple.com
    type: hysteria
    udp: true
    up: 100
  - name: "\U0001F1F3\U0001F1F1荷兰1 | ⬇️ 876KB/s"
    network: ws
    port: 8880
    server: ***********
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /TelegramU0001F1E8U0001F1F3 @MxlShare @WangCai2 /?ed=2560
    servername: reedfree8mahsang2.redorg.ir
  - name: "\U0001F1EB\U0001F1F7法国13 | ⬇️ 7.4MB/s"
    network: ws
    port: 8080
    server: *************
    type: vless
    udp: true
    uuid: 4ea841c1-0dc1-4563-9f47-deba8407cb4e
    ws-opts:
      headers:
        Host: J9.oDOtZrHUoO.ZuLAIR.ORg.
        User-Agent: >-
          Mozilla/5.0 (X11; Datanyze; Linux x86_64) AppleWebKit/537.36 (KHTML,
          like Gecko) Chrome/65.0.3325.181 Safari/537.36
      path: /?ed=2048
    xudp: true
  - name: "\U0001F1E9\U0001F1EA德国8 | ⬇️ 775KB/s"
    network: ws
    port: 8880
    server: *************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /Telegram@ShadowProxy66/?ed=2560
  - alterId: 0
    cipher: auto
    name: "\U0001F1ED\U0001F1F0香港18 | ⬇️ 4.3MB/s"
    network: ws
    port: 8080
    server: 896665a1-swexs0-sznzxg-1jfvb.hgc1.tcpbbr.net
    skip-cert-verify: true
    tls: false
    type: vmess
    udp: true
    uuid: 06b2ac52-fcc6-11ec-bb74-f23c9164ca5d
    ws-opts:
      path: /
      headers:
        Host: 896665a1-swexs0-sznzxg-1jfvb.hgc1.tcpbbr.net
  - name: "\U0001F1E9\U0001F1EA德国9 | ⬇️ 613KB/s"
    password: RlzoEILU
    port: 33019
    server: *************
    skip-cert-verify: true
    type: trojan
    udp: true
  - alterId: 0
    cipher: auto
    name: "\U0001F1ED\U0001F1F0香港19 | ⬇️ 3.9MB/s"
    network: ws
    port: 8080
    server: e721307a-swexs0-sx9j1n-1timk.hgc1.tcpbbr.net
    skip-cert-verify: true
    tls: false
    type: vmess
    udp: true
    uuid: cfe61764-0004-11f0-a910-f23c9164ca5d
    ws-opts:
      path: /
      headers:
        Host: e721307a-swexs0-sx9j1n-1timk.hgc1.tcpbbr.net
  - name: "\U0001F1FA\U0001F1F8美国167 | ⬇️ 4.6MB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /?ed=2560
    servername: reedfree8mahsang2.redorg.ir
  - cipher: aes-256-cfb
    name: "\U0001F300其他29-VN | ⬇️ 716KB/s"
    password: Xn8jKdmDM00IeO%#$#fJAMtsEAEUOpH/YWYtYqDFnT0SV
    port: 38388
    server: **************
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1EF\U0001F1F5日本10 | ⬇️ 2.3MB/s"
    password: JGELAO2JRZ3S2YOC
    port: 18011
    server: ti3hyra4.slashdevslashnetslashtun.net
    type: ss
    udp: true
  - alterId: 0
    cipher: auto
    name: "\U0001F1ED\U0001F1F0香港20 | ⬇️ 2.1MB/s"
    network: ws
    port: 8080
    server: d5fa3bb7-swd340-sx9mz9-m0b9.hgc1.tcpbbr.net
    skip-cert-verify: true
    tls: false
    type: vmess
    udp: true
    uuid: bb85e074-b0c2-11ea-ad28-f23c913c8d2b
    ws-opts:
      path: /
      headers:
        Host: d5fa3bb7-swd340-sx9mz9-m0b9.hgc1.tcpbbr.net
  - name: "\U0001F1F0\U0001F1F7韩国7 | ⬇️ 590KB/s"
    network: ws
    port: 8880
    server: ***********
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /TelegramU0001F1E8U0001F1F3 @MxlShare @WangCai2 /?ed=2560
    servername: reedfree8mahsang2.redorg.ir
  - name: "\U0001F1EB\U0001F1EE芬兰1 | ⬇️ 8.8MB/s"
    network: ws
    port: 8880
    server: ***********
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: "/Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
  - name: "\U0001F1F3\U0001F1F1荷兰2 | ⬇️ 679KB/s"
    network: ws
    port: 8880
    server: ***********2
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: "/Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
  - name: "\U0001F1FA\U0001F1F8美国168 | ⬇️ 2.2MB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /TelegramU0001F1E8U0001F1F3 @MxlShare @WangCai2 /?ed=2560
    servername: reedfree8mahsang2.redorg.ir
  - auth: 8e0f8ef2-5fe3-11ec-a8bf-f23c91cfbbc9
    name: "\U0001F1FA\U0001F1F8美国169 | ⬇️ 938KB/s"
    password: 8e0f8ef2-5fe3-11ec-a8bf-f23c91cfbbc9
    port: 8443
    server: 4496cf9b-swxgg0-t14dgl-duku.hy2.gotochinatown.net
    type: hysteria2
    udp: true
    sni: 4496cf9b-swxgg0-t14dgl-duku.hy2.gotochinatown.net
  - name: "\U0001F1EB\U0001F1EE芬兰2 | ⬇️ 10.2MB/s"
    network: ws
    port: 8880
    server: *************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /?ed=2560
    servername: reedfree8mahsang2.redorg.ir
  - cipher: aes-256-gcm
    name: "\U0001F1ED\U0001F1F0香港21 | ⬇️ 3.9MB/s"
    password: Z78JQTL5YGPNOWFX
    port: 15009
    server: 8tv68qhq.slashdevslashnetslashtun.net
    type: ss
    udp: true
  - name: "\U0001F1FA\U0001F1F8美国170 | ⬇️ 3.6MB/s"
    password: bcc58e88-e147-11ec-b286-f23c91cfbbc9
    port: 15229
    server: 792c2394-swxgg0-sxu45k-ggww.cu.plebai.net
    type: trojan
    udp: true
    sni: 792c2394-swxgg0-sxu45k-ggww.cu.plebai.net
  - auth: 8f854788-85ba-4ab5-8626-5071bb6a43d3
    down: 125
    name: "\U0001F1F7\U0001F1FA俄罗斯33 | ⬇️ 2.2MB/s"
    password: 8f854788-85ba-4ab5-8626-5071bb6a43d3
    port: 35766
    server: ru2.node.cross666.top
    skip-cert-verify: true
    sni: mirror.hyperdedic.ru
    type: hysteria2
    udp: true
    up: 100
  - auth: 127e3f92-f714-11ef-bbb0-f23c91cfbbc9
    name: "\U0001F1FA\U0001F1F8美国171 | ⬇️ 715KB/s"
    password: 127e3f92-f714-11ef-bbb0-f23c91cfbbc9
    port: 8443
    server: eccca7a6-swxgg0-t2c3sq-1spnr.hy2.gotochinatown.net
    type: hysteria2
    udp: true
    sni: eccca7a6-swxgg0-t2c3sq-1spnr.hy2.gotochinatown.net
  - auth: 7b8dc3c9-cd74-4c22-9b29-************
    down: 200
    name: "\U0001F1FA\U0001F1F8美国172 | ⬇️ 2.5MB/s"
    password: 7b8dc3c9-cd74-4c22-9b29-************
    port: 36843
    server: use.node.cross666.top
    skip-cert-verify: true
    sni: www.yahoo.com
    type: hysteria2
    udp: true
    up: 100
  - name: "\U0001F1EE\U0001F1F3印度1 | ⬇️ 903KB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /TelegramU0001F1E8U0001F1F3 @MxlShare @WangCai2 /?ed=2560
    servername: reedfree8mahsang2.redorg.ir
  - cipher: aes-256-gcm
    name: "\U0001F1ED\U0001F1F0香港22 | ⬇️ 2.2MB/s"
    password: G5UYEUFON27A3ATP
    port: 15012
    server: 8tv68qhq.slashdevslashnetslashtun.net
    type: ss
    udp: true
  - name: "\U0001F1EB\U0001F1F7法国14 | ⬇️ 9.7MB/s"
    network: ws
    port: 8080
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 4ea841c1-0dc1-4563-9f47-deba8407cb4e
    ws-opts:
      headers:
        Host: J9.oDOtZrHUoO.ZuLAIR.ORg.
      path: /?ed=2048
  - auth: 8f854788-85ba-4ab5-8626-5071bb6a43d3
    down: 300
    name: "\U0001F1FA\U0001F1F8美国173 | ⬇️ 2.2MB/s"
    password: 8f854788-85ba-4ab5-8626-5071bb6a43d3
    port: 37066
    server: us4.node.cross666.top
    skip-cert-verify: true
    sni: www.yahoo.com
    type: hysteria2
    udp: true
    up: 100
  - name: "\U0001F1FA\U0001F1F8美国174 | ⬇️ 522KB/s"
    network: ws
    port: 8880
    server: *************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /Telegram@ShadowProxy66/?ed=2560
  - auth: bcc58e88-e147-11ec-b286-f23c91cfbbc9
    name: "\U0001F1FA\U0001F1F8美国175 | ⬇️ 866KB/s"
    password: bcc58e88-e147-11ec-b286-f23c91cfbbc9
    port: 8443
    server: 6d722f2c-swxgg0-sxu45k-ggww.hy2.gotochinatown.net
    type: hysteria2
    udp: true
    sni: 6d722f2c-swxgg0-sxu45k-ggww.hy2.gotochinatown.net
  - alterId: 2
    cipher: auto
    name: "\U0001F1ED\U0001F1F0香港23 | ⬇️ 13.5MB/s"
    network: ws
    port: 459
    server: 9d6c2f7e-swvls0-syb15h-8caj.hkt.east.wctype.com
    tls: false
    type: vmess
    udp: true
    uuid: a67a6c18-3f6d-11ef-ab9c-f23c9313b177
    ws-opts:
      headers:
        Host: a605477178.m.ctrip.com
      path: /
  - auth: 336501b6-51d2-11ee-a993-f23c9164ca5d
    name: "\U0001F1FA\U0001F1F8美国176 | ⬇️ 834KB/s"
    password: 336501b6-51d2-11ee-a993-f23c9164ca5d
    port: 8443
    server: 4f88735f-swxgg0-t8kd6j-1c9em.hy2.gotochinatown.net
    type: hysteria2
    udp: true
    sni: 4f88735f-swxgg0-t8kd6j-1c9em.hy2.gotochinatown.net
  - alpn:
      - http/1.1
    client-fingerprint: randomized
    name: "\U0001F1EC\U0001F1E7英国5 | ⬇️ 1.5MB/s"
    network: ws
    port: 2096
    server: allthetropes.org
    tls: true
    type: vless
    udp: true
    uuid: ec7de7e0-3f09-4ef4-8a34-e441917d65fa
    ws-opts:
      headers:
        Host: verina.cantarella.dns-dynamic.net
      path: /proxyip=robin.yaemiko.ggff.net
    servername: verina.cantarella.dns-dynamic.net
  - name: "\U0001F1FA\U0001F1F8美国177 | ⬇️ 1.8MB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /?ed=2560
    servername: reedfree8mahsang2.redorg.ir
  - name: "\U0001F1FA\U0001F1F8美国178 | ⬇️ 1.1MB/s"
    network: ws
    port: 8880
    server: **************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: "/Telegram\U0001F1E8\U0001F1F3 @pgkj666 /?ed=2560"
  - auth: 8f854788-85ba-4ab5-8626-5071bb6a43d3
    down: 160
    name: "\U0001F1FA\U0001F1F8美国179 | ⬇️ 2.5MB/s"
    password: 8f854788-85ba-4ab5-8626-5071bb6a43d3
    port: 36668
    server: us01.node.cross666.top
    skip-cert-verify: true
    sni: www.yahoo.com
    type: hysteria2
    udp: true
    up: 100
  - cipher: dummy
    name: "\U0001F1FA\U0001F1F8美国180 | ⬇️ 1.1MB/s"
    obfs: plain
    obfs-param: cbcdc8198.microsoft.com
    password: mantouyun888
    port: 44008
    protocol: auth_chain_a
    protocol-param: '8198:CsJt8BBrKFOSrjv2'
    server: 88gg.mt.mt5888.top
    type: ssr
    udp: true
  - name: "\U0001F1FA\U0001F1F8美国181 | ⬇️ 5.4MB/s"
    password: b55123f4-0ba2-11f0-a2ef-f23c91cfbbc9
    port: 15229
    server: d700ff89-swvls0-t5w83q-1slpx.cu.plebai.net
    type: trojan
    udp: true
    sni: d700ff89-swvls0-t5w83q-1slpx.cu.plebai.net
  - name: "\U0001F1E9\U0001F1EA德国10 | ⬇️ 1010KB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: "/Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
  - cipher: aes-256-gcm
    name: "\U0001F1EF\U0001F1F5日本11 | ⬇️ 803KB/s"
    password: 7J1VD0VJGLP8IB57
    port: 18016
    server: ti3hyra4.slashdevslashnetslashtun.net
    type: ss
    udp: true
  - auth: 3fa7157d-efe8-832e-9b3c-582d78efbc52
    name: "\U0001F1FA\U0001F1F8美国182 | ⬇️ 549KB/s"
    password: 3fa7157d-efe8-832e-9b3c-582d78efbc52
    port: 8443
    server: 11590083-swxgg0-tf70jh-vm13.hy2.gotochinatown.net
    type: hysteria2
    udp: true
    sni: 11590083-swxgg0-tf70jh-vm13.hy2.gotochinatown.net
  - cipher: aes-256-gcm
    name: "\U0001F1EF\U0001F1F5日本12 | ⬇️ 1.9MB/s"
    password: W4F74LOS5AG63FQC
    port: 18013
    server: ti3hyra4.slashdevslashnetslashtun.net
    type: ss
    udp: true
  - auth: 7b8dc3c9-cd74-4c22-9b29-************
    down: 180
    name: "\U0001F1FA\U0001F1F8美国183 | ⬇️ 2.3MB/s"
    password: 7b8dc3c9-cd74-4c22-9b29-************
    port: 35968
    server: us3.node.cross666.top
    skip-cert-verify: true
    sni: www.yahoo.com
    type: hysteria2
    udp: true
    up: 100
  - auth: c6d93228-6393-41ec-8403-da852407390a
    down: 300
    name: "\U0001F1FA\U0001F1F8美国184 | ⬇️ 1.7MB/s"
    password: c6d93228-6393-41ec-8403-da852407390a
    port: 36270
    server: us4.node.cross666.top
    skip-cert-verify: true
    sni: www.yahoo.com
    type: hysteria2
    udp: true
    up: 100
  - cipher: aes-256-gcm
    name: "\U0001F1E8\U0001F1E6加拿大1 | ⬇️ 1.2MB/s"
    password: T7J7LMC1NN9PUI3J
    port: 20027
    server: **************
    type: ss
    udp: true
  - name: "\U0001F1FA\U0001F1F8美国185 | ⬇️ 806KB/s"
    network: ws
    port: 80
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: '/Telegram:@vpnAndroid2/?ed=2560'
  - cipher: aes-256-gcm
    name: "\U0001F1EF\U0001F1F5日本13 | ⬇️ 802KB/s"
    password: OMMFQHYPSZCBSLVV
    port: 18002
    server: 8tv68qhq.slashdevslashnetslashtun.net
    type: ss
    udp: true
  - name: "\U0001F1E6\U0001F1EA阿拉伯酋长国2 | ⬇️ 12.2MB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /?ed=2560
  - auth: 85ac1c2d-f3c4-496d-9a01-8d0d84eab17d
    down: 400
    name: "\U0001F300其他30-TR | ⬇️ 2.5MB/s"
    password: 85ac1c2d-f3c4-496d-9a01-8d0d84eab17d
    port: 35999
    server: tr01.node.cross666.top
    skip-cert-verify: true
    sni: mirror.dogado.de
    type: hysteria2
    udp: true
    up: 100
  - auth: c6d93228-6393-41ec-8403-da852407390a
    down: 200
    name: "\U0001F1FA\U0001F1F8美国186 | ⬇️ 1.9MB/s"
    password: c6d93228-6393-41ec-8403-da852407390a
    port: 39003
    server: use.node.cross666.top
    skip-cert-verify: true
    sni: www.yahoo.com
    type: hysteria2
    udp: true
    up: 100
  - auth: 8f854788-85ba-4ab5-8626-5071bb6a43d3
    down: 160
    name: "\U0001F1FA\U0001F1F8美国187 | ⬇️ 2.1MB/s"
    password: 8f854788-85ba-4ab5-8626-5071bb6a43d3
    port: 39356
    server: us01.node.cross666.top
    skip-cert-verify: true
    sni: www.yahoo.com
    type: hysteria2
    udp: true
    up: 100
  - auth: 3d7182ce-8dcc-11ef-a3f6-f23c9164ca5d
    name: "\U0001F1FA\U0001F1F8美国188 | ⬇️ 684KB/s"
    password: 3d7182ce-8dcc-11ef-a3f6-f23c9164ca5d
    port: 8443
    server: e8034c27-swxgg0-thdi1k-19yro.hy2.gotochinatown.net
    type: hysteria2
    udp: true
    sni: e8034c27-swxgg0-thdi1k-19yro.hy2.gotochinatown.net
  - auth: 31676725-b0fd-fed2-c857-2da8ce5a6e4f
    name: "\U0001F1FA\U0001F1F8美国189 | ⬇️ 619KB/s"
    password: 31676725-b0fd-fed2-c857-2da8ce5a6e4f
    port: 8443
    server: 7f1f2efa-swxgg0-t0dyyh-jjv2.hy2.gotochinatown.net
    type: hysteria2
    udp: true
    sni: 7f1f2efa-swxgg0-t0dyyh-jjv2.hy2.gotochinatown.net
  - auth: 7b8dc3c9-cd74-4c22-9b29-************
    down: 140
    name: "\U0001F300其他31-VN | ⬇️ 2.2MB/s"
    password: 7b8dc3c9-cd74-4c22-9b29-************
    port: 38981
    server: vn1r.node.cross666.top
    skip-cert-verify: true
    sni: mirror.sg.gs
    type: hysteria2
    udp: true
    up: 100
  - name: "\U0001F1FA\U0001F1F8美国190 | ⬇️ 570KB/s"
    password: 0de37cdc-abff-11ef-b7c6-f23c913c8d2b
    port: 15229
    server: 2c7b4b56-swvls0-syhw90-1rsuw.cu.plebai.net
    type: trojan
    udp: true
    sni: 2c7b4b56-swvls0-syhw90-1rsuw.cu.plebai.net
  - name: "\U0001F1F0\U0001F1F7韩国8 | ⬇️ 700KB/s"
    network: ws
    port: 8880
    server: **************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /?ed=2560
  - cipher: aes-256-gcm
    name: "\U0001F1EF\U0001F1F5日本14 | ⬇️ 2.9MB/s"
    password: PUXUB2FJ86BJ6VUH
    port: 18003
    server: ti3hyra4.slashdevslashnetslashtun.net
    type: ss
    udp: true
  - name: "\U0001F1E9\U0001F1EA德国11 | ⬇️ 819KB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: "/Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
  - name: "\U0001F1FA\U0001F1F8美国191 | ⬇️ 964KB/s"
    network: ws
    port: 80
    server: *************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: '/Telegram: @vpnAndroid2/?ed=2560'
  - auth: 5479f2d5-a0c4-437e-95c9-f79a0c485af1
    down: 300
    name: "\U0001F1FA\U0001F1F8美国192 | ⬇️ 2.2MB/s"
    password: 5479f2d5-a0c4-437e-95c9-f79a0c485af1
    port: 36641
    server: us4.node.cross666.top
    skip-cert-verify: true
    sni: www.yahoo.com
    type: hysteria2
    udp: true
    up: 100
  - auth: 5479f2d5-a0c4-437e-95c9-f79a0c485af1
    down: 400
    name: "\U0001F300其他32-TR | ⬇️ 1.7MB/s"
    password: 5479f2d5-a0c4-437e-95c9-f79a0c485af1
    port: 38374
    server: tr01.node.cross666.top
    skip-cert-verify: true
    sni: mirror.dogado.de
    type: hysteria2
    udp: true
    up: 100
  - auth: 7b8dc3c9-cd74-4c22-9b29-************
    down: 140
    name: "\U0001F300其他33-VN | ⬇️ 2.0MB/s"
    password: 7b8dc3c9-cd74-4c22-9b29-************
    port: 38118
    server: vn1r.node.cross666.top
    skip-cert-verify: true
    sni: mirror.sg.gs
    type: hysteria2
    udp: true
    up: 100
  - name: "\U0001F300其他34-未识别 | ⬇️ 610KB/s"
    network: ws
    port: 8880
    server: *************
    tls: false
    type: vless
    udp: true
    uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
    ws-opts:
      headers:
        Host: us.laoyoutiao.link
      path: /TelegramU0001F1E8U0001F1F3 @MxlShare @WangCai2 /?ed=2560
    servername: us.laoyoutiao.link
  - name: "\U0001F1E9\U0001F1EA德国12 | ⬇️ 1.1MB/s"
    network: ws
    port: 8880
    server: ***********
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /TelegramU0001F1E8U0001F1F3 @MxlShare @WangCai2 /?ed=2560
    servername: reedfree8mahsang2.redorg.ir
  - name: "\U0001F1FA\U0001F1F8美国193 | ⬇️ 2.8MB/s"
    password: 861726bb-c61a-b0e0-99fa-5ddfdcf21902
    port: 15229
    server: 3aadf07e-swvls0-ta5nd4-e06r.cu.plebai.net
    type: trojan
    udp: true
    sni: 3aadf07e-swvls0-ta5nd4-e06r.cu.plebai.net
  - name: "\U0001F1E9\U0001F1EA德国13 | ⬇️ 1.5MB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /TelegramU0001F1E8U0001F1F3 @MxlShare @WangCai2 /?ed=2560
    servername: reedfree8mahsang2.redorg.ir
  - auth: ba4672b3-e0af-4699-a180-f711cfaa95ed
    name: "\U0001F1FA\U0001F1F8美国194 | ⬇️ 4.5MB/s"
    password: ba4672b3-e0af-4699-a180-f711cfaa95ed
    port: 43999
    server: jiangzhidb.54264944.xyz
    skip-cert-verify: true
    sni: jiangzhidb.54264944.xyz
    type: hysteria2
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1F8\U0001F1EC新加坡19 | ⬇️ 776KB/s"
    password: 3IJTYIFVEXKSJ92G
    port: 16015
    server: ti3hyra4.slashdevslashnetslashtun.net
    type: ss
    udp: true
  - name: "\U0001F1FA\U0001F1F8美国195 | ⬇️ 623KB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: "/Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
  - auth: c6d93228-6393-41ec-8403-da852407390a
    down: 400
    name: "\U0001F300其他35-TR | ⬇️ 1.8MB/s"
    password: c6d93228-6393-41ec-8403-da852407390a
    port: 39218
    server: tr01.node.cross666.top
    skip-cert-verify: true
    sni: mirror.dogado.de
    type: hysteria2
    udp: true
    up: 100
  - auth: ef18aaca-4711-11ec-a8bf-f23c91cfbbc9
    name: "\U0001F1FA\U0001F1F8美国196 | ⬇️ 971KB/s"
    password: ef18aaca-4711-11ec-a8bf-f23c91cfbbc9
    port: 8443
    server: fae5424f-swxgg0-sy439s-laev.hy2.gotochinatown.net
    type: hysteria2
    udp: true
    sni: fae5424f-swxgg0-sy439s-laev.hy2.gotochinatown.net
  - name: "\U0001F1FA\U0001F1F8美国197 | ⬇️ 532KB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /TelegramU0001F1E8U0001F1F3 @MxlShare @WangCai2 /?ed=2560
    servername: reedfree8mahsang2.redorg.ir
  - auth: afc216c8-e407-48e0-86b1-6bf20f709c72
    down: 400
    name: "\U0001F300其他36-TR | ⬇️ 2.6MB/s"
    password: afc216c8-e407-48e0-86b1-6bf20f709c72
    port: 37334
    server: tr01.node.cross666.top
    skip-cert-verify: true
    sni: mirror.dogado.de
    type: hysteria2
    udp: true
    up: 100
  - auth: 7b8dc3c9-cd74-4c22-9b29-************
    down: 160
    name: "\U0001F1FA\U0001F1F8美国198 | ⬇️ 2.6MB/s"
    password: 7b8dc3c9-cd74-4c22-9b29-************
    port: 35519
    server: us01.node.cross666.top
    skip-cert-verify: true
    sni: www.yahoo.com
    type: hysteria2
    udp: true
    up: 100
  - name: "\U0001F1FA\U0001F1F8美国199 | ⬇️ 1.8MB/s"
    network: ws
    port: 8880
    server: *************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /?ed=2560
  - name: "\U0001F1FA\U0001F1F8美国200 | ⬇️ 2.3MB/s"
    network: ws
    port: 8880
    server: ***********
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /TelegramU0001F1E8U0001F1F3 @MxlShare @WangCai2 /?ed=2560
    servername: reedfree8mahsang2.redorg.ir
  - name: "\U0001F1FA\U0001F1F8美国201 | ⬇️ 1.4MB/s"
    password: 0da8651e-e1f6-11ec-bd7c-f23c913c8d2b
    port: 15229
    server: eb10f4d6-swxgg0-tcinla-hrtf.cu.plebai.net
    type: trojan
    udp: true
    sni: eb10f4d6-swxgg0-tcinla-hrtf.cu.plebai.net
  - auth: c6d93228-6393-41ec-8403-da852407390a
    down: 160
    name: "\U0001F1FA\U0001F1F8美国202 | ⬇️ 1.6MB/s"
    password: c6d93228-6393-41ec-8403-da852407390a
    port: 37417
    server: us01.node.cross666.top
    skip-cert-verify: true
    sni: www.yahoo.com
    type: hysteria2
    udp: true
    up: 100
  - auth: c6d93228-6393-41ec-8403-da852407390a
    down: 400
    name: "\U0001F1EB\U0001F1F7法国15 | ⬇️ 2.4MB/s"
    password: c6d93228-6393-41ec-8403-da852407390a
    port: 37370
    server: fr1.node.cross666.top
    skip-cert-verify: true
    sni: ubuntu.mirrors.ovh.net
    type: hysteria2
    udp: true
    up: 100
  - auth: 498c79e0-d4bb-43b0-a6c6-4bd5031c2405
    name: "\U0001F1EB\U0001F1F7法国16 | ⬇️ 5.9MB/s"
    password: 498c79e0-d4bb-43b0-a6c6-4bd5031c2405
    port: 43999
    server: jiangzhifg.54264944.xyz
    skip-cert-verify: true
    sni: jiangzhifr.54264944.xyz
    type: hysteria2
    udp: true
  - name: "\U0001F300其他37-未识别 | ⬇️ 1.3MB/s"
    network: ws
    port: 8880
    server: *************
    tls: false
    type: vless
    udp: true
    uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
    ws-opts:
      headers:
        Host: us.laoyoutiao.link
      path: "/Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
  - alpn:
      - http/1.1
    client-fingerprint: chrome
    name: "\U0001F1FA\U0001F1F8美国203 | ⬇️ 6.1MB/s"
    network: ws
    port: 443
    server: **************
    tls: true
    type: vless
    udp: true
    uuid: 2734c14b-e4ce-48b3-b3de-f80788cb4c47
    ws-opts:
      headers:
        Host: xcdf4.288288.shop
      path: /eq5Bb2boLXSG8DqNAB
    servername: xCdF4.288288.ShOP
  - cipher: aes-256-gcm
    name: "\U0001F1F8\U0001F1EC新加坡20 | ⬇️ 598KB/s"
    password: 360HQ7YMYU1HE7A8
    port: 16001
    server: ti3hyra4.slashdevslashnetslashtun.net
    type: ss
    udp: true
  - name: "\U0001F1E9\U0001F1EA德国14 | ⬇️ 2.2MB/s"
    network: ws
    port: 8880
    server: *************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /?ed=2560
    servername: reedfree8mahsang2.redorg.ir
  - name: "\U0001F1F8\U0001F1EC新加坡21 | ⬇️ 4.3MB/s"
    network: ws
    port: 443
    server: www.racknerd.com
    tls: true
    type: vless
    udp: true
    uuid: 6c7a6a6a-6a6a-4000-8000-000000000002
    ws-opts:
      headers:
        Host: legsgv4.lzj520hxw.dpdns.org
      path: /lzjjjjjjj
    servername: www.racknerd.com
  - cipher: aes-256-gcm
    name: "\U0001F1EF\U0001F1F5日本15 | ⬇️ 1.6MB/s"
    password: HP2F68D3F8G1AXVG
    port: 18006
    server: ti3hyra4.slashdevslashnetslashtun.net
    type: ss
    udp: true
  - name: "\U0001F300其他38-SE | ⬇️ 512KB/s"
    password: FYCO02u8Ny3RS38axzSTl3pcaegKlZ3BZyeS7Eaj6A9ODYIpwCAXF5CR4DqxDn
    port: 443
    server: ***********
    skip-cert-verify: true
    sni: loosely.freetrade.link
    type: trojan
    udp: true
  - auth: 5479f2d5-a0c4-437e-95c9-f79a0c485af1
    down: 400
    name: "\U0001F300其他39-TR | ⬇️ 2.1MB/s"
    password: 5479f2d5-a0c4-437e-95c9-f79a0c485af1
    port: 38653
    server: tr01.node.cross666.top
    skip-cert-verify: true
    sni: mirror.dogado.de
    type: hysteria2
    udp: true
    up: 100
  - name: "\U0001F1FA\U0001F1F8美国204 | ⬇️ 1.1MB/s"
    network: ws
    port: 8880
    server: **************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /?ed=2560
    servername: reedfree8mahsang2.redorg.ir
  - name: "\U0001F1FA\U0001F1F8美国205 | ⬇️ 6.4MB/s"
    password: 48c1e014-28d6-11ec-a0fc-f23c913c8d2b
    port: 15229
    server: d078fddf-swvls0-sxlsd4-3z3v.cu.plebai.net
    type: trojan
    udp: true
    sni: d078fddf-swvls0-sxlsd4-3z3v.cu.plebai.net
  - name: "\U0001F1ED\U0001F1F0香港24 | ⬇️ 529KB/s"
    network: ws
    port: 8880
    server: *************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /?ed=2560
  - name: "\U0001F1F8\U0001F1EC新加坡22 | ⬇️ 1.2MB/s"
    network: ws
    port: 80
    server: support.zoom.us
    tls: false
    type: vless
    udp: true
    uuid: 438f9559-1671-45cf-9d2c-338fe6766acf
    ws-opts:
      headers:
        Host: 14.sahanwickramasinghe.shop
      path: /
    servername: 14.sahanwickramasinghe.shop
  - name: "\U0001F1FA\U0001F1F8美国206 | ⬇️ 6.2MB/s"
    password: 0a335fd6-be0b-11ec-8dfa-f23c91cfbbc9
    port: 15229
    server: 8a1a9600-swvls0-sxkd63-17z95.cu.plebai.net
    skip-cert-verify: false
    type: trojan
    udp: true
    sni: 8a1a9600-swvls0-sxkd63-17z95.cu.plebai.net
  - auth: cfe61764-0004-11f0-a910-f23c9164ca5d
    name: "\U0001F1FA\U0001F1F8美国207 | ⬇️ 811KB/s"
    password: cfe61764-0004-11f0-a910-f23c9164ca5d
    port: 8443
    server: 3ff6b8af-swxgg0-sx9j1n-1timk.hy2.gotochinatown.net
    type: hysteria2
    udp: true
    sni: 3ff6b8af-swxgg0-sx9j1n-1timk.hy2.gotochinatown.net
  - cipher: aes-256-gcm
    name: "\U0001F1E8\U0001F1E6加拿大2 | ⬇️ 2.2MB/s"
    password: N12BY6B8U2YZ6WR3
    port: 20016
    server: ***************
    type: ss
    udp: true
  - name: "\U0001F1E6\U0001F1EA阿拉伯酋长国3 | ⬇️ 767KB/s"
    network: ws
    port: 8880
    server: *************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /?ed=2560
  - auth: ba4672b3-e0af-4699-a180-f711cfaa95ed
    name: "\U0001F1EB\U0001F1F7法国17 | ⬇️ 6.0MB/s"
    password: ba4672b3-e0af-4699-a180-f711cfaa95ed
    port: 43999
    server: jiangzhifg.54264944.xyz
    skip-cert-verify: true
    sni: jiangzhifr.54264944.xyz
    type: hysteria2
    udp: true
  - auth: 8f854788-85ba-4ab5-8626-5071bb6a43d3
    down: 400
    name: "\U0001F300其他40-TR | ⬇️ 2.5MB/s"
    password: 8f854788-85ba-4ab5-8626-5071bb6a43d3
    port: 37491
    server: tr01.node.cross666.top
    skip-cert-verify: true
    sni: mirror.dogado.de
    type: hysteria2
    udp: true
    up: 100
  - name: "\U0001F1FA\U0001F1F8美国208 | ⬇️ 1.5MB/s"
    password: 905fcd78-866c-81e1-3cb8-1beb59d661ab
    port: 15229
    server: 2d0a4973-swxgg0-sydxa9-fa3p.cu.plebai.net
    type: trojan
    udp: true
    sni: 2d0a4973-swxgg0-sydxa9-fa3p.cu.plebai.net
  - auth: 7b8dc3c9-cd74-4c22-9b29-************
    down: 200
    name: "\U0001F1FA\U0001F1F8美国209 | ⬇️ 2.6MB/s"
    password: 7b8dc3c9-cd74-4c22-9b29-************
    port: 38239
    server: use.node.cross666.top
    skip-cert-verify: true
    sni: www.yahoo.com
    type: hysteria2
    udp: true
    up: 100
  - name: "\U0001F1FA\U0001F1F8美国210 | ⬇️ 678KB/s"
    network: ws
    port: 8880
    server: ***********
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /?ed=2560
    servername: reedfree8mahsang2.redorg.ir
  - auth: c6c1e4eb-06de-450a-9ad6-14a903db8406
    down: 200
    name: "\U0001F1FA\U0001F1F8美国211 | ⬇️ 2.2MB/s"
    password: c6c1e4eb-06de-450a-9ad6-14a903db8406
    port: 35076
    server: use.node.cross666.top
    skip-cert-verify: true
    sni: www.yahoo.com
    type: hysteria2
    udp: true
    up: 100
  - auth: 796ec552-f8e4-43c7-ac2f-5c2e668074de
    name: "\U0001F1FA\U0001F1F8美国212 | ⬇️ 1.4MB/s"
    password: 796ec552-f8e4-43c7-ac2f-5c2e668074de
    port: 30003
    server: qymg.qy1357.top
    skip-cert-verify: true
    sni: qymg.qy1357.top
    type: hysteria2
    udp: true
  - name: "\U0001F1EE\U0001F1F3印度2 | ⬇️ 4.1MB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /TelegramU0001F1E8U0001F1F3 @MxlShare @WangCai2 /?ed=2560
    servername: reedfree8mahsang2.redorg.ir
  - auth: 8f854788-85ba-4ab5-8626-5071bb6a43d3
    down: 200
    name: "\U0001F1FA\U0001F1F8美国213 | ⬇️ 2.1MB/s"
    password: 8f854788-85ba-4ab5-8626-5071bb6a43d3
    port: 37064
    server: use.node.cross666.top
    skip-cert-verify: true
    sni: www.yahoo.com
    type: hysteria2
    udp: true
    up: 100
  - auth: 7b8dc3c9-cd74-4c22-9b29-************
    down: 400
    name: "\U0001F300其他41-TR | ⬇️ 2.6MB/s"
    password: 7b8dc3c9-cd74-4c22-9b29-************
    port: 36010
    server: tr01.node.cross666.top
    skip-cert-verify: true
    sni: mirror.dogado.de
    type: hysteria2
    udp: true
    up: 100
  - name: "\U0001F1EB\U0001F1EE芬兰3 | ⬇️ 1.7MB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /TelegramU0001F1E8U0001F1F3 @MxlShare @WangCai2 /?ed=2560
    servername: reedfree8mahsang2.redorg.ir
  - name: "\U0001F1EB\U0001F1EE芬兰4 | ⬇️ 2.8MB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: "/Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
  - name: "\U0001F1FA\U0001F1F8美国214 | ⬇️ 602KB/s"
    network: ws
    port: 443
    server: *************
    tls: true
    type: vless
    udp: true
    uuid: 5453ae26-250d-4e79-b4ec-016baf806865
    ws-opts:
      headers:
        Host: 1ssdddffffhhhjjjj.20220420.pp.ua
      path: /XcQF058rNJ3gc4aj
    servername: 1SsdDdfFffHhHJjJJ.20220420.PP.ua
  - auth: 088a0bbe-4f9f-11ea-a15d-f23c913c8d2b
    name: "\U0001F1FA\U0001F1F8美国215 | ⬇️ 1.5MB/s"
    password: 088a0bbe-4f9f-11ea-a15d-f23c913c8d2b
    port: 8443
    server: eba507f2-swxgg0-t5vrf3-716s.hy2.gotochinatown.net
    type: hysteria2
    udp: true
    sni: eba507f2-swxgg0-t5vrf3-716s.hy2.gotochinatown.net
  - name: "\U0001F1FA\U0001F1F8美国216 | ⬇️ 835KB/s"
    password: 7ddc7156-57be-11ee-9acd-f23c9164ca5d
    port: 15229
    server: 89c3e317-swvls0-tfmoda-25xl.cu.plebai.net
    type: trojan
    udp: true
    sni: 89c3e317-swvls0-tfmoda-25xl.cu.plebai.net
  - cipher: dummy
    name: "\U0001F300其他42-SC | ⬇️ 12.6MB/s"
    obfs: plain
    obfs-param: cbcdc8198.microsoft.com
    password: mantouyun888
    port: 41115
    protocol: auth_chain_a
    protocol-param: '8198:CsJt8BBrKFOSrjv2'
    server: 9988.mt.mt5888.top
    type: ssr
    udp: true
  - auth: 7b8dc3c9-cd74-4c22-9b29-************
    down: 160
    name: "\U0001F1FA\U0001F1F8美国217 | ⬇️ 2.7MB/s"
    password: 7b8dc3c9-cd74-4c22-9b29-************
    port: 35991
    server: us01.node.cross666.top
    skip-cert-verify: true
    sni: www.yahoo.com
    type: hysteria2
    udp: true
    up: 100
  - name: "\U0001F1FA\U0001F1F8美国218 | ⬇️ 944KB/s"
    network: ws
    port: 8880
    server: *************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /?ed=2560
  - name: "\U0001F1FA\U0001F1F8美国219 | ⬇️ 603KB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /TelegramU0001F1E8U0001F1F3 @MxlShare @WangCai2 /?ed=2560
    servername: reedfree8mahsang2.redorg.ir
  - cipher: aes-256-gcm
    name: "\U0001F1E8\U0001F1E6加拿大3 | ⬇️ 1.1MB/s"
    password: MG279Q3C5KLF7HGE
    port: 20015
    server: **************
    type: ss
    udp: true
  - name: "\U0001F1FA\U0001F1F8美国220 | ⬇️ 2.9MB/s"
    password: 0a335fd6-be0b-11ec-8dfa-f23c91cfbbc9
    port: 15229
    server: 68f1a514-sx4v40-sxkd63-17z95.cu.plebai.net
    skip-cert-verify: false
    type: trojan
    udp: true
    sni: 68f1a514-sx4v40-sxkd63-17z95.cu.plebai.net
  - name: "\U0001F1E9\U0001F1EA德国15 | ⬇️ 2.2MB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /TelegramU0001F1E8U0001F1F3 @MxlShare @WangCai2 /?ed=2560
    servername: reedfree8mahsang2.redorg.ir
  - auth: c6d93228-6393-41ec-8403-da852407390a
    down: 200
    name: "\U0001F1ED\U0001F1F0香港25 | ⬇️ 2.5MB/s"
    password: c6d93228-6393-41ec-8403-da852407390a
    port: 35227
    server: hka.node.cross666.top
    skip-cert-verify: true
    sni: mirrors.xtom.hk
    type: hysteria2
    udp: true
    up: 100
  - name: "\U0001F1E9\U0001F1EA德国16 | ⬇️ 687KB/s"
    network: ws
    port: 8880
    server: ***********
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: "/Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
  - name: "\U0001F1ED\U0001F1F0香港26 | ⬇️ 1.1MB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /TelegramU0001F1E8U0001F1F3 @MxlShare @WangCai2 /?ed=2560
    servername: reedfree8mahsang2.redorg.ir
  - auth: c6c1e4eb-06de-450a-9ad6-14a903db8406
    down: 400
    name: "\U0001F300其他43-TR | ⬇️ 1.9MB/s"
    password: c6c1e4eb-06de-450a-9ad6-14a903db8406
    port: 35123
    server: tr01.node.cross666.top
    skip-cert-verify: true
    sni: mirror.dogado.de
    type: hysteria2
    udp: true
    up: 100
  - auth: 796ec552-f8e4-43c7-ac2f-5c2e668074de
    name: "\U0001F1EC\U0001F1E7英国6 | ⬇️ 7.1MB/s"
    password: 796ec552-f8e4-43c7-ac2f-5c2e668074de
    port: 30003
    server: qyyg.qy1357.top
    skip-cert-verify: true
    sni: qyyg.qy1357.top
    type: hysteria2
    udp: true
  - auth: afc216c8-e407-48e0-86b1-6bf20f709c72
    down: 100
    name: "\U0001F1F7\U0001F1FA俄罗斯34 | ⬇️ 2.5MB/s"
    password: afc216c8-e407-48e0-86b1-6bf20f709c72
    port: 38956
    server: rua.node.cross666.top
    skip-cert-verify: true
    sni: mirror.hyperdedic.ru
    type: hysteria2
    udp: true
    up: 100
  - cipher: chacha20-ietf-poly1305
    name: "\U0001F1FA\U0001F1F8美国221 | ⬇️ 4.0MB/s"
    password: 0fbed6c9-0fb4-4ec3-8582-f569f8d773d5
    port: 23340
    server: ***************
    type: ss
    udp: true
  - name: "\U0001F1FA\U0001F1F8美国222 | ⬇️ 777KB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /TelegramU0001F1E8U0001F1F3 @MxlShare @WangCai2 /?ed=2560
    servername: reedfree8mahsang2.redorg.ir
  - name: "\U0001F1F0\U0001F1F7韩国9 | ⬇️ 595KB/s"
    network: ws
    port: 8880
    server: *************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /?ed=2560
    servername: reedfree8mahsang2.redorg.ir
  - alpn:
      - h3%2Ch2%2Chttp%2F1.1
    client-fingerprint: chrome
    http-opts:
      headers:
        Host:
          - hkg.gtnet.uk
      method: GET
      path:
        - /
    name: "\U0001F1ED\U0001F1F0香港27 | ⬇️ 642KB/s"
    network: http
    port: 8443
    server: hkg.gtnet.uk
    tls: true
    type: vless
    udp: true
    uuid: 42e062f1-38f3-47c9-9ed7-7294163012e4
    servername: hkg.gtnet.uk
  - alpn:
      - h3
    disable-sni: false
    name: "\U0001F1F7\U0001F1FA俄罗斯35 | ⬇️ 10.5MB/s"
    password: f3ba3679-f2f4-465f-92a5-de320b775a88
    port: 43516
    reduce-rtt: false
    request-timeout: 0
    server: **************
    skip-cert-verify: true
    sni: www.bing.com
    type: tuic
    udp-relay-mode: native
    uuid: f3ba3679-f2f4-465f-92a5-de320b775a88
    version: 5
  - auth: 8f854788-85ba-4ab5-8626-5071bb6a43d3
    down: 400
    name: "\U0001F300其他44-TR | ⬇️ 2.5MB/s"
    password: 8f854788-85ba-4ab5-8626-5071bb6a43d3
    port: 39733
    server: tr01.node.cross666.top
    skip-cert-verify: true
    sni: mirror.dogado.de
    type: hysteria2
    udp: true
    up: 100
  - name: "\U0001F300其他45-未识别 | ⬇️ 609KB/s"
    network: ws
    port: 8880
    server: *************
    tls: false
    type: vless
    udp: true
    uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
    ws-opts:
      headers:
        Host: us.laoyoutiao.link
      path: /telegramu0001f1e8u0001f1f3 @mxlshare @wangcai2 /?ed=2560
    servername: us.laoyoutiao.link
  - auth: 498c79e0-d4bb-43b0-a6c6-4bd5031c2405
    name: "\U0001F1FA\U0001F1F8美国223 | ⬇️ 6.3MB/s"
    password: 498c79e0-d4bb-43b0-a6c6-4bd5031c2405
    port: 43999
    server: jiangzhidb.54264944.xyz
    skip-cert-verify: true
    sni: jiangzhidb.54264944.xyz
    type: hysteria2
    udp: true
  - name: "\U0001F1FA\U0001F1F8美国224 | ⬇️ 1.0MB/s"
    network: ws
    port: 8880
    server: ***********
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /TelegramU0001F1E8U0001F1F3 @MxlShare @WangCai2 /?ed=2560
    servername: reedfree8mahsang2.redorg.ir
  - alpn:
      - http/1.1
    name: "\U0001F1FA\U0001F1F8美国225 | ⬇️ 684KB/s"
    network: ws
    port: 443
    server: **************
    tls: true
    type: vless
    udp: true
    uuid: 4e41764d-ac31-43f5-8e9b-231ddf252d1f
    ws-opts:
      headers:
        Host: 899.288288.shop
      path: /wAlPWPCQWnsVcNgLsMVz
    servername: 899.288288.shoP
  - auth: c6c1e4eb-06de-450a-9ad6-14a903db8406
    down: 125
    name: "\U0001F1F7\U0001F1FA俄罗斯36 | ⬇️ 2.4MB/s"
    password: c6c1e4eb-06de-450a-9ad6-14a903db8406
    port: 38635
    server: ru2.node.cross666.top
    skip-cert-verify: true
    sni: mirror.hyperdedic.ru
    type: hysteria2
    udp: true
    up: 100
  - auth: 0e00f9c1-65c3-4e20-9da8-47fa7c363423
    down: 200
    name: "\U0001F1FA\U0001F1F8美国226 | ⬇️ 2.0MB/s"
    password: 0e00f9c1-65c3-4e20-9da8-47fa7c363423
    port: 39386
    server: use.node.cross666.top
    skip-cert-verify: true
    sni: www.yahoo.com
    type: hysteria2
    udp: true
    up: 100
  - cipher: aes-256-gcm
    name: "\U0001F1F8\U0001F1EC新加坡23 | ⬇️ 1.3MB/s"
    password: FXLEJQN4Y4G6ZUHJ
    port: 16014
    server: 8tv68qhq.slashdevslashnetslashtun.net
    type: ss
    udp: true
  - client-fingerprint: chrome
    flow: xtls-rprx-vision
    name: "\U0001F1E8\U0001F1E6加拿大4 | ⬇️ 1.3MB/s"
    network: tcp
    port: 42557
    reality-opts:
      public-key: LiHpb4jWrgHBSpi1mjKH3I8m2ahpVNexeNDh-sMW3Xo
      short-id: f430927d
    server: *************
    tls: true
    type: vless
    udp: true
    uuid: 2429bee3-e0c1-47dd-b420-75e6512b184b
    servername: www.yahoo.com
  - name: "\U0001F1F3\U0001F1F1荷兰3 | ⬇️ 2.3MB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /?ed=2560
  - auth: 5479f2d5-a0c4-437e-95c9-f79a0c485af1
    down: 200
    name: "\U0001F1FA\U0001F1F8美国227 | ⬇️ 1.4MB/s"
    password: 5479f2d5-a0c4-437e-95c9-f79a0c485af1
    port: 37570
    server: use.node.cross666.top
    skip-cert-verify: true
    sni: www.yahoo.com
    type: hysteria2
    udp: true
    up: 100
  - name: "\U0001F1FA\U0001F1F8美国228 | ⬇️ 4.0MB/s"
    password: cfe61764-0004-11f0-a910-f23c9164ca5d
    port: 15229
    server: 82ab6fb4-swvls0-sx9j1n-1timk.cu.plebai.net
    type: trojan
    udp: true
    sni: 82ab6fb4-swvls0-sx9j1n-1timk.cu.plebai.net
  - name: "\U0001F1F0\U0001F1F7韩国10 | ⬇️ 894KB/s"
    network: ws
    port: 8880
    server: ***********
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /TelegramU0001F1E8U0001F1F3 @MxlShare @WangCai2 /?ed=2560
    servername: reedfree8mahsang2.redorg.ir
  - cipher: aes-256-gcm
    name: "\U0001F1F8\U0001F1EC新加坡24 | ⬇️ 742KB/s"
    password: S13NYGOMXSL7F9NA
    port: 16004
    server: ti3hyra4.slashdevslashnetslashtun.net
    type: ss
    udp: true
  - name: "\U0001F300其他46-SE | ⬇️ 6.2MB/s"
    password: E3gATlaOeIYBCc09x6apnS8yYSyCx5AF2RSuqpzF4wRZZlNKCDD3jaO833De7X
    port: 443
    server: *************
    skip-cert-verify: true
    sni: bribery.freetrade.link
    type: trojan
    udp: true
  - name: "\U0001F1FA\U0001F1F8美国229 | ⬇️ 4.2MB/s"
    password: bb85e074-b0c2-11ea-ad28-f23c913c8d2b
    port: 15229
    server: 684f1982-swvls0-sx9mz9-m0b9.cu.plebai.net
    type: trojan
    udp: true
    sni: 684f1982-swvls0-sx9mz9-m0b9.cu.plebai.net
  - name: "\U0001F1F0\U0001F1F7韩国11 | ⬇️ 930KB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /TelegramU0001F1E8U0001F1F3 @MxlShare @WangCai2 /?ed=2560
    servername: reedfree8mahsang2.redorg.ir
  - client-fingerprint: chrome
    flow: xtls-rprx-vision
    name: "\U0001F300其他47-TR | ⬇️ 4.2MB/s"
    network: tcp
    port: 20411
    reality-opts:
      public-key: 9rx7JwMO-KRZZEM9TQBO19BOAmmGjJyjN86ll2J7uVc
    server: **************
    tls: true
    type: vless
    udp: true
    uuid: 54ffbe99-7aaf-4ceb-9a82-048fb2489757
    servername: www.speedtest.net
  - name: "\U0001F1ED\U0001F1F0香港28 | ⬇️ 662KB/s"
    network: ws
    port: 8880
    server: **************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /Telegram@ShadowProxy66/?ed=2560
  - name: "\U0001F1E9\U0001F1EA德国17 | ⬇️ 975KB/s"
    network: ws
    port: 80
    server: ***********
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: "/Telegram\U0001F1E8\U0001F1F3 @WangCai2 /?ed=2560"
  - cipher: aes-256-gcm
    name: "\U0001F1E8\U0001F1E6加拿大5 | ⬇️ 1.3MB/s"
    password: J0ETCCQH1OB3BMZ7
    port: 20034
    server: *************
    type: ss
    udp: true
  - name: "\U0001F1E9\U0001F1EA德国18 | ⬇️ 1.4MB/s"
    network: ws
    port: 8880
    server: ***********
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /Telegram@ShadowProxy66/?ed=2560
  - auth: c6d93228-6393-41ec-8403-da852407390a
    down: 400
    name: "\U0001F300其他48-TR | ⬇️ 2.2MB/s"
    password: c6d93228-6393-41ec-8403-da852407390a
    port: 39870
    server: tr01.node.cross666.top
    skip-cert-verify: true
    sni: mirror.dogado.de
    type: hysteria2
    udp: true
    up: 100
  - name: "\U0001F1ED\U0001F1F0香港29 | ⬇️ 593KB/s"
    network: ws
    port: 8880
    server: ***********
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /?ed=2560
    servername: reedfree8mahsang2.redorg.ir
  - name: "\U0001F1FA\U0001F1F8美国230 | ⬇️ 607KB/s"
    password: 384d1b42-655f-11ed-a8bf-f23c91cfbbc9
    port: 15229
    server: b72211be-swxgg0-sxuzn1-11p9g.cu.plebai.net
    type: trojan
    udp: true
    sni: b72211be-swxgg0-sxuzn1-11p9g.cu.plebai.net
  - name: "\U0001F1FA\U0001F1F8美国231 | ⬇️ 3.9MB/s"
    password: affe7124-c118-11ef-b6b2-f23c9164ca5d
    port: 15229
    server: f7c9656d-swvls0-td1w5f-1t3cz.cu.plebai.net
    type: trojan
    udp: true
    sni: f7c9656d-swvls0-td1w5f-1t3cz.cu.plebai.net
  - auth: 869a0163-456f-4c06-bd4a-2376e4563eae
    name: "\U0001F1EC\U0001F1E7英国7 | ⬇️ 5.7MB/s"
    password: 869a0163-456f-4c06-bd4a-2376e4563eae
    port: 30003
    server: qyyg.qy1357.top
    skip-cert-verify: true
    sni: qyyg.qy1357.top
    type: hysteria2
    udp: true
  - name: "\U0001F1FA\U0001F1F8美国232 | ⬇️ 584KB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: "/Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
  - auth: 0e00f9c1-65c3-4e20-9da8-47fa7c363423
    down: 160
    name: "\U0001F1FA\U0001F1F8美国233 | ⬇️ 2.4MB/s"
    password: 0e00f9c1-65c3-4e20-9da8-47fa7c363423
    port: 37178
    server: us01.node.cross666.top
    skip-cert-verify: true
    sni: www.yahoo.com
    type: hysteria2
    udp: true
    up: 100
  - name: "\U0001F1E9\U0001F1EA德国19 | ⬇️ 761KB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /TelegramU0001F1E8U0001F1F3 @MxlShare @WangCai2 /?ed=2560
    servername: reedfree8mahsang2.redorg.ir
  - auth: 5479f2d5-a0c4-437e-95c9-f79a0c485af1
    down: 160
    name: "\U0001F1FA\U0001F1F8美国234 | ⬇️ 1.9MB/s"
    password: 5479f2d5-a0c4-437e-95c9-f79a0c485af1
    port: 35303
    server: us01.node.cross666.top
    skip-cert-verify: true
    sni: www.yahoo.com
    type: hysteria2
    udp: true
    up: 100
  - name: "\U0001F1E9\U0001F1EA德国20 | ⬇️ 1.1MB/s"
    network: ws
    port: 8880
    server: ***********5
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: "/Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
  - cipher: dummy
    name: "\U0001F1FA\U0001F1F8美国235 | ⬇️ 1.7MB/s"
    obfs: plain
    obfs-param: cbcdc8198.microsoft.com
    password: mantouyun888
    port: 44005
    protocol: auth_chain_a
    protocol-param: '8198:CsJt8BBrKFOSrjv2'
    server: 88gg.mt.mt5888.top
    type: ssr
    udp: true
  - auth: 34f0cf42-0f8a-11ec-a8bf-f23c91cfbbc9
    name: "\U0001F1FA\U0001F1F8美国236 | ⬇️ 1.6MB/s"
    password: 34f0cf42-0f8a-11ec-a8bf-f23c91cfbbc9
    port: 8443
    server: 99ea4f21-swxgg0-szdere-155d9.hy2.gotochinatown.net
    type: hysteria2
    udp: true
    sni: 99ea4f21-swxgg0-szdere-155d9.hy2.gotochinatown.net
  - name: "\U0001F1E9\U0001F1EA德国21 | ⬇️ 853KB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /?ed=2560
  - name: "\U0001F1F0\U0001F1F7韩国12 | ⬇️ 649KB/s"
    network: ws
    port: 8880
    server: ***********
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /TelegramU0001F1E8U0001F1F3 @MxlShare @WangCai2 /?ed=2560
    servername: reedfree8mahsang2.redorg.ir
  - alterId: 64
    cipher: auto
    name: "\U0001F1F8\U0001F1EC新加坡25 | ⬇️ 549KB/s"
    port: 49302
    server: *************
    tls: false
    type: vmess
    udp: true
    uuid: 418048af-a293-4b99-9b0c-98ca3580dd24
  - auth: 384d1b42-655f-11ed-a8bf-f23c91cfbbc9
    name: "\U0001F1FA\U0001F1F8美国237 | ⬇️ 1.3MB/s"
    password: 384d1b42-655f-11ed-a8bf-f23c91cfbbc9
    port: 8443
    server: 1e2fa6c6-swxgg0-sxuzn1-11p9g.hy2.gotochinatown.net
    type: hysteria2
    udp: true
    sni: 1e2fa6c6-swxgg0-sxuzn1-11p9g.hy2.gotochinatown.net
  - auth: c6d93228-6393-41ec-8403-da852407390a
    down: 160
    name: "\U0001F1FA\U0001F1F8美国238 | ⬇️ 2.0MB/s"
    password: c6d93228-6393-41ec-8403-da852407390a
    port: 35699
    server: us01.node.cross666.top
    skip-cert-verify: true
    sni: www.yahoo.com
    type: hysteria2
    udp: true
    up: 100
  - auth: 48c1e014-28d6-11ec-a0fc-f23c913c8d2b
    name: "\U0001F1FA\U0001F1F8美国239 | ⬇️ 1.5MB/s"
    password: 48c1e014-28d6-11ec-a0fc-f23c913c8d2b
    port: 8443
    server: 7a48defb-swxgg0-sxlsd4-3z3v.hy2.gotochinatown.net
    type: hysteria2
    udp: true
    sni: 7a48defb-swxgg0-sxlsd4-3z3v.hy2.gotochinatown.net
  - name: "\U0001F1FA\U0001F1F8美国240 | ⬇️ 7.4MB/s"
    network: ws
    password: f82fb954-06a1-4f0b-9180-17e07585b61f
    port: 443
    server: *************
    skip-cert-verify: false
    sni: 6tghjk.131.pp.ua
    type: trojan
    udp: true
    ws-opts:
      headers:
        Host: 6tghjk.131.pp.ua
      path: /%2FYu29UN3Yz8hcnI3SlZor0
  - auth: 7b8dc3c9-cd74-4c22-9b29-************
    down: 400
    name: "\U0001F1EB\U0001F1F7法国18 | ⬇️ 2.3MB/s"
    password: 7b8dc3c9-cd74-4c22-9b29-************
    port: 35699
    server: fr1.node.cross666.top
    skip-cert-verify: true
    sni: ubuntu.mirrors.ovh.net
    type: hysteria2
    udp: true
    up: 100
  - auth: 47e91fb6-25b8-df52-bc18-3cdc8548192f
    name: "\U0001F1FA\U0001F1F8美国241 | ⬇️ 2.5MB/s"
    password: 47e91fb6-25b8-df52-bc18-3cdc8548192f
    port: 8443
    server: c536160e-swxgg0-sydzqp-81nv.hy2.gotochinatown.net
    type: hysteria2
    udp: true
    sni: c536160e-swxgg0-sydzqp-81nv.hy2.gotochinatown.net
  - auth: c6d93228-6393-41ec-8403-da852407390a
    down: 180
    name: "\U0001F1FA\U0001F1F8美国242 | ⬇️ 2.4MB/s"
    password: c6d93228-6393-41ec-8403-da852407390a
    port: 37874
    server: us3.node.cross666.top
    skip-cert-verify: true
    sni: www.yahoo.com
    type: hysteria2
    udp: true
    up: 100
  - name: "\U0001F1FA\U0001F1F8美国243 | ⬇️ 3.0MB/s"
    password: 7ddc7156-57be-11ee-9acd-f23c9164ca5d
    port: 15229
    server: 29d1920a-swxgg0-tfmoda-25xl.cu.plebai.net
    type: trojan
    udp: true
    sni: 29d1920a-swxgg0-tfmoda-25xl.cu.plebai.net
  - name: "\U0001F1FA\U0001F1F8美国244 | ⬇️ 1.3MB/s"
    network: ws
    port: 80
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: '/Telegram:'
  - name: "\U0001F1E9\U0001F1EA德国22 | ⬇️ 561KB/s"
    network: ws
    port: 8880
    server: **************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /?ed=2560
  - name: "\U0001F1FA\U0001F1F8美国245 | ⬇️ 2.6MB/s"
    network: ws
    port: 8880
    server: ***********
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /TelegramU0001F1E8U0001F1F3 @MxlShare @WangCai2 /?ed=2560
    servername: reedfree8mahsang2.redorg.ir
  - auth: 06b2ac52-fcc6-11ec-bb74-f23c9164ca5d
    name: "\U0001F1FA\U0001F1F8美国246 | ⬇️ 3.7MB/s"
    password: 06b2ac52-fcc6-11ec-bb74-f23c9164ca5d
    port: 8443
    server: 2c055931-swxgg0-sznzxg-1jfvb.hy2.gotochinatown.net
    type: hysteria2
    udp: true
    sni: 2c055931-swxgg0-sznzxg-1jfvb.hy2.gotochinatown.net
  - cipher: aes-256-gcm
    name: "\U0001F1ED\U0001F1F0香港30 | ⬇️ 4.6MB/s"
    password: 74UQT9UIS5T2UFKL
    port: 15016
    server: ti3hyra4.slashdevslashnetslashtun.net
    type: ss
    udp: true
  - name: "\U0001F300其他49-未识别 | ⬇️ 869KB/s"
    network: ws
    port: 8880
    server: *************
    tls: false
    type: vless
    udp: true
    uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
    ws-opts:
      headers:
        Host: us.laoyoutiao.link
      path: "/Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
  - alpn:
      - h3
    auth-str: dongtaiwang.com
    auth_str: dongtaiwang.com
    down: 55 Mbps
    name: "\U0001F1FA\U0001F1F8美国247 | ⬇️ 5.5MB/s"
    port: 11512
    protocol: udp
    server: *************
    skip-cert-verify: true
    sni: apple.com
    type: hysteria
    udp: true
    up: 11 Mbps
  - name: "\U0001F1FA\U0001F1F8美国248 | ⬇️ 906KB/s"
    network: ws
    port: 8880
    server: **************
    tls: false
    type: vless
    udp: true
    uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
    ws-opts:
      headers:
        Host: us.laoyoutiao.link
      path: "/Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
  - auth: 8f854788-85ba-4ab5-8626-5071bb6a43d3
    down: 400
    name: "\U0001F1EB\U0001F1F7法国19 | ⬇️ 2.5MB/s"
    password: 8f854788-85ba-4ab5-8626-5071bb6a43d3
    port: 37887
    server: fr1.node.cross666.top
    skip-cert-verify: true
    sni: ubuntu.mirrors.ovh.net
    type: hysteria2
    udp: true
    up: 100
  - auth: 7b8dc3c9-cd74-4c22-9b29-************
    down: 300
    name: "\U0001F1F8\U0001F1EC新加坡26 | ⬇️ 877KB/s"
    password: 7b8dc3c9-cd74-4c22-9b29-************
    port: 36139
    server: sg02.node.cross666.top
    skip-cert-verify: true
    sni: mirror.sg.gs
    type: hysteria2
    udp: true
    up: 100
  - cipher: aes-256-gcm
    name: "\U0001F1F8\U0001F1EC新加坡27 | ⬇️ 1.3MB/s"
    password: QQT24A5FM1OAYMPS
    port: 16013
    server: ti3hyra4.slashdevslashnetslashtun.net
    type: ss
    udp: true
  - name: "\U0001F1FA\U0001F1F8美国249 | ⬇️ 4.3MB/s"
    password: b79d79ae-8bce-11ef-a2b8-f23c9164ca5d
    port: 15229
    server: db072d42-swxgg0-t67sv3-1snfs.cu.plebai.net
    type: trojan
    udp: true
    sni: db072d42-swxgg0-t67sv3-1snfs.cu.plebai.net
  - auth: 7b8dc3c9-cd74-4c22-9b29-************
    down: 400
    name: "\U0001F300其他50-TR | ⬇️ 2.5MB/s"
    password: 7b8dc3c9-cd74-4c22-9b29-************
    port: 36179
    server: tr01.node.cross666.top
    skip-cert-verify: true
    sni: mirror.dogado.de
    type: hysteria2
    udp: true
    up: 100
  - auth: c6c1e4eb-06de-450a-9ad6-14a903db8406
    down: 100
    name: "\U0001F1F7\U0001F1FA俄罗斯37 | ⬇️ 2.6MB/s"
    password: c6c1e4eb-06de-450a-9ad6-14a903db8406
    port: 35198
    server: rua.node.cross666.top
    skip-cert-verify: true
    sni: mirror.hyperdedic.ru
    type: hysteria2
    udp: true
    up: 100
  - auth: afc216c8-e407-48e0-86b1-6bf20f709c72
    down: 400
    name: "\U0001F300其他51-TR | ⬇️ 2.5MB/s"
    password: afc216c8-e407-48e0-86b1-6bf20f709c72
    port: 39167
    server: tr01.node.cross666.top
    skip-cert-verify: true
    sni: mirror.dogado.de
    type: hysteria2
    udp: true
    up: 100
  - name: "\U0001F1FA\U0001F1F8美国250 | ⬇️ 1.5MB/s"
    network: ws
    port: 8880
    server: **************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /Telegram@ShadowProxy66/?ed=2560
  - name: "\U0001F1FA\U0001F1F8美国251 | ⬇️ 2.0MB/s"
    password: 127e3f92-f714-11ef-bbb0-f23c91cfbbc9
    port: 15229
    server: 31e79955-swxgg0-t2c3sq-1spnr.cu.plebai.net
    type: trojan
    udp: true
    sni: 31e79955-swxgg0-t2c3sq-1spnr.cu.plebai.net
  - cipher: aes-256-gcm
    name: "\U0001F1F8\U0001F1EC新加坡28 | ⬇️ 623KB/s"
    password: RFUKD9DMSTSXGAJ8
    port: 16015
    server: ti3hyra4.slashdevslashnetslashtun.net
    type: ss
    udp: true
  - auth: 24a562c3-75be-4a36-8442-b341463480d5
    down: 50
    name: "\U0001F1FA\U0001F1F8美国252 | ⬇️ 698KB/s"
    obfs: salamander
    obfs-password: NDhhNmY5YTY0MGYzOTgxYQ==
    password: 24a562c3-75be-4a36-8442-b341463480d5
    port: 31005
    server: **************
    skip-cert-verify: true
    type: hysteria2
    udp: true
    up: 50
  - name: "\U0001F300其他52-未识别 | ⬇️ 710KB/s"
    network: ws
    port: 80
    server: ***************
    tls: false
    type: vless
    udp: true
    uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
    ws-opts:
      headers:
        Host: us.laoyoutiao.link
      path: "/Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
  - auth: 8f854788-85ba-4ab5-8626-5071bb6a43d3
    down: 400
    name: "\U0001F300其他53-TR | ⬇️ 1.9MB/s"
    password: 8f854788-85ba-4ab5-8626-5071bb6a43d3
    port: 39646
    server: tr01.node.cross666.top
    skip-cert-verify: true
    sni: mirror.dogado.de
    type: hysteria2
    udp: true
    up: 100
  - auth: c6d93228-6393-41ec-8403-da852407390a
    down: 300
    name: "\U0001F1FA\U0001F1F8美国253 | ⬇️ 1.9MB/s"
    password: c6d93228-6393-41ec-8403-da852407390a
    port: 36258
    server: us4.node.cross666.top
    skip-cert-verify: true
    sni: www.yahoo.com
    type: hysteria2
    udp: true
    up: 100
  - name: "\U0001F1FA\U0001F1F8美国254 | ⬇️ 6.3MB/s"
    password: 088a0bbe-4f9f-11ea-a15d-f23c913c8d2b
    port: 15229
    server: b6b67e0e-swvls0-t5vrf3-716s.cu.plebai.net
    type: trojan
    udp: true
    sni: b6b67e0e-swvls0-t5vrf3-716s.cu.plebai.net
  - auth: c6d93228-6393-41ec-8403-da852407390a
    down: 400
    name: "\U0001F300其他54-TR | ⬇️ 1.9MB/s"
    password: c6d93228-6393-41ec-8403-da852407390a
    port: 37805
    server: tr01.node.cross666.top
    skip-cert-verify: true
    sni: mirror.dogado.de
    type: hysteria2
    udp: true
    up: 100
  - auth: c6d93228-6393-41ec-8403-da852407390a
    down: 400
    name: "\U0001F300其他55-TR | ⬇️ 1.0MB/s"
    password: c6d93228-6393-41ec-8403-da852407390a
    port: 35009
    server: tr01.node.cross666.top
    skip-cert-verify: true
    sni: mirror.dogado.de
    type: hysteria2
    udp: true
    up: 100
  - client-fingerprint: randomized
    name: "\U0001F1E8\U0001F1E6加拿大6 | ⬇️ 2.1MB/s"
    network: ws
    port: 2053
    server: **************
    tls: true
    type: vless
    udp: true
    uuid: d6b8011a-c725-435a-9fec-bf6d3530392c
    ws-opts:
      headers:
        Host: vle.amclubsapp.dpdns.org
      path: /?ed=2560&PROT_TYPE=vless
    servername: vle.amclubsapp.dpdns.org
  - name: "\U0001F1FA\U0001F1F8美国255 | ⬇️ 4.2MB/s"
    password: 3d7182ce-8dcc-11ef-a3f6-f23c9164ca5d
    port: 15229
    server: f22a91be-swvls0-thdi1k-19yro.cu.plebai.net
    type: trojan
    udp: true
    sni: f22a91be-swvls0-thdi1k-19yro.cu.plebai.net
  - auth: c6d93228-6393-41ec-8403-da852407390a
    down: 200
    name: "\U0001F1FA\U0001F1F8美国256 | ⬇️ 2.0MB/s"
    password: c6d93228-6393-41ec-8403-da852407390a
    port: 35925
    server: use.node.cross666.top
    skip-cert-verify: true
    sni: www.yahoo.com
    type: hysteria2
    udp: true
    up: 100
  - auth: 0e00f9c1-65c3-4e20-9da8-47fa7c363423
    down: 300
    name: "\U0001F1FA\U0001F1F8美国257 | ⬇️ 2.4MB/s"
    password: 0e00f9c1-65c3-4e20-9da8-47fa7c363423
    port: 35844
    server: us4.node.cross666.top
    skip-cert-verify: true
    sni: www.yahoo.com
    type: hysteria2
    udp: true
    up: 100
  - alpn:
      - http/1.1
    name: "\U0001F1FA\U0001F1F8美国258 | ⬇️ 4.9MB/s"
    network: ws
    port: 443
    server: *************
    tls: true
    type: vless
    udp: true
    uuid: 585acd30-52bf-4b70-a537-e13649fafefc
    ws-opts:
      headers:
        Host: bba.890601.pp.ua
      path: /rU9rSjDSOd4yY2fOe
    servername: bBA.890601.pP.UA
  - cipher: aes-256-gcm
    name: "\U0001F1ED\U0001F1F0香港31 | ⬇️ 7.9MB/s"
    password: 4KGHWKCKQAJOVPHO
    port: 15003
    server: 8tv68qhq.slashdevslashnetslashtun.net
    type: ss
    udp: true
  - name: "\U0001F1FA\U0001F1F8美国259 | ⬇️ 929KB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /TelegramU0001F1E8U0001F1F3 @MxlShare @WangCai2 /?ed=2560
    servername: reedfree8mahsang2.redorg.ir
  - name: "\U0001F1FA\U0001F1F8美国260 | ⬇️ 5.6MB/s"
    password: c1785de0-212f-11ef-92aa-f23c9164ca5d
    port: 15229
    server: 9622193a-swvls0-t6570q-1q3dm.cu.plebai.net
    type: trojan
    udp: true
    sni: 9622193a-swvls0-t6570q-1q3dm.cu.plebai.net
  - name: "\U0001F1FA\U0001F1F8美国261 | ⬇️ 1.7MB/s"
    password: a5a81a34-f257-11ef-ba82-f23c913c8d2b
    port: 15229
    server: e3bd1706-swxgg0-syc5sd-1teuc.cu.plebai.net
    type: trojan
    udp: true
    sni: e3bd1706-swxgg0-syc5sd-1teuc.cu.plebai.net
  - name: "\U0001F1EB\U0001F1EE芬兰5 | ⬇️ 592KB/s"
    network: ws
    port: 80
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: '/Telegram: @vpnAndroid2/?ed=2560'
  - name: "\U0001F1FA\U0001F1F8美国262 | ⬇️ 538KB/s"
    password: affe7124-c118-11ef-b6b2-f23c9164ca5d
    port: 15229
    server: 85326a62-swxgg0-td1w5f-1t3cz.cu.plebai.net
    type: trojan
    udp: true
    sni: 85326a62-swxgg0-td1w5f-1t3cz.cu.plebai.net
  - name: "\U0001F1FA\U0001F1F8美国263 | ⬇️ 2.4MB/s"
    network: ws
    port: 80
    server: ***********
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: '/Telegram: @vpnAndroid2/?ed=2560'
  - client-fingerprint: chrome
    name: "\U0001F1FA\U0001F1F8美国264 | ⬇️ 1.3MB/s"
    network: tcp
    port: 20230
    reality-opts:
      public-key: zWY5WwfH-Q8xvLZAr28WjRVk05L4WCp4B7F1lWPM0A8
      short-id: c1839f26
    server: us001.421421.xyz
    tls: true
    type: vless
    udp: true
    uuid: 2ba7e4d0-f2f9-456e-a43e-f5a724d7e367
    servername: www.nvidia.com
  - name: "\U0001F1FA\U0001F1F8美国265 | ⬇️ 540KB/s"
    network: ws
    port: 80
    server: *************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: '/Telegram: @vpnAndroid2/?ed=2560'
  - name: "\U0001F1FA\U0001F1F8美国266 | ⬇️ 3.3MB/s"
    password: 0da8651e-e1f6-11ec-bd7c-f23c913c8d2b
    port: 15229
    server: 32f54781-swvls0-tcinla-hrtf.cu.plebai.net
    type: trojan
    udp: true
    sni: 32f54781-swvls0-tcinla-hrtf.cu.plebai.net
  - auth: b585dac6-dc69-11ef-9f1c-f23c913c8d2b
    name: "\U0001F1FA\U0001F1F8美国267 | ⬇️ 1.4MB/s"
    password: b585dac6-dc69-11ef-9f1c-f23c913c8d2b
    port: 8443
    server: 089afcf5-swxgg0-sy1j4r-1mqag.hy2.gotochinatown.net
    type: hysteria2
    udp: true
    sni: 089afcf5-swxgg0-sy1j4r-1mqag.hy2.gotochinatown.net
  - name: "\U0001F1FA\U0001F1F8美国268 | ⬇️ 1.2MB/s"
    password: 7d7c111e-fbc1-11ef-ab5f-f23c913c8d2b
    port: 15229
    server: 73f77226-swxgg0-sxhurg-1th8j.cu.plebai.net
    type: trojan
    udp: true
    sni: 73f77226-swxgg0-sxhurg-1th8j.cu.plebai.net
  - name: "\U0001F1F0\U0001F1F7韩国13 | ⬇️ 600KB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /TelegramU0001F1E8U0001F1F3 @MxlShare @WangCai2 /?ed=2560
    servername: reedfree8mahsang2.redorg.ir
  - auth: c6d93228-6393-41ec-8403-da852407390a
    down: 400
    name: "\U0001F300其他56-TR | ⬇️ 2.2MB/s"
    password: c6d93228-6393-41ec-8403-da852407390a
    port: 37820
    server: tr01.node.cross666.top
    skip-cert-verify: true
    sni: mirror.dogado.de
    type: hysteria2
    udp: true
    up: 100
  - cipher: aes-256-gcm
    name: "\U0001F300其他57-PE | ⬇️ 625KB/s"
    password: AARDHP6T88RMA84P
    port: 20009
    server: *************
    type: ss
    udp: true
  - auth: afc216c8-e407-48e0-86b1-6bf20f709c72
    down: 200
    name: "\U0001F1FA\U0001F1F8美国269 | ⬇️ 2.4MB/s"
    password: afc216c8-e407-48e0-86b1-6bf20f709c72
    port: 37228
    server: use.node.cross666.top
    skip-cert-verify: true
    sni: www.yahoo.com
    type: hysteria2
    udp: true
    up: 100
  - auth: c6d93228-6393-41ec-8403-da852407390a
    down: 400
    name: "\U0001F300其他58-TR | ⬇️ 1.1MB/s"
    password: c6d93228-6393-41ec-8403-da852407390a
    port: 35037
    server: tr01.node.cross666.top
    skip-cert-verify: true
    sni: mirror.dogado.de
    type: hysteria2
    udp: true
    up: 100
  - name: "\U0001F1FA\U0001F1F8美国270 | ⬇️ 732KB/s"
    network: ws
    port: 8880
    server: ***********
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /TelegramU0001F1E8U0001F1F3 @MxlShare @WangCai2 /?ed=2560
    servername: reedfree8mahsang2.redorg.ir
  - auth: 905fcd78-866c-81e1-3cb8-1beb59d661ab
    name: "\U0001F1FA\U0001F1F8美国271 | ⬇️ 3.4MB/s"
    password: 905fcd78-866c-81e1-3cb8-1beb59d661ab
    port: 8443
    server: e1b5e2e9-swxgg0-sydxa9-fa3p.hy2.gotochinatown.net
    type: hysteria2
    udp: true
    sni: e1b5e2e9-swxgg0-sydxa9-fa3p.hy2.gotochinatown.net
  - auth: c6c1e4eb-06de-450a-9ad6-14a903db8406
    down: 400
    name: "\U0001F300其他59-TR | ⬇️ 2.2MB/s"
    password: c6c1e4eb-06de-450a-9ad6-14a903db8406
    port: 37209
    server: tr01.node.cross666.top
    skip-cert-verify: true
    sni: mirror.dogado.de
    type: hysteria2
    udp: true
    up: 100
  - name: "\U0001F1FA\U0001F1F8美国272 | ⬇️ 756KB/s"
    network: ws
    port: 8880
    server: **************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /?ed=2560
  - cipher: aes-256-gcm
    name: "\U0001F1EF\U0001F1F5日本16 | ⬇️ 1.1MB/s"
    password: 3SX9TMBC7QL6P28X
    port: 18012
    server: ti3hyra4.slashdevslashnetslashtun.net
    type: ss
    udp: true
  - auth: 8f854788-85ba-4ab5-8626-5071bb6a43d3
    down: 300
    name: "\U0001F1FA\U0001F1F8美国273 | ⬇️ 2.2MB/s"
    password: 8f854788-85ba-4ab5-8626-5071bb6a43d3
    port: 35395
    server: us4.node.cross666.top
    skip-cert-verify: true
    sni: www.yahoo.com
    type: hysteria2
    udp: true
    up: 100
  - cipher: aes-256-gcm
    name: "\U0001F1ED\U0001F1F0香港32 | ⬇️ 2.5MB/s"
    password: GMB5RP19CE51DJ6J
    port: 15007
    server: ti3hyra4.slashdevslashnetslashtun.net
    type: ss
    udp: true
  - cipher: aes-128-gcm
    name: "\U0001F1EC\U0001F1E7英国8 | ⬇️ 600KB/s"
    password: 763bf612-4c66-4fd4-b54b-5349bdea6bca
    port: 634
    server: neweur.upperlay.xyz
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1ED\U0001F1F0香港33 | ⬇️ 1.4MB/s"
    password: PSGZOPQW8YFSZOCJ
    port: 15005
    server: 8tv68qhq.slashdevslashnetslashtun.net
    type: ss
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1E8\U0001F1E6加拿大7 | ⬇️ 6.1MB/s"
    password: USAAWWY4IGI7M6O6
    port: 20026
    server: *************
    type: ss
    udp: true
  - name: "\U0001F300其他60-未识别 | ⬇️ 1.3MB/s"
    network: ws
    port: 80
    server: **************
    tls: false
    type: vless
    udp: true
    uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
    ws-opts:
      headers:
        Host: cs.laoyoutiao.link
      path: "/Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
  - name: "\U0001F1EB\U0001F1F7法国20 | ⬇️ 1.8MB/s"
    network: ws
    port: 80
    server: www.speedtest.net
    tls: false
    type: vless
    udp: true
    uuid: 2aab812a-8cb5-4f39-9af5-90602aaf8a9f
    ws-opts:
      headers:
        Host: www.speedtest.net1.svgspeedtest.net.
      path: >-
        /@SavageNet----@SavageNet----@SavageNet----@SavageNet----@SavageNet----@SavageNet----@SavageNet----@SavageNet----@SavageNet----@SavageNet----@SavageNet----@SavageNet?ed=2048
  - name: "\U0001F1ED\U0001F1F0香港34 | ⬇️ 990KB/s"
    network: ws
    port: 8880
    server: ***********
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /?ed=2560
  - auth: 85ac1c2d-f3c4-496d-9a01-8d0d84eab17d
    down: 200
    name: "\U0001F1FA\U0001F1F8美国274 | ⬇️ 2.3MB/s"
    password: 85ac1c2d-f3c4-496d-9a01-8d0d84eab17d
    port: 38522
    server: use.node.cross666.top
    skip-cert-verify: true
    sni: www.yahoo.com
    type: hysteria2
    udp: true
    up: 100
  - cipher: aes-256-gcm
    name: "\U0001F1E8\U0001F1E6加拿大8 | ⬇️ 979KB/s"
    password: TWOQKRHH53YN2YKF
    port: 20035
    server: **************
    type: ss
    udp: true
  - name: "\U0001F1FA\U0001F1F8美国275 | ⬇️ 771KB/s"
    password: 7d7c111e-fbc1-11ef-ab5f-f23c913c8d2b
    port: 15229
    server: 2d79f7e0-swvls0-sxhurg-1th8j.cu.plebai.net
    type: trojan
    udp: true
    sni: 2d79f7e0-swvls0-sxhurg-1th8j.cu.plebai.net
  - name: "\U0001F1ED\U0001F1F0香港35 | ⬇️ 932KB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /TelegramU0001F1E8U0001F1F3 @MxlShare @WangCai2 /?ed=2560
    servername: reedfree8mahsang2.redorg.ir
  - name: "\U0001F1FA\U0001F1F8美国276 | ⬇️ 562KB/s"
    password: 088a0bbe-4f9f-11ea-a15d-f23c913c8d2b
    port: 15229
    server: 7a25ddbb-swxgg0-t5vrf3-716s.cu.plebai.net
    type: trojan
    udp: true
    sni: 7a25ddbb-swxgg0-t5vrf3-716s.cu.plebai.net
  - auth: c6d93228-6393-41ec-8403-da852407390a
    down: 300
    name: "\U0001F1FA\U0001F1F8美国277 | ⬇️ 1.9MB/s"
    password: c6d93228-6393-41ec-8403-da852407390a
    port: 35524
    server: us4.node.cross666.top
    skip-cert-verify: true
    sni: www.yahoo.com
    type: hysteria2
    udp: true
    up: 100
  - name: "\U0001F1F3\U0001F1F1荷兰4 | ⬇️ 680KB/s"
    network: ws
    port: 8880
    server: **************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /?ed=2560
  - name: "\U0001F1FA\U0001F1F8美国278 | ⬇️ 4.3MB/s"
    password: 3d7182ce-8dcc-11ef-a3f6-f23c9164ca5d
    port: 15229
    server: 7670ad60-swxgg0-thdi1k-19yro.cu.plebai.net
    type: trojan
    udp: true
    sni: 7670ad60-swxgg0-thdi1k-19yro.cu.plebai.net
  - auth: c6d93228-6393-41ec-8403-da852407390a
    name: "\U0001F1FA\U0001F1F8美国279 | ⬇️ 2.1MB/s"
    password: c6d93228-6393-41ec-8403-da852407390a
    port: 38030
    server: use.node.cross666.top
    skip-cert-verify: true
    sni: www.yahoo.com
    type: hysteria2
    udp: true
  - auth: 91365a7a-46d7-11ee-a8b9-f23c9164ca5d
    name: "\U0001F1FA\U0001F1F8美国280 | ⬇️ 564KB/s"
    password: 91365a7a-46d7-11ee-a8b9-f23c9164ca5d
    port: 8443
    server: 798b60f9-swxgg0-t3o6u7-1osdm.hy2.gotochinatown.net
    type: hysteria2
    udp: true
    sni: 798b60f9-swxgg0-t3o6u7-1osdm.hy2.gotochinatown.net
  - auth: c6d93228-6393-41ec-8403-da852407390a
    down: 400
    name: "\U0001F300其他61-TR | ⬇️ 2.1MB/s"
    password: c6d93228-6393-41ec-8403-da852407390a
    port: 38663
    server: tr01.node.cross666.top
    skip-cert-verify: true
    sni: mirror.dogado.de
    type: hysteria2
    udp: true
    up: 100
  - alpn:
      - http/1.1
    name: "\U0001F1FA\U0001F1F8美国281 | ⬇️ 1.5MB/s"
    network: ws
    password: 061ec9d5-ba40-428a-8be0-1947b10b5cfc
    port: 443
    server: *************
    skip-cert-verify: false
    sni: 16gG.irAN.pP.UA
    type: trojan
    udp: true
    ws-opts:
      headers:
        Host: 16gG.irAN.pP.UA
      path: /Dmby2AKiZKvMo1YauoQwY5
  - auth: e3975a05-b89e-11ec-a50d-f23c91cfbbc9
    name: "\U0001F1FA\U0001F1F8美国282 | ⬇️ 516KB/s"
    password: e3975a05-b89e-11ec-a50d-f23c91cfbbc9
    port: 8443
    server: 77ccec48-swxgg0-sxf56z-1f596.hy2.gotochinatown.net
    type: hysteria2
    udp: true
    sni: 77ccec48-swxgg0-sxf56z-1f596.hy2.gotochinatown.net
  - auth: 94d40708-8273-11ea-8fc9-f23c913c8d2b
    name: "\U0001F1FA\U0001F1F8美国283 | ⬇️ 578KB/s"
    password: 94d40708-8273-11ea-8fc9-f23c913c8d2b
    port: 8443
    server: 92addeea-swxgg0-sye5o0-mv7m.hy2.gotochinatown.net
    type: hysteria2
    udp: true
    sni: 92addeea-swxgg0-sye5o0-mv7m.hy2.gotochinatown.net
  - auth: 0da8651e-e1f6-11ec-bd7c-f23c913c8d2b
    name: "\U0001F1FA\U0001F1F8美国284 | ⬇️ 689KB/s"
    password: 0da8651e-e1f6-11ec-bd7c-f23c913c8d2b
    port: 8443
    server: b864a754-swxgg0-tcinla-hrtf.hy2.gotochinatown.net
    type: hysteria2
    udp: true
    sni: b864a754-swxgg0-tcinla-hrtf.hy2.gotochinatown.net
  - name: "\U0001F300其他62-未识别 | ⬇️ 877KB/s"
    network: ws
    port: 8880
    server: *************
    tls: false
    type: vless
    udp: true
    uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
    ws-opts:
      headers:
        Host: us.laoyoutiao.link
      path: "/Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
  - auth: 8f854788-85ba-4ab5-8626-5071bb6a43d3
    down: 200
    name: "\U0001F1FA\U0001F1F8美国285 | ⬇️ 1.9MB/s"
    password: 8f854788-85ba-4ab5-8626-5071bb6a43d3
    port: 36160
    server: use.node.cross666.top
    skip-cert-verify: true
    sni: www.yahoo.com
    type: hysteria2
    udp: true
    up: 100
  - name: "\U0001F1F3\U0001F1F1荷兰5 | ⬇️ 568KB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /TelegramU0001F1E8U0001F1F3 @MxlShare @WangCai2 /?ed=2560
    servername: reedfree8mahsang2.redorg.ir
  - auth: 5479f2d5-a0c4-437e-95c9-f79a0c485af1
    down: 200
    name: "\U0001F1FA\U0001F1F8美国286 | ⬇️ 2.3MB/s"
    password: 5479f2d5-a0c4-437e-95c9-f79a0c485af1
    port: 39025
    server: use.node.cross666.top
    skip-cert-verify: true
    sni: www.yahoo.com
    type: hysteria2
    udp: true
    up: 100
  - name: "\U0001F1FA\U0001F1F8美国287 | ⬇️ 3.4MB/s"
    password: 06b2ac52-fcc6-11ec-bb74-f23c9164ca5d
    port: 15229
    server: 4ff95e70-swvls0-sznzxg-1jfvb.cu.plebai.net
    type: trojan
    udp: true
    sni: 4ff95e70-swvls0-sznzxg-1jfvb.cu.plebai.net
  - name: "\U0001F1E9\U0001F1EA德国23 | ⬇️ 887KB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /TelegramU0001F1E8U0001F1F3 @MxlShare @WangCai2 /?ed=2560
    servername: reedfree8mahsang2.redorg.ir
  - name: "\U0001F1FA\U0001F1F8美国288 | ⬇️ 2.9MB/s"
    password: 93fb69fc-77cf-11ee-85ee-f23c91369f2d
    port: 15229
    server: e0967449-swxgg0-t12cnj-1ol97.cu.plebai.net
    type: trojan
    udp: true
    sni: e0967449-swxgg0-t12cnj-1ol97.cu.plebai.net
  - cipher: aes-256-gcm
    name: "\U0001F1E8\U0001F1E6加拿大9 | ⬇️ 5.7MB/s"
    password: TDBT3LH5U9JD65PK
    port: 20032
    server: ************
    type: ss
    udp: true
  - name: "\U0001F1FA\U0001F1F8美国289 | ⬇️ 516KB/s"
    password: a5a81a34-f257-11ef-ba82-f23c913c8d2b
    port: 15229
    server: ba45dd68-swvls0-syc5sd-1teuc.cu.plebai.net
    type: trojan
    udp: true
    sni: ba45dd68-swvls0-syc5sd-1teuc.cu.plebai.net
  - name: "\U0001F300其他63-未识别 | ⬇️ 1.5MB/s"
    network: ws
    password: 2ba7e4d0-f2f9-456e-a43e-f5a724d7e367
    port: 20230
    server: jp006.421421.xyz
    skip-cert-verify: true
    sni: www.alibaba.com
    type: trojan
    udp: true
    ws-opts:
      headers:
        Host: www.alibaba.com
      path: /
  - auth: c6d93228-6393-41ec-8403-da852407390a
    down: 400
    name: "\U0001F300其他64-TR | ⬇️ 2.1MB/s"
    password: c6d93228-6393-41ec-8403-da852407390a
    port: 39552
    server: tr01.node.cross666.top
    skip-cert-verify: true
    sni: mirror.dogado.de
    type: hysteria2
    udp: true
    up: 100
  - alpn:
      - h3
      - h2
    client-fingerprint: chrome
    name: "\U0001F300其他65-MD | ⬇️ 6.7MB/s"
    network: ws
    port: 2096
    server: *************
    tls: true
    type: vless
    udp: true
    uuid: 96a1b724-68d2-4f4d-ab25-38ecd83577bc
    ws-opts:
      headers:
        Host: HiDyVnDe14.bInTeRmA.OrG
      path: /
    servername: HiDyVnDe14.bInTeRmA.OrG
  - name: "\U0001F1FA\U0001F1F8美国290 | ⬇️ 553KB/s"
    password: 8e0f8ef2-5fe3-11ec-a8bf-f23c91cfbbc9
    port: 15229
    server: 0114ec0c-swxgg0-t14dgl-duku.cu.plebai.net
    type: trojan
    udp: true
    sni: 0114ec0c-swxgg0-t14dgl-duku.cu.plebai.net
  - auth: 93fb69fc-77cf-11ee-85ee-f23c91369f2d
    name: "\U0001F1FA\U0001F1F8美国291 | ⬇️ 1.1MB/s"
    password: 93fb69fc-77cf-11ee-85ee-f23c91369f2d
    port: 8443
    server: 9d881534-swxgg0-t12cnj-1ol97.hy2.gotochinatown.net
    type: hysteria2
    udp: true
    sni: 9d881534-swxgg0-t12cnj-1ol97.hy2.gotochinatown.net
  - name: "\U0001F1FA\U0001F1F8美国292 | ⬇️ 671KB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /?ed=2560
  - auth: c6c1e4eb-06de-450a-9ad6-14a903db8406
    down: 400
    name: "\U0001F300其他66-TR | ⬇️ 2.2MB/s"
    password: c6c1e4eb-06de-450a-9ad6-14a903db8406
    port: 35655
    server: tr01.node.cross666.top
    skip-cert-verify: true
    sni: mirror.dogado.de
    type: hysteria2
    udp: true
    up: 100
  - auth: c6d93228-6393-41ec-8403-da852407390a
    down: 200
    name: "\U0001F1FA\U0001F1F8美国293 | ⬇️ 2.2MB/s"
    password: c6d93228-6393-41ec-8403-da852407390a
    port: 36152
    server: use.node.cross666.top
    skip-cert-verify: true
    sni: www.yahoo.com
    type: hysteria2
    udp: true
    up: 100
  - alpn:
      - http/1.1
    name: "\U0001F1FA\U0001F1F8美国294 | ⬇️ 3.4MB/s"
    network: ws
    password: f0f6e76e-e5fe-4e2c-9faf-34832e021eae
    port: 443
    server: ty.457.pp.ua
    skip-cert-verify: false
    sni: Ty.457.pP.uA
    type: trojan
    udp: true
    ws-opts:
      headers:
        Host: Ty.457.pP.uA
      path: /mZr1mA5hub7QHHkQBzYO
  - auth: c6c1e4eb-06de-450a-9ad6-14a903db8406
    down: 180
    name: "\U0001F1FA\U0001F1F8美国295 | ⬇️ 2.3MB/s"
    password: c6c1e4eb-06de-450a-9ad6-14a903db8406
    port: 38760
    server: us3.node.cross666.top
    skip-cert-verify: true
    sni: www.yahoo.com
    type: hysteria2
    udp: true
    up: 100
  - name: "\U0001F1F3\U0001F1F1荷兰6 | ⬇️ 1.6MB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: "/Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
  - auth: c6d93228-6393-41ec-8403-da852407390a
    down: 160
    name: "\U0001F1FA\U0001F1F8美国296 | ⬇️ 1.9MB/s"
    password: c6d93228-6393-41ec-8403-da852407390a
    port: 35321
    server: us01.node.cross666.top
    skip-cert-verify: true
    sni: www.yahoo.com
    type: hysteria2
    udp: true
    up: 100
  - auth: 8f854788-85ba-4ab5-8626-5071bb6a43d3
    down: 200
    name: "\U0001F1FA\U0001F1F8美国297 | ⬇️ 2.0MB/s"
    password: 8f854788-85ba-4ab5-8626-5071bb6a43d3
    port: 38632
    server: use.node.cross666.top
    skip-cert-verify: true
    sni: www.yahoo.com
    type: hysteria2
    udp: true
    up: 100
  - auth: 7b8dc3c9-cd74-4c22-9b29-************
    down: 160
    name: "\U0001F1FA\U0001F1F8美国298 | ⬇️ 2.4MB/s"
    password: 7b8dc3c9-cd74-4c22-9b29-************
    port: 39399
    server: us01.node.cross666.top
    skip-cert-verify: true
    sni: www.yahoo.com
    type: hysteria2
    udp: true
    up: 100
  - name: "\U0001F1FA\U0001F1F8美国299 | ⬇️ 931KB/s"
    network: ws
    port: 8880
    server: **************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: "/Telegram\U0001F1E8\U0001F1F3 @pgkj666 /?ed=2560"
  - auth: b79d79ae-8bce-11ef-a2b8-f23c9164ca5d
    name: "\U0001F1FA\U0001F1F8美国300 | ⬇️ 1.1MB/s"
    password: b79d79ae-8bce-11ef-a2b8-f23c9164ca5d
    port: 8443
    server: 2d17f52b-swxgg0-t67sv3-1snfs.hy2.gotochinatown.net
    type: hysteria2
    udp: true
    sni: 2d17f52b-swxgg0-t67sv3-1snfs.hy2.gotochinatown.net
  - auth: 869a0163-456f-4c06-bd4a-2376e4563eae
    name: "\U0001F1FA\U0001F1F8美国301 | ⬇️ 1.9MB/s"
    password: 869a0163-456f-4c06-bd4a-2376e4563eae
    port: 33003
    server: qydg.qy1357.top
    skip-cert-verify: true
    sni: qydg.qy1357.top
    type: hysteria2
    udp: true
  - cipher: aes-256-gcm
    name: "\U0001F1E8\U0001F1E6加拿大10 | ⬇️ 6.8MB/s"
    password: 5CUB6RJAELMTNHHA
    port: 20025
    server: **************
    type: ss
    udp: true
  - auth: 5479f2d5-a0c4-437e-95c9-f79a0c485af1
    down: 300
    name: "\U0001F1F8\U0001F1EC新加坡29 | ⬇️ 1.4MB/s"
    password: 5479f2d5-a0c4-437e-95c9-f79a0c485af1
    port: 39597
    server: sg02.node.cross666.top
    skip-cert-verify: true
    sni: mirror.sg.gs
    type: hysteria2
    udp: true
    up: 100
  - auth: 7b8dc3c9-cd74-4c22-9b29-************
    down: 300
    name: "\U0001F1F8\U0001F1EC新加坡30 | ⬇️ 1.2MB/s"
    password: 7b8dc3c9-cd74-4c22-9b29-************
    port: 35366
    server: sg02.node.cross666.top
    skip-cert-verify: true
    sni: mirror.sg.gs
    type: hysteria2
    udp: true
    up: 100
  - name: "\U0001F300其他67-未识别 | ⬇️ 697KB/s"
    network: ws
    port: 2052
    server: www.speedtest.net
    tls: false
    type: vless
    udp: true
    uuid: dfc1a9ab-6241-4b73-92f7-b299fc6b7751
    ws-opts:
      headers:
        Host: >-
          Yak-Khanda-kUn-eY-gUL.biNG.com.xn--cR8HaaaAaAAAaAaaAaAaAAaAAaaaAAAAAaAaaAaaaAaaaaAAaaAAAaaaaAa.xN--Cr8hAaAaaAaaAAAAAAaaaAAAaAAaaAaAaAAAAAA.TREPAspeEdTEsT.NEtrag.cOm.PX.Com.sh017.IR.XN--Cr8hAaaAaaAaaAaaaAAAaaaAaaAaaaAaAAAaaaA.yek.dDnS-ip.nEt
      path: /?ed=1080
    servername: >-
      Yak-Khanda-kUn-eY-gUL.biNG.com.xn--cR8HaaaAaAAAaAaaAaAaAAaAAaaaAAAAAaAaaAaaaAaaaaAAaaAAAaaaaAa.xN--Cr8hAaAaaAaaAAAAAAaaaAAAaAAaaAaAaAAAAAA.TREPAspeEdTEsT.NEtrag.cOm.PX.Com.sh017.IR.XN--Cr8hAaaAaaAaaAaaaAAAaaaAaaAaaaAaAAAaaaA.yek.dDnS-ip.nEt
  - name: "\U0001F1F0\U0001F1F7韩国14 | ⬇️ 1.2MB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /TelegramU0001F1E8U0001F1F3 @MxlShare @WangCai2 /?ed=2560
    servername: reedfree8mahsang2.redorg.ir
  - name: "\U0001F1FA\U0001F1F8美国302 | ⬇️ 504KB/s"
    network: ws
    port: 8880
    server: *************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /?ed=2560
  - name: "\U0001F1F0\U0001F1F7韩国15 | ⬇️ 591KB/s"
    network: ws
    port: 8880
    server: ***********
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: "/Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
  - auth: 8f854788-85ba-4ab5-8626-5071bb6a43d3
    down: 200
    name: "\U0001F1FA\U0001F1F8美国303 | ⬇️ 1.3MB/s"
    password: 8f854788-85ba-4ab5-8626-5071bb6a43d3
    port: 37985
    server: use.node.cross666.top
    skip-cert-verify: true
    sni: www.yahoo.com
    type: hysteria2
    udp: true
    up: 100
  - auth: afc216c8-e407-48e0-86b1-6bf20f709c72
    down: 160
    name: "\U0001F1FA\U0001F1F8美国304 | ⬇️ 2.3MB/s"
    password: afc216c8-e407-48e0-86b1-6bf20f709c72
    port: 39936
    server: us01.node.cross666.top
    skip-cert-verify: true
    sni: www.yahoo.com
    type: hysteria2
    udp: true
    up: 100
  - name: "\U0001F1EE\U0001F1F3印度3 | ⬇️ 604KB/s"
    network: ws
    port: 8880
    server: **********54
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /TelegramU0001F1E8U0001F1F3 @MxlShare @WangCai2 /?ed=2560
    servername: reedfree8mahsang2.redorg.ir
  - auth: c6d93228-6393-41ec-8403-da852407390a
    down: 160
    name: "\U0001F1FA\U0001F1F8美国305 | ⬇️ 2.0MB/s"
    password: c6d93228-6393-41ec-8403-da852407390a
    port: 38900
    server: us01.node.cross666.top
    skip-cert-verify: true
    sni: www.yahoo.com
    type: hysteria2
    udp: true
    up: 100
  - auth: 8f854788-85ba-4ab5-8626-5071bb6a43d3
    down: 300
    name: "\U0001F1F8\U0001F1EC新加坡31 | ⬇️ 2.2MB/s"
    password: 8f854788-85ba-4ab5-8626-5071bb6a43d3
    port: 37312
    server: sg02.node.cross666.top
    skip-cert-verify: true
    sni: mirror.sg.gs
    type: hysteria2
    udp: true
    up: 100
  - auth: ba4672b3-e0af-4699-a180-f711cfaa95ed
    name: "\U0001F1E9\U0001F1EA德国24 | ⬇️ 5.3MB/s"
    password: ba4672b3-e0af-4699-a180-f711cfaa95ed
    port: 43999
    server: jiangzhidg.54264944.xyz
    skip-cert-verify: true
    sni: jiangzhidg.54264944.xyz
    type: hysteria2
    udp: true
  - auth: 5479f2d5-a0c4-437e-95c9-f79a0c485af1
    down: 400
    name: "\U0001F300其他68-TR | ⬇️ 2.1MB/s"
    password: 5479f2d5-a0c4-437e-95c9-f79a0c485af1
    port: 38081
    server: tr01.node.cross666.top
    skip-cert-verify: true
    sni: mirror.dogado.de
    type: hysteria2
    udp: true
    up: 100
  - cipher: aes-256-gcm
    name: "\U0001F300其他69-TR | ⬇️ 6.6MB/s"
    password: ZWPD3479FWRUHQWC
    port: 20024
    server: ***************
    type: ss
    udp: true
  - auth: 591ac730-bd6f-11ed-a8bf-f23c91cfbbc9
    name: "\U0001F1FA\U0001F1F8美国306 | ⬇️ 608KB/s"
    password: 591ac730-bd6f-11ed-a8bf-f23c91cfbbc9
    port: 8443
    server: 76aa25f7-swxgg0-sy3zfb-ezjz.hy2.gotochinatown.net
    type: hysteria2
    udp: true
    sni: 76aa25f7-swxgg0-sy3zfb-ezjz.hy2.gotochinatown.net
  - client-fingerprint: chrome
    name: "\U0001F1EC\U0001F1E7英国9 | ⬇️ 3.5MB/s"
    network: ws
    port: 443
    server: uk.oldcloud.online
    tls: true
    type: vless
    udp: true
    uuid: a4ce45d9-60ee-4496-9a2d-ce88828d4f36
    ws-opts:
      headers:
        Host: uk.oldcloud.online
      path: /
    servername: uk.oldcloud.online
  - name: "\U0001F1FA\U0001F1F8美国307 | ⬇️ 556KB/s"
    password: dcccacba-fa44-11ef-8400-f23c9164ca5d
    port: 15229
    server: b009f292-swvls0-sx0fe4-1j6h0.cu.plebai.net
    type: trojan
    udp: true
    sni: b009f292-swvls0-sx0fe4-1j6h0.cu.plebai.net
  - name: "\U0001F1FA\U0001F1F8美国308 | ⬇️ 1.1MB/s"
    password: b585dac6-dc69-11ef-9f1c-f23c913c8d2b
    port: 15229
    server: 1020d4f3-swxgg0-sy1j4r-1mqag.cu.plebai.net
    type: trojan
    udp: true
    sni: 1020d4f3-swxgg0-sy1j4r-1mqag.cu.plebai.net
  - auth: 7b8dc3c9-cd74-4c22-9b29-************
    down: 125
    name: "\U0001F1F7\U0001F1FA俄罗斯38 | ⬇️ 2.7MB/s"
    password: 7b8dc3c9-cd74-4c22-9b29-************
    port: 37971
    server: ru2.node.cross666.top
    skip-cert-verify: true
    sni: mirror.hyperdedic.ru
    type: hysteria2
    udp: true
    up: 100
  - cipher: aes-256-gcm
    name: "\U0001F1E8\U0001F1E6加拿大11 | ⬇️ 2.8MB/s"
    password: Z343JAT1LWKCJVRQ
    port: 20010
    server: **************
    type: ss
    udp: true
  - auth: abef57a5-ac14-4356-ba5d-c3fdc85df62b
    down: 400
    name: "\U0001F300其他70-TR | ⬇️ 2.1MB/s"
    password: abef57a5-ac14-4356-ba5d-c3fdc85df62b
    port: 35339
    server: tr01.node.cross666.top
    skip-cert-verify: true
    sni: mirror.dogado.de
    type: hysteria2
    udp: true
    up: 100
  - name: "\U0001F1E6\U0001F1EA阿拉伯酋长国4 | ⬇️ 12.7MB/s"
    network: ws
    port: 8880
    server: *************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /?ed=2560
  - auth: c6d93228-6393-41ec-8403-da852407390a
    down: 400
    name: "\U0001F300其他71-TR | ⬇️ 2.0MB/s"
    password: c6d93228-6393-41ec-8403-da852407390a
    port: 37636
    server: tr01.node.cross666.top
    skip-cert-verify: true
    sni: mirror.dogado.de
    type: hysteria2
    udp: true
    up: 100
  - name: "\U0001F1FA\U0001F1F8美国309 | ⬇️ 1.1MB/s"
    network: ws
    port: 8880
    server: *************
    tls: false
    type: vless
    udp: true
    uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
    ws-opts:
      headers:
        Host: us.laoyoutiao.link
      path: "/Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
  - auth: c6d93228-6393-41ec-8403-da852407390a
    down: 400
    name: "\U0001F300其他72-TR | ⬇️ 907KB/s"
    password: c6d93228-6393-41ec-8403-da852407390a
    port: 39847
    server: tr01.node.cross666.top
    skip-cert-verify: true
    sni: mirror.dogado.de
    type: hysteria2
    udp: true
    up: 100
  - cipher: aes-256-gcm
    name: "\U0001F1EF\U0001F1F5日本17 | ⬇️ 699KB/s"
    password: 4M2YKH5SAKJIZMQ3
    port: 18014
    server: 8tv68qhq.slashdevslashnetslashtun.net
    type: ss
    udp: true
  - auth: afc216c8-e407-48e0-86b1-6bf20f709c72
    down: 160
    name: "\U0001F1FA\U0001F1F8美国310 | ⬇️ 2.6MB/s"
    password: afc216c8-e407-48e0-86b1-6bf20f709c72
    port: 36173
    server: us01.node.cross666.top
    skip-cert-verify: true
    sni: www.yahoo.com
    type: hysteria2
    udp: true
    up: 100
  - auth: abef57a5-ac14-4356-ba5d-c3fdc85df62b
    down: 200
    name: "\U0001F1FA\U0001F1F8美国311 | ⬇️ 2.1MB/s"
    password: abef57a5-ac14-4356-ba5d-c3fdc85df62b
    port: 39616
    server: use.node.cross666.top
    skip-cert-verify: true
    sni: www.yahoo.com
    type: hysteria2
    udp: true
    up: 100
  - alpn:
      - h3
    auth-str: dongtaiwang.com
    auth_str: dongtaiwang.com
    down: 100
    name: "\U0001F1EB\U0001F1F7法国21 | ⬇️ 7.8MB/s"
    port: 14241
    protocol: udp
    server: ************
    skip-cert-verify: true
    sni: apple.com
    type: hysteria
    udp: true
    up: 100
  - name: "\U0001F1FA\U0001F1F8美国312 | ⬇️ 851KB/s"
    password: e3975a05-b89e-11ec-a50d-f23c91cfbbc9
    port: 15229
    server: cdfaf2c8-swxgg0-sxf56z-1f596.cu.plebai.net
    type: trojan
    udp: true
    sni: cdfaf2c8-swxgg0-sxf56z-1f596.cu.plebai.net
  - auth: bb85e074-b0c2-11ea-ad28-f23c913c8d2b
    name: "\U0001F1FA\U0001F1F8美国313 | ⬇️ 1.3MB/s"
    password: bb85e074-b0c2-11ea-ad28-f23c913c8d2b
    port: 8443
    server: 2d6c68cd-swxgg0-sx9mz9-m0b9.hy2.gotochinatown.net
    type: hysteria2
    udp: true
    sni: 2d6c68cd-swxgg0-sx9mz9-m0b9.hy2.gotochinatown.net
  - name: "\U0001F1E9\U0001F1EA德国25 | ⬇️ 548KB/s"
    network: ws
    port: 80
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: '/Telegram: @vpnAndroid2/?ed=2560'
  - cipher: aes-256-gcm
    name: "\U0001F1EF\U0001F1F5日本18 | ⬇️ 532KB/s"
    password: SGIW64LUPB19J9HR
    port: 18015
    server: ti3hyra4.slashdevslashnetslashtun.net
    type: ss
    udp: true
  - client-fingerprint: randomized
    name: "\U0001F1E8\U0001F1E6加拿大12 | ⬇️ 953KB/s"
    network: ws
    port: 443
    server: *************
    tls: true
    type: vless
    udp: true
    uuid: d6b8011a-c725-435a-9fec-bf6d3530392c
    ws-opts:
      headers:
        Host: vle.amclubsapp.dpdns.org
      path: /?ed=2560
    servername: vle.amclubsapp.dpdns.org
  - name: "\U0001F1FA\U0001F1F8美国314 | ⬇️ 12.2MB/s"
    network: ws
    port: 8880
    server: **********
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: "/Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
  - name: "\U0001F1E9\U0001F1EA德国26 | ⬇️ 901KB/s"
    network: ws
    port: 80
    server: *************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: "/Telegram\U0001F1E8\U0001F1F3 @WangCai2 /?ed=2560"
  - name: "\U0001F1EE\U0001F1F3印度4 | ⬇️ 964KB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /TelegramU0001F1E8U0001F1F3 @MxlShare @WangCai2 /?ed=2560
    servername: reedfree8mahsang2.redorg.ir
  - auth: c6c1e4eb-06de-450a-9ad6-14a903db8406
    down: 300
    name: "\U0001F1FA\U0001F1F8美国315 | ⬇️ 1.7MB/s"
    password: c6c1e4eb-06de-450a-9ad6-14a903db8406
    port: 39667
    server: us4.node.cross666.top
    skip-cert-verify: true
    sni: www.yahoo.com
    type: hysteria2
    udp: true
    up: 100
  - auth: c6d93228-6393-41ec-8403-da852407390a
    down: 200
    name: "\U0001F1FA\U0001F1F8美国316 | ⬇️ 2.0MB/s"
    password: c6d93228-6393-41ec-8403-da852407390a
    port: 38633
    server: use.node.cross666.top
    skip-cert-verify: true
    sni: www.yahoo.com
    type: hysteria2
    udp: true
    up: 100
  - name: "\U0001F1FA\U0001F1F8美国317 | ⬇️ 5.0MB/s"
    password: ef18aaca-4711-11ec-a8bf-f23c91cfbbc9
    port: 15229
    server: b02d624d-swxgg0-sy439s-laev.cu.plebai.net
    type: trojan
    udp: true
    sni: b02d624d-swxgg0-sy439s-laev.cu.plebai.net
  - name: "\U0001F1E9\U0001F1EA德国27 | ⬇️ 10.5MB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /TelegramU0001F1E8U0001F1F3 @MxlShare @WangCai2 /?ed=2560
    servername: reedfree8mahsang2.redorg.ir
  - alterId: 2
    cipher: auto
    name: "\U0001F1ED\U0001F1F0香港36 | ⬇️ 9.1MB/s"
    network: ws
    port: 459
    server: 688f6e4a-swxgg0-syb15h-8caj.hkt.east.wctype.com
    tls: false
    type: vmess
    udp: true
    uuid: a67a6c18-3f6d-11ef-ab9c-f23c9313b177
    ws-opts:
      headers:
        Host: a605477178.m.ctrip.com
      path: /
  - name: "\U0001F1F0\U0001F1F7韩国16 | ⬇️ 3.0MB/s"
    network: ws
    port: 8880
    server: **********4
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /TelegramU0001F1E8U0001F1F3 @MxlShare @WangCai2 /?ed=2560
    servername: reedfree8mahsang2.redorg.ir
  - name: "\U0001F1F3\U0001F1F1荷兰7 | ⬇️ 1.3MB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /TelegramU0001F1E8U0001F1F3 @MxlShare @WangCai2 /?ed=2560
    servername: reedfree8mahsang2.redorg.ir
  - auth: d4018e28-e328-11ed-98a7-f23c913c8d2b
    name: "\U0001F1FA\U0001F1F8美国318 | ⬇️ 1.6MB/s"
    password: d4018e28-e328-11ed-98a7-f23c913c8d2b
    port: 8443
    server: 2179bb5b-swxgg0-sx3h07-1g8k0.hy2.gotochinatown.net
    type: hysteria2
    udp: true
    sni: 2179bb5b-swxgg0-sx3h07-1g8k0.hy2.gotochinatown.net
  - name: "\U0001F1FA\U0001F1F8美国319 | ⬇️ 660KB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /TelegramU0001F1E8U0001F1F3 @pgkj666 /?ed=2560
    servername: reedfree8mahsang2.redorg.ir
  - name: "\U0001F1F0\U0001F1F7韩国17 | ⬇️ 770KB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /?ed=2560
    servername: reedfree8mahsang2.redorg.ir
  - client-fingerprint: chrome
    grpc-opts:
      grpc-mode: gun
      grpc-service-name: tj
    name: "\U0001F1FA\U0001F1F8美国320 | ⬇️ 5.5MB/s"
    network: grpc
    port: 20230
    reality-opts:
      public-key: Bq_RQyPwxp7zTYywC37jLxmcjV9npNN6H6_1d_R6GT0
      short-id: c9916730
    server: us002.421421.xyz
    tls: true
    type: vless
    udp: true
    uuid: fdd1d613-f234-40f6-b63d-97517e0fc4b3
    servername: www.nvidia.com
  - name: "\U0001F1E9\U0001F1EA德国28 | ⬇️ 2.5MB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /TelegramU0001F1E8U0001F1F3 @MxlShare @WangCai2 /?ed=2560
    servername: reedfree8mahsang2.redorg.ir
  - auth: 5479f2d5-a0c4-437e-95c9-f79a0c485af1
    down: 400
    name: "\U0001F300其他73-TR | ⬇️ 2.0MB/s"
    password: 5479f2d5-a0c4-437e-95c9-f79a0c485af1
    port: 35666
    server: tr01.node.cross666.top
    skip-cert-verify: true
    sni: mirror.dogado.de
    type: hysteria2
    udp: true
    up: 100
  - cipher: aes-256-gcm
    name: "\U0001F300其他74-MY | ⬇️ 4.7MB/s"
    password: TPCIM1dLUG64W3Lom1k9NavzLx14ArpNJmr1PUXVrJU=
    port: 54111
    server: *************
    type: ss
    udp: true
  - auth: c6c1e4eb-06de-450a-9ad6-14a903db8406
    down: 200
    name: "\U0001F1FA\U0001F1F8美国321 | ⬇️ 2.5MB/s"
    password: c6c1e4eb-06de-450a-9ad6-14a903db8406
    port: 39294
    server: use.node.cross666.top
    skip-cert-verify: true
    sni: www.yahoo.com
    type: hysteria2
    udp: true
    up: 100
  - auth: c6d93228-6393-41ec-8403-da852407390a
    down: 100
    name: "\U0001F1F7\U0001F1FA俄罗斯39 | ⬇️ 2.2MB/s"
    password: c6d93228-6393-41ec-8403-da852407390a
    port: 39991
    server: rua.node.cross666.top
    skip-cert-verify: true
    sni: mirror.hyperdedic.ru
    type: hysteria2
    udp: true
    up: 100
  - auth: 6e9cdc20-b92a-11ef-973a-f23c913c8d2b
    name: "\U0001F1FA\U0001F1F8美国322 | ⬇️ 856KB/s"
    password: 6e9cdc20-b92a-11ef-973a-f23c913c8d2b
    port: 8443
    server: d518699a-swxgg0-sww7b0-1qwp5.hy2.gotochinatown.net
    type: hysteria2
    udp: true
    sni: d518699a-swxgg0-sww7b0-1qwp5.hy2.gotochinatown.net
  - auth: 7b8dc3c9-cd74-4c22-9b29-************
    down: 400
    name: "\U0001F300其他75-TR | ⬇️ 2.3MB/s"
    password: 7b8dc3c9-cd74-4c22-9b29-************
    port: 39927
    server: tr01.node.cross666.top
    skip-cert-verify: true
    sni: mirror.dogado.de
    type: hysteria2
    udp: true
    up: 100
  - cipher: aes-256-gcm
    name: "\U0001F1F8\U0001F1EC新加坡32 | ⬇️ 1.4MB/s"
    password: GAIJP655ZYFMN22R
    port: 16011
    server: ti3hyra4.slashdevslashnetslashtun.net
    type: ss
    udp: true
  - auth: dcccacba-fa44-11ef-8400-f23c9164ca5d
    name: "\U0001F1FA\U0001F1F8美国323 | ⬇️ 3.4MB/s"
    password: dcccacba-fa44-11ef-8400-f23c9164ca5d
    port: 8443
    server: 0a3979fe-swxgg0-sx0fe4-1j6h0.hy2.gotochinatown.net
    type: hysteria2
    udp: true
    sni: 0a3979fe-swxgg0-sx0fe4-1j6h0.hy2.gotochinatown.net
  - auth: 8f854788-85ba-4ab5-8626-5071bb6a43d3
    down: 300
    name: "\U0001F1FA\U0001F1F8美国324 | ⬇️ 2.2MB/s"
    password: 8f854788-85ba-4ab5-8626-5071bb6a43d3
    port: 36798
    server: us4.node.cross666.top
    skip-cert-verify: true
    sni: www.yahoo.com
    type: hysteria2
    udp: true
    up: 100
  - name: "\U0001F1FA\U0001F1F8美国325 | ⬇️ 1.5MB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: "/Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
  - auth: 0e00f9c1-65c3-4e20-9da8-47fa7c363423
    down: 400
    name: "\U0001F300其他76-TR | ⬇️ 2.4MB/s"
    password: 0e00f9c1-65c3-4e20-9da8-47fa7c363423
    port: 39153
    server: tr01.node.cross666.top
    skip-cert-verify: true
    sni: mirror.dogado.de
    type: hysteria2
    udp: true
    up: 100
  - name: "\U0001F1FA\U0001F1F8美国326 | ⬇️ 746KB/s"
    password: b79d79ae-8bce-11ef-a2b8-f23c9164ca5d
    port: 15229
    server: 7cde4333-swvls0-t67sv3-1snfs.cu.plebai.net
    type: trojan
    udp: true
    sni: 7cde4333-swvls0-t67sv3-1snfs.cu.plebai.net
  - auth: c6d93228-6393-41ec-8403-da852407390a
    down: 200
    name: "\U0001F1FA\U0001F1F8美国327 | ⬇️ 2.1MB/s"
    password: c6d93228-6393-41ec-8403-da852407390a
    port: 39046
    server: use.node.cross666.top
    skip-cert-verify: true
    sni: www.yahoo.com
    type: hysteria2
    udp: true
    up: 100
  - auth: affe7124-c118-11ef-b6b2-f23c9164ca5d
    name: "\U0001F1FA\U0001F1F8美国328 | ⬇️ 2.2MB/s"
    password: affe7124-c118-11ef-b6b2-f23c9164ca5d
    port: 8443
    server: 30eaed42-swxgg0-td1w5f-1t3cz.hy2.gotochinatown.net
    type: hysteria2
    udp: true
    sni: 30eaed42-swxgg0-td1w5f-1t3cz.hy2.gotochinatown.net
  - cipher: aes-256-gcm
    name: "\U0001F1E8\U0001F1E6加拿大13 | ⬇️ 4.4MB/s"
    password: W8O6RDUYCDA9OTHB
    port: 20031
    server: **************
    type: ss
    udp: true
  - name: "\U0001F1E9\U0001F1EA德国29 | ⬇️ 1.1MB/s"
    network: ws
    port: 80
    server: ***************
    tls: false
    type: vless
    udp: true
    uuid: fab7bf9c-ddb9-4563-8a04-fb01ce6c0fbf
    ws-opts:
      headers:
        Host: cs.laoyoutiao.link
      path: "/Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
  - auth: c6d93228-6393-41ec-8403-da852407390a
    down: 200
    name: "\U0001F1FA\U0001F1F8美国329 | ⬇️ 1.2MB/s"
    password: c6d93228-6393-41ec-8403-da852407390a
    port: 35345
    server: use.node.cross666.top
    skip-cert-verify: true
    sni: www.yahoo.com
    type: hysteria2
    udp: true
    up: 100
  - auth: 8f854788-85ba-4ab5-8626-5071bb6a43d3
    down: 400
    name: "\U0001F300其他77-TR | ⬇️ 2.4MB/s"
    password: 8f854788-85ba-4ab5-8626-5071bb6a43d3
    port: 36032
    server: tr01.node.cross666.top
    skip-cert-verify: true
    sni: mirror.dogado.de
    type: hysteria2
    udp: true
    up: 100
  - name: "\U0001F1FA\U0001F1F8美国330 | ⬇️ 6.7MB/s"
    password: 591ac730-bd6f-11ed-a8bf-f23c91cfbbc9
    port: 15229
    server: 94e89b13-swxgg0-sy3zfb-ezjz.cu.plebai.net
    type: trojan
    udp: true
    sni: 94e89b13-swxgg0-sy3zfb-ezjz.cu.plebai.net
  - cipher: aes-256-gcm
    name: "\U0001F1ED\U0001F1F0香港37 | ⬇️ 669KB/s"
    password: Y6W3CVHLH2SZGVVE
    port: 15015
    server: ti3hyra4.slashdevslashnetslashtun.net
    type: ss
    udp: true
  - name: "\U0001F1FA\U0001F1F8美国331 | ⬇️ 916KB/s"
    password: 591ac730-bd6f-11ed-a8bf-f23c91cfbbc9
    port: 15229
    server: a2f012ea-swvls0-sy3zfb-ezjz.cu.plebai.net
    type: trojan
    udp: true
    sni: a2f012ea-swvls0-sy3zfb-ezjz.cu.plebai.net
  - auth: 5479f2d5-a0c4-437e-95c9-f79a0c485af1
    down: 200
    name: "\U0001F1FA\U0001F1F8美国332 | ⬇️ 2.2MB/s"
    password: 5479f2d5-a0c4-437e-95c9-f79a0c485af1
    port: 38881
    server: use.node.cross666.top
    skip-cert-verify: true
    sni: www.yahoo.com
    type: hysteria2
    udp: true
    up: 100
  - name: "\U0001F1FA\U0001F1F8美国333 | ⬇️ 5.3MB/s"
    password: dcccacba-fa44-11ef-8400-f23c9164ca5d
    port: 15229
    server: 7a467131-swxgg0-sx0fe4-1j6h0.cu.plebai.net
    type: trojan
    udp: true
    sni: 7a467131-swxgg0-sx0fe4-1j6h0.cu.plebai.net
  - name: "\U0001F1FA\U0001F1F8美国334 | ⬇️ 3.2MB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /?ed=2560
  - client-fingerprint: chrome
    name: "\U0001F1EC\U0001F1E7英国10 | ⬇️ 660KB/s"
    network: ws
    port: 2096
    server: *************
    tls: true
    type: vless
    udp: true
    uuid: 2404f667-5969-4883-a71a-ea2e0db2d25b
    ws-opts:
      headers:
        Host: wwwspeedtestnetwhiteberinsistomd6625996s30.afaghnegwork.ir
      path: /
    servername: wwwspeedtestnetwhiteberinsistomd6625996s30.afaghnegwork.ir
  - alpn:
      - ''
    alterId: '0'
    cipher: auto
    name: "\U0001F1ED\U0001F1F0香港38 | ⬇️ 5.1MB/s"
    network: ws
    port: 8080
    server: 209aeef5-sw1z40-swqixu-1luqs.hgc1.tcpbbr.net
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 788328ee-d49f-11ef-bd97-f23c9164ca5d
    ws-opts:
      headers:
        Host: broadcastlv.chat.bilibili.com
      path: /
    xudp: true
  - cipher: aes-128-gcm
    name: "\U0001F1E9\U0001F1EA德国30 | ⬇️ 1.2MB/s"
    password: 763bf612-4c66-4fd4-b54b-5349bdea6bca
    port: 640
    server: neweur.upperlay.xyz
    type: ss
    udp: true
  - alpn:
      - ''
    alterId: '0'
    cipher: auto
    name: "\U0001F1E7\U0001F1F7巴西1 | ⬇️ 1.8MB/s"
    network: ws
    port: 2086
    server: yx2.niaoba.top
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 763b29f0-ffba-42e8-a054-d595092135e6
    ws-opts:
      headers:
        Host: 1176552658409.vmbr2.taobao.yyds.niaoba.top
      path: /
    xudp: true
  - alpn:
      - ''
    alterId: '0'
    cipher: auto
    name: "\U0001F300其他78-TH | ⬇️ 9.4MB/s"
    network: ws
    port: 8080
    server: yx2.niaoba.top
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 763b29f0-ffba-42e8-a054-d595092135e6
    ws-opts:
      headers:
        Host: 1176552658409.vmth.taobao.pdd.niaoba.top
      path: /
    xudp: true
  - client-fingerprint: chrome
    name: "\U0001F1FA\U0001F1F8美国335 | ⬇️ 3.0MB/s"
    network: ws
    port: 443
    server: *************
    tls: true
    type: vless
    udp: true
    uuid: 52c0e96b-b8d7-44a5-a995-bd866bf39ec6
    ws-opts:
      headers:
        Host: M10DdD.457.Pp.ua
        User-Agent: >-
          Mozilla/5.0 (Linux; Android 6.0.1; SM-G532M Build/MMB29T)
          AppleWebKit/537.36 (KHTML, like Gecko) Chrome/55.0.2883.91 Mobile
          Safari/537.36
      path: /YzFLmzNIEQsAmceLxv94lPbgMw
    xudp: true
    servername: M10DdD.457.Pp.ua
  - client-fingerprint: chrome
    name: "\U0001F1FA\U0001F1F8美国336 | ⬇️ 645KB/s"
    network: ws
    port: 443
    server: *************
    tls: true
    type: vless
    udp: true
    uuid: 5453ae26-250d-4e79-b4ec-016baf806865
    ws-opts:
      headers:
        Host: 7d33510a-5dA0-4F85-b1d5-5321a9eaA72a.890603.pP.uA
        User-Agent: >-
          Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML,
          like Gecko) Chrome/64.0.3282.167 Safari/537.36
      path: /XcQF058rNJ3gc4aj
    xudp: true
    servername: 7d33510a-5dA0-4F85-b1d5-5321a9eaA72a.890603.pP.uA
  - client-fingerprint: chrome
    flow: xtls-rprx-vision
    http-opts:
      headers: {}
      path:
        - /
    name: "\U0001F1EC\U0001F1E7英国11 | ⬇️ 7.7MB/s"
    network: tcp
    port: 444
    reality-opts:
      public-key: FOSoac3DIrSrCUcBtQA-BdHMxCtqe2CYCpdvGxKMiUw
      short-id: 82b9ca5d8d39ba72
    server: *************
    tls: true
    type: vless
    udp: true
    uuid: faaaf16e-188e-42f2-bcc1-17feadc84fad
    xudp: true
    servername: opensuse.org
  - client-fingerprint: chrome
    name: "\U0001F1F8\U0001F1EC新加坡33 | ⬇️ 11.2MB/s"
    password: 82762002-40f5-4775-a9ad-3da97a2772f7
    port: 55015
    server: sssg03.521pokemon.com
    skip-cert-verify: true
    sni: **************
    type: trojan
    udp: true
  - alpn:
      - ''
    alterId: '0'
    cipher: auto
    name: "\U0001F1FA\U0001F1F8美国337 | ⬇️ 746KB/s"
    network: ws
    port: 38904
    server: free-relay.themars.top
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 781807c4-37df-4da3-9942-c6e82032399a
    ws-opts:
      headers:
        Host: www.cctv.com
      path: /cctv1.m3u8
    xudp: true
  - client-fingerprint: chrome
    name: "\U0001F1E9\U0001F1EA德国31 | ⬇️ 646KB/s"
    password: 7669a04d-a459-4c96-bc0e-fde51cb984f3
    port: 12070
    server: cn2.cdn.xfltd-cdn.top
    skip-cert-verify: true
    sni: cdn.alibaba.com
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F300其他79-NG | ⬇️ 860KB/s"
    password: 7669a04d-a459-4c96-bc0e-fde51cb984f3
    port: 12073
    server: cn2.cdn.xfltd-cdn.top
    skip-cert-verify: true
    sni: cdn.alibaba.com
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F300其他80-MY | ⬇️ 9.4MB/s"
    password: 7669a04d-a459-4c96-bc0e-fde51cb984f3
    port: 12067
    server: cn2.cdn.xfltd-cdn.top
    skip-cert-verify: true
    sni: cdn.alibaba.com
    type: trojan
    udp: true
  - client-fingerprint: chrome
    name: "\U0001F1E8\U0001F1E6加拿大14 | ⬇️ 3.3MB/s"
    network: ws
    port: 37215
    server: *************
    tls: true
    type: vless
    udp: true
    uuid: d342d11e-d424-4583-b36e-524ab1f0afa4
    ws-opts:
      headers:
        Host: 3th.go4sharing.free.nf
        User-Agent: >-
          Mozilla/5.0 (Linux; Android 6.0.1; SM-G532M Build/MMB29T)
          AppleWebKit/537.36 (KHTML, like Gecko) Chrome/55.0.2883.91 Mobile
          Safari/537.36
      path: /?ed=2560
    xudp: true
    servername: 3th.go4sharing.free.nf
  - client-fingerprint: chrome
    name: "\U0001F1F0\U0001F1F7韩国18 | ⬇️ 4.2MB/s"
    network: ws
    port: 11371
    server: *************
    tls: true
    type: vless
    udp: true
    uuid: d342d11e-d424-4583-b36e-524ab1f0afa4
    ws-opts:
      headers:
        Host: 3th.go4sharing.free.nf
        User-Agent: >-
          Mozilla/5.0 (Windows NT 6.1) AppleWebKit/537.36 (KHTML, like Gecko)
          Chrome/41.0.2228.0 Safari/537.36
      path: /?ed=2560
    xudp: true
    servername: 3th.go4sharing.free.nf
  - client-fingerprint: chrome
    name: "\U0001F1F0\U0001F1F7韩国19 | ⬇️ 2.2MB/s"
    network: ws
    port: 32459
    server: **************
    tls: true
    type: vless
    udp: true
    uuid: d342d11e-d424-4583-b36e-524ab1f0afa4
    ws-opts:
      headers:
        Host: 3th.go4sharing.free.nf
        User-Agent: >-
          Mozilla/5.0 (Windows NT 5.1) AppleWebKit/537.36 (KHTML, like Gecko)
          Chrome/49.0.2623.112 Safari/537.36
      path: /?ed=2560
    xudp: true
    servername: 3th.go4sharing.free.nf
  - client-fingerprint: chrome
    name: "\U0001F1F0\U0001F1F7韩国20 | ⬇️ 4.1MB/s"
    network: ws
    port: 19000
    server: *************
    tls: true
    type: vless
    udp: true
    uuid: d342d11e-d424-4583-b36e-524ab1f0afa4
    ws-opts:
      headers:
        Host: 3th.go4sharing.free.nf
        User-Agent: >-
          Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like
          Gecko) Chrome/57.0.2987.133 Safari/537.36
      path: /?ed=2560
    xudp: true
    servername: 3th.go4sharing.free.nf
  - client-fingerprint: chrome
    name: "\U0001F1F0\U0001F1F7韩国21 | ⬇️ 1.1MB/s"
    network: ws
    port: 50000
    server: **************
    tls: true
    type: vless
    udp: true
    uuid: d342d11e-d424-4583-b36e-524ab1f0afa4
    ws-opts:
      headers:
        Host: 3th.go4sharing.free.nf
        User-Agent: >-
          Mozilla/5.0 (Windows NT 6.1) AppleWebKit/537.36 (KHTML, like Gecko)
          Chrome/57.0.2987.133 Safari/537.36
      path: /?ed=2560
    xudp: true
    servername: 3th.go4sharing.free.nf
  - client-fingerprint: chrome
    name: "\U0001F1E8\U0001F1E6加拿大15 | ⬇️ 2.3MB/s"
    network: ws
    port: 50000
    server: **************
    tls: true
    type: vless
    udp: true
    uuid: d342d11e-d424-4583-b36e-524ab1f0afa4
    ws-opts:
      headers:
        Host: 3th.go4sharing.free.nf
        User-Agent: >-
          Mozilla/5.0 (Windows NT 6.3; Win64; x64) AppleWebKit/537.36 (KHTML,
          like Gecko) Chrome/62.0.3202.94 Safari/537.36
      path: /?ed=2560
    xudp: true
    servername: 3th.go4sharing.free.nf
  - client-fingerprint: chrome
    name: "\U0001F1F0\U0001F1F7韩国22 | ⬇️ 1.9MB/s"
    network: ws
    port: 50000
    server: ************
    tls: true
    type: vless
    udp: true
    uuid: d342d11e-d424-4583-b36e-524ab1f0afa4
    ws-opts:
      headers:
        Host: 3th.go4sharing.free.nf
        User-Agent: >-
          Mozilla/5.0 (Windows NT 6.2; WOW64) AppleWebKit/537.36 (KHTML, like
          Gecko) Chrome/51.0.2704.106 Safari/537.36
      path: /?ed=2560
    xudp: true
    servername: 3th.go4sharing.free.nf
  - client-fingerprint: chrome
    name: "\U0001F1E8\U0001F1E6加拿大16 | ⬇️ 4.0MB/s"
    network: ws
    port: 50000
    server: **************
    tls: true
    type: vless
    udp: true
    uuid: d342d11e-d424-4583-b36e-524ab1f0afa4
    ws-opts:
      headers:
        Host: 3th.go4sharing.free.nf
        User-Agent: >-
          Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML,
          like Gecko) Chrome/56.0.2924.87 Safari/537.36
      path: /?ed=2560
    xudp: true
    servername: 3th.go4sharing.free.nf
  - client-fingerprint: chrome
    name: "\U0001F1F0\U0001F1F7韩国23 | ⬇️ 3.6MB/s"
    network: ws
    port: 11000
    server: *************
    tls: true
    type: vless
    udp: true
    uuid: d342d11e-d424-4583-b36e-524ab1f0afa4
    ws-opts:
      headers:
        Host: 3th.go4sharing.free.nf
        User-Agent: >-
          Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML,
          like Gecko) Chrome/70.0.3538.67 Safari/537.36
      path: /?ed=2560
    xudp: true
    servername: 3th.go4sharing.free.nf
  - client-fingerprint: chrome
    name: "\U0001F1F0\U0001F1F7韩国24 | ⬇️ 891KB/s"
    network: ws
    port: 13722
    server: ***********
    tls: true
    type: vless
    udp: true
    uuid: d342d11e-d424-4583-b36e-524ab1f0afa4
    ws-opts:
      headers:
        Host: 3th.go4sharing.free.nf
        User-Agent: >-
          Mozilla/5.0 (Linux; Android 8.0.0; FIG-LX3 Build/HUAWEIFIG-LX3)
          AppleWebKit/537.36 (KHTML, like Gecko) Chrome/69.0.3497.100 Mobile
          Safari/537.36
      path: /?ed=2560
    xudp: true
    servername: 3th.go4sharing.free.nf
  - client-fingerprint: chrome
    name: "\U0001F1E8\U0001F1E6加拿大17 | ⬇️ 1.2MB/s"
    network: ws
    port: 50000
    server: ***************
    tls: true
    type: vless
    udp: true
    uuid: d342d11e-d424-4583-b36e-524ab1f0afa4
    ws-opts:
      headers:
        Host: 3th.go4sharing.free.nf
        User-Agent: >-
          Mozilla/5.0 (Linux; Android 5.1; A1601 Build/LMY47I)
          AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.2743.98 Mobile
          Safari/537.36
      path: /?ed=2560
    xudp: true
    servername: 3th.go4sharing.free.nf
  - client-fingerprint: chrome
    grpc-opts:
      grpc-service-name: >-
        CANALTELEGRAM--jet_config--TELEGRAM--jet_config--CANALTELEGRAM--jet_config--TELEGRAM--jet_config--CANALTELEGRAM--jet_config
    name: "\U0001F1EC\U0001F1E7英国12 | ⬇️ 6.4MB/s"
    network: grpc
    port: 2030
    reality-opts:
      public-key: YWfCdTnr4FAOMYTY2dLrMtQUokyxOGpPhYEEszPj20E
      short-id: 7fe29733
    server: ns7.esfahansiman.com
    tls: true
    type: vless
    udp: true
    uuid: 98e5553e-abcc-4c9a-941a-8f0e9741d541
    xudp: true
    servername: refersion.com
  - alpn:
      - ''
    alterId: '0'
    cipher: auto
    name: "\U0001F1ED\U0001F1F0香港39 | ⬇️ 4.4MB/s"
    network: ws
    port: 8080
    server: f1028021-swd340-sx9j1n-1timk.hgc1.tcpbbr.net
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: cfe61764-0004-11f0-a910-f23c9164ca5d
    ws-opts:
      headers:
        Host: broadcastlv.chat.bilibili.com
      path: /
    xudp: true
  - alpn:
      - ''
    alterId: '0'
    cipher: auto
    name: "\U0001F1ED\U0001F1F0香港40 | ⬇️ 3.4MB/s"
    network: ws
    port: 8080
    server: d2812392-swd340-t67sv3-1snfs.hgc1.tcpbbr.net
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: b79d79ae-8bce-11ef-a2b8-f23c9164ca5d
    ws-opts:
      headers:
        Host: broadcastlv.chat.bilibili.com
      path: /
    xudp: true
  - alpn:
      - ''
    alterId: '0'
    cipher: auto
    name: "\U0001F1ED\U0001F1F0香港41 | ⬇️ 2.0MB/s"
    network: ws
    port: 8080
    server: 507465c4-swd340-td1w5f-1t3cz.hgc1.tcpbbr.net
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: affe7124-c118-11ef-b6b2-f23c9164ca5d
    ws-opts:
      headers:
        Host: broadcastlv.chat.bilibili.com
      path: /
    xudp: true
  - alpn:
      - ''
    alterId: '0'
    cipher: auto
    name: "\U0001F1ED\U0001F1F0香港42 | ⬇️ 3.5MB/s"
    network: ws
    port: 8080
    server: 5c273aaf-swd340-t0dyyh-jjv2.hgc1.tcpbbr.net
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 31676725-b0fd-fed2-c857-2da8ce5a6e4f
    ws-opts:
      headers:
        Host: broadcastlv.chat.bilibili.com
      path: /
    xudp: true
  - alpn:
      - ''
    alterId: '0'
    cipher: auto
    name: "\U0001F1ED\U0001F1F0香港43 | ⬇️ 1.6MB/s"
    network: ws
    port: 8080
    server: 46194f76-swd340-szdere-155d9.hgc1.tcpbbr.net
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 34f0cf42-0f8a-11ec-a8bf-f23c91cfbbc9
    ws-opts:
      headers:
        Host: broadcastlv.chat.bilibili.com
      path: /
    xudp: true
  - alpn:
      - ''
    alterId: '0'
    cipher: auto
    name: "\U0001F1ED\U0001F1F0香港44 | ⬇️ 2.0MB/s"
    network: ws
    port: 8080
    server: a4c2de75-swd340-sx0fe4-1j6h0.hgc1.tcpbbr.net
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: dcccacba-fa44-11ef-8400-f23c9164ca5d
    ws-opts:
      headers:
        Host: broadcastlv.chat.bilibili.com
      path: /
    xudp: true
  - name: "\U0001F1EB\U0001F1EE芬兰6 | ⬇️ 1.4MB/s"
    network: ws
    port: 8880
    server: ***********
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
        User-Agent: >-
          Mozilla/5.0 (Linux; Android 7.1.2; Redmi Note 5A Build/N2G47H; wv)
          AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0
          Chrome/63.0.3239.111 Mobile Safari/537.36
      path: "/Telegram\U0001F1E8\U0001F1F3 @pgkj666 /?ed=2560"
    xudp: true
  - alpn:
      - ''
    alterId: '0'
    cipher: auto
    name: "\U0001F1ED\U0001F1F0香港45 | ⬇️ 971KB/s"
    network: ws
    port: 8080
    server: 3cd49d00-swd340-t8kd6j-1c9em.hgc1.tcpbbr.net
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 336501b6-51d2-11ee-a993-f23c9164ca5d
    ws-opts:
      headers:
        Host: broadcastlv.chat.bilibili.com
      path: /
    xudp: true
  - name: "\U0001F1ED\U0001F1F0香港46 | ⬇️ 926KB/s"
    network: ws
    port: 8880
    server: ************
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
        User-Agent: >-
          Mozilla/5.0 (Linux; Android 6.0; vivo 1610 Build/MMB29M)
          AppleWebKit/537.36 (KHTML, like Gecko) Chrome/53.0.2785.124 Mobile
          Safari/537.36
      path: "/Telegram\U0001F1E8\U0001F1F3 @pgkj666 /?ed=2560"
    xudp: true
  - alpn:
      - ''
    alterId: '0'
    cipher: auto
    name: "\U0001F1ED\U0001F1F0香港47 | ⬇️ 2.4MB/s"
    network: ws
    port: 8080
    server: 67892b95-swd340-t3o6u7-1osdm.hgc1.tcpbbr.net
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 91365a7a-46d7-11ee-a8b9-f23c9164ca5d
    ws-opts:
      headers:
        Host: broadcastlv.chat.bilibili.com
      path: /
    xudp: true
  - alpn:
      - ''
    alterId: '0'
    cipher: auto
    name: "\U0001F1ED\U0001F1F0香港48 | ⬇️ 3.4MB/s"
    network: ws
    port: 8080
    server: 212b7541-swd340-sxhurg-1th8j.hgc1.tcpbbr.net
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 7d7c111e-fbc1-11ef-ab5f-f23c913c8d2b
    ws-opts:
      headers:
        Host: broadcastlv.chat.bilibili.com
      path: /
    xudp: true
  - name: "\U0001F1E9\U0001F1EA德国32 | ⬇️ 788KB/s"
    network: ws
    port: 8880
    server: ***********0
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
        User-Agent: >-
          Mozilla/5.0 (Linux; Android 6.0.1; SM-J700M Build/MMB29K)
          AppleWebKit/537.36 (KHTML, like Gecko) Chrome/69.0.3497.100 Mobile
          Safari/537.36
      path: "/Telegram\U0001F1E8\U0001F1F3 @pgkj666 /?ed=2560"
    xudp: true
  - alpn:
      - ''
    alterId: '0'
    cipher: auto
    name: "\U0001F1ED\U0001F1F0香港49 | ⬇️ 1.4MB/s"
    network: ws
    port: 8080
    server: 2e88b440-swd340-sx3h07-1g8k0.hgc1.tcpbbr.net
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: d4018e28-e328-11ed-98a7-f23c913c8d2b
    ws-opts:
      headers:
        Host: broadcastlv.chat.bilibili.com
      path: /
    xudp: true
  - name: "\U0001F1EB\U0001F1EE芬兰7 | ⬇️ 568KB/s"
    network: ws
    port: 8880
    server: ************
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
        User-Agent: >-
          Mozilla/5.0 (Windows NT 5.1; WOW64) AppleWebKit/537.36 (KHTML, like
          Gecko) Chrome/46.0.2490.80 Safari/537.36
      path: "/Telegram\U0001F1E8\U0001F1F3 @pgkj666 /?ed=2560"
    xudp: true
  - name: "\U0001F1FA\U0001F1F8美国338 | ⬇️ 1.1MB/s"
    network: ws
    port: 8880
    server: ************
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
        User-Agent: >-
          Mozilla/5.0 (Windows NT 6.0; Win64; x64) AppleWebKit/537.36 (KHTML,
          like Gecko) Chrome/55.0.2883.87 Safari/537.36
      path: "/Telegram\U0001F1E8\U0001F1F3 @pgkj666 /?ed=2560"
    xudp: true
  - name: "\U0001F1E9\U0001F1EA德国33 | ⬇️ 2.2MB/s"
    network: ws
    port: 8880
    server: ***********3
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
        User-Agent: >-
          Mozilla/5.0 (Windows NT 6.2; Win64; x64) AppleWebKit/537.36 (KHTML,
          like Gecko) Chrome/69.0.3497.100 Safari/537.36
      path: "/Telegram\U0001F1E8\U0001F1F3 @pgkj666 /?ed=2560"
    xudp: true
  - name: "\U0001F1EE\U0001F1F3印度5 | ⬇️ 586KB/s"
    network: ws
    port: 8880
    server: ************
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
        User-Agent: >-
          Mozilla/5.0 (Windows NT 6.1) AppleWebKit/537.36 (KHTML, like Gecko)
          Chrome/70.0.3538.67 Safari/537.36
      path: "/Telegram\U0001F1E8\U0001F1F3 @pgkj666 /?ed=2560"
    xudp: true
  - name: "\U0001F1FA\U0001F1F8美国339 | ⬇️ 1.0MB/s"
    network: ws
    port: 8880
    server: ************
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
        User-Agent: >-
          Mozilla/5.0 (Windows NT 6.1) AppleWebKit/537.36 (KHTML, like Gecko)
          Chrome/41.0.2272.104 Safari/537.36
      path: "/Telegram\U0001F1E8\U0001F1F3 @pgkj666 /?ed=2560"
    xudp: true
  - name: "\U0001F1F0\U0001F1F7韩国25 | ⬇️ 683KB/s"
    network: ws
    port: 8880
    server: ************
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
        User-Agent: >-
          Mozilla/5.0 (Windows NT 6.3; WOW64) AppleWebKit/537.36 (KHTML, like
          Gecko) Chrome/43.0.2357.65 Safari/537.36
      path: "/Telegram\U0001F1E8\U0001F1F3 @pgkj666 /?ed=2560"
    xudp: true
  - name: "\U0001F1E9\U0001F1EA德国34 | ⬇️ 581KB/s"
    network: ws
    port: 8880
    server: ************
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
        User-Agent: >-
          Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like
          Gecko) Chrome/54.0.2840.71 Safari/537.36
      path: "/Telegram\U0001F1E8\U0001F1F3 @pgkj666 /?ed=2560"
    xudp: true
  - name: "\U0001F1FA\U0001F1F8美国340 | ⬇️ 627KB/s"
    network: ws
    port: 8880
    server: ************
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
        User-Agent: >-
          Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML,
          like Gecko) Chrome/67.0.3396.87 Safari/537.36
      path: "/Telegram\U0001F1E8\U0001F1F3 @pgkj666 /?ed=2560"
    xudp: true
  - name: "\U0001F1E9\U0001F1EA德国35 | ⬇️ 984KB/s"
    network: ws
    port: 8880
    server: ************
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
        User-Agent: >-
          Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like
          Gecko) Chrome/69.0.3497.100 Safari/537.36
      path: "/Telegram\U0001F1E8\U0001F1F3 @pgkj666 /?ed=2560"
    xudp: true
  - name: "\U0001F1EB\U0001F1EE芬兰8 | ⬇️ 858KB/s"
    network: ws
    port: 8880
    server: ***********4
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
        User-Agent: >-
          Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML,
          like Gecko) Chrome/58.0.3029.110 Safari/537.36
      path: "/Telegram\U0001F1E8\U0001F1F3 @pgkj666 /?ed=2560"
    xudp: true
  - name: "\U0001F1E6\U0001F1EA阿拉伯酋长国5 | ⬇️ 7.2MB/s"
    network: ws
    port: 8880
    server: ************
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
        User-Agent: >-
          Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like
          Gecko) Chrome/47.0.2526.111 Safari/537.36 MVisionPlayer/*******
      path: "/Telegram\U0001F1E8\U0001F1F3 @pgkj666 /?ed=2560"
    xudp: true
  - name: "\U0001F1FA\U0001F1F8美国341 | ⬇️ 532KB/s"
    network: ws
    port: 8880
    server: ************
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
        User-Agent: >-
          Mozilla/5.0 (Linux; Android 7.0; SM-G570M Build/NRD90M)
          AppleWebKit/537.36 (KHTML, like Gecko) Chrome/69.0.3497.100 Mobile
          Safari/537.36
      path: "/Telegram\U0001F1E8\U0001F1F3 @pgkj666 /?ed=2560"
    xudp: true
  - alpn:
      - ''
    alterId: '0'
    cipher: auto
    name: "\U0001F1ED\U0001F1F0香港50 | ⬇️ 1.7MB/s"
    network: ws
    port: 8080
    server: ff4511d3-sw7j40-sxf56z-1f596.hgc1.tcpbbr.net
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: e3975a05-b89e-11ec-a50d-f23c91cfbbc9
    ws-opts:
      headers:
        Host: broadcastlv.chat.bilibili.com
      path: /
    xudp: true
  - alpn:
      - ''
    alterId: '64'
    cipher: auto
    name: "\U0001F1FA\U0001F1F8美国342 | ⬇️ 618KB/s"
    network: tcp
    port: 42174
    server: ***************
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 418048af-a293-4b99-9b0c-98ca3580dd24
    xudp: true
  - alpn:
      - ''
    alterId: '0'
    cipher: auto
    name: "\U0001F1ED\U0001F1F0香港51 | ⬇️ 1.8MB/s"
    network: ws
    port: 8080
    server: 629ea6c1-svwf40-swdpud-duku.hgc1.tcpbbr.net
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 8e0f8ef2-5fe3-11ec-a8bf-f23c91cfbbc9
    ws-opts:
      headers:
        Host: broadcastlv.chat.bilibili.com
      path: /
    xudp: true
  - alpn:
      - ''
    alterId: '0'
    cipher: auto
    name: "\U0001F1ED\U0001F1F0香港52 | ⬇️ 4.3MB/s"
    network: ws
    port: 8080
    server: 29b6531a-svwf40-sw0dp4-3z3v.hgc1.tcpbbr.net
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 48c1e014-28d6-11ec-a0fc-f23c913c8d2b
    ws-opts:
      headers:
        Host: broadcastlv.chat.bilibili.com
      path: /
    xudp: true
  - alterId: '0'
    cipher: auto
    name: "\U0001F1ED\U0001F1F0香港53 | ⬇️ 6.6MB/s"
    network: ws
    port: 8080
    server: 78759a3f-swkhs0-t3o6u7-1osdm.hgc1.tcpbbr.net
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 91365a7a-46d7-11ee-a8b9-f23c9164ca5d
    ws-opts:
      headers:
        Host: broadcastlv.chat.bilibili.com
      path: /
    xudp: true
  - alterId: '0'
    cipher: auto
    name: "\U0001F1ED\U0001F1F0香港54 | ⬇️ 5.7MB/s"
    network: ws
    port: 8080
    server: a759528f-swkhs0-sww7b0-1qwp5.hgc1.tcpbbr.net
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 6e9cdc20-b92a-11ef-973a-f23c913c8d2b
    ws-opts:
      headers:
        Host: broadcastlv.chat.bilibili.com
      path: /
    xudp: true
  - alterId: '0'
    cipher: auto
    name: "\U0001F1ED\U0001F1F0香港55 | ⬇️ 4.9MB/s"
    network: ws
    port: 8080
    server: 69608311-swkhs0-sxuzn1-11p9g.hgc1.tcpbbr.net
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 384d1b42-655f-11ed-a8bf-f23c91cfbbc9
    ws-opts:
      headers:
        Host: broadcastlv.chat.bilibili.com
      path: /
    xudp: true
  - alterId: '0'
    cipher: auto
    name: "\U0001F1ED\U0001F1F0香港56 | ⬇️ 4.9MB/s"
    network: ws
    port: 8080
    server: 15a4b57b-swexs0-sxf56z-1f596.hgc1.tcpbbr.net
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: e3975a05-b89e-11ec-a50d-f23c91cfbbc9
    ws-opts:
      headers:
        Host: broadcastlv.chat.bilibili.com
      path: /
    xudp: true
  - alterId: '0'
    cipher: auto
    name: "\U0001F1ED\U0001F1F0香港57 | ⬇️ 2.5MB/s"
    network: ws
    port: 8080
    server: 2eb241f9-swexs0-t3o6u7-1osdm.hgc1.tcpbbr.net
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 91365a7a-46d7-11ee-a8b9-f23c9164ca5d
    ws-opts:
      headers:
        Host: broadcastlv.chat.bilibili.com
      path: /
    xudp: true
  - alterId: '0'
    cipher: auto
    name: "\U0001F1ED\U0001F1F0香港58 | ⬇️ 4.6MB/s"
    network: ws
    port: 8080
    server: 2bdf2559-swexs0-sxlsd4-3z3v.hgc1.tcpbbr.net
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 48c1e014-28d6-11ec-a0fc-f23c913c8d2b
    ws-opts:
      headers:
        Host: broadcastlv.chat.bilibili.com
      path: /
    xudp: true
  - alterId: '0'
    cipher: auto
    name: "\U0001F1ED\U0001F1F0香港59 | ⬇️ 2.8MB/s"
    network: ws
    port: 8080
    server: 59c9fee0-swexs0-swdpud-duku.hgc1.tcpbbr.net
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 8e0f8ef2-5fe3-11ec-a8bf-f23c91cfbbc9
    ws-opts:
      headers:
        Host: broadcastlv.chat.bilibili.com
      path: /
    xudp: true
  - alterId: '0'
    cipher: auto
    name: "\U0001F1ED\U0001F1F0香港60 | ⬇️ 3.9MB/s"
    network: ws
    port: 8080
    server: f0f81700-swexs0-sxuzn1-11p9g.hgc1.tcpbbr.net
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 384d1b42-655f-11ed-a8bf-f23c91cfbbc9
    ws-opts:
      headers:
        Host: broadcastlv.chat.bilibili.com
      path: /
    xudp: true
  - alterId: '0'
    cipher: auto
    name: "\U0001F1ED\U0001F1F0香港61 | ⬇️ 3.7MB/s"
    network: ws
    port: 8080
    server: b1f6976e-swexs0-td1w5f-1t3cz.hgc1.tcpbbr.net
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: affe7124-c118-11ef-b6b2-f23c9164ca5d
    ws-opts:
      headers:
        Host: broadcastlv.chat.bilibili.com
      path: /
    xudp: true
  - alterId: '0'
    cipher: auto
    name: "\U0001F1ED\U0001F1F0香港62 | ⬇️ 2.9MB/s"
    network: ws
    port: 8080
    server: fb8d1310-swexs0-t12cnj-1ol97.hgc1.tcpbbr.net
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 93fb69fc-77cf-11ee-85ee-f23c91369f2d
    ws-opts:
      headers:
        Host: broadcastlv.chat.bilibili.com
      path: /
    xudp: true
  - alterId: '0'
    cipher: auto
    name: "\U0001F1ED\U0001F1F0香港63 | ⬇️ 6.7MB/s"
    network: ws
    port: 8080
    server: a9f31e9c-swexs0-t0dyyh-jjv2.hgc1.tcpbbr.net
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: 31676725-b0fd-fed2-c857-2da8ce5a6e4f
    ws-opts:
      headers:
        Host: broadcastlv.chat.bilibili.com
      path: /
    xudp: true
  - alterId: '0'
    cipher: auto
    name: "\U0001F1ED\U0001F1F0香港64 | ⬇️ 5.8MB/s"
    network: ws
    port: 8080
    server: dcfe50f5-swexs0-szapkm-1hpo0.hgc1.tcpbbr.net
    skip-cert-verify: false
    tls: false
    type: vmess
    udp: true
    uuid: c3dc8d26-ab2b-11ec-a8bf-f23c91cfbbc9
    ws-opts:
      headers:
        Host: broadcastlv.chat.bilibili.com
      path: /
    xudp: true
  - alterId: 2
    cipher: auto
    name: "\U0001F1FA\U0001F1F8美国343 | ⬇️ 551KB/s"
    network: ws
    port: 30829
    server: v29.heduian.link
    skip-cert-verify: true
    tls: false
    type: vmess
    uuid: cbb3f877-d1fb-344c-87a9-d153bffd5484
    ws-opts:
      headers:
        Host: v29.heduian.link
      path: /oooo
    servername: v29.heduian.link
  - alterId: 0
    cipher: auto
    name: "\U0001F1FA\U0001F1F8美国344 | ⬇️ 1.6MB/s"
    network: ws
    port: 2053
    server: ***************
    skip-cert-verify: true
    tls: true
    type: vmess
    uuid: 381cb6d1-6ad4-4909-8494-b8d786cf78ce
    ws-opts:
      headers:
        Host: 1744007578.speed.mamha.cccp.freefly.pp.ua
      path: /
    servername: 1744007578.speed.mamha.cccp.freefly.pp.ua
  - alterId: 0
    cipher: auto
    name: "\U0001F300其他81-未识别 | ⬇️ 671KB/s"
    network: ws
    port: 443
    server: cloudgetservice.mcloudservice.site
    skip-cert-verify: true
    tls: true
    type: vmess
    uuid: 81cfc74e-79f6-4187-9c11-58b6acdc0e83
    ws-opts:
      headers:
        Host: uSa-Vp-149.BLazECloudMain.Site
      path: /linkvkws
    servername: cloudgetservice.mcloudservice.site
  - alpn:
      - http/1.1
    client-fingerprint: chrome
    name: "\U0001F300其他82-未识别 | ⬇️ 1.0MB/s"
    network: ws
    port: 443
    server: *************
    skip-cert-verify: true
    tfo: false
    tls: true
    type: vless
    uuid: f0f6e76e-e5fe-4e2c-9faf-34832e021eae
    ws-opts:
      headers:
        Host: Ty.457.PP.uA
      path: /AMpQdWyvpaPkb7QHHkQBzYO
    servername: Ty.457.PP.uA
  - auth: c6d93228-6393-41ec-8403-da852407390a
    down: 200
    name: "\U0001F1FA\U0001F1F8美国345 | ⬇️ 1.8MB/s"
    password: c6d93228-6393-41ec-8403-da852407390a
    port: 36925
    server: use.node.cross666.top
    skip-cert-verify: true
    sni: www.yahoo.com
    type: hysteria2
    udp: true
    up: 100
  - name: "\U0001F1FA\U0001F1F8美国346 | ⬇️ 501KB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: "/Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
  - auth: c6d93228-6393-41ec-8403-da852407390a
    down: 125
    name: "\U0001F1F7\U0001F1FA俄罗斯40 | ⬇️ 2.6MB/s"
    password: c6d93228-6393-41ec-8403-da852407390a
    port: 37432
    server: ru2.node.cross666.top
    skip-cert-verify: true
    sni: mirror.hyperdedic.ru
    type: hysteria2
    udp: true
    up: 100
  - auth: ba4672b3-e0af-4699-a180-f711cfaa95ed
    name: "\U0001F1EE\U0001F1F3印度6 | ⬇️ 4.7MB/s"
    password: ba4672b3-e0af-4699-a180-f711cfaa95ed
    port: 43999
    server: jiangzhiyd.54264944.xyz
    skip-cert-verify: true
    sni: jiangzhiyd.54264944.xyz
    type: hysteria2
    udp: true
  - auth: c6d93228-6393-41ec-8403-da852407390a
    down: 100
    name: "\U0001F1F7\U0001F1FA俄罗斯41 | ⬇️ 2.1MB/s"
    password: c6d93228-6393-41ec-8403-da852407390a
    port: 36499
    server: rua.node.cross666.top
    skip-cert-verify: true
    sni: mirror.hyperdedic.ru
    type: hysteria2
    udp: true
    up: 100
  - auth: 8f854788-85ba-4ab5-8626-5071bb6a43d3
    down: 160
    name: "\U0001F1FA\U0001F1F8美国347 | ⬇️ 2.0MB/s"
    password: 8f854788-85ba-4ab5-8626-5071bb6a43d3
    port: 39296
    server: us01.node.cross666.top
    skip-cert-verify: true
    sni: www.yahoo.com
    type: hysteria2
    udp: true
    up: 100
  - auth: 861726bb-c61a-b0e0-99fa-5ddfdcf21902
    name: "\U0001F1FA\U0001F1F8美国348 | ⬇️ 3.7MB/s"
    password: 861726bb-c61a-b0e0-99fa-5ddfdcf21902
    port: 8443
    server: 3ec98078-swvls0-ta5nd4-e06r.hy2.gotochinatown.net
    type: hysteria2
    udp: true
    sni: 3ec98078-swvls0-ta5nd4-e06r.hy2.gotochinatown.net
  - auth: 0e00f9c1-65c3-4e20-9da8-47fa7c363423
    down: 100
    name: "\U0001F1F7\U0001F1FA俄罗斯42 | ⬇️ 2.5MB/s"
    password: 0e00f9c1-65c3-4e20-9da8-47fa7c363423
    port: 35548
    server: rua.node.cross666.top
    skip-cert-verify: true
    sni: mirror.hyperdedic.ru
    type: hysteria2
    udp: true
    up: 100
  - auth: c6d93228-6393-41ec-8403-da852407390a
    down: 300
    name: "\U0001F1FA\U0001F1F8美国349 | ⬇️ 2.2MB/s"
    password: c6d93228-6393-41ec-8403-da852407390a
    port: 38332
    server: us4.node.cross666.top
    skip-cert-verify: true
    sni: www.yahoo.com
    type: hysteria2
    udp: true
    up: 100
  - auth: 34f0cf42-0f8a-11ec-a8bf-f23c91cfbbc9
    name: "\U0001F1FA\U0001F1F8美国350 | ⬇️ 3.1MB/s"
    password: 34f0cf42-0f8a-11ec-a8bf-f23c91cfbbc9
    port: 8443
    server: d9137bac-swvls0-szdere-155d9.hy2.gotochinatown.net
    type: hysteria2
    udp: true
    sni: d9137bac-swvls0-szdere-155d9.hy2.gotochinatown.net
  - auth: c6d93228-6393-41ec-8403-da852407390a
    down: 160
    name: "\U0001F1FA\U0001F1F8美国351 | ⬇️ 2.3MB/s"
    password: c6d93228-6393-41ec-8403-da852407390a
    port: 39963
    server: us01.node.cross666.top
    skip-cert-verify: true
    sni: www.yahoo.com
    type: hysteria2
    udp: true
    up: 100
  - auth: c6c1e4eb-06de-450a-9ad6-14a903db8406
    down: 300
    name: "\U0001F1F8\U0001F1EC新加坡34 | ⬇️ 824KB/s"
    password: c6c1e4eb-06de-450a-9ad6-14a903db8406
    port: 38410
    server: sg02.node.cross666.top
    skip-cert-verify: true
    sni: mirror.sg.gs
    type: hysteria2
    udp: true
    up: 100
  - auth: 8f854788-85ba-4ab5-8626-5071bb6a43d3
    down: 200
    name: "\U0001F1FA\U0001F1F8美国352 | ⬇️ 2.1MB/s"
    password: 8f854788-85ba-4ab5-8626-5071bb6a43d3
    port: 38361
    server: use.node.cross666.top
    skip-cert-verify: true
    sni: www.yahoo.com
    type: hysteria2
    udp: true
    up: 100
  - auth: c6c1e4eb-06de-450a-9ad6-14a903db8406
    down: 125
    name: "\U0001F1F7\U0001F1FA俄罗斯43 | ⬇️ 2.1MB/s"
    password: c6c1e4eb-06de-450a-9ad6-14a903db8406
    port: 36276
    server: ru2.node.cross666.top
    skip-cert-verify: true
    sni: mirror.hyperdedic.ru
    type: hysteria2
    udp: true
    up: 100
  - auth: c6c1e4eb-06de-450a-9ad6-14a903db8406
    down: 160
    name: "\U0001F1FA\U0001F1F8美国353 | ⬇️ 631KB/s"
    password: c6c1e4eb-06de-450a-9ad6-14a903db8406
    port: 36657
    server: us01.node.cross666.top
    skip-cert-verify: true
    sni: www.yahoo.com
    type: hysteria2
    udp: true
    up: 100
  - auth: 0da8651e-e1f6-11ec-bd7c-f23c913c8d2b
    name: "\U0001F1FA\U0001F1F8美国354 | ⬇️ 4.8MB/s"
    password: 0da8651e-e1f6-11ec-bd7c-f23c913c8d2b
    port: 8443
    server: d329acde-swvls0-tcinla-hrtf.hy2.gotochinatown.net
    type: hysteria2
    udp: true
    sni: d329acde-swvls0-tcinla-hrtf.hy2.gotochinatown.net
  - auth: abef57a5-ac14-4356-ba5d-c3fdc85df62b
    down: 300
    name: "\U0001F1F8\U0001F1EC新加坡35 | ⬇️ 1.9MB/s"
    password: abef57a5-ac14-4356-ba5d-c3fdc85df62b
    port: 37844
    server: sg02.node.cross666.top
    skip-cert-verify: true
    sni: mirror.sg.gs
    type: hysteria2
    udp: true
    up: 100
  - name: "\U0001F1FA\U0001F1F8美国355 | ⬇️ 2.2MB/s"
    network: ws
    port: 8880
    server: ***********
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: "/Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
  - name: "\U0001F1F0\U0001F1F7韩国26 | ⬇️ 878KB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: "/Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
  - cipher: dummy
    name: "\U0001F1F8\U0001F1EC新加坡36 | ⬇️ 786KB/s"
    obfs: plain
    obfs-param: cbcdc8198.microsoft.com
    password: mantouyun888
    port: 44003
    protocol: auth_chain_a
    protocol-param: '8198:CsJt8BBrKFOSrjv2'
    server: 88gg.mt.mt5888.top
    type: ssr
    udp: true
  - auth: 8f854788-85ba-4ab5-8626-5071bb6a43d3
    down: 180
    name: "\U0001F1FA\U0001F1F8美国356 | ⬇️ 1.2MB/s"
    password: 8f854788-85ba-4ab5-8626-5071bb6a43d3
    port: 37414
    server: us3.node.cross666.top
    skip-cert-verify: true
    sni: www.yahoo.com
    type: hysteria2
    udp: true
    up: 100
  - auth: 498c79e0-d4bb-43b0-a6c6-4bd5031c2405
    name: "\U0001F1EE\U0001F1F3印度7 | ⬇️ 5.8MB/s"
    password: 498c79e0-d4bb-43b0-a6c6-4bd5031c2405
    port: 43999
    server: jiangzhiyd.54264944.xyz
    skip-cert-verify: true
    sni: jiangzhiyd.54264944.xyz
    type: hysteria2
    udp: true
  - auth: d010926e-0311-4924-a013-b84fbae430f9
    name: "\U0001F1EC\U0001F1E7英国13 | ⬇️ 4.9MB/s"
    password: d010926e-0311-4924-a013-b84fbae430f9
    port: 30003
    server: qyyg.qy1357.top
    skip-cert-verify: true
    sni: qyyg.qy1357.top
    type: hysteria2
    udp: true
  - name: "\U0001F1F0\U0001F1F7韩国27 | ⬇️ 753KB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: "/Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
  - auth: abef57a5-ac14-4356-ba5d-c3fdc85df62b
    down: 125
    name: "\U0001F1F7\U0001F1FA俄罗斯44 | ⬇️ 1.8MB/s"
    password: abef57a5-ac14-4356-ba5d-c3fdc85df62b
    port: 39168
    server: ru2.node.cross666.top
    skip-cert-verify: true
    sni: mirror.hyperdedic.ru
    type: hysteria2
    udp: true
    up: 100
  - auth: c6d93228-6393-41ec-8403-da852407390a
    down: 160
    name: "\U0001F1FA\U0001F1F8美国357 | ⬇️ 2.3MB/s"
    password: c6d93228-6393-41ec-8403-da852407390a
    port: 35313
    server: us01.node.cross666.top
    skip-cert-verify: true
    sni: www.yahoo.com
    type: hysteria2
    udp: true
    up: 100
  - auth: 8f854788-85ba-4ab5-8626-5071bb6a43d3
    down: 200
    name: "\U0001F1FA\U0001F1F8美国358 | ⬇️ 2.3MB/s"
    password: 8f854788-85ba-4ab5-8626-5071bb6a43d3
    port: 35360
    server: use.node.cross666.top
    skip-cert-verify: true
    sni: www.yahoo.com
    type: hysteria2
    udp: true
    up: 100
  - auth: c6d93228-6393-41ec-8403-da852407390a
    down: 300
    name: "\U0001F1FA\U0001F1F8美国359 | ⬇️ 2.6MB/s"
    password: c6d93228-6393-41ec-8403-da852407390a
    port: 35416
    server: us4.node.cross666.top
    skip-cert-verify: true
    sni: www.yahoo.com
    type: hysteria2
    udp: true
    up: 100
  - auth: c6d93228-6393-41ec-8403-da852407390a
    down: 125
    name: "\U0001F1F7\U0001F1FA俄罗斯45 | ⬇️ 2.3MB/s"
    password: c6d93228-6393-41ec-8403-da852407390a
    port: 36504
    server: ru2.node.cross666.top
    skip-cert-verify: true
    sni: mirror.hyperdedic.ru
    type: hysteria2
    udp: true
    up: 100
  - auth: c6d93228-6393-41ec-8403-da852407390a
    down: 100
    name: "\U0001F1F7\U0001F1FA俄罗斯46 | ⬇️ 2.3MB/s"
    password: c6d93228-6393-41ec-8403-da852407390a
    port: 36263
    server: rua.node.cross666.top
    skip-cert-verify: true
    sni: mirror.hyperdedic.ru
    type: hysteria2
    udp: true
    up: 100
  - auth: 8f854788-85ba-4ab5-8626-5071bb6a43d3
    down: 125
    name: "\U0001F1F7\U0001F1FA俄罗斯47 | ⬇️ 2.2MB/s"
    password: 8f854788-85ba-4ab5-8626-5071bb6a43d3
    port: 36724
    server: ru2.node.cross666.top
    skip-cert-verify: true
    sni: mirror.hyperdedic.ru
    type: hysteria2
    udp: true
    up: 100
  - auth: 7b8dc3c9-cd74-4c22-9b29-************
    down: 300
    name: "\U0001F1FA\U0001F1F8美国360 | ⬇️ 2.7MB/s"
    password: 7b8dc3c9-cd74-4c22-9b29-************
    port: 37174
    server: us4.node.cross666.top
    skip-cert-verify: true
    sni: www.yahoo.com
    type: hysteria2
    udp: true
    up: 100
  - auth: c6d93228-6393-41ec-8403-da852407390a
    down: 160
    name: "\U0001F1FA\U0001F1F8美国361 | ⬇️ 2.2MB/s"
    password: c6d93228-6393-41ec-8403-da852407390a
    port: 37571
    server: us01.node.cross666.top
    skip-cert-verify: true
    sni: www.yahoo.com
    type: hysteria2
    udp: true
    up: 100
  - auth: c6d93228-6393-41ec-8403-da852407390a
    down: 125
    name: "\U0001F1F7\U0001F1FA俄罗斯48 | ⬇️ 2.2MB/s"
    password: c6d93228-6393-41ec-8403-da852407390a
    port: 38293
    server: ru2.node.cross666.top
    skip-cert-verify: true
    sni: mirror.hyperdedic.ru
    type: hysteria2
    udp: true
    up: 100
  - auth: 8f854788-85ba-4ab5-8626-5071bb6a43d3
    down: 300
    name: "\U0001F1F8\U0001F1EC新加坡37 | ⬇️ 1.1MB/s"
    password: 8f854788-85ba-4ab5-8626-5071bb6a43d3
    port: 37966
    server: sg02.node.cross666.top
    skip-cert-verify: true
    sni: mirror.sg.gs
    type: hysteria2
    udp: true
    up: 100
  - auth: c6c1e4eb-06de-450a-9ad6-14a903db8406
    down: 160
    name: "\U0001F1FA\U0001F1F8美国362 | ⬇️ 880KB/s"
    password: c6c1e4eb-06de-450a-9ad6-14a903db8406
    port: 37738
    server: us01.node.cross666.top
    skip-cert-verify: true
    sni: www.yahoo.com
    type: hysteria2
    udp: true
    up: 100
  - auth: abef57a5-ac14-4356-ba5d-c3fdc85df62b
    down: 300
    name: "\U0001F1FA\U0001F1F8美国363 | ⬇️ 2.2MB/s"
    password: abef57a5-ac14-4356-ba5d-c3fdc85df62b
    port: 38282
    server: us4.node.cross666.top
    skip-cert-verify: true
    sni: www.yahoo.com
    type: hysteria2
    udp: true
    up: 100
  - auth: 0e00f9c1-65c3-4e20-9da8-47fa7c363423
    down: 100
    name: "\U0001F1F7\U0001F1FA俄罗斯49 | ⬇️ 2.4MB/s"
    password: 0e00f9c1-65c3-4e20-9da8-47fa7c363423
    port: 39606
    server: rua.node.cross666.top
    skip-cert-verify: true
    sni: mirror.hyperdedic.ru
    type: hysteria2
    udp: true
    up: 100
  - auth: 8f854788-85ba-4ab5-8626-5071bb6a43d3
    down: 100
    name: "\U0001F1F7\U0001F1FA俄罗斯50 | ⬇️ 2.2MB/s"
    password: 8f854788-85ba-4ab5-8626-5071bb6a43d3
    port: 35230
    server: rua.node.cross666.top
    skip-cert-verify: true
    sni: mirror.hyperdedic.ru
    type: hysteria2
    udp: true
    up: 100
  - auth: c6d93228-6393-41ec-8403-da852407390a
    down: 160
    name: "\U0001F1FA\U0001F1F8美国364 | ⬇️ 2.2MB/s"
    password: c6d93228-6393-41ec-8403-da852407390a
    port: 37731
    server: us01.node.cross666.top
    skip-cert-verify: true
    sni: www.yahoo.com
    type: hysteria2
    udp: true
    up: 100
  - name: "\U0001F1FA\U0001F1F8美国365 | ⬇️ 2.0MB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: "/Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
  - auth: 7b8dc3c9-cd74-4c22-9b29-************
    down: 300
    name: "\U0001F1F8\U0001F1EC新加坡38 | ⬇️ 1.4MB/s"
    password: 7b8dc3c9-cd74-4c22-9b29-************
    port: 37403
    server: sg02.node.cross666.top
    skip-cert-verify: true
    sni: mirror.sg.gs
    type: hysteria2
    udp: true
    up: 100
  - auth: 8f854788-85ba-4ab5-8626-5071bb6a43d3
    down: 100
    name: "\U0001F1F7\U0001F1FA俄罗斯51 | ⬇️ 1.7MB/s"
    password: 8f854788-85ba-4ab5-8626-5071bb6a43d3
    port: 39719
    server: rua.node.cross666.top
    skip-cert-verify: true
    sni: mirror.hyperdedic.ru
    type: hysteria2
    udp: true
    up: 100
  - auth: 4a6b73d6-697a-4195-9f3c-11034a579c5e
    down: 50
    name: "\U0001F1FA\U0001F1F8美国366 | ⬇️ 958KB/s"
    obfs: salamander
    obfs-password: NDhhNmY5YTY0MGYzOTgxYQ==
    password: 4a6b73d6-697a-4195-9f3c-11034a579c5e
    port: 33076
    server: **************
    skip-cert-verify: true
    type: hysteria2
    udp: true
    up: 50
  - auth: c6c1e4eb-06de-450a-9ad6-14a903db8406
    down: 300
    name: "\U0001F1FA\U0001F1F8美国367 | ⬇️ 2.5MB/s"
    password: c6c1e4eb-06de-450a-9ad6-14a903db8406
    port: 39535
    server: us4.node.cross666.top
    skip-cert-verify: true
    sni: www.yahoo.com
    type: hysteria2
    udp: true
    up: 100
  - auth: 8f854788-85ba-4ab5-8626-5071bb6a43d3
    down: 125
    name: "\U0001F1F7\U0001F1FA俄罗斯52 | ⬇️ 2.4MB/s"
    password: 8f854788-85ba-4ab5-8626-5071bb6a43d3
    port: 39545
    server: ru2.node.cross666.top
    skip-cert-verify: true
    sni: mirror.hyperdedic.ru
    type: hysteria2
    udp: true
    up: 100
  - name: "\U0001F1F3\U0001F1F1荷兰8 | ⬇️ 972KB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: "/Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
  - auth: dcccacba-fa44-11ef-8400-f23c9164ca5d
    name: "\U0001F1FA\U0001F1F8美国368 | ⬇️ 7.1MB/s"
    password: dcccacba-fa44-11ef-8400-f23c9164ca5d
    port: 8443
    server: d465b594-swvls0-sx0fe4-1j6h0.hy2.gotochinatown.net
    type: hysteria2
    udp: true
    sni: d465b594-swvls0-sx0fe4-1j6h0.hy2.gotochinatown.net
  - auth: c6c1e4eb-06de-450a-9ad6-14a903db8406
    down: 125
    name: "\U0001F1F7\U0001F1FA俄罗斯53 | ⬇️ 2.2MB/s"
    password: c6c1e4eb-06de-450a-9ad6-14a903db8406
    port: 36754
    server: ru2.node.cross666.top
    skip-cert-verify: true
    sni: mirror.hyperdedic.ru
    type: hysteria2
    udp: true
    up: 100
  - auth: 8f854788-85ba-4ab5-8626-5071bb6a43d3
    down: 100
    name: "\U0001F1F7\U0001F1FA俄罗斯54 | ⬇️ 2.3MB/s"
    password: 8f854788-85ba-4ab5-8626-5071bb6a43d3
    port: 38722
    server: rua.node.cross666.top
    skip-cert-verify: true
    sni: mirror.hyperdedic.ru
    type: hysteria2
    udp: true
    up: 100
  - auth: 8f854788-85ba-4ab5-8626-5071bb6a43d3
    down: 300
    name: "\U0001F1FA\U0001F1F8美国369 | ⬇️ 2.2MB/s"
    password: 8f854788-85ba-4ab5-8626-5071bb6a43d3
    port: 39980
    server: us4.node.cross666.top
    skip-cert-verify: true
    sni: www.yahoo.com
    type: hysteria2
    udp: true
    up: 100
  - auth: 219e2f40-759b-4e00-9d4b-4bd588e5b84e
    down: 10
    name: "\U0001F1FA\U0001F1F8美国370 | ⬇️ 588KB/s"
    obfs: salamander
    obfs-password: NDhhNmY5YTY0MGYzOTgxYQ==
    password: 219e2f40-759b-4e00-9d4b-4bd588e5b84e
    port: 33076
    server: **************
    skip-cert-verify: true
    type: hysteria2
    udp: true
    up: 10
  - auth: 7b8dc3c9-cd74-4c22-9b29-************
    down: 100
    name: "\U0001F1F7\U0001F1FA俄罗斯55 | ⬇️ 2.2MB/s"
    password: 7b8dc3c9-cd74-4c22-9b29-************
    port: 37958
    server: rua.node.cross666.top
    skip-cert-verify: true
    sni: mirror.hyperdedic.ru
    type: hysteria2
    udp: true
    up: 100
  - auth: c6d93228-6393-41ec-8403-da852407390a
    down: 125
    name: "\U0001F1F7\U0001F1FA俄罗斯56 | ⬇️ 2.4MB/s"
    password: c6d93228-6393-41ec-8403-da852407390a
    port: 35898
    server: ru2.node.cross666.top
    skip-cert-verify: true
    sni: mirror.hyperdedic.ru
    type: hysteria2
    udp: true
    up: 100
  - auth: 869a0163-456f-4c06-bd4a-2376e4563eae
    name: "\U0001F1FA\U0001F1F8美国371 | ⬇️ 4.3MB/s"
    password: 869a0163-456f-4c06-bd4a-2376e4563eae
    port: 30003
    server: qymg.qy1357.top
    skip-cert-verify: true
    sni: qymg.qy1357.top
    type: hysteria2
    udp: true
  - auth: c6d93228-6393-41ec-8403-da852407390a
    down: 160
    name: "\U0001F1FA\U0001F1F8美国372 | ⬇️ 2.6MB/s"
    password: c6d93228-6393-41ec-8403-da852407390a
    port: 36347
    server: us01.node.cross666.top
    skip-cert-verify: true
    sni: www.yahoo.com
    type: hysteria2
    udp: true
    up: 100
  - name: "\U0001F1EB\U0001F1EE芬兰9 | ⬇️ 523KB/s"
    network: ws
    port: 80
    server: ***************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: '/Telegram:'
  - auth: c6d93228-6393-41ec-8403-da852407390a
    down: 300
    name: "\U0001F1F8\U0001F1EC新加坡39 | ⬇️ 2.1MB/s"
    password: c6d93228-6393-41ec-8403-da852407390a
    port: 38289
    server: sg02.node.cross666.top
    skip-cert-verify: true
    sni: mirror.sg.gs
    type: hysteria2
    udp: true
    up: 100
  - auth: c6d93228-6393-41ec-8403-da852407390a
    down: 180
    name: "\U0001F1FA\U0001F1F8美国373 | ⬇️ 1.9MB/s"
    password: c6d93228-6393-41ec-8403-da852407390a
    port: 38239
    server: us3.node.cross666.top
    skip-cert-verify: true
    sni: www.yahoo.com
    type: hysteria2
    udp: true
    up: 100
  - auth: 0e00f9c1-65c3-4e20-9da8-47fa7c363423
    down: 300
    name: "\U0001F1F8\U0001F1EC新加坡40 | ⬇️ 551KB/s"
    password: 0e00f9c1-65c3-4e20-9da8-47fa7c363423
    port: 36609
    server: sg02.node.cross666.top
    skip-cert-verify: true
    sni: mirror.sg.gs
    type: hysteria2
    udp: true
    up: 100
  - name: "\U0001F1F3\U0001F1F1荷兰9 | ⬇️ 1.6MB/s"
    network: ws
    port: 8880
    server: ***********
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: "/Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
  - auth: abef57a5-ac14-4356-ba5d-c3fdc85df62b
    down: 180
    name: "\U0001F1FA\U0001F1F8美国374 | ⬇️ 2.6MB/s"
    password: abef57a5-ac14-4356-ba5d-c3fdc85df62b
    port: 36529
    server: us3.node.cross666.top
    skip-cert-verify: true
    sni: www.yahoo.com
    type: hysteria2
    udp: true
    up: 100
  - auth: c6d93228-6393-41ec-8403-da852407390a
    down: 200
    name: "\U0001F1ED\U0001F1F0香港65 | ⬇️ 1.3MB/s"
    password: c6d93228-6393-41ec-8403-da852407390a
    port: 37825
    server: hka.node.cross666.top
    skip-cert-verify: true
    sni: mirrors.xtom.hk
    type: hysteria2
    udp: true
    up: 100
  - auth: 5479f2d5-a0c4-437e-95c9-f79a0c485af1
    down: 200
    name: "\U0001F1FA\U0001F1F8美国375 | ⬇️ 1.7MB/s"
    password: 5479f2d5-a0c4-437e-95c9-f79a0c485af1
    port: 39470
    server: use.node.cross666.top
    skip-cert-verify: true
    sni: www.yahoo.com
    type: hysteria2
    udp: true
    up: 100
  - auth: 8f854788-85ba-4ab5-8626-5071bb6a43d3
    down: 160
    name: "\U0001F1FA\U0001F1F8美国376 | ⬇️ 1.6MB/s"
    password: 8f854788-85ba-4ab5-8626-5071bb6a43d3
    port: 39423
    server: us01.node.cross666.top
    skip-cert-verify: true
    sni: www.yahoo.com
    type: hysteria2
    udp: true
    up: 100
  - auth: c6d93228-6393-41ec-8403-da852407390a
    down: 300
    name: "\U0001F1FA\U0001F1F8美国377 | ⬇️ 2.4MB/s"
    password: c6d93228-6393-41ec-8403-da852407390a
    port: 36414
    server: us4.node.cross666.top
    skip-cert-verify: true
    sni: www.yahoo.com
    type: hysteria2
    udp: true
    up: 100
  - auth: 8f854788-85ba-4ab5-8626-5071bb6a43d3
    down: 100
    name: "\U0001F1F7\U0001F1FA俄罗斯57 | ⬇️ 2.2MB/s"
    password: 8f854788-85ba-4ab5-8626-5071bb6a43d3
    port: 37883
    server: rua.node.cross666.top
    skip-cert-verify: true
    sni: mirror.hyperdedic.ru
    type: hysteria2
    udp: true
    up: 100
  - auth: 8f854788-85ba-4ab5-8626-5071bb6a43d3
    down: 125
    name: "\U0001F1F7\U0001F1FA俄罗斯58 | ⬇️ 2.0MB/s"
    password: 8f854788-85ba-4ab5-8626-5071bb6a43d3
    port: 38486
    server: ru2.node.cross666.top
    skip-cert-verify: true
    sni: mirror.hyperdedic.ru
    type: hysteria2
    udp: true
    up: 100
  - auth: 5479f2d5-a0c4-437e-95c9-f79a0c485af1
    down: 200
    name: "\U0001F1FA\U0001F1F8美国378 | ⬇️ 764KB/s"
    password: 5479f2d5-a0c4-437e-95c9-f79a0c485af1
    port: 38372
    server: use.node.cross666.top
    skip-cert-verify: true
    sni: www.yahoo.com
    type: hysteria2
    udp: true
    up: 100
  - name: "\U0001F1FA\U0001F1F8美国379 | ⬇️ 766KB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: "/Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
  - auth: 8f854788-85ba-4ab5-8626-5071bb6a43d3
    down: 180
    name: "\U0001F1FA\U0001F1F8美国380 | ⬇️ 2.5MB/s"
    password: 8f854788-85ba-4ab5-8626-5071bb6a43d3
    port: 37046
    server: us3.node.cross666.top
    skip-cert-verify: true
    sni: www.yahoo.com
    type: hysteria2
    udp: true
    up: 100
  - auth: abef57a5-ac14-4356-ba5d-c3fdc85df62b
    down: 100
    name: "\U0001F1F7\U0001F1FA俄罗斯59 | ⬇️ 2.6MB/s"
    password: abef57a5-ac14-4356-ba5d-c3fdc85df62b
    port: 37446
    server: rua.node.cross666.top
    skip-cert-verify: true
    sni: mirror.hyperdedic.ru
    type: hysteria2
    udp: true
    up: 100
  - auth: c6d93228-6393-41ec-8403-da852407390a
    down: 100
    name: "\U0001F1F7\U0001F1FA俄罗斯60 | ⬇️ 1.4MB/s"
    password: c6d93228-6393-41ec-8403-da852407390a
    port: 38417
    server: rua.node.cross666.top
    skip-cert-verify: true
    sni: mirror.hyperdedic.ru
    type: hysteria2
    udp: true
    up: 100
  - auth: fbZYdngMs7US5yJHmFNEpqOtfUQ
    name: "\U0001F300其他83-AT | ⬇️ 5.1MB/s"
    password: fbZYdngMs7US5yJHmFNEpqOtfUQ
    port: 13852
    server: ************
    skip-cert-verify: true
    sni: bing.com
    type: hysteria2
    udp: true
  - auth: c6d93228-6393-41ec-8403-da852407390a
    down: 100
    name: "\U0001F1F7\U0001F1FA俄罗斯61 | ⬇️ 986KB/s"
    password: c6d93228-6393-41ec-8403-da852407390a
    port: 37216
    server: rua.node.cross666.top
    skip-cert-verify: true
    sni: mirror.hyperdedic.ru
    type: hysteria2
    udp: true
    up: 100
  - name: "\U0001F1E9\U0001F1EA德国36 | ⬇️ 1.6MB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: "/Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
  - auth: abef57a5-ac14-4356-ba5d-c3fdc85df62b
    down: 160
    name: "\U0001F1FA\U0001F1F8美国381 | ⬇️ 2.4MB/s"
    password: abef57a5-ac14-4356-ba5d-c3fdc85df62b
    port: 38217
    server: us01.node.cross666.top
    skip-cert-verify: true
    sni: www.yahoo.com
    type: hysteria2
    udp: true
    up: 100
  - auth: c6d93228-6393-41ec-8403-da852407390a
    down: 400
    name: "\U0001F300其他84-TR | ⬇️ 2.5MB/s"
    password: c6d93228-6393-41ec-8403-da852407390a
    port: 35258
    server: tr01.node.cross666.top
    skip-cert-verify: true
    sni: mirror.dogado.de
    type: hysteria2
    udp: true
    up: 100
  - name: "\U0001F1E9\U0001F1EA德国37 | ⬇️ 829KB/s"
    network: ws
    port: 8880
    server: ***********
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: "/Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
  - auth: 8f854788-85ba-4ab5-8626-5071bb6a43d3
    down: 180
    name: "\U0001F1FA\U0001F1F8美国382 | ⬇️ 2.1MB/s"
    password: 8f854788-85ba-4ab5-8626-5071bb6a43d3
    port: 35126
    server: us3.node.cross666.top
    skip-cert-verify: true
    sni: www.yahoo.com
    type: hysteria2
    udp: true
    up: 100
  - name: "\U0001F1FA\U0001F1F8美国383 | ⬇️ 921KB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: "/Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
  - auth: 8f854788-85ba-4ab5-8626-5071bb6a43d3
    down: 125
    name: "\U0001F1F7\U0001F1FA俄罗斯62 | ⬇️ 2.3MB/s"
    password: 8f854788-85ba-4ab5-8626-5071bb6a43d3
    port: 38031
    server: ru2.node.cross666.top
    skip-cert-verify: true
    sni: mirror.hyperdedic.ru
    type: hysteria2
    udp: true
    up: 100
  - auth: c6d93228-6393-41ec-8403-da852407390a
    down: 300
    name: "\U0001F1FA\U0001F1F8美国384 | ⬇️ 2.6MB/s"
    password: c6d93228-6393-41ec-8403-da852407390a
    port: 38572
    server: us4.node.cross666.top
    skip-cert-verify: true
    sni: www.yahoo.com
    type: hysteria2
    udp: true
    up: 100
  - name: "\U0001F1EB\U0001F1EE芬兰10 | ⬇️ 662KB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: "/Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
  - auth: afc216c8-e407-48e0-86b1-6bf20f709c72
    down: 100
    name: "\U0001F1F7\U0001F1FA俄罗斯63 | ⬇️ 2.4MB/s"
    password: afc216c8-e407-48e0-86b1-6bf20f709c72
    port: 39054
    server: rua.node.cross666.top
    skip-cert-verify: true
    sni: mirror.hyperdedic.ru
    type: hysteria2
    udp: true
    up: 100
  - name: "\U0001F1F0\U0001F1F7韩国28 | ⬇️ 775KB/s"
    network: ws
    port: 8880
    server: **********29
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: "/Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
  - name: "\U0001F1FA\U0001F1F8美国385 | ⬇️ 1.9MB/s"
    network: ws
    port: 8880
    server: *************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /?ed=2560
  - name: "\U0001F1FA\U0001F1F8美国386 | ⬇️ 2.6MB/s"
    network: ws
    port: 8880
    server: *************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /?ed=2560
  - name: "\U0001F1F0\U0001F1F7韩国29 | ⬇️ 2.3MB/s"
    network: ws
    port: 8880
    server: **************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /Telegram@ShadowProxy66/?ed=2560
  - auth: 5479f2d5-a0c4-437e-95c9-f79a0c485af1
    down: 300
    name: "\U0001F1FA\U0001F1F8美国387 | ⬇️ 2.3MB/s"
    password: 5479f2d5-a0c4-437e-95c9-f79a0c485af1
    port: 38382
    server: us4.node.cross666.top
    skip-cert-verify: true
    sni: www.yahoo.com
    type: hysteria2
    udp: true
    up: 100
  - auth: 8f854788-85ba-4ab5-8626-5071bb6a43d3
    down: 100
    name: "\U0001F1F7\U0001F1FA俄罗斯64 | ⬇️ 2.2MB/s"
    password: 8f854788-85ba-4ab5-8626-5071bb6a43d3
    port: 39675
    server: rua.node.cross666.top
    skip-cert-verify: true
    sni: mirror.hyperdedic.ru
    type: hysteria2
    udp: true
    up: 100
  - auth: c6d93228-6393-41ec-8403-da852407390a
    down: 100
    name: "\U0001F1F7\U0001F1FA俄罗斯65 | ⬇️ 1.5MB/s"
    password: c6d93228-6393-41ec-8403-da852407390a
    port: 37491
    server: rua.node.cross666.top
    skip-cert-verify: true
    sni: mirror.hyperdedic.ru
    type: hysteria2
    udp: true
    up: 100
  - auth: c6d93228-6393-41ec-8403-da852407390a
    down: 100
    name: "\U0001F1F7\U0001F1FA俄罗斯66 | ⬇️ 1.3MB/s"
    password: c6d93228-6393-41ec-8403-da852407390a
    port: 36278
    server: rua.node.cross666.top
    skip-cert-verify: true
    sni: mirror.hyperdedic.ru
    type: hysteria2
    udp: true
    up: 100
  - name: "\U0001F1EE\U0001F1F3印度8 | ⬇️ 603KB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: "/Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
  - name: "\U0001F1EB\U0001F1EE芬兰11 | ⬇️ 2.3MB/s"
    network: ws
    port: 8880
    server: ***********
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: /?ed=2560
  - name: "\U0001F1ED\U0001F1F0香港66 | ⬇️ 2.0MB/s"
    network: ws
    port: 8880
    server: ***********
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: "/Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
  - auth: 8f854788-85ba-4ab5-8626-5071bb6a43d3
    down: 100
    name: "\U0001F1F7\U0001F1FA俄罗斯67 | ⬇️ 2.5MB/s"
    password: 8f854788-85ba-4ab5-8626-5071bb6a43d3
    port: 37778
    server: rua.node.cross666.top
    skip-cert-verify: true
    sni: mirror.hyperdedic.ru
    type: hysteria2
    udp: true
    up: 100
  - auth: afc216c8-e407-48e0-86b1-6bf20f709c72
    down: 125
    name: "\U0001F1F7\U0001F1FA俄罗斯68 | ⬇️ 2.2MB/s"
    password: afc216c8-e407-48e0-86b1-6bf20f709c72
    port: 39060
    server: ru2.node.cross666.top
    skip-cert-verify: true
    sni: mirror.hyperdedic.ru
    type: hysteria2
    udp: true
    up: 100
  - auth: c6d93228-6393-41ec-8403-da852407390a
    down: 300
    name: "\U0001F1FA\U0001F1F8美国388 | ⬇️ 2.3MB/s"
    password: c6d93228-6393-41ec-8403-da852407390a
    port: 39860
    server: us4.node.cross666.top
    skip-cert-verify: true
    sni: www.yahoo.com
    type: hysteria2
    udp: true
    up: 100
  - auth: 8f854788-85ba-4ab5-8626-5071bb6a43d3
    down: 125
    name: "\U0001F1F7\U0001F1FA俄罗斯69 | ⬇️ 2.4MB/s"
    password: 8f854788-85ba-4ab5-8626-5071bb6a43d3
    port: 38042
    server: ru2.node.cross666.top
    skip-cert-verify: true
    sni: mirror.hyperdedic.ru
    type: hysteria2
    udp: true
    up: 100
  - name: "\U0001F1FA\U0001F1F8美国389 | ⬇️ 6.4MB/s"
    network: ws
    port: 8880
    server: ************
    tls: false
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
      path: "/Telegram\U0001F1E8\U0001F1F3 @MxlShare @WangCai2 /?ed=2560"
  - auth: 5479f2d5-a0c4-437e-95c9-f79a0c485af1
    down: 200
    name: "\U0001F1FA\U0001F1F8美国390 | ⬇️ 1.6MB/s"
    password: 5479f2d5-a0c4-437e-95c9-f79a0c485af1
    port: 39914
    server: use.node.cross666.top
    skip-cert-verify: true
    sni: www.yahoo.com
    type: hysteria2
    udp: true
    up: 100
  - alpn:
      - h3
    auth-str: dongtaiwang.com
    auth_str: dongtaiwang.com
    down: 55
    name: "\U0001F1EB\U0001F1F7法国22 | ⬇️ 4.5MB/s"
    port: 46938
    protocol: udp
    server: ***************
    skip-cert-verify: true
    sni: apple.com
    type: hysteria
    udp: true
    up: 11
  - auth: 8f854788-85ba-4ab5-8626-5071bb6a43d3
    down: 300
    name: "\U0001F1F8\U0001F1EC新加坡41 | ⬇️ 582KB/s"
    password: 8f854788-85ba-4ab5-8626-5071bb6a43d3
    port: 39373
    server: sg02.node.cross666.top
    skip-cert-verify: true
    sni: mirror.sg.gs
    type: hysteria2
    udp: true
    up: 100
  - name: "\U0001F1EB\U0001F1EE芬兰12 | ⬇️ 1.2MB/s"
    network: ws
    port: 80
    server: ************
    type: vless
    udp: true
    uuid: 53fa8faf-ba4b-4322-9c69-a3e5b1555049
    ws-opts:
      headers:
        Host: reedfree8mahsang2.redorg.ir
        User-Agent: >-
          Mozilla/5.0 (Windows NT 6.3; WOW64) AppleWebKit/537.36 (KHTML, like
          Gecko) Chrome/48.0.2564.109 Safari/537.36
      path: '/Telegram:@vpnAndroid2/?ed=2560'
    xudp: true
port: 7890
socks-port: 7891
redir-port: 7892
mixed-port: 7893
tproxy-port: 7894
ipv6: false
allow-lan: true
unified-delay: true
tcp-concurrent: true
geodata-mode: false
geodata-loader: standard
geo-auto-update: true
geo-update-interval: 48
geox-url:
  geoip: 'https://testingcf.jsdelivr.net/gh/MetaCubeX/meta-rules-dat@release/geoip.dat'
  geosite: >-
    https://testingcf.jsdelivr.net/gh/MetaCubeX/meta-rules-dat@release/geosite.dat
  mmdb: >-
    https://testingcf.jsdelivr.net/gh/MetaCubeX/meta-rules-dat@release/country.mmdb
  asn: >-
    https://github.com/xishang0128/geoip/releases/download/latest/GeoLite2-ASN.mmdb
profile:
  store-selected: true
  store-fake-ip: true
sniffer:
  enable: true
  sniff:
    HTTP:
      ports:
        - 80
        - 8080-8880
      override-destination: true
    TLS:
      ports:
        - 443
        - 8443
    QUIC:
      ports:
        - 443
        - 8443
  force-domain:
    - +.v2ex.com
  skip-domain:
    - Mijia Cloud
    - dlg.io.mi.com
    - +.push.apple.com
    - +.apple.com
dns:
  enable: true
  listen: '0.0.0.0:1053'
  ipv6: false
  respect-rules: true
  enhanced-mode: fake-ip
  fake-ip-range: ********/8
  fake-ip-filter-mode: blacklist
  fake-ip-filter:
    - +.lan
    - +.local
    - 'geosite:private'
    - 'geosite:cn'
  default-nameserver:
    - *********
    - ************
  proxy-server-nameserver:
    - *********
    - ************
  nameserver:
    - *********
    - ************
  nameserver-policy:
    'rule-set:private_domain,cn_domain':
      - *********
      - ************
    'rule-set:geolocation-!cn':
      - 'https://dns.cloudflare.com/dns-query'
      - 'https://dns.google/dns-query'
pr:
  type: select
  proxies:
    - "\U0001F680 节点选择"
    - "\U0001F1ED\U0001F1F0 香港负载均衡"
    - "\U0001F1EF\U0001F1F5 日本负载均衡"
    - "\U0001F1F0\U0001F1F7 韩国负载均衡"
    - "\U0001F1F8\U0001F1EC 新加坡负载均衡"
    - "\U0001F1FA\U0001F1F8 美国负载均衡"
    - "\U0001F1ED\U0001F1F0 香港自动"
    - "\U0001F1EF\U0001F1F5 日本自动"
    - "\U0001F1F0\U0001F1F7 韩国自动"
    - "\U0001F1F8\U0001F1EC 新加坡自动"
    - "\U0001F1FA\U0001F1F8 美国自动"
    - ♻️ 自动选择
    - "\U0001F1ED\U0001F1F0 香港节点"
    - "\U0001F1EF\U0001F1F5 日本节点"
    - "\U0001F1F0\U0001F1F7 韩国节点"
    - "\U0001F1F8\U0001F1EC 新加坡节点"
    - "\U0001F1FA\U0001F1F8 美国节点"
    - "\U0001F310 全部节点"
proxy-groups:
  - name: "\U0001F310 全部节点"
    type: select
    include-all: true
  - name: "\U0001F680 节点选择"
    type: select
    proxies:
      - "\U0001F1ED\U0001F1F0 香港负载均衡"
      - "\U0001F1EF\U0001F1F5 日本负载均衡"
      - "\U0001F1F0\U0001F1F7 韩国负载均衡"
      - "\U0001F1F8\U0001F1EC 新加坡负载均衡"
      - "\U0001F1FA\U0001F1F8 美国负载均衡"
      - "\U0001F1ED\U0001F1F0 香港自动"
      - "\U0001F1EF\U0001F1F5 日本自动"
      - "\U0001F1F0\U0001F1F7 韩国自动"
      - "\U0001F1F8\U0001F1EC 新加坡自动"
      - "\U0001F1FA\U0001F1F8 美国自动"
      - ♻️ 自动选择
      - "\U0001F1ED\U0001F1F0 香港节点"
      - "\U0001F1EF\U0001F1F5 日本节点"
      - "\U0001F1F0\U0001F1F7 韩国节点"
      - "\U0001F1F8\U0001F1EC 新加坡节点"
      - "\U0001F1FA\U0001F1F8 美国节点"
      - "\U0001F310 全部节点"
  - name: "\U0001F4AC ChatGPT"
    type: select
    proxies:
      - "\U0001F680 节点选择"
      - "\U0001F1ED\U0001F1F0 香港负载均衡"
      - "\U0001F1EF\U0001F1F5 日本负载均衡"
      - "\U0001F1F0\U0001F1F7 韩国负载均衡"
      - "\U0001F1F8\U0001F1EC 新加坡负载均衡"
      - "\U0001F1FA\U0001F1F8 美国负载均衡"
      - "\U0001F1ED\U0001F1F0 香港自动"
      - "\U0001F1EF\U0001F1F5 日本自动"
      - "\U0001F1F0\U0001F1F7 韩国自动"
      - "\U0001F1F8\U0001F1EC 新加坡自动"
      - "\U0001F1FA\U0001F1F8 美国自动"
      - ♻️ 自动选择
      - "\U0001F1ED\U0001F1F0 香港节点"
      - "\U0001F1EF\U0001F1F5 日本节点"
      - "\U0001F1F0\U0001F1F7 韩国节点"
      - "\U0001F1F8\U0001F1EC 新加坡节点"
      - "\U0001F1FA\U0001F1F8 美国节点"
      - "\U0001F310 全部节点"
  - name: "\U0001F4FA YouTube"
    type: select
    proxies:
      - "\U0001F680 节点选择"
      - "\U0001F1ED\U0001F1F0 香港负载均衡"
      - "\U0001F1EF\U0001F1F5 日本负载均衡"
      - "\U0001F1F0\U0001F1F7 韩国负载均衡"
      - "\U0001F1F8\U0001F1EC 新加坡负载均衡"
      - "\U0001F1FA\U0001F1F8 美国负载均衡"
      - "\U0001F1ED\U0001F1F0 香港自动"
      - "\U0001F1EF\U0001F1F5 日本自动"
      - "\U0001F1F0\U0001F1F7 韩国自动"
      - "\U0001F1F8\U0001F1EC 新加坡自动"
      - "\U0001F1FA\U0001F1F8 美国自动"
      - ♻️ 自动选择
      - "\U0001F1ED\U0001F1F0 香港节点"
      - "\U0001F1EF\U0001F1F5 日本节点"
      - "\U0001F1F0\U0001F1F7 韩国节点"
      - "\U0001F1F8\U0001F1EC 新加坡节点"
      - "\U0001F1FA\U0001F1F8 美国节点"
      - "\U0001F310 全部节点"
  - name: "\U0001F3B5 TikTok"
    type: select
    proxies:
      - "\U0001F680 节点选择"
      - "\U0001F1ED\U0001F1F0 香港负载均衡"
      - "\U0001F1EF\U0001F1F5 日本负载均衡"
      - "\U0001F1F0\U0001F1F7 韩国负载均衡"
      - "\U0001F1F8\U0001F1EC 新加坡负载均衡"
      - "\U0001F1FA\U0001F1F8 美国负载均衡"
      - "\U0001F1ED\U0001F1F0 香港自动"
      - "\U0001F1EF\U0001F1F5 日本自动"
      - "\U0001F1F0\U0001F1F7 韩国自动"
      - "\U0001F1F8\U0001F1EC 新加坡自动"
      - "\U0001F1FA\U0001F1F8 美国自动"
      - ♻️ 自动选择
      - "\U0001F1ED\U0001F1F0 香港节点"
      - "\U0001F1EF\U0001F1F5 日本节点"
      - "\U0001F1F0\U0001F1F7 韩国节点"
      - "\U0001F1F8\U0001F1EC 新加坡节点"
      - "\U0001F1FA\U0001F1F8 美国节点"
      - "\U0001F310 全部节点"
  - name: "\U0001F3AC NETFLIX"
    type: select
    proxies:
      - "\U0001F680 节点选择"
      - "\U0001F1ED\U0001F1F0 香港负载均衡"
      - "\U0001F1EF\U0001F1F5 日本负载均衡"
      - "\U0001F1F0\U0001F1F7 韩国负载均衡"
      - "\U0001F1F8\U0001F1EC 新加坡负载均衡"
      - "\U0001F1FA\U0001F1F8 美国负载均衡"
      - "\U0001F1ED\U0001F1F0 香港自动"
      - "\U0001F1EF\U0001F1F5 日本自动"
      - "\U0001F1F0\U0001F1F7 韩国自动"
      - "\U0001F1F8\U0001F1EC 新加坡自动"
      - "\U0001F1FA\U0001F1F8 美国自动"
      - ♻️ 自动选择
      - "\U0001F1ED\U0001F1F0 香港节点"
      - "\U0001F1EF\U0001F1F5 日本节点"
      - "\U0001F1F0\U0001F1F7 韩国节点"
      - "\U0001F1F8\U0001F1EC 新加坡节点"
      - "\U0001F1FA\U0001F1F8 美国节点"
      - "\U0001F310 全部节点"
  - name: "\U0001F1ED\U0001F1F0 香港节点"
    type: select
    include-all: true
    filter: (?i)港|hk|hongkong|hong kong
  - name: "\U0001F1EF\U0001F1F5 日本节点"
    type: select
    include-all: true
    filter: (?i)日|jp|japan
  - name: "\U0001F1F0\U0001F1F7 韩国节点"
    type: select
    include-all: true
    filter: (?i)韩|kr|korea
  - name: "\U0001F1F8\U0001F1EC 新加坡节点"
    type: select
    include-all: true
    filter: (?i)新|狮|sg|singapore|新加坡
  - name: "\U0001F1FA\U0001F1F8 美国节点"
    type: select
    include-all: true
    filter: (?i)美|us|unitedstates|united states
  - name: "\U0001F1ED\U0001F1F0 香港自动"
    type: url-test
    include-all: true
    tolerance: 20
    interval: 300
    filter: (?=.*(港|HK|(?i)Hong))^((?!(台|日|韩|新|深|美)).)*$
  - name: "\U0001F1EF\U0001F1F5 日本自动"
    type: url-test
    include-all: true
    tolerance: 20
    interval: 300
    filter: (?=.*(日|JP|(?i)Japan))^((?!(港|台|韩|新|美)).)*$
  - name: "\U0001F1F0\U0001F1F7 韩国自动"
    type: url-test
    include-all: true
    tolerance: 20
    interval: 300
    filter: (?=.*(韩|KR|(?i)Korea))^((?!(港|台|日|新|美)).)*$
  - name: "\U0001F1F8\U0001F1EC 新加坡自动"
    type: url-test
    include-all: true
    tolerance: 20
    interval: 300
    filter: (?=.*(新|狮|SG|(?i)Singapore))^((?!(港|台|日|韩|美)).)*$
  - name: "\U0001F1FA\U0001F1F8 美国自动"
    type: url-test
    include-all: true
    tolerance: 20
    interval: 300
    filter: (?=.*(美|US|(?i)States|America))^((?!(港|台|日|韩|新)).)*$
  - name: ♻️ 自动选择
    type: url-test
    include-all: true
    tolerance: 20
    interval: 300
    filter: ^((?!(直连)).)*$
  - name: "\U0001F1ED\U0001F1F0 香港负载均衡"
    type: load-balance
    include-all: true
    interval: 60
    lazy: true
    url: 'http://www.google.com/blank.html'
    disable-udp: false
    strategy: round-robin
    timeout: 2000
    max-failed-times: 3
    filter: (?=.*(港|HK|(?i)Hong))^((?!(台|日|韩|新|深|美)).)*$
  - name: "\U0001F1EF\U0001F1F5 日本负载均衡"
    type: load-balance
    include-all: true
    interval: 60
    lazy: true
    url: 'http://www.google.com/blank.html'
    disable-udp: false
    strategy: round-robin
    timeout: 2000
    max-failed-times: 3
    filter: (?=.*(日|JP|(?i)Japan))^((?!(港|台|韩|新|美)).)*$
  - name: "\U0001F1F0\U0001F1F7 韩国负载均衡"
    type: load-balance
    include-all: true
    interval: 60
    lazy: true
    url: 'http://www.google.com/blank.html'
    disable-udp: false
    strategy: round-robin
    timeout: 2000
    max-failed-times: 3
    filter: (?=.*(韩|KR|(?i)Korea))^((?!(港|台|日|新|美)).)*$
  - name: "\U0001F1F8\U0001F1EC 新加坡负载均衡"
    type: load-balance
    include-all: true
    interval: 60
    lazy: true
    url: 'http://www.google.com/blank.html'
    disable-udp: false
    strategy: round-robin
    timeout: 2000
    max-failed-times: 3
    filter: (?=.*(新|狮|SG|(?i)Singapore))^((?!(港|台|日|韩|美)).)*$
  - name: "\U0001F1FA\U0001F1F8 美国负载均衡"
    type: load-balance
    include-all: true
    interval: 60
    lazy: true
    url: 'http://www.google.com/blank.html'
    disable-udp: false
    strategy: round-robin
    timeout: 2000
    max-failed-times: 3
    filter: (?=.*(美|US|(?i)States|America))^((?!(港|台|日|韩|新)).)*$
  - name: "\U0001F3AF 全球直连"
    type: select
    proxies:
      - DIRECT
      - "\U0001F680 节点选择"
  - name: "\U0001F420 漏网之鱼"
    type: select
    proxies:
      - "\U0001F680 节点选择"
      - "\U0001F1ED\U0001F1F0 香港负载均衡"
      - "\U0001F1EF\U0001F1F5 日本负载均衡"
      - "\U0001F1F0\U0001F1F7 韩国负载均衡"
      - "\U0001F1F8\U0001F1EC 新加坡负载均衡"
      - "\U0001F1FA\U0001F1F8 美国负载均衡"
      - "\U0001F1ED\U0001F1F0 香港自动"
      - "\U0001F1EF\U0001F1F5 日本自动"
      - "\U0001F1F0\U0001F1F7 韩国自动"
      - "\U0001F1F8\U0001F1EC 新加坡自动"
      - "\U0001F1FA\U0001F1F8 美国自动"
      - ♻️ 自动选择
      - "\U0001F1ED\U0001F1F0 香港节点"
      - "\U0001F1EF\U0001F1F5 日本节点"
      - "\U0001F1F0\U0001F1F7 韩国节点"
      - "\U0001F1F8\U0001F1EC 新加坡节点"
      - "\U0001F1FA\U0001F1F8 美国节点"
      - "\U0001F310 全部节点"
rules:
  - 'RULE-SET,BanAD,REJECT'
  - 'RULE-SET,BanProgramAD,REJECT'
  - 'RULE-SET,adobe,REJECT'
  - "RULE-SET,youtube_domain,\U0001F4FA YouTube"
  - 'RULE-SET,tencent,DIRECT'
  - 'RULE-SET,private_domain,DIRECT'
  - 'RULE-SET,TencentVideo,DIRECT'
  - "RULE-SET,apple_domain,\U0001F3AF 全球直连"
  - "RULE-SET,ai,\U0001F4AC ChatGPT"
  - "RULE-SET,Spotify,\U0001F680 节点选择"
  - "RULE-SET,github_domain,\U0001F680 节点选择"
  - "RULE-SET,google_domain,\U0001F680 节点选择"
  - "RULE-SET,onedrive_domain,\U0001F3AF 全球直连"
  - "RULE-SET,microsoft_domain,\U0001F3AF 全球直连"
  - "RULE-SET,tiktok_domain,\U0001F3B5 TikTok"
  - "RULE-SET,speedtest_domain,\U0001F680 节点选择"
  - "RULE-SET,telegram_domain,\U0001F680 节点选择"
  - "RULE-SET,netflix_domain,\U0001F3AC NETFLIX"
  - "RULE-SET,Netflix,\U0001F3AC NETFLIX"
  - "RULE-SET,paypal_domain,\U0001F680 节点选择"
  - "RULE-SET,geolocation-!cn,\U0001F680 节点选择"
  - "RULE-SET,cn_domain,\U0001F3AF 全球直连"
  - "RULE-SET,google_ip,\U0001F680 节点选择,no-resolve"
  - "RULE-SET,netflix_ip,\U0001F3AC NETFLIX,no-resolve"
  - "RULE-SET,telegram_ip,\U0001F680 节点选择,no-resolve"
  - "RULE-SET,cn_ip,\U0001F3AF 全球直连"
  - "RULE-SET,proxylite,\U0001F680 节点选择"
  - "MATCH,\U0001F420 漏网之鱼"
rule-anchor:
  ip:
    type: http
    interval: 86400
    behavior: ipcidr
    format: mrs
  domain:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
  qcy:
    type: http
    interval: 86400
    behavior: domain
    format: text
  class:
    type: http
    interval: 86400
    behavior: classical
    format: text
rule-providers:
  BanAD:
    type: http
    interval: 86400
    behavior: classical
    format: text
    url: 'https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/Clash/BanAD.list'
  tencent:
    type: http
    interval: 86400
    behavior: classical
    format: text
    url: >-
      https://cdn.jsdelivr.net/gh/blackmatrix7/ios_rule_script@master/rule/Shadowrocket/Tencent/Tencent.list
  TencentVideo:
    type: http
    interval: 86400
    behavior: classical
    format: text
    url: >-
      https://cdn.jsdelivr.net/gh/blackmatrix7/ios_rule_script@master/rule/Shadowrocket/TencentVideo/TencentVideo.list
  BanProgramAD:
    type: http
    interval: 86400
    behavior: classical
    format: text
    url: >-
      https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/Clash/BanProgramAD.list
  adobe:
    type: http
    interval: 86400
    behavior: classical
    format: text
    url: 'https://raw.githubusercontent.com/cmliu/ACL4SSR/main/Clash/adobe.list'
  private_domain:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/private.mrs
  ai:
    type: http
    interval: 86400
    behavior: classical
    format: text
    url: >-
      https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/a98edee7cd64d64cb58ce26d63d2dbd2475575ad/rule/Clash/OpenAI/OpenAI.list
  Spotify:
    type: http
    interval: 86400
    behavior: classical
    format: text
    url: >-
      https://cdn.jsdelivr.net/gh/blackmatrix7/ios_rule_script@release/rule/Shadowrocket/Spotify/Spotify.list
  Netflix:
    type: http
    interval: 86400
    behavior: classical
    format: text
    url: >-
      https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/a98edee7cd64d64cb58ce26d63d2dbd2475575ad/rule/Clash/Netflix/Netflix.list
  youtube_domain:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/youtube.mrs
  google_domain:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/google.mrs
  github_domain:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/github.mrs
  telegram_domain:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/telegram.mrs
  netflix_domain:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/netflix.mrs
  paypal_domain:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/paypal.mrs
  onedrive_domain:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/onedrive.mrs
  microsoft_domain:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/microsoft.mrs
  apple_domain:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/apple-cn.mrs
  speedtest_domain:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/ookla-speedtest.mrs
  tiktok_domain:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/tiktok.mrs
  gfw_domain:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/gfw.mrs
  geolocation-!cn:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/geolocation-!cn.mrs
  cn_domain:
    type: http
    interval: 86400
    behavior: domain
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geosite/cn.mrs
  proxylite:
    type: http
    interval: 86400
    behavior: classical
    format: text
    url: >-
      https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/refs/heads/master/rule/Clash/ProxyLite/ProxyLite.list
  cn_ip:
    type: http
    interval: 86400
    behavior: ipcidr
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geoip/cn.mrs
  google_ip:
    type: http
    interval: 86400
    behavior: ipcidr
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geoip/google.mrs
  telegram_ip:
    type: http
    interval: 86400
    behavior: ipcidr
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geoip/telegram.mrs
  netflix_ip:
    type: http
    interval: 86400
    behavior: ipcidr
    format: mrs
    url: >-
      https://raw.githubusercontent.com/MetaCubeX/meta-rules-dat/meta/geo/geoip/netflix.mrs
